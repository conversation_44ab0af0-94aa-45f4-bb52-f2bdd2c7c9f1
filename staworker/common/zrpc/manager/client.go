package manager

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/projectdeviceservice"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	device projectdeviceservice.ProjectDeviceService
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		device: projectdeviceservice.NewProjectDeviceService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) AcquireProjectDevice(
	ctx context.Context, in *managerpb.AcquireProjectDeviceReq, opts ...grpc.CallOption,
) (*managerpb.AcquireProjectDeviceResp, error) {
	return c.device.AcquireProjectDevice(ctx, in, opts...)
}

func (c *RPCClient) ReleaseProjectDevice(
	ctx context.Context, in *managerpb.ReleaseProjectDeviceReq, opts ...grpc.CallOption,
) (*managerpb.ReleaseProjectDeviceResp, error) {
	return c.device.ReleaseProjectDevice(ctx, in, opts...)
}
