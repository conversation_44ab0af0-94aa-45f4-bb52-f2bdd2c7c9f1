package account

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/client/account"
	accountpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	client account.Account
}

func NewRPCClient(conf zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: conf,

		client: account.NewAccount(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *RPCClient) AcquireAccount(
	ctx context.Context, in *accountpb.QueryAccountPoolEnvDataRequest, opts ...grpc.CallOption,
) (*accountpb.QueryAccountPoolEnvDataResponse, error) {
	return c.client.QueryAccountPoolEnvData(ctx, in, opts...)
}

func (c *RPCClient) ReleaseAccount(
	ctx context.Context, in *accountpb.ReleaseTestAccountRequest, opts ...grpc.CallOption,
) (*accountpb.ReleaseTestAccountResponse, error) {
	return c.client.ReleaseTestAccount(ctx, in, opts...)
}
