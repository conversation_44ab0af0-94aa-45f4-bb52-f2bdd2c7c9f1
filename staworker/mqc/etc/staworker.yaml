Name: staworker

Log:
  ServiceName: mqc.staworker
  Encoding: plain
  Level: info
  Path: /app/logs/staworker

#Prometheus:
#  Host: 0.0.0.0
#  Port: 9101
#  Path: /metrics
#
#Telemetry:
#  Name: mqc.staworker
#  Endpoint: http://tt-yw-tracing-jaeger.ttyuyin.com:9511
#  Sampler: 1.0
#  Batcher: zipkin
#
#DevServer:
#  Enabled: true
#  Port: 6470

Redis:
  Key: mqc.staworker
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 23

DispatcherRedis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 4

StabilityWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:staworker
  ConsumerTag: mqc:staworker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 23
  MaxWorker: 0

DeviceHubProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:devicehub
  Db: 17

LocalPath: ./pvc/stability_test
