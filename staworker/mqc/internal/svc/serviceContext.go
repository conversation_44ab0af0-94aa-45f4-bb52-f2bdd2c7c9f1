package svc

import (
	"github.com/electricbubble/gadb"
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common/zrpc/account"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common/zrpc/manager"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis           *redis.Redis
	RedisNode       red.UniversalClient
	DispatcherRedis red.UniversalClient

	ADBClient *gadb.Client

	AccountRPC *account.RPCClient
	ManagerRPC *manager.RPCClient

	StabilityWorkerConsumer *consumer.Consumer
	DeviceHubProducer       *producer.Producer
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,

		Redis:           redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		RedisNode:       qetredis.NewClient(c.Redis.RedisConf),
		DispatcherRedis: qetredis.NewClient(c.DispatcherRedis),

		ADBClient: mustNewADBClient(),

		AccountRPC: account.NewRPCClient(c.Account),
		ManagerRPC: manager.NewRPCClient(c.Manager),

		StabilityWorkerConsumer: consumer.NewConsumer(c.StabilityWorkerConsumer),
		DeviceHubProducer:       producer.NewProducer(c.DeviceHubProducer),
	}
}

func mustNewADBClient() *gadb.Client {
	client, err := gadb.NewClient()
	logx.Must(err)
	return client
}
