package internal

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/proc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

func HandleSetupOperations(svcCtx *svc.ServiceContext) error {
	if err := svcCtx.ADBClient.StartServer(); err != nil {
		return errors.Errorf("failed to start adb server, error: %+v", err)
	}

	proc.AddShutdownListener(
		func() {
			_ = svcCtx.ADBClient.KillServer()
		},
	)

	return nil
}
