package tasks

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

var _ base.Handler = (*StabilityPlanTaskProcessor)(nil)

type StabilityPlanTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewStabilityPlanTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &StabilityPlanTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *StabilityPlanTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	var req dispatcherpb.WorkerReq
	if err = protobuf.UnmarshalJSON(task.Payload, &req); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of stability test task, payload: %s, error: %+v",
			task.Payload, err,
		)
	}

	//info, err := getTaskInfoFromTaskReq(&req)
	//if err != nil {
	//	return []byte(constants.FAILURE), err
	//}
	//
	//ctx = updateContext(ctx, info)
	//logger.Infof("stability task info: %s", protobuf.MarshalJSONIgnoreError(info))
	//
	//if err = statest.NewExecuteStabilityTestLogic(ctx, p.svcCtx, info).Execute(); err != nil {
	//	return []byte(constants.FAILURE), err
	//}

	return []byte(constants.SUCCESS), nil
}

//func getTaskInfoFromTaskReq(req *dispatcherpb.WorkerReq) (*commonpb.StabilityCaseTaskInfo, error) {
//	data := req.GetStabilityCase()
//	if data == nil {
//		return nil, errorx.Errorf(
//			errorx.ValidateParamError,
//			"the stability case is null, task payload: %s",
//			protobuf.MarshalJSONIgnoreError(req),
//		)
//	}
//
//	info := &commonpb.StabilityCaseTaskInfo{
//		ProjectId:       req.GetProjectId(),
//		PlanId:          data.GetStabilityPlanId(),
//		TaskId:          req.GetTaskId(),
//		ExecuteId:       req.GetExecuteId(),
//		PlanExecuteId:   data.GetStabilityPlanExecuteId(),
//		TriggerMode:     req.GetTriggerMode(),
//		DeviceType:      data.GetDeviceType(),
//		PlatformType:    data.GetPlatformType(),
//		Udid:            data.GetUdid(),
//		AppDownloadLink: data.GetAppDownloadLink(),
//		Activities:      data.GetActivities(),
//		CustomScript:    data.GetCustomScript(),
//		Duration:        data.GetDuration(),
//	}
//	if err := info.ValidateAll(); err != nil {
//		return nil, errorx.Errorf(
//			errorx.ValidateParamError,
//			"the stability case is invalid, task payload: %s, error: %+v",
//			protobuf.MarshalJSONIgnoreError(req), err,
//		)
//	}
//
//	return info, nil
//}
