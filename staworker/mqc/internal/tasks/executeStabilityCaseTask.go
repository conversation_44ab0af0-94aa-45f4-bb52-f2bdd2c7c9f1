package tasks

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

var _ base.Handler = (*StabilityCaseTaskProcessor)(nil)

type StabilityCaseTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewStabilityCaseTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &StabilityCaseTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *StabilityCaseTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	var info commonpb.StabilityCaseTaskInfo
	if err := protobuf.UnmarshalJSON(task.Payload, &info); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of stability case task, payload: %s, error: %+v",
			task.Payload, err,
		)
	}

	if err := info.ValidateAll(); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"failed to validate the payload of stability case task, payload: %s, error: %+v",
			task.Payload, err,
		)
	}

	logger.Infof("stability case task info: %s", protobuf.MarshalJSONIgnoreError(&info))
	ctx = updateContext(ctx, info.GetTaskId(), info.GetExecuteId())

	return []byte(constants.SUCCESS), nil
}
