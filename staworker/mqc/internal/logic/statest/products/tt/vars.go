package tt

import gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"

var (
	launchedActivities = []string{activityOfComposeLogin, activityOfLogin, activityOfHome}
)

var (
	selectorOfSkip          = gu2.ByText(textOfSkip)          // 跳过
	selectorOfAgree         = gu2.ByText(textOfAgree)         // 同意
	selectorOfUserAgreement = gu2.ByText(textOfUserAgreement) // 用户协议
	selectorOfJuvenileMode  = gu2.ByText(textOfJuvenileMode)  // 未成年人模式
	selectorOfIKnow         = gu2.ByText(textOfIKnow)         // 我知道了

	selectorOfHasAccount    = gu2.ByResourceId(resourceIDOfHasAccount)    // 已有账号？
	selectorOfLoginPhone    = gu2.ByResourceId(resourceIDOfLoginPhone)    // 请输入手机号/TT语音号
	selectorOfLoginPassword = gu2.ByResourceId(resourceIDOfLoginPassword) // 请输入密码
	selectorOfLoginProxy    = gu2.ByResourceId(resourceIDOfLoginProxy)    // 我已阅读并同意
	selectorOfLoginButton   = gu2.ByResourceId(resourceIDOfLoginButton)   // 登录

	selectorOfLoginLotteryCloseButton = gu2.ByResourceId(resourceIDOfLoginLotteryCloseButton) // 签到领好礼
	selectorOfHomeChannel             = gu2.ByResourceId(resourceIDOfHomeChannel)             // 首页
)
