package tt

import (
	"context"
	"slices"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/statest/device"
)

type AndroidLogic struct {
	logx.Logger
	ctx context.Context

	device *device.AndroidDevice
}

func NewAndroidLogic(ctx context.Context, device *device.AndroidDevice) *AndroidLogic {
	return &AndroidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		device: device,
	}
}

func (l *AndroidLogic) Launch() error {
	var (
		adb  = l.device.ADB()
		udid = l.device.UDID()
	)

	driver, err := gu2.NewDriver(adb)
	if err != nil {
		return errors.Wrapf(err, "failed to new uiautomator2 driver, udid: %s", udid)
	}
	defer func(driver *gu2.Driver) {
		if driver != nil {
			_ = driver.Close()
		}
	}(driver)

	if err = l.device.AppLaunch(packageName); err != nil {
		return err
	}

	timer := timewheel.NewTimer(10 * time.Second)
	defer timer.Stop()

	skipE := driver.FindElementBySelectorOptions(selectorOfSkip)   // 广告的「跳过」按钮
	agreeE := driver.FindElementBySelectorOptions(selectorOfAgree) // 「温馨提示」的「同意」按钮

	for {
		select {
		case <-l.ctx.Done():
			return errors.Errorf(
				"got a done signal while launching the app, udid: %s, error: %+v",
				l.device.UDID(), l.ctx.Err(),
			)
		case <-timer.C:
			return nil
		default:
			if activity, err := l.device.TopActivity(); err == nil && activity != nil {
				if slices.Contains(launchedActivities, activity.ActivityName) {
					return nil
				}
			}

			for _, element := range []*gu2.Element{
				skipE,
				agreeE,
			} {
				if ok, _ := element.Exist(); ok {
					_ = element.Click()
				}
			}

			time.Sleep(time.Second)
		}
	}
}

func (l *AndroidLogic) Login(account, password string) error {
	var (
		adb  = l.device.ADB()
		udid = l.device.UDID()
	)

	driver, err := gu2.NewDriver(adb)
	if err != nil {
		return errors.Wrapf(err, "failed to new uiautomator2 driver, udid: %s", udid)
	}
	defer func(driver *gu2.Driver) {
		if driver != nil {
			_ = driver.Close()
		}
	}(driver)

	// 「已有账号？」
	_ = driver.FindElementBySelectorOptions(selectorOfHasAccount).Click(gu2.WithTimeout(time.Second))

	// 页面切换到「欢迎登录TT语音」
	time.Sleep(2 * time.Second)

	// 「请输入手机号/TT语音号」
	_ = driver.FindElementBySelectorOptions(selectorOfLoginPhone).SetText(account, gu2.WithTimeout(5*time.Second))

	// 「请输入密码」
	passwordE := driver.FindElementBySelectorOptions(selectorOfLoginPassword)
	_ = passwordE.ClearText()
	_ = passwordE.SetText(password, gu2.WithTimeout(5*time.Second))

	// 「我已阅读并同意」
	loginProxyE := driver.FindElementBySelectorOptions(selectorOfLoginProxy)
	info, err := loginProxyE.Info()
	if err != nil || !info.Selected {
		_ = loginProxyE.Click()
	}

	// 「登录」按钮
	_ = driver.FindElementBySelectorOptions(selectorOfLoginButton).Click(gu2.WithTimeout(5 * time.Second))

	timeout := time.Minute
	timer := timewheel.NewTimer(timeout)
	defer timer.Stop()

	var (
		homeE          = driver.FindElementBySelectorOptions(selectorOfHomeChannel)             // 首页
		userAgreementE = driver.FindElementBySelectorOptions(selectorOfUserAgreement)           // 「用户协议更新」
		agreeE         = driver.FindElementBySelectorOptions(selectorOfAgree)                   // 「同意」按钮
		loginLotteryE  = driver.FindElementBySelectorOptions(selectorOfLoginLotteryCloseButton) // 「签到领好礼」
		juvenileModeE  = driver.FindElementBySelectorOptions(selectorOfJuvenileMode)            // 「未成年人模式」
		iKnowE         = driver.FindElementBySelectorOptions(selectorOfIKnow)                   // 「我知道了」按钮
	)

	for {
		select {
		case <-l.ctx.Done():
			return errors.Errorf(
				"got a done signal while processing the login logic, udid: %s, error: %+v", udid, l.ctx.Err(),
			)
		case <-timer.C:
			return errors.Errorf(
				"timeout while processing the login logic, udid: %s, timeout: %s", udid, timeout.String(),
			)
		default:
			if exist, err := homeE.Exist(); err == nil && exist {
				return nil
			}

			// 「用户协议更新」
			if ok, _ := userAgreementE.Exist(); ok {
				_ = agreeE.Click()
			}

			// 「签到领好礼」
			if ok, _ := loginLotteryE.Exist(); ok {
				_ = loginLotteryE.Click()
			}

			// 「未成年人模式」
			if ok, _ := juvenileModeE.Exist(); ok {
				_ = iKnowE.Click()
			}

			time.Sleep(time.Second)
		}
	}
}
