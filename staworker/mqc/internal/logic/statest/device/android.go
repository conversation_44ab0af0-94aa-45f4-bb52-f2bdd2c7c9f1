package device

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/dlclark/regexp2"
	"github.com/electricbubble/gadb"
	"github.com/pkg/errors"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"
	"go.uber.org/multierr"

	// gu2 "github.com/electricbubble/guia2" // client package for https://github.com/appium/appium-uiautomator2-server
	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2" // client package for https://github.com/openatx/android-uiautomator-server
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/collector"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

const (
	groupNameOfX            = "x"
	groupNameOfY            = "y"
	groupNameOfWidth        = "width"
	groupNameOfHeight       = "height"
	groupNameOfDensity      = "density"
	groupNameOfOrientation  = "orientation"
	groupNameOfPackageName  = "package_name"
	groupNameOfActivityName = "activity_name"
	groupNameOfPid          = "pid"

	basePathOnAndroid   = "/data/local/tmp"
	sdcardPathOnAndroid = "/sdcard"
	logNameOfFastbot    = "fastbot.log"

	strOfMonkeyAborted         = "monkey aborted"
	strOfNoSuchFileOrDirectory = "No such file or directory"
	strOfTrue                  = "true"
	strOfScreenStateOn         = "SCREEN_STATE_ON"

	commandOfGetWindowSize     = "wm size"
	commandOfGetDisplay        = "dumpsys display"
	commandOfGetWindow         = "dumpsys window"
	commandOfGetSurfaceFlinger = "dumpsys SurfaceFlinger"
	commandOfGetInput          = "dumpsys input"
	commandOfGetWindowDisplays = "dumpsys window displays"
	commandOfInstallApp        = "pm install -r -t %s"
	commandOfUninstallApp      = "pm uninstall %s"
	commandOfGetVersion        = "dumpsys package %s | grep versionName"
	commandOfLaunchApp         = "monkey -p %s -c android.intent.category.LAUNCHER 1"
	commandOfGetTopActivity    = "dumpsys activity top"
	commandOfGetWindowPolicy   = "dumpsys window policy"
	commandOfListPackages      = "pm list packages"
	commandOfMonkeyTest        = `CLASSPATH=/sdcard/monkeyq.jar:/sdcard/framework.jar:/sdcard/fastbot-thirdpart.jar exec app_process /system/bin com.android.commands.monkey.Monkey -p %s --agent reuseq --running-minutes %d --throttle %d --bugreport --output-directory %s -v -v`
	commandOfGetCPUInfo        = `dumpsys cpuinfo | grep "/%s" | grep -v grep`
	commandOfGetMemoryInfo     = `dumpsys meminfo %s | grep TOTAL | grep -v grep`
	commandOfGetFXInfo         = `dumpsys gfxinfo %s | grep "50th percentile:"`

	commandOfMenuKeyEvent = "input keyevent MENU"
	commandOfBackKeyEvent = "input keyevent BACK"

	filenameOfMaxValidStrings          = "max.valid.strings"
	filenameOfActivityWhiteListStrings = "awl.strings"
	filenameOfMaxActivityStatisticsLog = "max.activity.statistics.log"

	timeoutOfPushMaxValidStrings          = 10 * time.Second
	timeoutOfPushActivityWhiteListStrings = 10 * time.Second
)

var (
	windowSizeRE = regexp2.MustCompile(
		fmt.Sprintf(`(?<%s>\d+)x(?<%s>\d+)\s*$`, groupNameOfWidth, groupNameOfHeight), regexp2.None,
	)
	physicalDisplayRE = regexp2.MustCompile(
		fmt.Sprintf(
			`.*PhysicalDisplayInfo{(?<%s>\d+) x (?<%s>\d+), .*, density (?<%s>[\d.]+).*`,
			groupNameOfWidth, groupNameOfHeight, groupNameOfDensity,
		),
		regexp2.None,
	)
	unrestrictedScreenRE = regexp2.MustCompile(
		fmt.Sprintf(
			`\s*mUnrestrictedScreen=\((?<%s>\d+),(?<%s>\d+)\) (?<%s>\d+)x(?<%s>\d+)`,
			groupNameOfX, groupNameOfY, groupNameOfWidth, groupNameOfHeight,
		), regexp2.None,
	)
	displayWHRE = regexp2.MustCompile(
		fmt.Sprintf(`\s*DisplayWidth=(?<%s>\d+) *DisplayHeight=(?<%s>\d+)`, groupNameOfWidth, groupNameOfHeight),
		regexp2.None,
	)
	orientationRE = regexp2.MustCompile(
		fmt.Sprintf(`orientation=(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	surfaceOrientationRE = regexp2.MustCompile(
		fmt.Sprintf(`SurfaceOrientation:\s+(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	displayFramesRE = regexp2.MustCompile(
		fmt.Sprintf(`DisplayFrames.*r=(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	activityRE = regexp2.MustCompile(
		fmt.Sprintf(
			`\s*ACTIVITY (?<%s>[A-Za-z0-9_.$]+)/(?<%s>[A-Za-z0-9_.$]+) \w+ pid=(?<%s>\d+)`,
			groupNameOfPackageName, groupNameOfActivityName, groupNameOfPid,
		), regexp2.None,
	)
	screenOnRE    = regexp2.MustCompile("mScreenOnFully=(true|false)", regexp2.None)
	screenStateRE = regexp2.MustCompile("screenState=(SCREEN_STATE_ON|SCREEN_STATE_OFF)", regexp2.None)
	lockScreenRE  = regexp2.MustCompile(
		"(?:mShowingLockscreen|isStatusBarKeyguard|showing)=(true|false)", regexp2.None,
	)
	manualConfirmationTexts = []string{"继续安装", "确定", "完成"}

	_ IDevice = (*AndroidDevice)(nil)
)

type AndroidDevice struct {
	logx.Logger
	ctx context.Context

	deviceType            commonpb.DeviceType
	serial, remoteAddress string

	client *gadb.Client
	device *gadb.Device

	displayInfo *DisplayInfo
}

func NewAndroidDevice(
	ctx context.Context, deviceType commonpb.DeviceType, serial, remoteAddress string,
) (*AndroidDevice, error) {
	client, device, err := utils.ADBConnect(serial, remoteAddress)
	if err != nil {
		return nil, err
	}

	if serial == "" {
		serial = device.Serial()
	}

	d := &AndroidDevice{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		deviceType:    deviceType,
		serial:        serial,
		remoteAddress: remoteAddress,

		client: client,
		device: device,
	}

	d.displayInfo, err = d.DisplayInfo()
	if err != nil {
		return nil, err
	}

	return d, nil
}

func (d *AndroidDevice) Close() (err error) {
	return err
}

func (d *AndroidDevice) DeviceType() commonpb.DeviceType {
	return d.deviceType
}

func (d *AndroidDevice) PlatformType() commonpb.PlatformType {
	return commonpb.PlatformType_ANDROID
}

func (d *AndroidDevice) UDID() string {
	return d.serial
}

func (d *AndroidDevice) ADB() *gadb.Device {
	return d.device
}

func (d *AndroidDevice) DisplayInfo() (*DisplayInfo, error) {
	var err error
	for _, item := range []*findAndMatch{
		{
			cmd: commandOfGetWindowSize,
			re:  windowSizeRE,
		},
		{
			cmd: commandOfGetDisplay,
			re:  physicalDisplayRE,
		},
		{
			cmd: commandOfGetWindow,
			re:  unrestrictedScreenRE,
		},
		{
			cmd: commandOfGetWindow,
			re:  displayWHRE,
		},
	} {
		di, e := d.getDisplayInfo(item)
		if e != nil {
			err = multierr.Append(err, e)
			continue
		}

		return di, nil
	}

	return nil, err
}

func (d *AndroidDevice) getDisplayInfo(fm *findAndMatch) (*DisplayInfo, error) {
	output, err := d.device.RunShellCommand(fm.cmd)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the display info, serial: %s", d.serial)
	}

	match, err := fm.re.FindStringMatch(output)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to search for the Regexp, serial: %s, output: %s", d.serial, output)
	} else if match == nil {
		return nil, errors.Errorf("no content matching the Regexp was found, serial: %s, output: %s", d.serial, output)
	}

	group := match.GroupByName(groupNameOfWidth)
	if group == nil {
		return nil, errors.Errorf("failed to get the width, serial: %s, match: %s", d.serial, match.String())
	}
	width, err := strconv.Atoi(group.String())
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to convert the width to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	group = match.GroupByName(groupNameOfHeight)
	if group == nil {
		return nil, errors.Errorf("failed to get the height, serial: %s, match: %s", d.serial, match.String())
	}
	height, err := strconv.Atoi(group.String())
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to convert the height to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	return &DisplayInfo{
		Width:  width,
		Height: height,
	}, nil
}

func (d *AndroidDevice) Orientation() (int, error) {
	var err error
	for _, item := range []*findAndMatch{
		{
			cmd: commandOfGetSurfaceFlinger,
			re:  orientationRE,
		},
		{
			cmd: commandOfGetInput,
			re:  surfaceOrientationRE,
		},
		{
			cmd: commandOfGetWindowDisplays,
			re:  displayFramesRE,
		},
	} {
		ori, e := d.getOrientation(item)
		if e != nil {
			err = multierr.Append(err, e)
			continue
		}

		return ori, nil
	}

	return 0, err
}

func (d *AndroidDevice) getOrientation(fm *findAndMatch) (int, error) {
	output, err := d.device.RunShellCommand(fm.cmd)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to get the orientation, serial: %s", d.serial)
	}

	match, err := fm.re.FindStringMatch(output)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to search for the Regexp, serial: %s, output: %s", d.serial, output)
	} else if match == nil {
		return 0, errors.Errorf("no content matching the Regexp was found, serial: %s, output: %s", d.serial, output)
	}

	group := match.GroupByName(groupNameOfOrientation)
	if group == nil {
		return 0, errors.Errorf("failed to get the orientation, serial: %s, match: %s", d.serial, match.String())
	}
	orientation, err := strconv.Atoi(group.String())
	if err != nil {
		return 0, errors.Wrapf(
			err, "failed to convert the orientation to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	return orientation, nil
}

func (d *AndroidDevice) IsExists(filepath string) bool {
	output, err := d.device.RunShellCommand("ls", filepath)
	if err != nil {
		return false
	}

	return !strings.Contains(output, strOfNoSuchFileOrDirectory)
}

func (d *AndroidDevice) AppInstall(appPath string) error {
	var (
		needToReinstall, needToInstall bool

		driver *gu2.Driver
		exitCh chan lang.PlaceholderType
	)

	defer func() {
		if exitCh != nil {
			close(exitCh)
		}
		if driver != nil {
			_ = driver.Close()
		}
	}()

	info, err := apk.OpenFile(appPath)
	if err != nil {
		return errors.Wrapf(err, "failed to open the apk file, serial: %s, file: %s", d.serial, appPath)
	}

	packageName := info.PackageName()
	packages, err := d.ListPackages(WithFilterOfPackageName(packageName))
	if err != nil {
		return err
	}

	var cmd, output string
	if packages.Size() != 0 {
		cmd = fmt.Sprintf(commandOfGetVersion, packageName)
		output, err = d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(
				err, "failed to get the version of the app, serial: %s, package_name: %s", d.serial, packageName,
			)
		}

		target := strings.TrimSpace(output)
		target = strings.TrimPrefix(target, "versionName=")
		source := info.Manifest().VersionName

		if !strings.EqualFold(source, target) {
			d.Infof(
				"need to reinstall the app, serial: %s, file: %s, package_name: %s, version: %s(%s => %s)",
				d.serial, appPath, info.PackageName(), source, target,
			)
			needToInstall = true
			needToReinstall = true
		}
	} else {
		needToInstall = true
	}

	if needToInstall {
		if needToReinstall {
			cmd = fmt.Sprintf(commandOfUninstallApp, packageName)
			if _, err = d.device.RunShellCommand(cmd); err != nil {
				return errors.Wrapf(
					err, "failed to uninstall the app, serial: %s, package_name: %s", d.serial, packageName,
				)
			}
		}

		pathOnDevice := filepath.Join(basePathOnAndroid, fmt.Sprintf("%s.apk", info.PackageName()))
		if err = d.device.PushFile(appPath, pathOnDevice); err != nil {
			return errors.Wrapf(
				err,
				"failed to push apk file to the device, serial: %s, file: %s, path: %s",
				d.serial, appPath, pathOnDevice,
			)
		}

		// if the device is an Android real phone, then try to check whether
		// it requires manual confirmation when installing an app.
		if d.deviceType == commonpb.DeviceType_REAL_PHONE {
			driver, err = gu2.NewDriver(d.device)
			if err != nil {
				d.Errorf("failed to new uiautomator2 driver, serial: %s, error: %+v", d.serial, err)
			} else {
				exitCh = make(chan lang.PlaceholderType)
				threading.GoSafeCtx(
					d.ctx, func() {
						d.appInstallWithManualConfirmation(driver, exitCh)
					},
				)
			}
		}

		cmd = fmt.Sprintf(commandOfInstallApp, pathOnDevice)
		output, err = d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(err, "failed to run the shell command, serial: %s, command: %q", d.serial, cmd)
		}

		if d.deviceType == commonpb.DeviceType_REAL_PHONE {
			// sleep for 3 seconds to avoid missing the pop-up window after installing the app.
			time.Sleep(3 * time.Second)
		}
		d.Infof(
			"succeed to install the app, serial: %s, file: %s, package_name: %s, version: %s, command: %q, result: %s",
			d.serial, appPath, info.PackageName(), info.Manifest().VersionName, cmd, output,
		)
	} else {
		d.Debugf(
			"the app has been installed, serial: %s, file: %s, package_name: %s, version: %s",
			d.serial, appPath, info.PackageName(), info.Manifest().VersionName,
		)
	}

	return nil
}

func (d *AndroidDevice) appInstallWithManualConfirmation(driver *gu2.Driver, exitCh <-chan lang.PlaceholderType) {
	for {
		select {
		case <-d.ctx.Done():
			d.Errorf("got a done signal while installing the app, serial: %s, error: %+v", d.serial, d.ctx.Err())
			return
		case <-exitCh:
			d.Debugf("got an exit signal while installing the app, serial: %s", d.serial)
			return
		default:
			for _, text := range manualConfirmationTexts {
				element := driver.FindElementBySelectorOptions(gu2.ByText(text))
				if element == nil {
					continue
				} else if ok, err := element.Exist(); err != nil || !ok {
					continue
				} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
					continue
				}

				d.Debugf("click th element successfully, serial: %s, text: %s", d.serial, text)
				break
			}

			time.Sleep(time.Second)
		}
	}
}

func (d *AndroidDevice) AppUninstall(packageName string) error {
	packages, err := d.ListPackages(WithFilterOfPackageName(packageName))
	if err != nil {
		return err
	}

	if packages.Size() != 0 {
		cmd := fmt.Sprintf(commandOfUninstallApp, packageName)
		output, err := d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(err, "failed to uninstall the app, serial: %s, package_name: %s", d.serial, packageName)
		}

		d.Infof("succeed to uninstall the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output)
	} else {
		d.Debugf("the app has not been installed, serial: %s, package_name: %s", d.serial, packageName)
	}

	return nil
}

func (d *AndroidDevice) AppLaunch(packageName string) error {
	cmd := fmt.Sprintf(commandOfLaunchApp, packageName)
	output, err := d.device.RunShellCommand(cmd)
	if err != nil {
		return errors.Wrapf(err, "failed to launch the app, serial: %s, package_name: %s", d.serial, packageName)
	}

	output = strings.TrimSpace(output)
	if strings.Contains(output, strOfMonkeyAborted) {
		return errors.Errorf(
			"failed to launch the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output,
		)
	}

	d.Infof("succeed to launch the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output)
	return nil
}

func (d *AndroidDevice) TopActivity() (*Activity, error) {
	output, err := d.device.RunShellCommand(commandOfGetTopActivity)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the top activity, serial: %s", d.serial)
	}

	match, err := activityRE.FindStringMatch(output)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to match the top activity, serial: %s, output: %s", d.serial, output)
	}

	activities := make([]*Activity, 0, constants.ConstDefaultMakeSliceSize)
	fn := func() error {
		defer func() {
			match, _ = activityRE.FindNextMatch(match)
		}()

		group := match.GroupByName(groupNameOfPackageName)
		if group == nil {
			return errors.Errorf(
				"failed to get the package name from the top activity, serial: %s, match: %s", d.serial, match.String(),
			)
		}
		packageName := group.String()

		group = match.GroupByName(groupNameOfActivityName)
		if group == nil {
			return errors.Errorf(
				"failed to get the activity name from the top activity, serial: %s, match: %s", d.serial,
				match.String(),
			)
		}
		activityName := group.String()

		group = match.GroupByName(groupNameOfPid)
		if group == nil {
			return errors.Errorf(
				"failed to get the pid from the top activity, serial: %s, match: %s", d.serial, match.String(),
			)
		}
		pid, err := strconv.ParseInt(group.String(), 10, 64)
		if err != nil {
			return errors.Wrapf(
				err,
				"failed to parse the pid from the top activity, serial: %s, match: %s, group: %s",
				d.serial, match.String(), group.String(),
			)
		}

		activities = append(
			activities, &Activity{
				PackageName:  packageName,
				ActivityName: activityName,
				Pid:          pid,
			},
		)
		return nil
	}
	for match != nil {
		if err = fn(); err != nil {
			break
		}
	}
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the top activity, serial: %s, output: %s", d.serial, output)
	} else if len(activities) == 0 {
		return nil, errors.Errorf("not found any activities, serial: %s, output: %s", d.serial, output)
	}

	return activities[len(activities)-1], nil
}

func (d *AndroidDevice) IsScreenOn() bool {
	output, err := d.device.RunShellCommand(commandOfGetWindowPolicy)
	if err != nil {
		return false
	}

	match, err := screenOnRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfTrue
		}
	}

	match, err = screenStateRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfScreenStateOn
		}
	}

	return false
}

func (d *AndroidDevice) IsLocked() bool {
	output, err := d.device.RunShellCommand(commandOfGetWindowPolicy)
	if err != nil {
		return false
	}

	match, err := lockScreenRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfTrue
		}
	}

	return false
}

// Unlock the screen
// NOTE: might not work on all devices
func (d *AndroidDevice) Unlock() {
	_, _ = d.device.RunShellCommand(commandOfMenuKeyEvent)
	_, _ = d.device.RunShellCommand(commandOfBackKeyEvent)
}

type (
	ListPackagesOption func(*listPackagesOptions)

	listPackagesOptions struct {
		OnlySystemPackages     bool
		OnlyThirdPartyPackages bool
		FilterOfPackageName    string
	}
)

func WithOnlySystemPackages() ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.OnlySystemPackages = true
	}
}

func WithOnlyThirdPartyPackages() ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.OnlyThirdPartyPackages = true
	}
}

func WithFilterOfPackageName(filter string) ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.FilterOfPackageName = filter
	}
}

func (d *AndroidDevice) ListPackages(opts ...ListPackagesOption) (*set.Set[string], error) {
	o := &listPackagesOptions{}
	for _, opt := range opts {
		opt(o)
	}

	cmd := commandOfListPackages
	if o.OnlySystemPackages {
		cmd += " -s"
	}
	if o.OnlyThirdPartyPackages {
		cmd += " -3"
	}
	if o.FilterOfPackageName != "" {
		cmd += " " + o.FilterOfPackageName
	}

	output, err := d.device.RunShellCommand(cmd)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to list the packages, serial: %s", d.serial)
	}

	output = strings.TrimSpace(output)
	lines := strings.Split(output, "\n")
	packages := set.NewHashset(uint64(len(lines)), generic.Equals, generic.HashString)
	for _, line := range lines {
		if line == "" {
			continue
		}

		fields := strings.Split(line, ":")
		if len(fields) < 2 {
			continue
		}

		packages.Put(fields[1])
	}

	return &packages, nil
}

func (d *AndroidDevice) ListThirdPartyPackages() (*set.Set[string], error) {
	return d.ListPackages(WithOnlyThirdPartyPackages())
}

func (d *AndroidDevice) MonkeyTest(key, packageName string, duration int64, reportPath string) error {
	ctx, cancel := utils.NewTimeoutContext(d.ctx, time.Duration(duration+2)*time.Minute)
	defer cancel()

	threading.GoSafeCtx(
		ctx, func() {
			if err := d.collectPerfData(ctx, packageName); err != nil {
				d.Errorf(
					"failed to collect the perf data, serial: %s, package_name: %s, error: %+v",
					d.serial, packageName, err,
				)
			}
		},
	)

}

func (d *AndroidDevice) collectPerfData(
	ctx context.Context, packageName string, opts ...collector.Option,
) error {
	c := collector.NewAndroidCollector(ctx, d.device, packageName, opts...)
	if err := c.Start(); err != nil {
		return err
	}
	defer func(c *collector.AndroidCollector) {
		if c != nil {
			_ = c.Stop()
		}
	}(c)

	<-ctx.Done()
	return nil
}

type (
	FastbotOption func(*fastbotOptions)

	fastbotOptions struct {
		duration   int64    // 运行时间，单位：分钟
		throttle   int64    // 时间频率
		reportPath string   // 报告地址
		appPath    string   // App路径，非空表示尝试添加限定词
		activities []string // Activity白名单配置
	}
)

func WithFastbotDuration(duration int64) FastbotOption {
	return func(o *fastbotOptions) {
		if duration < minDuration {
			duration = minDuration
		} else if duration > maxDuration {
			duration = maxDuration
		} else {
			o.duration = duration
		}
	}
}

func WithFastbotThrottle(throttle int64) FastbotOption {
	return func(o *fastbotOptions) {
		if throttle < minThrottle {
			throttle = minThrottle
		} else if throttle > maxThrottle {
			throttle = maxThrottle
		} else {
			o.throttle = throttle
		}
	}
}

func WithFastbotReportPath(reportPath string) FastbotOption {
	return func(o *fastbotOptions) {
		o.reportPath = reportPath
	}
}

func WithFastbotAppPath(appPath string) FastbotOption {
	return func(o *fastbotOptions) {
		o.appPath = appPath
	}
}

func WithFastbotActivities(activities ...string) FastbotOption {
	return func(o *fastbotOptions) {
		o.activities = activities
	}
}

func (d *AndroidDevice) runFastbot(ctx context.Context, key, packageName string, opts ...FastbotOption) error {
	o := &fastbotOptions{
		duration: defaultDuration,
		throttle: defaultThrottle,
	}
	for _, opt := range opts {
		opt(o)
	}

	d.removeFastbotFiles(key)

	if o.appPath != "" {
		if err := d.pushMaxValidStringsFile(ctx, o.appPath); err != nil {
			d.Warnf("failed to push the max valid strings file, serial: %s, error: %+v", d.serial, err)
		}
	}
	if len(o.activities) > 0 {
		if err := d.pushActivityWhiteListFile(ctx, o.activities); err != nil {
			d.Warnf("failed to push the activity white list file, serial: %s, error: %+v", d.serial, err)
		}
	}

	session, err := d.device.NewSession()
	if err != nil {
		return errors.Wrapf(err, "failed to create the session, serial: %s", d.serial)
	}
	defer func(session *gadb.Session) {
		if session != nil {
			_ = session.Close()
		}
	}(session)

	var localOutputPath, remoteOutputPath string

	if o.reportPath != "" {
		localOutputPath = filepath.Join(o.reportPath, d.serial)
		logPath := filepath.Join(localOutputPath, logNameOfFastbot)
		logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0o755)
		if err != nil {
			d.Errorf("failed to open log file, serial: %s, file: %s, error: %+v", d.serial, logPath, err)
		} else {
			defer func(file *os.File) {
				if file != nil {
					_ = file.Close()
				}
			}(logFile)
			session.Stdout = logFile
			session.Stderr = logFile
		}
	}

	remoteOutputPath := filepath.Join(sdcardPathOnAndroid, "fastbot", key)
	cmd := fmt.Sprintf(commandOfMonkeyTest, packageName, o.duration, o.throttle, remoteOutputPath)
	if len(o.activities) > 0 {
		cmd += fmt.Sprintf(
			" --act-whitelist-file %s", filepath.Join(sdcardPathOnAndroid, filenameOfActivityWhiteListStrings),
		)
	}
	if err = session.Start(cmd); err != nil {
		return errors.Wrapf(err, "failed to start the monkey test, serial: %s, command: %q", d.serial, cmd)
	}

	resultCh := make(chan error, 1)
	threading.GoSafe(
		func() {
			resultCh <- session.Wait()
		},
	)
	defer func() {
		if e := d.collectFastbotResult(remoteOutputPath); e != nil {
			d.Errorf("failed to collect the fastbot result, serial: %s, error: %+v", d.serial, e)
		}

		if e := d.saveFastbotFiles(localOutputPath, remoteOutputPath); e != nil {
			d.Errorf("failed to save the fastbot files, serial: %s, error: %+v", d.serial, e)
		} else {
			d.removeFastbotFiles(key)
		}
	}()

	select {
	case <-d.ctx.Done():
		d.Debugf(
			"got a done signal while running the monkey test, serial: %s, package_name: %s, duration: %d",
			d.serial, packageName, o.duration,
		)
		return nil
	case <-ctx.Done():
		return errors.Errorf(
			"running the monkey test timeout, serial: %s, package_name: %s, duration: %d",
			d.serial, packageName, o.duration,
		)
	case err = <-resultCh:
		return err
	}
}

func (d *AndroidDevice) removeFastbotFiles(key string) {
	// rm -rf /sdcard/fastbot/{short_task_id}
	if _, err := d.device.RunShellCommand("rm", "-rf", filepath.Join(sdcardPathOnAndroid, "fastbot", key)); err != nil {
		d.Errorf("failed to remove the fastbot files, serial: %s, key: %s, error: %+v", d.serial, key, err)
	}

	// rm -f /sdcard/max.valid.strings
	if _, err := d.device.RunShellCommand(
		"rm", "-f", filepath.Join(sdcardPathOnAndroid, filenameOfMaxValidStrings),
	); err != nil {
		d.Errorf("failed to remove the max valid strings file, serial: %s, error: %+v", d.serial, err)
	}

	// rm -f /sdcard/awl.strings
	if _, err := d.device.RunShellCommand(
		"rm", "-f", filepath.Join(sdcardPathOnAndroid, filenameOfActivityWhiteListStrings),
	); err != nil {
		d.Errorf("failed to remove the activity white list file, serial: %s, error: %+v", d.serial, err)
	}
}

func (d *AndroidDevice) pushMaxValidStringsFile(ctx context.Context, appPath string) error {
	ctx, cancel := context.WithTimeout(ctx, timeoutOfPushMaxValidStrings)
	defer cancel()

	var (
		remotePath = filepath.Join(sdcardPathOnAndroid, filenameOfMaxValidStrings)
		doneCh     = make(chan lang.PlaceholderType)

		err error
	)

	threading.GoSafeCtx(
		ctx, func() {
			defer close(doneCh)

			var output []byte
			cmd := qetutils.CommandContext(ctx, "aapt2", "dump", "strings", appPath)
			cmd.WaitDelay = time.Second
			output, err = cmd.Output()
			if err != nil {
				err = errors.Wrapf(err, "failed to execute the command, serial: %s, command: %q", d.serial, cmd)
				return
			}

			err = d.device.PushByReader(bytes.NewReader(output), remotePath)
			if err != nil {
				err = errors.Wrapf(
					err, "failed to push %q to the device, serial: %s, path: %s",
					filenameOfMaxValidStrings, d.serial, remotePath,
				)
			}
		},
	)

	select {
	case <-ctx.Done():
		return errors.Errorf(
			"pushing %q to the device timed out, serial: %s, path: %s",
			filenameOfMaxValidStrings, d.serial, remotePath,
		)
	case <-doneCh:
		return err
	}
}

func (d *AndroidDevice) pushActivityWhiteListFile(ctx context.Context, activities []string) error {
	ctx, cancel := context.WithTimeout(ctx, timeoutOfPushActivityWhiteListStrings)
	defer cancel()

	var (
		remotePath = filepath.Join(sdcardPathOnAndroid, filenameOfActivityWhiteListStrings)
		doneCh     = make(chan lang.PlaceholderType)

		err error
	)

	threading.GoSafeCtx(
		ctx, func() {
			defer close(doneCh)

			buf := new(bytes.Buffer)
			for _, activity := range activities {
				_, _ = buf.WriteString(activity + "\n")
			}

			err = d.device.PushByReader(buf, remotePath)
			if err != nil {
				err = errors.Wrapf(
					err, "failed to push %q to the device, serial: %s, path: %s",
					filenameOfActivityWhiteListStrings, d.serial, remotePath,
				)
			}
		},
	)

	select {
	case <-ctx.Done():
		return errors.Errorf(
			"pushing %q to the device timed out, serial: %s, path: %s",
			filenameOfActivityWhiteListStrings, d.serial, remotePath,
		)
	case <-doneCh:
		return err
	}
}

func (d *AndroidDevice) collectFastbotResult(remotePath string) error {
	if remotePath == "" {
		return nil
	}

	path := filepath.Join(remotePath, filenameOfMaxActivityStatisticsLog)
	buf := new(bytes.Buffer)
	if err := d.device.ReadFile(path, buf); err != nil {
		return errors.Wrapf(err, "failed to read the file, serial: %s, path: %s", d.serial, path)
	}

	var result ActivityStatistics
	if err := json.NewDecoder(buf).Decode(&result); err != nil {
		return errors.Wrapf(err, "failed to decode the file, serial: %s, path: %s", d.serial, path)
	}

	return nil
}

func (d *AndroidDevice) saveFastbotFiles(localPath, remotePath string) error {
	if localPath == "" || remotePath == "" {
		return nil
	}

	return d.device.Pull(localPath, remotePath)
}

//func (d *AndroidDevice) GetCUPUsage(packageName string) (float64, error) {
//	// adb shell dumpsys cpuinfo | grep "/com.yiyou.ga" | grep -v grep
//	//  200% 21231/com.yiyou.ga: 148% user + 51% kernel / faults: 1647495 minor 4 major
//	//  4.8% 4519/com.yiyou.ga:pushservice: 2.2% user + 2.6% kernel / faults: 18122 minor
//	cmd := fmt.Sprintf(commandOfGetCPUInfo, packageName)
//	output, err := d.device.RunShellCommand(cmd)
//	if err != nil {
//		return 0, errors.Wrapf(
//			err, "failed to get the cpu info of the app, serial: %s, package_name: %s", d.serial, packageName,
//		)
//	}
//
//	output = strings.TrimSpace(output)
//	lines := strings.Split(output, "\n")
//	for _, line := range lines {
//		fields := strings.Fields(line)
//		if len(fields) < 2 {
//			continue
//		}
//
//		fields[0] = strings.TrimSuffix(fields[0], "%")
//		usage, err := strconv.ParseFloat(fields[0], 64)
//		if err != nil {
//			continue
//		}
//
//		fields[1] = strings.TrimSuffix(fields[1], ":")
//		tmp := strings.Split(fields[1], "/")
//		if len(tmp) != 2 {
//			continue
//		} else if tmp[1] != packageName {
//			continue
//		}
//
//		d.Debugf("the cpu info of the app, serial: %s, package_name: %s, line: %s", d.serial, packageName, line)
//		return usage, nil
//	}
//
//	d.Debugf(
//		"not found the cpu info of the app, serial: %s, package_name: %s, output: %s", d.serial, packageName, output,
//	)
//	return 0, nil
//}
//
//func (d *AndroidDevice) GetMemoryUsage(packageName string) (int64, error) {
//	// adb shell dumpsys meminfo com.yiyou.ga | grep TOTAL | grep -v grep
//	//       TOTAL  1299748   465392   126920   687312   745196   899444   842038    57405
//	//          TOTAL PSS:  1299748            TOTAL RSS:   745196       TOTAL SWAP PSS:   687312
//	cmd := fmt.Sprintf(commandOfGetMemoryInfo, packageName)
//	output, err := d.device.RunShellCommand(cmd)
//	if err != nil {
//		return 0, errors.Wrapf(
//			err, "failed to get the memory info of the app, serial: %s, package_name: %s", d.serial, packageName,
//		)
//	}
//
//	output = strings.TrimSpace(output)
//	lines := strings.Split(output, "\n")
//	for _, line := range lines {
//		fields := strings.Fields(line)
//		tmp := ""
//		if strings.Contains(line, "TOTAL PSS:") {
//			if len(fields) < 3 {
//				continue
//			}
//			tmp = fields[2]
//		} else {
//			if len(fields) < 2 {
//				continue
//			}
//			tmp = fields[1]
//		}
//
//		if tmp == "" {
//			continue
//		}
//		usage, err := strconv.ParseInt(tmp, 10, 64)
//		if err != nil {
//			continue
//		}
//
//		d.Debugf("the memory info of the app, serial: %s, package_name: %s, line: %s", d.serial, packageName, line)
//		return usage, nil
//	}
//
//	d.Debugf(
//		"not found the memory info of the app, serial: %s, package_name: %s, output: %s", d.serial, packageName, output,
//	)
//	return 0, nil
//}
//
//func (d *AndroidDevice) GetFPS() (float64, error) {
//	return 0, nil
//}

func (d *AndroidDevice) Snapshot() {

}

func (d *AndroidDevice) snapshotByMiniCap() {

}

func (d *AndroidDevice) snapshotByADB() {

}
