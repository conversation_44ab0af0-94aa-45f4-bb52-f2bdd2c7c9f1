package statest

import (
	"context"
	"fmt"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/electricbubble/gidevice/pkg/ipa"
	"github.com/pkg/errors"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/zrpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	accountcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	accountpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/logger"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managercommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/statest/device"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/statest/products/tt"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	logger *logger.StepLogger
	stopCh chan lang.PlaceholderType

	taskInfo *commonpb.StabilityCaseTaskInfo
	state    dispatcherpb.ComponentState

	key        string
	appPath    string                // 待测试的App文件
	appName    string                // 待测试的App名称（Android: package_name, iOS: bundle_id）
	reportPath string                // 测试报告的存放路径
	devices    []*devicehubpb.Device // 占用的设备列表（注：正常情况下只会占用一个设备）
	accounts   []*Account            // 占用的账号列表（注：正常情况下只会占用一个账号）

	device        *device.AndroidDevice // 注：后续支持`iOS`时，这里需要改成接口
	businessLogic *tt.AndroidLogic      // 注：后续支持其它产品时，这里需要改成接口
}

func NewBaseLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, taskInfo *commonpb.StabilityCaseTaskInfo,
) *BaseLogic {
	l := &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		logger: logger.NewStepLogger(ctx, svcCtx.Config.Log),
		stopCh: make(chan lang.PlaceholderType),

		taskInfo: taskInfo,
		state:    dispatcherpb.ComponentState_Pending,

		devices:  make([]*devicehubpb.Device, 0, 1),
		accounts: make([]*Account, 0, 1),
	}
	l.init()
	return l
}

func (l *BaseLogic) init() {
	taskID := l.taskInfo.GetTaskId()
	if ss := strings.Split(taskID, ":"); len(ss) >= 2 {
		taskID = ss[1]
	}
	l.key = taskID

	suffix := ""
	switch l.taskInfo.GetPlatformType() {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		suffix = common.ConstSuffixOfApk
	case commonpb.PlatformType_IOS:
		suffix = common.ConstSuffixOfIpa
	default:
		l.Warnf("invalid platform type: %s", l.taskInfo.GetPlatformType().String())
	}

	l.appPath = filepath.Join(
		l.svcCtx.Config.LocalPath, common.ConstAppDownloadPath, fmt.Sprintf("%s%s", l.key, suffix),
	)
	l.reportPath = filepath.Join(l.svcCtx.Config.LocalPath, common.ConstStabilityTestReportPath, l.key)
}

func (l *BaseLogic) downloadAppPackage() error {
	var (
		packageName = l.taskInfo.GetPackageName()
		link        = l.taskInfo.GetAppDownloadLink()

		err error
	)

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfDownloadAppPackage)
	defer cancel()

	if link != "" {
		err = utils.DownloadFromUrl(ctx, link, l.appPath)
	} else {
		puller, err := pkgpuller.NewAppPkgPuller(ctx, pkgpuller.AppPkgNameType(packageName), l.appPath)
		if err != nil {
			return errors.Errorf(
				"failed to new app package puller, package_name: %s, path: %s, error: %+v", packageName, l.appPath, err,
			)
		}

		link, err = puller.Pull()
	}
	if err != nil {
		return errors.Errorf(
			"failed to download the app package, package_name: %s, link: %s, path: %s, error: %+v",
			packageName, link, l.appPath, err,
		)
	}

	switch l.taskInfo.GetPlatformType() {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		info, err := apk.OpenFile(l.appPath)
		if err != nil {
			l.logger.Errorf(
				"failed to open the apk file, package_name: %s, link: %s, path: %s, error: %+v",
				packageName, link, l.appPath, err,
			)
		} else {
			l.appName = info.PackageName()
		}
	case commonpb.PlatformType_IOS:
		info, err := ipa.Info(l.appPath)
		if err != nil {
			l.logger.Errorf(
				"failed to open the ipa file, package_name: %s, link: %s, path: %s, error: %+v",
				packageName, link, l.appPath, err,
			)
		} else {
			l.appName = info.CFBundleIdentifier
		}
	}

	l.logger.Infof("finish to download the app package, link: %s, path: %s, name: %s", link, l.appPath, l.appName)
	return nil
}

func (l *BaseLogic) acquireAccount() (err error) {
	accountConfig := l.taskInfo.GetAccountConfig()

	defer func() {
		if err != nil {
			_ = l.releaseAccount()
		}
	}()

	out, err := l.svcCtx.AccountRPC.AcquireAccount(
		l.ctx, &accountpb.QueryAccountPoolEnvDataRequest{
			PoolEnvTable:  accountConfig.GetPoolEnvTable(),
			ExpectedCount: 1,
		},
		zrpc.WithCallTimeout(accountcommon.ConstRPCAcquireAccountTimeout),
	)
	if err != nil {
		return err
	}

	if len(out.GetMatchData()) == 0 {
		return errors.Errorf("no accounts were occupied, pool_env_table %s", accountConfig.GetPoolEnvTable())
	}
	l.logger.Infof("acquired accounts: %s", protobuf.MarshalJSONIgnoreError(out))

	for _, account := range out.GetMatchData() {
		item := &Account{}
		for _, column := range account.GetAccount() {
			switch column.GetField() {
			case accountcommon.BuiltinTableFieldOfAccount:
				item.Username = column.GetValue()
				item.Token = column.GetLockValue()
			case accountcommon.BuiltinTableFieldOfPassword:
				item.Password = column.GetValue()
			default:
			}

			if item.Username != "" && item.Password != "" && item.Token != "" {
				break
			}
		}

		if item.Username != "" && item.Password != "" && item.Token != "" {
			l.accounts = append(l.accounts, item)
		}
	}

	return nil
}

func (l *BaseLogic) releaseAccount() (err error) {
	accountConfig := l.taskInfo.GetAccountConfig()

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, accountcommon.ConstRPCReleaseAccountTimeout)
	defer cancel()

	in := &accountpb.ReleaseTestAccountRequest{
		ReleaseTasAccountArray: []*accountpb.ReleaseTestAccountRequest_PoolAccount{
			{
				PoolEnvTable: accountConfig.GetPoolEnvTable(),
				AccountArray: make([]*accountpb.ReleaseTestAccountRequest_Account, 0, len(l.accounts)),
			},
		},
	}
	for _, account := range l.accounts {
		in.ReleaseTasAccountArray[0].AccountArray = append(
			in.ReleaseTasAccountArray[0].AccountArray, &accountpb.ReleaseTestAccountRequest_Account{
				Account:   account.Username,
				LockValue: account.Token,
			},
		)
	}

	if _, err = l.svcCtx.AccountRPC.ReleaseAccount(ctx, in); err != nil {
		l.logger.Errorf(
			"failed to release the account, accounts: %s, error: %+v",
			jsonx.MarshalIgnoreError(l.accounts), err,
		)
	}

	return err
}

func (l *BaseLogic) acquireDevice() (err error) {
	var (
		projectID = l.taskInfo.GetProjectId()

		deviceType   = l.taskInfo.GetDeviceType()
		platformType = l.taskInfo.GetPlatformType()
		udid         = l.taskInfo.GetUdid()
	)

	defer func() {
		if err != nil {
			_ = l.releaseDevice()
		}
	}()

	condition := &rpc.Condition{
		Group: &rpc.GroupCondition{
			Relationship: constants.AND,
			Conditions: []*rpc.Condition{
				{
					Single: &rpc.SingleCondition{
						Field:   string(devicehubcommon.DeviceFieldOfType),
						Compare: constants.EQ,
						Other: &rpc.Other{
							Value: strconv.Itoa(int(deviceType.Number())),
						},
					},
				},
				{
					Single: &rpc.SingleCondition{
						Field:   string(devicehubcommon.DeviceFieldOfPlatform),
						Compare: constants.EQ,
						Other: &rpc.Other{
							Value: strconv.Itoa(int(platformType.Number())),
						},
					},
				},
			},
		},
	}
	if !strings.HasPrefix(udid, common.ConstPrefixOfRandomDevice) {
		condition.Group.Conditions = append(
			condition.Group.Conditions, &rpc.Condition{
				Single: &rpc.SingleCondition{
					Field:   string(devicehubcommon.DeviceFieldOfUDID),
					Compare: constants.EQ,
					Other: &rpc.Other{
						Value: udid,
					},
				},
			},
		)
	}

	out, err := l.svcCtx.ManagerRPC.AcquireProjectDevice(
		l.ctx, &managerpb.AcquireProjectDeviceReq{
			ProjectId: projectID,
			Usage:     commonpb.DeviceUsage_STABILITY_TESTING,
			Condition: condition,
			Count:     1,
		},
		zrpc.WithCallTimeout(devicehubcommon.ConstRPCAcquireDeviceTimeout),
	)
	if err != nil {
		return errors.Wrapf(
			err,
			"failed to acquire project device, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}

	if len(out.GetDevices()) == 0 {
		return errors.Errorf(
			"no project devices were occupied, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}
	l.logger.Infof("acquired project devices: %s", protobuf.MarshalJSONIgnoreError(out))

	for _, d := range out.GetDevices() {
		if d == nil || d.GetDevice() == nil {
			continue
		}

		l.devices = append(l.devices, d.GetDevice())
	}

	return nil
}

func (l *BaseLogic) releaseDevice() (err error) {
	if err = mr.MapReduceVoid[*devicehubpb.Device, any](
		func(source chan<- *devicehubpb.Device) {
			for _, d := range l.devices {
				if d == nil || d.GetUdid() == "" || d.GetToken() == "" {
					continue
				}

				source <- d
			}
		},
		func(item *devicehubpb.Device, writer mr.Writer[any], cancel func(error)) {
			var (
				udid  = item.GetUdid()
				token = item.GetToken()
			)

			l.logger.Infof("ready to release the project device, udid: %s, token: %s", udid, token)

			ctx1, cancel1 := commonutils.NewTimeoutContext(l.ctx, managercommon.ConstRPCReleaseProjectDeviceTimeout)
			defer cancel1()

			if _, err := l.svcCtx.ManagerRPC.ReleaseProjectDevice(
				ctx1, &managerpb.ReleaseProjectDeviceReq{
					ProjectId: l.taskInfo.GetProjectId(),
					Udid:      udid,
					Token:     token,
				},
			); err != nil {
				l.logger.Errorf(
					"failed to release the project device by grpc, udid: %s, token: %s, error: %+v",
					udid, token, err,
				)

				l.logger.Infof("try to release the project device by mq, udid: %s, token: %s", udid, token)
				ctx2, cancel2 := commonutils.NewTimeoutContext(l.ctx, devicehubcommon.ConstRPCReleaseDeviceTimeout)
				defer cancel2()

				if _, err := l.svcCtx.DeviceHubProducer.Send(
					ctx2, base.NewTask(
						commonconsts.MQTaskTypeDeviceHubHandleReleaseDevice,
						protobuf.MarshalJSONIgnoreError(
							&devicehubpb.ReleaseDeviceReq{
								Udid:  udid,
								Token: token,
							},
						),
					), base.QueuePriorityDefault,
				); err != nil {
					l.logger.Errorf(
						"failed to release the project device by mq, udid: %s, token: %s, error: %+v",
						udid, token, err,
					)
					return
				}
			}

			l.logger.Infof("finish to release the project device, udid: %s, token: %s", udid, token)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	); err != nil {
		l.logger.Errorf(
			"got an error while releasing the project device, devices: %s, error: %+v",
			protobuf.MarshalJSONWithMessagesToStringIgnoreError(l.devices), err,
		)
	}

	return err
}

func (l *BaseLogic) initDevice() error {
	var (
		deviceType   = l.taskInfo.GetDeviceType()
		platformType = l.taskInfo.GetPlatformType()

		err error
	)

	if len(l.devices) == 0 {
		return errors.Errorf(
			"no devices were acquired, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}

	device_ := l.devices[0] // 只取第一个
	udid := device_.GetUdid()
	addr := device_.GetRemoteAddress()

	switch platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		l.device, err = device.NewAndroidDevice(l.ctx, deviceType, udid, addr)
		if err != nil {
			return err
		}

		l.businessLogic = tt.NewAndroidLogic(l.ctx, l.device)
	default:
		err = errors.Errorf("unsupported platform type: %s", platformType.String())
	}

	return err
}

func (l *BaseLogic) runMonkeyTest() error {
	var (
		deviceType   = l.taskInfo.GetDeviceType()
		platformType = l.taskInfo.GetPlatformType()
	)

	switch platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
	default:

	}

	return nil
}
