package statest

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

const (
	intervalOfWatchStopSignal = 5 * time.Second
)

type ExecuteStabilityCaseTaskLogic struct {
	*BaseLogic

	steps []*reporterpb.StabilityCaseStep
}

func NewExecuteStabilityCaseTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, taskInfo *commonpb.StabilityCaseTaskInfo,
) *ExecuteStabilityCaseTaskLogic {
	return &ExecuteStabilityCaseTaskLogic{
		BaseLogic: NewBaseLogic(ctx, svcCtx, taskInfo),

		steps: make([]*reporterpb.StabilityCaseStep, 0, 16),
	}
}

func (l *ExecuteStabilityCaseTaskLogic) Execute() (err error) {
	threading.GoSafeCtx(l.ctx, l.watchStopSignal)

	defer func() {
		_ = l.runSteps(l.teardownSteps())
	}()

	if err = l.runSteps(l.setupSteps()); err != nil {
		return err
	}

	if err = l.runSteps(l.testSteps()); err != nil {
		return err
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) watchStopSignal() {
	ticker := timewheel.NewTicker(intervalOfWatchStopSignal)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if err := l.isStopped(); err != nil {
				close(l.stopCh)
				return
			}
		}
	}
}

func (l *ExecuteStabilityCaseTaskLogic) isStopped() (err error) {
	taskID := l.taskInfo.GetTaskId()

	defer func() {
		if r := recover(); r != nil {
			l.state = dispatcherpb.ComponentState_Panic
			err = errors.Errorf("got a panic while checking the stop status, task_id: %s, error: %+v", taskID, r)
		}
	}()

	stop, err := utils.GetStopStatus(l.ctx, l.svcCtx.DispatcherRedis, taskID)
	if err != nil {
		l.state = dispatcherpb.ComponentState_Panic
		return errors.Wrapf(err, "failed to get the stop status of task, task_id: %s", taskID)
	} else if stop {
		l.state = dispatcherpb.ComponentState_Stop
		return errors.Errorf("got a stop signal of task, task_id: %s", taskID)
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) setupSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_SETUP,
		Steps: []*Step{
			{
				Desc: StepDesc{
					EN: descOfDownloadAppPackageEN,
					ZH: descOfDownloadAppPackageZH,
				},
				Func: l.downloadAppPackage,
			},
			{
				Desc: StepDesc{
					EN: descOfAcquireAccountsEN,
					ZH: descOfAcquireAccountsZH,
				},
				Func: l.acquireAccount,
			},
			{
				Desc: StepDesc{
					EN: descOfAcquireDevicesEN,
					ZH: descOfAcquireDevicesZH,
				},
				Func: l.acquireDevice,
			},
		},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) testSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_TEST,
		Steps: []*Step{},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) teardownSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_TEARDOWN,
		Steps: []*Step{
			{
				Desc: StepDesc{
					EN: descOfReleaseDevicesEN,
					ZH: descOfReleaseDevicesZH,
				},
				Func: l.releaseDevice,
			},
			{
				Desc: StepDesc{
					EN: descOfReleaseAccountsEN,
					ZH: descOfReleaseAccountsZH,
				},
				Func: l.releaseAccount,
			},
		},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) runSteps(stageSteps *StageSteps) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		udid   = l.device.UDID()

		stage      = stageSteps.Stage
		strOfStage = protobuf.GetEnumStringOf(stage)
	)

	if stage == commonpb.TestStage_TS_NULL {
		return errors.Errorf("invalid stage: %v", stage)
	} else if len(stageSteps.Steps) == 0 {
		return nil
	}

	l.Infof("start to run the %s steps, task_id: %s, udid: %s", strOfStage, taskID, udid)
	defer func() {
		l.Infof("finish running the %s steps, task_id: %s, udid: %s", strOfStage, taskID, udid)
	}()

	if stage == commonpb.TestStage_SETUP {
		l.state = dispatcherpb.ComponentState_Started
	}

	defer func() {
		if r := recover(); r != nil {
			if stage == commonpb.TestStage_TEARDOWN {
				l.Errorf(
					"got a panic while running the teardown steps, task_id: %s, udid: %s, error: %+v", taskID, udid, r,
				)
			} else {
				l.state = dispatcherpb.ComponentState_Panic
				err = errors.Errorf(
					"got a panic while running the %s steps, task_id: %s, udid: %s, error: %+v",
					strOfStage, taskID, udid, r,
				)
			}
		} else if err != nil {
			l.state = dispatcherpb.ComponentState_Failure
		}
	}()

	for _, step := range stageSteps.Steps {
		if step == nil || step.Func == nil {
			continue
		}

		if err = l.runStep(stage, step); err != nil {
			if stage == commonpb.TestStage_TEARDOWN {
				l.Errorf(
					"failed to run the teardown step, task_id: %s, udid: %s, step: %q, error: %+v",
					strOfStage, taskID, udid, step.Desc.EN, err,
				)
			} else {
				return errors.Wrapf(
					err,
					"failed to run the %s step, task_id: %s, udid: %s, step: %q, error: %+v",
					strOfStage, taskID, udid, step.Desc.EN, err,
				)
			}
		}
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) runStep(stage commonpb.TestStage, step *Step) error {
	if stage == commonpb.TestStage_TS_NULL {
		return errors.Errorf("invalid stage: %v", stage)
	} else if step == nil || step.Func == nil {
		return errors.New("the test step is null")
	}

	var (
		taskID     = l.taskInfo.GetTaskId()
		strOfStage = protobuf.GetEnumStringOf(stage)

		startedAt = time.Now()
		status    = dispatcherpb.ComponentState_Failure
	)

	err := step.Func()
	if err != nil {
		l.logger.Errorf("failed to run the test step, stage: %q, step: %q, error: %+v", strOfStage, step.Desc.EN, err)
	} else {
		status = dispatcherpb.ComponentState_Success
		l.logger.Infof("succeed to run the test step, stage: %q, step: %q", strOfStage, step.Desc.EN)
	}

	l.steps = append(
		l.steps, &reporterpb.StabilityCaseStep{
			TaskId:    taskID,
			Stage:     stage,
			Name:      step.Desc.ZH,
			Status:    status.String(),
			Content:   l.logger.Sync(),
			StartedAt: startedAt.UnixMilli(),
			EndedAt:   time.Now().UnixMilli(),
		},
	)

	return nil
}
