package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"
)

const (
	rootCmdUse   = "staworker"
	rootCmdShort = "staworker is one of the microservices of the Quality Platform"
	rootCmdLong  = `staworker is one of the microservices of the Quality Platform.
The main function is to execute the stability testing tasks.`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
