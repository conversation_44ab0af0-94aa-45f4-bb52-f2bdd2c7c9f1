package tasks

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commonredis "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/logic/uitest"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/svc"
)

var _ base.Handler = (*ProcessorExecuteUITest)(nil)

type ProcessorExecuteUITest struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorExecuteUiTest(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorExecuteUITest{
		svcCtx: svcCtx,
	}
}

func (processor *ProcessorExecuteUITest) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	var req dispatcherpb.WorkerReq
	if err = protobuf.UnmarshalJSON(task.Payload, &req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of ui test task, payload: %s, error: %+v", task.Payload, err,
		)
	}

	keepAlive2Consumer(task, processor.svcCtx, &req, true)
	defer func() {
		keepAlive2Consumer(task, processor.svcCtx, &req, false)
	}()

	if ok, err := acquireTaskExecLock(ctx, task, processor.svcCtx, &req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to acquire the task exec lock, error: %+v", err,
		)
	} else if !ok {
		return []byte(constants.SKIPPED), nil
	}

	logic, err := NewExecuteUiTestTaskLogic(ctx, processor.svcCtx, &req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"failed to new logic of ui test, error: %+v", err,
		)
	}
	defer logic.logger.Free()

	err = logic.Run()
	if err != nil {
		logger.Errorf(
			"failed to execute the ui test task, task_id: %s, target: %s, error: %+v",
			logic.taskInfo.GetTaskId(), logic.taskInfo.GetTestTarget(), err,
		)
	}

	return []byte(constants.SUCCESS), nil
}

func genTaskExecLockKey(taskID, executeID string) string {
	return fmt.Sprintf("%s:%s:%s", commonredis.ConstRedisUIWorkerTaskExecLockKey, taskID, executeID)
}

func acquireTaskExecLock(
	ctx context.Context, task *base.Task, svcCtx *svc.ServiceContext, req *dispatcherpb.WorkerReq,
) (bool, error) {
	if strings.HasPrefix(task.Queue, constants.MQNameUIWorkerDervicePrefix) {
		taskID := req.GetTaskId()
		executeID := req.GetUiCase().GetUiCaseExecuteId()
		lockKey := genTaskExecLockKey(taskID, executeID)
		return svcCtx.Redis.SetnxExCtx(ctx, lockKey, task.Queue, 60*60*24)
	}
	return true, nil
}

func keepAlive2Consumer(
	task *base.Task, svcCtx *svc.ServiceContext,
	req *dispatcherpb.WorkerReq, isActive bool,
) {
	if strings.HasPrefix(task.Queue, constants.MQNameUIWorkerDervicePrefix) {
		svcCtx.ConsumerManager.KeepAlive2Consumer(
			task.Queue, mq.ConvertPbEnumerationToQueuePriority(req.GetPriorityType()), isActive,
		)
	}
}

type ExecuteUITestTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	logger *common.StepLogger

	taskReq  *dispatcherpb.WorkerReq
	taskInfo *dispatcherpb.UITestTaskInfo
	state    dispatcherpb.ComponentState
	steps    []*reporterpb.UICaseStep
}

func NewExecuteUiTestTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, req *dispatcherpb.WorkerReq,
) (*ExecuteUITestTaskLogic, error) {
	info, err := getTaskInfoFromTaskReq(req)
	if err != nil {
		return nil, err
	}

	return &ExecuteUITestTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		logger: common.NewStepLogger(ctx, svcCtx.Config.Log),

		taskReq:  req,
		taskInfo: info,
		state:    dispatcherpb.ComponentState_Pending,
		steps:    make([]*reporterpb.UICaseStep, 0, 16),
	}, nil
}

func getTaskInfoFromTaskReq(req *dispatcherpb.WorkerReq) (*dispatcherpb.UITestTaskInfo, error) {
	metaData := req.GetUiCase().GetMetaData()

	gitURL := metaData.GetGitConfig().GetUrl()
	gitAccessToken := metaData.GetGitConfig().GetAccessToken()
	u, err := url.Parse(gitURL)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse the git url, url: %s", gitURL)
	} else if _, ok := u.User.Password(); !ok || u.User.Username() == "" {
		u.User = url.UserPassword(constants.ConstDefaultGitUsername, gitAccessToken)
	}
	gitURLWithAuth := u.String()

	devices := metaData.GetDevices()
	together := metaData.GetTogether()
	udid := req.GetNodeData().GetUiCase().GetUdid()
	if !together && len(udid) > 0 {
		devices = []string{udid}
	}

	return &dispatcherpb.UITestTaskInfo{
		ProjectId:           req.GetProjectId(),
		PlanId:              req.GetUiCase().GetUiPlanId(),
		TaskId:              req.GetTaskId(),
		TestTarget:          req.GetNodeData().GetUiCase().GetPath(),
		TestFrameworkUrl:    gitURLWithAuth,
		TestFrameworkBranch: metaData.GetGitConfig().GetBranch(),
		DeviceType:          metaData.GetDeviceType(),
		PlatformType:        metaData.GetPlatformType(),
		AppDownloadLink:     metaData.GetAppDownloadLink(),
		TestLanguage:        metaData.GetTestLanguage(),
		TestLanguageVersion: metaData.GetTestLanguageVersion(),
		TestFramework:       metaData.GetTestFramework(),
		TestArgs:            metaData.GetTestArgs(),
		Devices:             devices,
		PackageName:         metaData.GetPackageName(),
		AppVersion:          metaData.GetAppVersion(),
		TriggerMode:         req.GetTriggerMode(),
		TriggerRule:         req.GetTriggerRule(),
		ExecuteType:         req.GetExecuteType(),
		CallbackType:        dispatcherpb.CallbackType_CallbackType_UI_CASE,
		UiCase:              req.GetUiCase(),
		PriorityType:        req.GetPriorityType(),
		ExecutedBy:          req.GetUserId(),
	}, nil
}

func (l *ExecuteUITestTaskLogic) Run() (err error) {
	var (
		taskID    = l.taskInfo.GetTaskId()
		target    = l.taskInfo.GetTestTarget()
		language  = l.taskInfo.GetTestLanguage()
		framework = l.taskInfo.GetTestFramework()

		errCh  = make(chan error, 1)
		stopCh = make(chan lang.PlaceholderType)

		executor uitest.Executor
	)

	defer func() {
		if e := l.sendTaskResultToReporter(executor); e != nil {
			l.Error(e)
		}
	}()

	switch language {
	case commonpb.TestLanguage_TestLanguage_PYTHON:
		switch framework {
		case commonpb.TestFramework_TestFramework_PYTEST:
			executor = uitest.NewExecutePytestLogic(l.ctx, l.svcCtx, l.logger, l.taskInfo, stopCh)
		default:
			l.state = dispatcherpb.ComponentState_Panic
			return errorx.Errorf(
				errorx.DoesNotSupport,
				"unsupported test framework, task_id: %s, target: %s, framework: %s",
				taskID, target, framework.String(),
			)
		}
	case commonpb.TestLanguage_TestLanguage_GOLANG:
		l.state = dispatcherpb.ComponentState_Panic
		return errorx.Errorf(
			errorx.DoesNotSupport,
			"unsupported test language, task_id: %s, target: %s, language: %s", taskID, target, language.String(),
		)
	default:
		l.state = dispatcherpb.ComponentState_Panic
		return errorx.Errorf(
			errorx.DoesNotSupport,
			"unsupported test language, task_id: %s, target: %s, language: %s", taskID, target, language.String(),
		)
	}

	threading.GoSafe(
		func() {
			l.watchStopSignal(stopCh)
		},
	)

	fn := func() (err error) {
		defer func() {
			if err == nil {
				l.state = dispatcherpb.ComponentState_Success
			}

			l.runTeardownSteps(executor)

			if e := l.sendTaskCallbackDataToDispatcher(); e != nil {
				l.Error(e)
			}
		}()

		if err = l.sendTaskInfoToDispatcher(); err != nil {
			l.state = dispatcherpb.ComponentState_Failure
			return errorx.Errorf(
				codes.ExecuteSubTaskFailure,
				"failed to send ui test task info to dispatcher, task_id: %s, target: %s, error: %+v",
				taskID, target, err,
			)
		}

		if err = l.runSetupSteps(executor); err != nil {
			return err
		}

		if err = l.runTestSteps(executor); err != nil {
			return err
		}

		return nil
	}
	threading.GoSafe(
		func() {
			errCh <- fn()
		},
	)

	return <-errCh
}

func (l *ExecuteUITestTaskLogic) watchStopSignal(ch chan<- lang.PlaceholderType) {
	ticker := timewheel.NewTicker(common.DefaultPeriodOfWatchStopSignal)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if err := l.isStopped(); err != nil {
				close(ch)
				l.Error(err)
				return
			}
		}
	}
}

func (l *ExecuteUITestTaskLogic) isStopped() (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()
	)

	defer func() {
		if r := recover(); r != nil {
			l.state = dispatcherpb.ComponentState_Panic
			err = errorx.Errorf(
				codes.ExecuteSubTaskFailure,
				"got a panic while checking the stop status, task_id: %s, target: %s, error: %+v",
				taskID, target, r,
			)
		}
	}()

	stop, err := utils.GetStopStatus(l.ctx, l.svcCtx.DispatcherRedis, taskID)
	if err != nil {
		l.state = dispatcherpb.ComponentState_Panic
		return errorx.Errorf(
			codes.ExecuteSubTaskFailure,
			"failed to get the stop status of task, task_id: %s, target: %s, err: %+v", taskID, target, err,
		)
	} else if stop {
		l.state = dispatcherpb.ComponentState_Stop
		return errorx.Errorf(
			codes.ExecuteSubTaskFailure, "got a stop signal of task, task_id: %s, target: %s", taskID, target,
		)
	}

	return nil
}

func (l *ExecuteUITestTaskLogic) sendTaskInfoToDispatcher() error {
	data := &dispatcherpb.DistributeReq{
		ProjectId: l.taskInfo.GetProjectId(),
		TaskId:    l.taskInfo.GetTaskId(),
		Data: &dispatcherpb.DistributeReq_UiPlan{
			UiPlan: &dispatcherpb.UIPlanDistributeData{
				UiPlanId:        l.taskInfo.GetPlanId(),
				UiPlanExecuteId: l.taskInfo.GetUiCase().GetUiPlanExecuteId(),
			},
		},
	}
	pbData, err := protobuf.MarshalJSON(data)
	if err != nil {
		return errors.Errorf("failed to marshal the protobuf message in JSON format, data: %+v, error: %+v", data, err)
	}

	task := base.NewTask(
		constants.MQTaskTypeDispatcherPublishTaskCaseExecuting, pbData,
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(time.Minute*60),
	)
	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, task, mq.ConvertPbEnumerationToQueuePriority(l.taskInfo.GetPriorityType()),
	)
	if err != nil {
		return errors.Errorf(
			"failed to send the task to mq, task_name: %s, payload: %s, error: %+v", task.Typename, task.Payload, err,
		)
	}

	return nil
}

func (l *ExecuteUITestTaskLogic) sendTaskCallbackDataToDispatcher() error {
	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.DefaultTimeoutOfSendTask)
	defer cancel()

	data := l.callbackData()
	task := base.NewTask(
		constants.MQTaskTypeDispatcherCallback, data,
		base.WithMaxRetryOptions(0),
	)
	if _, err := l.svcCtx.DispatcherProducer.Send(ctx, task, base.QueuePriorityDefault); err != nil {
		return errors.Errorf(
			"failed to send the task to mq, task_name: %s, payload: %s, error: %+v",
			task.Typename, task.Payload, err,
		)
	}

	return nil
}

func (l *ExecuteUITestTaskLogic) sendTaskResultToReporter(executor uitest.Executor) error {
	var (
		taskID = l.taskReq.GetTaskId()
		uiCase = l.taskReq.GetUiCase()
	)

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.DefaultTimeoutOfInvokeRPC)
	defer cancel()

	content, err := protobuf.MarshalJSONWithMessagesToString(l.steps)
	if err != nil {
		return errors.Errorf(
			"failed to marshal the ui case execution step data, data: %+v, error: %+v", l.steps, err,
		)
	}

	var udid string
	if executor != nil && len(executor.GetDevices()) > 0 {
		udid = executor.GetDevices()[0].GetUdid()
	}

	req := &reporterpb.PutUICaseRecordRequest{
		TaskId:         taskID,
		ProjectId:      l.taskReq.GetProjectId(),
		ExecuteId:      uiCase.GetUiCaseExecuteId(),
		CaseId:         uiCase.GetUiCaseId(),
		CaseName:       l.taskReq.GetNodeData().GetUiCase().GetName(),
		SuiteExecuteId: uiCase.GetUiSuiteExecuteId(),
		Udid:           udid,
		Status:         l.state.String(),
		Content:        content,
		ExecutedBy:     l.taskReq.GetUserId(),
		EndedAt:        time.Now().UnixMilli(),
	}

	_, err = l.svcCtx.ReporterRPC.ModifyRecord(ctx, req)
	if err != nil {
		return errors.Errorf(
			"failed to modify ui case record, task_id: %s, execute_id: %s, error: %+v",
			taskID, uiCase.GetUiCaseExecuteId(), err,
		)
	}

	return nil
}

func (l *ExecuteUITestTaskLogic) runSetupSteps(executor uitest.Executor) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()
	)

	l.Infof("begin to execute setup steps, task_id: %s, target: %s", taskID, target)
	defer func() {
		l.Infof("finish to execute setup steps, task_id: %s, target: %s", taskID, target)
	}()

	l.state = dispatcherpb.ComponentState_Started

	defer func() {
		if r := recover(); r != nil {
			l.state = dispatcherpb.ComponentState_Panic
			err = errorx.Errorf(
				codes.ExecuteSubTaskFailure,
				"got a panic while executing the setup steps, task_id: %s, target: %s, error: %+v",
				taskID, target, r,
			)
		} else if err != nil {
			l.state = dispatcherpb.ComponentState_Failure
		}
	}()

	// execute setup steps
	for i, step := range executor.SetupSteps() {
		if step == nil || step.Func == nil {
			continue
		}

		var s *reporterpb.UICaseStep
		s, err = l.runStep(commonpb.TestStage_SETUP, step)
		if s == nil {
			continue
		}
		s.Index = int64(i + 1)
		l.steps = append(l.steps, s)

		if err != nil {
			return errorx.Errorf(
				codes.ExecuteSubTaskFailure,
				"failed to execute the setup step, task_id: %s, target: %s, step: %q, error: %+v",
				taskID, target, step.Desc.EN, err,
			)
		}
	}

	return nil
}

func (l *ExecuteUITestTaskLogic) runTestSteps(executor uitest.Executor) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()
	)

	l.Infof("begin to execute test steps, task_id: %s, target: %s", taskID, target)
	defer func() {
		l.Infof("finish to execute test steps, task_id: %s, target: %s", taskID, target)
	}()

	defer func() {
		if r := recover(); r != nil {
			l.state = dispatcherpb.ComponentState_Panic
			err = errorx.Errorf(
				codes.ExecuteSubTaskFailure,
				"got a panic while executing the test steps, task_id: %s, target: %s, error: %+v",
				taskID, target, r,
			)
		} else if err != nil {
			l.state = dispatcherpb.ComponentState_Failure
		}
	}()

	// execute test steps
	for i, step := range executor.TestSteps() {
		if step == nil || step.Func == nil {
			continue
		}

		var s *reporterpb.UICaseStep
		s, err = l.runStep(commonpb.TestStage_TEST, step)
		if s == nil {
			continue
		}
		s.Index = int64(i + 1)
		l.steps = append(l.steps, s)

		if err != nil {
			return errorx.Errorf(
				codes.ExecuteSubTaskFailure,
				"failed to execute the test step, task_id: %s, target: %s, step: %q, error: %+v",
				taskID, target, step.Desc.EN, err,
			)
		}
	}

	return nil
}

func (l *ExecuteUITestTaskLogic) runTeardownSteps(executor uitest.Executor) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()
	)

	l.Infof("begin to execute teardown steps, task_id: %s, target: %s", taskID, target)
	defer func() {
		l.Infof("finish to execute teardown steps, task_id: %s, target: %s", taskID, target)
	}()

	defer func() {
		if r := recover(); r != nil {
			l.Errorf(
				"got a panic while executing the teardown steps, task_id: %s, target: %s, error: %+v",
				taskID, target, r,
			)
		}
	}()

	// execute teardown steps
	for i, step := range executor.TeardownSteps() {
		if step == nil || step.Func == nil {
			continue
		}

		s, err := l.runStep(commonpb.TestStage_TEARDOWN, step)
		if err != nil {
			l.Errorf(
				"failed to execute the teardown step, task_id: %s, target: %s, step: %q, error: %+v",
				taskID, target, step.Desc.EN, err,
			)
		}
		if s == nil {
			continue
		}
		s.Index = int64(i + 1)
		l.steps = append(l.steps, s)
	}
}

func (l *ExecuteUITestTaskLogic) runStep(stage commonpb.TestStage, step *uitest.Step) (*reporterpb.UICaseStep, error) {
	if step == nil || step.Func == nil || stage == commonpb.TestStage_TS_NULL {
		return nil, nil
	}

	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		startedAt = time.Now()
		status    = dispatcherpb.ComponentState_Failure
		output    string
	)

	err := step.Func()
	if err != nil {
		l.logger.Error(err)
		output = l.logger.Sync()
	} else {
		status = dispatcherpb.ComponentState_Success
		l.logger.Infof(
			"succeeded in executing the %s step, task_id: %s, target: %s, step: %q",
			strings.ToLower(stage.String()), taskID, target, step.Desc.EN,
		)
		output = l.logger.Sync()
	}

	return &reporterpb.UICaseStep{
		TaskId:    taskID,
		Stage:     stage,
		Name:      step.Desc.ZH,
		Status:    status.String(),
		Content:   output,
		StartedAt: startedAt.UnixMilli(),
		EndedAt:   time.Now().UnixMilli(),
	}, err
}

func (l *ExecuteUITestTaskLogic) callbackData() []byte {
	uiCase := l.taskInfo.GetUiCase()
	data, _ := protobuf.MarshalJSON(
		&dispatcherpb.CallbackReq{
			TriggerMode:  l.taskInfo.GetTriggerMode(),
			TriggerRule:  l.taskInfo.GetTriggerRule(),
			ProjectId:    l.taskInfo.GetProjectId(),
			TaskId:       l.taskInfo.GetTaskId(),
			ExecuteType:  l.taskInfo.GetExecuteType(),
			CallbackType: l.taskInfo.GetCallbackType(),
			Data: &dispatcherpb.CallbackReq_UiCase{
				UiCase: &dispatcherpb.UICaseCallbackData{
					UiPlanId:         uiCase.GetUiPlanId(),
					UiPlanExecuteId:  uiCase.GetUiPlanExecuteId(),
					UiSuiteId:        uiCase.GetUiSuiteId(),
					UiSuiteExecuteId: uiCase.GetUiSuiteExecuteId(),
					CaseState:        l.state,
					UiCaseId:         uiCase.GetUiCaseId(),
					UiCaseExecuteId:  uiCase.GetUiCaseExecuteId(),
				},
			},
		},
	)

	return data
}
