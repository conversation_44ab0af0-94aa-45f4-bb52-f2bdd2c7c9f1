package manager

import (
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"
	"go.uber.org/atomic"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
)

type Consumer struct {
	consumer        *consumerv2.Consumer
	is_active       *atomic.Bool
	alive_time      *atomic.Time
	queue_name      string
	priority_queues map[string]lang.PlaceholderType
	stopOnce        sync.Once
}

func (c *Consumer) IsActive() bool {
	return c.is_active.Load()
}

func (c *Consumer) GetAliveTime() time.Time {
	return c.alive_time.Load()
}

func (c *Consumer) GetQueueName() string {
	return c.queue_name
}

func (c *Consumer) GetPriorityQueues() []string {
	queues := make([]string, 0, len(c.priority_queues))
	for queue := range c.priority_queues {
		queues = append(queues, queue)
	}
	return queues
}

func (c *Consumer) Start() {
	threading.GoSafe(c.consumer.StartWithoutListen)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the consumer in shutdown listener")
			c.Stop()
		},
	)
}

func (c *Consumer) Stop() {
	c.stopOnce.Do(func() {
		c.consumer.Shutdown()
	})
}
