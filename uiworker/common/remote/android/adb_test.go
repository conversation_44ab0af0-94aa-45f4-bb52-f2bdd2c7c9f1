package android

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/electricbubble/gadb"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/threading"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"
)

const (
	host   = "*************"
	port   = 20004
	serial = "9594292164004W0" // "LZYTYLZT9HFI6DLN" // "e5dbda0a" // "89NNNZE6W4UWTSKR"

	apkFile = "/Users/<USER>/Documents/App安装包/zhibo8.apk"
)

//func TestGOADBInstallApp(t *testing.T) {
//	client, err := adb.NewWithConfig(
//		adb.ServerConfig{
//			Port: adb.AdbPort,
//		},
//	)
//	if err != nil {
//		t.Fatalf("NewWithConfig: %+v", err)
//	}
//
//	if err = client.StartServer(); err != nil {
//		t.Fatalf("StartServer: %+v", err)
//	}
//
//	version, err := client.ServerVersion()
//	if err != nil {
//		t.Fatalf("ServerVersion: %+v", err)
//	}
//	t.Logf("ServerVersion: %d", version)
//
//	//if err = client.Connect(host, port); err != nil {
//	//	t.Fatalf("Connect: %+v", err)
//	//}
//	//time.Sleep(2 * time.Second)
//
//	device := client.Device(adb.DeviceWithSerial(serial))
//	info, err := device.DeviceInfo()
//	if err != nil {
//		t.Fatalf("DeviceInfo: %+v", err)
//	}
//	t.Logf("DeviceInfo: %s", jsonx.MarshalToStringIgnoreError(info))
//
//	//output, err := device.RunCommand("getprop", "|", "grep", "-i", "product")
//	//if err != nil {
//	//	t.Fatalf("RunCommand: %+v", err)
//	//}
//	//t.Logf("RunCommand: %s", output)
//
//	//path := "/data/local/tmp/new-zhibo8.apk"
//	//writer, err := device.OpenWrite(path, os.ModePerm, adb.MtimeOfClose)
//	//if err != nil {
//	//	t.Fatalf("OpenWrite: %+v", err)
//	//}
//	//defer func() {
//	//	if err != nil {
//	//		_ = writer.Close()
//	//	}
//	//}()
//	//
//	//b, err := os.ReadFile(apkFile)
//	//if err != nil {
//	//	t.Fatalf("ReadFile: %+v", err)
//	//}
//	//
//	//_, err = writer.Write(b)
//	//if err != nil {
//	//	t.Fatalf("Write: %+v", err)
//	//}
//	//_ = writer.Close()
//	//
//	//output, err := device.RunCommand("pm", "install", "-r", "-t", path)
//	//if err != nil {
//	//	t.Fatalf("RunCommand: %+v", err)
//	//}
//	//t.Logf("RunCommand: %s", output)
//}

func TestGADBConnectDevice(t *testing.T) {
	client, err := gadb.NewClient()
	if err != nil {
		t.Fatalf("NewClient: %+v", err)
	}

	if err = client.StartServer(); err != nil {
		t.Fatalf("StartServer: %+v", err)
	}

	version, err := client.ServerVersion()
	if err != nil {
		t.Fatalf("ServerVersion: %+v", err)
	}
	t.Logf("ServerVersion: %d", version)

	s := serial

	if err = client.Connect(host, port); err != nil {
		t.Errorf("Connect: %+v", err)
	}
	s = fmt.Sprintf("%s:%d", host, port)

	device, err := client.FindDeviceBySerial(s)
	if err != nil {
		t.Fatalf("FindDeviceBySerial: %+v", err)
	}
	defer func() {
		if err = client.Disconnect(host, port); err != nil {
			t.Errorf("Disconnect: %+v", err)
		}
	}()

	info := device.DeviceInfo()
	t.Logf("DeviceInfo: %s", jsonx.MarshalToStringIgnoreError(info))
}

func TestGADBInstallApp(t *testing.T) {
	client, err := gadb.NewClient()
	if err != nil {
		t.Fatalf("NewClient: %+v", err)
	}

	if err = client.StartServer(); err != nil {
		t.Fatalf("StartServer: %+v", err)
	}

	version, err := client.ServerVersion()
	if err != nil {
		t.Fatalf("ServerVersion: %+v", err)
	}
	t.Logf("ServerVersion: %d", version)

	s := serial

	if err = client.Connect(host, port); err != nil {
		t.Errorf("Connect: %+v", err)
		time.Sleep(time.Second)
	}
	s = fmt.Sprintf("%s:%d", host, port)

	device, err := client.FindDeviceBySerial(s)
	if err != nil {
		t.Fatalf("FindDeviceBySerial: %+v", err)
	}
	defer func() {
		if err = client.Disconnect(host, port); err != nil {
			t.Errorf("Disconnect: %+v", err)
		}
	}()

	info := device.DeviceInfo()
	t.Logf("DeviceInfo: %s", jsonx.MarshalToStringIgnoreError(info))

	var needToInstall bool

	_apk, err := apk.OpenFile(apkFile)
	if err != nil {
		t.Fatalf("OpenFile: %+v", err)
	}
	t.Logf("PackageName: %s, VersionName: %s", _apk.PackageName(), _apk.Manifest().VersionName)

	output, err := device.RunShellCommand(fmt.Sprintf("pm list packages %s", _apk.PackageName()))
	if err != nil {
		t.Fatalf("RunShellCommand: %+v", err)
	}
	output = strings.TrimSpace(output)
	if output != "" {
		if strings.HasPrefix(output, "package:") {
			output = output[len("package:"):]
		}

		output, err = device.RunShellCommand(fmt.Sprintf("dumpsys package %s | grep versionName", _apk.PackageName()))
		if err != nil {
			t.Fatalf("RunShellCommand: %+v", err)
		}

		target := strings.TrimSpace(output)
		if strings.HasPrefix(target, "versionName=") {
			target = target[len("versionName="):]
		}
		source := _apk.Manifest().VersionName
		if !strings.EqualFold(source, target) {
			t.Logf("need to reinstall the app: %s(%s => %s)", apkFile, source, target)
			needToInstall = true
		}
	} else {
		needToInstall = true
	}

	if needToInstall {
		file, err := os.Open(apkFile)
		if err != nil {
			t.Fatalf("Open: %+v", err)
		}

		path := filepath.Join("/data/local/tmp", fmt.Sprintf("%s.apk", _apk.PackageName()))
		if err = device.PushFile(file, path); err != nil {
			t.Fatalf("PushFile: %+v", err)
		}

		output, err = device.RunShellCommand("pm", "install", "-r", "-t", path)
		if err != nil {
			t.Fatalf("RunShellCommand: %+v", err)
		}
		t.Logf("RunShellCommand: %s", output)
	} else {
		t.Logf("no need to install the app: %s", apkFile)
	}
}

func TestGADBUninstallApp(t *testing.T) {
	client, err := gadb.NewClient()
	if err != nil {
		t.Fatalf("NewClient: %+v", err)
	}

	if err = client.StartServer(); err != nil {
		t.Fatalf("StartServer: %+v", err)
	}

	version, err := client.ServerVersion()
	if err != nil {
		t.Fatalf("ServerVersion: %+v", err)
	}
	t.Logf("ServerVersion: %d", version)

	s := serial

	if err = client.Connect(host, port); err != nil {
		t.Errorf("Connect: %+v", err)
		time.Sleep(time.Second)
	}
	s = fmt.Sprintf("%s:%d", host, port)

	device, err := client.FindDeviceBySerial(s)
	if err != nil {
		t.Fatalf("FindDeviceBySerial: %+v", err)
	}
	defer func() {
		if err = client.Disconnect(host, port); err != nil {
			t.Errorf("Disconnect: %+v", err)
		}
	}()

	info := device.DeviceInfo()
	t.Logf("DeviceInfo: %s", jsonx.MarshalToStringIgnoreError(info))

	_apk, err := apk.OpenFile(apkFile)
	if err != nil {
		t.Fatalf("OpenFile: %+v", err)
	}
	t.Logf("PackageName: %s, VersionName: %s", _apk.PackageName(), _apk.Manifest().VersionName)

	output, err := device.RunShellCommand(fmt.Sprintf("pm list packages %s", _apk.PackageName()))
	if err != nil {
		t.Fatalf("RunShellCommand: %+v", err)
	}
	output = strings.TrimSpace(output)
	if output != "" {
		output, err = device.RunShellCommand("pm", "uninstall", _apk.PackageName())
		if err != nil {
			t.Fatalf("RunCommand: %+v", err)
		}
		t.Logf("RunCommand: %s", output)
	} else {
		t.Logf("the app is not installed, so there is no need to uninstall it: %s", apkFile)
	}
}

//func TestFAInstallApp(t *testing.T) {
//	client := fa.NewClient("")
//
//	if err := client.StartServer(); err != nil {
//		t.Fatalf("StartServer: %+v", err)
//	}
//
//	version, err := client.ServerVersion()
//	if err != nil {
//		t.Fatalf("ServerVersion: %+v", err)
//	}
//	t.Logf("ServerVersion: %d", version)
//
//	device := client.DeviceWithSerial(serial)
//	properties, err := device.Properties()
//	if err != nil {
//		t.Fatalf("Properties: %+v", err)
//	}
//	t.Logf("Properties: %s", jsonx.MarshalToStringIgnoreError(properties))
//}

func TestGUIA2Click(t *testing.T) {
	client, err := gadb.NewClient()
	if err != nil {
		t.Fatalf("NewClient: %+v", err)
	}

	if err = client.StartServer(); err != nil {
		t.Fatalf("StartServer: %+v", err)
	}

	version, err := client.ServerVersion()
	if err != nil {
		t.Fatalf("ServerVersion: %+v", err)
	}
	t.Logf("ServerVersion: %d", version)

	s := serial
	device, err := client.FindDeviceBySerial(s)
	if err != nil {
		t.Fatalf("FindDeviceBySerial: %+v", err)
	}

	driver, err := gu2.NewDriver(device)
	if driver != nil {
		defer func() {
			_ = driver.Close()
		}()
	}
	if err != nil {
		t.Fatalf("NewDriver: %+v", err)
	}

	timer := time.NewTimer(time.Second * 30)
	defer timer.Stop()

	for {
		select {
		case <-timer.C:
			t.Errorf("Timeout")
			return
		default:
			for _, text := range []string{"继续安装", "确定", "完成"} {
				element := driver.FindElementBySelectorOptions(gu2.ByText(text))
				if element == nil {
					continue
				} else if ok, err := element.Exist(); err != nil || !ok {
					continue
				} else if info, err := element.Info(); err != nil || info == nil {
					continue
				} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
					continue
				} else {
					t.Logf("Element Info: %s", jsonx.MarshalToStringIgnoreError(info))
				}

				t.Logf("Click: %s", text)
				break
			}

			time.Sleep(time.Second)
		}
	}
}

func TestGADBInstallAppWithManualConfirmation(t *testing.T) {
	exitCh := make(chan lang.PlaceholderType)
	defer func() {
		close(exitCh)
	}()

	client, err := gadb.NewClient()
	if err != nil {
		t.Fatalf("NewClient: %+v", err)
	}

	if err = client.StartServer(); err != nil {
		t.Fatalf("StartServer: %+v", err)
	}

	version, err := client.ServerVersion()
	if err != nil {
		t.Fatalf("ServerVersion: %+v", err)
	}
	t.Logf("ServerVersion: %d", version)

	s := serial

	//if err = client.Connect(host, port); err != nil {
	//	t.Errorf("Connect: %+v", err)
	//	time.Sleep(time.Second)
	//}
	//s = fmt.Sprintf("%s:%d", host, port)

	device, err := client.FindDeviceBySerial(s)
	if err != nil {
		t.Fatalf("FindDeviceBySerial: %+v", err)
	}
	//defer func() {
	//	if err = client.Disconnect(host, port); err != nil {
	//		t.Errorf("Disconnect: %+v", err)
	//	}
	//}()

	info := device.DeviceInfo()
	t.Logf("DeviceInfo: %s", jsonx.MarshalToStringIgnoreError(info))

	var needToInstall bool

	_apk, err := apk.OpenFile(apkFile)
	if err != nil {
		t.Fatalf("OpenFile: %+v", err)
	}
	t.Logf("PackageName: %s, VersionName: %s", _apk.PackageName(), _apk.Manifest().VersionName)

	output, err := device.RunShellCommand(fmt.Sprintf("pm list packages %s", _apk.PackageName()))
	if err != nil {
		t.Fatalf("RunShellCommand: %+v", err)
	}
	output = strings.TrimSpace(output)
	if output != "" {
		if strings.HasPrefix(output, "package:") {
			output = output[len("package:"):]
		}

		output, err = device.RunShellCommand(fmt.Sprintf("dumpsys package %s | grep versionName", _apk.PackageName()))
		if err != nil {
			t.Fatalf("RunShellCommand: %+v", err)
		}

		target := strings.TrimSpace(output)
		if strings.HasPrefix(target, "versionName=") {
			target = target[len("versionName="):]
		}
		source := _apk.Manifest().VersionName
		if !strings.EqualFold(source, target) {
			t.Logf("need to reinstall the app: %s(%s => %s)", apkFile, source, target)
			needToInstall = true
		}
	} else {
		needToInstall = true
	}

	if needToInstall {
		file, err := os.Open(apkFile)
		if err != nil {
			t.Fatalf("Open: %+v", err)
		}

		path := filepath.Join("/data/local/tmp", fmt.Sprintf("%s.apk", _apk.PackageName()))
		if err = device.PushFile(file, path); err != nil {
			t.Fatalf("PushFile: %+v", err)
		}

		driver, err := gu2.NewDriver(device)
		defer func() {
			if driver != nil {
				_ = driver.Close()
			}
		}()
		if err != nil {
			t.Errorf("failed to new uiautomator2 driver, udid: %s, error: %+v", device.Serial(), err)
			return
		}

		if driver != nil {
			threading.GoSafe(
				func() {
					handleManualConfirmation(t, device, driver, exitCh)
				},
			)
		}

		output, err = device.RunShellCommand("pm", "install", "-r", "-t", path)
		if err != nil {
			t.Fatalf("RunShellCommand: %+v", err)
		}
		t.Logf("RunShellCommand: %s", output)
	} else {
		t.Logf("no need to install the app: %s", apkFile)
	}
}

func handleManualConfirmation(
	t *testing.T, device *gadb.Device, driver *gu2.Driver, exitCh <-chan lang.PlaceholderType,
) {
	for {
		select {
		case <-exitCh:
			t.Logf("got an exit signal from sub task of installing the android app, udid: %s", device.Serial())
			return
		default:
			for _, text := range []string{"继续安装", "确定", "完成"} {
				element := driver.FindElementBySelectorOptions(gu2.ByTextContains(text))
				if element == nil {
					continue
				} else if ok, err := element.Exist(); err != nil || !ok {
					continue
				} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
					continue
				}

				t.Logf(
					"click the element successfully while handling manual confirmation, udid: %s, text: %s",
					device.Serial(), text,
				)
				break
			}

			time.Sleep(time.Second)
		}
	}
}
