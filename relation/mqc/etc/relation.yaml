Name: mqc.relation

Log:
  ServiceName: mqc.relation
  Encoding: plain
  Level: info
  Path: /app/logs/relation

Prometheus:
  Host: 0.0.0.0
  Port: 20723
  Path: /metrics

Telemetry:
  Name: mqc.relation
  Endpoint: http://127.0.0.1:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DevServer:
  Enabled: true
  Port: 20733

Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 16

Cache:
  - Host: 127.0.0.1:6379
    Pass:
    DB: 16

DB:
#  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/relation?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
  DataSource: probe:Quwan@2020_TTinternation@tcp(************:3306)/relation?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Consumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:relation
  ConsumerTag: mqc:relation
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 16
  MaxWorker: 0

TTServiceUrl : http://testing-internal-api.ttyuyin.com/topology-trace-api/query

TLink:
  BaseURL: https://testing-internal-api.ttyuyin.com

DomainReplacements:
  - From: testing-api.ttyuyin.com
    To: testing-internal-api.ttyuyin.com
  - From: testing-api-open.ttyuyin.com
    To: testing-internal-api.ttyuyin.com
