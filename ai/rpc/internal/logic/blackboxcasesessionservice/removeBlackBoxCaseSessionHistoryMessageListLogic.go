package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type RemoveBlackBoxCaseSessionHistoryMessageListLogic struct {
	*logic.BaseLogic
}

func NewRemoveBlackBoxCaseSessionHistoryMessageListLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoveBlackBoxCaseSessionHistoryMessageListLogic {
	return &RemoveBlackBoxCaseSessionHistoryMessageListLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// RemoveBlackBoxCaseSessionHistoryMessageList removes black box case session history message list
func (l *RemoveBlackBoxCaseSessionHistoryMessageListLogic) RemoveBlackBoxCaseSessionHistoryMessageList(in *pb.RemoveBlackBoxCaseSessionHistoryMessageListReq) (
	out *pb.RemoveBlackBoxCaseSessionHistoryMessageListResp, err error,
) {
	err = httpc.DeleteSessionHistory(l.SvcCtx.ExternalAiDomain, in.SessionId)
	if err != nil {
		return nil, err
	}
	return &pb.RemoveBlackBoxCaseSessionHistoryMessageListResp{}, nil
}
