package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

type Config struct {
	zrpc.RpcServerConf

	DB    types.DBConfig
	Cache cache.CacheConf
	// Redis redis.RedisConf

	UserRpc zrpc.RpcClientConf

	ExternalAiDomain string
}

func (c Config) ListenOn() string {
	return c.RpcServerConf.ListenOn
}

func (c Config) LogConfig() logx.LogConf {
	return c.RpcServerConf.ServiceConf.Log
}
