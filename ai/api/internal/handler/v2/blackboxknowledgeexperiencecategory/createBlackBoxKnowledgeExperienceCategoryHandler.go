package blackboxknowledgeexperiencecategory

import (
	"net/http"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic/v2/blackboxknowledgeexperiencecategory"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

// create blackbox knowledge experience category
func CreateBlackBoxKnowledgeExperienceCategoryHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateBlackBoxKnowledgeExperienceCategoryReq
		if err := httpx.Parse(r, &req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ParseParamError, err.Error()), "failed to parse parameters, error: %+v", err))
			return
		}

		if err := svcCtx.Validator.Validate.StructCtx(r.Context(), req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ValidateParamError, svcCtx.Validator.Translate(err)), "failed to validate parameters, error: %+v", err))
			return
		}

		l := blackboxknowledgeexperiencecategory.NewCreateBlackBoxKnowledgeExperienceCategoryLogic(r.Context(), svcCtx)
		resp, err := l.CreateBlackBoxKnowledgeExperienceCategory(&req)
		response.MakeHttpResponse(r, w, resp, err)
	}
}
