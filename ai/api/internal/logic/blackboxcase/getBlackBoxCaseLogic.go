package blackboxcase

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseLogic {
	return &GetBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseLogic) GetBlackBoxCase(req *types.GetBlackBoxCaseReq) (resp *types.GetBlackBoxCaseResp, err error) {
	in := &pb.GetBlackBoxCaseReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseResp{Item: &types.BlackBoxCase{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	if out.Item.ReferenceDoc != "" {
		refDoc := types.ReferenceDoc{}
		err = json.Unmarshal([]byte(out.Item.ReferenceDoc), &refDoc)
		if err != nil {
			return nil, err
		}
		resp.Item.ReferenceDoc = &refDoc
	}

	return resp, nil
}
