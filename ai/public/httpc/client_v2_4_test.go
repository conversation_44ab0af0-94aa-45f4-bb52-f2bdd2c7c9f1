package httpc

import (
	"fmt"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestCreateBaseReviewMap(t *testing.T) {
	type args struct {
		baseUrl string
		in      CreateBlackBoxBaseReviewMapReq
	}
	tests := []struct {
		name string
		args args
		//want    ResponseData
		wantErr bool
	}{
		// TODO: Add test cases.

		{"test1",
			args{"http://probe-test.ttyuyin.com:8000",
				CreateBlackBoxBaseReviewMapReq{
					MapId:          "zzqabcabcmap_100111adad", // 每次都要换新的
					ProjectId:      "tt:xxx:1",
					KnowledgeDocId: "v2.4-test-1",
					//KnowledgeDocPgTitle: KnowledgeDocPgTitle{
					//	Id:    1510480,
					//	Title: "4、需求详述",
					//},
					KnowledgeTermList: []string{"手游社交-桌宠"},
					KnowledgeExperienceList: []KnowledgeExperience{
						{
							ID:                 "test_exp_1",
							ProjectID:          "tt:xxx:1",
							Project:            "TT语音",
							Tags:               []string{},
							Category:           "页面交互类型",
							TestExperience:     "拖拽",
							NormalFocusPoint:   "1、涉及拖动动作的时候，需要考虑动作方向。例如上、下、左、右、中间等",
							AbnormalFocusPoint: "1、杀进程\\n2、断网点击\\n3、快速点击",
						},
					},
					ModelCharacteristic: "just_right",
				},
			}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CreateBaseReviewMap(tt.args.baseUrl, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBaseReviewMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			jsonx.MarshalIgnoreError(got.Data)

			t.Log("res:", string(jsonx.MarshalIgnoreError(got.Data)))
		})
	}
}

func TestCreateFuncReviewMap(t *testing.T) {
	type args struct {
		baseUrl string
		in      CreateBlackBoxFuncReviewMapReq
	}
	tests := []struct {
		name string
		args args
		//want    ResponseData
		wantErr bool
	}{
		{"test1",
			args{"http://probe-test.ttyuyin.com:8000",
				CreateBlackBoxFuncReviewMapReq{
					MapId:          "qzqabcabcmap_100", // 每次都要换新的
					ProjectID:      "tt:xxx:1",
					KnowledgeDocID: "v2.4-test-1",
					//KnowledgeDocPgTitle: KnowledgeDocPgTitle{
					//	Id:    1510480,
					//	Title: "4、需求详述",
					//},
					KnowledgeTermList: []string{"手游社交-桌宠"},
					KnowledgeExperienceList: []KnowledgeExperience{
						{
							ID:                 "test_exp_1",
							ProjectID:          "tt:xxx:1",
							Project:            "TT语音",
							Tags:               []string{},
							Category:           "页面交互类型",
							TestExperience:     "拖拽",
							NormalFocusPoint:   "1、涉及拖动动作的时候，需要考虑动作方向。例如上、下、左、右、中间等",
							AbnormalFocusPoint: "1、杀进程\\n2、断网点击\\n3、快速点击",
						},
					},
					ModelCharacteristic: "just_right",
					FunctionPoint:       "",
					KnowledgeSupplementDoc: SupplementDoc{
						Type: "feishu",
						Feishu: FeishuDoc{
							KnowledgeDocId: "v2.4-test-1",
							KnowledgeDocPgTitle: []*KnowledgeDocPgTitle{
								{
									Id:    3654648,
									Title: "4.3 聊天优化",
								},
							},
						},
					},
					GenerateOpinion: "无",
				},
			}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CreateFuncReviewMap(tt.args.baseUrl, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateFuncReviewMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			jsonx.MarshalIgnoreError(got.Data)

			t.Log("res:", string(jsonx.MarshalIgnoreError(got.Data)))
		})
	}
}

func TestCreateSceneReviewMap(t *testing.T) {
	type args struct {
		baseUrl string
		in      CreateBlackBoxTestSceneReviewMapReq
	}
	tests := []struct {
		name string
		args args
		//want    ResponseData
		wantErr bool
	}{
		{"test1",
			args{"http://probe-test.ttyuyin.com:8000",
				CreateBlackBoxTestSceneReviewMapReq{
					MapId:          "adadadqzqabcabcmap_100", // 每次都要换新的
					ProjectID:      "tt:xxx:1",
					KnowledgeDocID: "v2.4-test-1",
					//KnowledgeDocPgTitle: KnowledgeDocPgTitle{
					//	Id:    1510480,
					//	Title: "4、需求详述",
					//},
					KnowledgeTermList: []string{"手游社交-桌宠"},
					KnowledgeExperienceList: []KnowledgeExperience{
						{
							ID:                 "test_exp_1",
							ProjectID:          "tt:xxx:1",
							Project:            "TT语音",
							Tags:               []string{},
							Category:           "页面交互类型",
							TestExperience:     "拖拽",
							NormalFocusPoint:   "1、涉及拖动动作的时候，需要考虑动作方向。例如上、下、左、右、中间等",
							AbnormalFocusPoint: "1、杀进程\\n2、断网点击\\n3、快速点击",
						},
					},
					ModelCharacteristic: "just_right",
					FunctionPoint:       "",
					KnowledgeSupplementDoc: SupplementDoc{
						Type: "feishu",
						Feishu: FeishuDoc{
							KnowledgeDocId: "v2.4-test-1",
							KnowledgeDocPgTitle: []*KnowledgeDocPgTitle{
								{
									Id:    3654648,
									Title: "4.3 聊天优化",
								},
							},
						},
					},
					GenerateOpinion: "无",
				},
			}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CreateTestSceneReviewMap(tt.args.baseUrl, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateTestSceneReviewMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			jsonx.MarshalIgnoreError(got.Data)

			t.Log("res:", string(jsonx.MarshalIgnoreError(got.Data)))
		})
	}
}

var (
	testCreateTestCaseRequest = `{
    "knowledge_doc_id": "v2.4-test-1",
    "knowledge_doc_pg_title": {
        "id": 1510480,
        "title": "4、需求详述"
    },
    "model_characteristic": "just_right",
    "knowledge_supplement_doc": [
        {
            "type": "feishu",
            "feishu": {
                "knowledge_doc_id": "v2.4-test-1",
                "knowledge_doc_pg_title": [
                    {
                        "id": 3654648,
                        "title": "4.3 聊天优化"
                    },
                    {
                        "id": 2987886,
                        "title": "4.4 跟随设置优化"
                    }
                ]
            }
        }
    ],
    "review_map": {
        "id": 513399,
        "data": {
            "text": "4、需求详述"
        },
        "children": [
            {
                "id": 526116,
                "data": {
                    "text": "4.4 跟随设置优化"
                },
                "children": [
                    {
                        "id": 386013,
                        "data": {
                            "text": " 跟随设置优化 "
                        },
                        "children": [
                            {
                                "id": 733959,
                                "data": {
                                    "text": " 用户已打开所有的跟随设置，在下次进入时，检查跟随设置是否收起在二级页并新增设置入口 "
                                },
                                "children": [
                                    {
                                        "id": 24510,
                                        "data": {
                                            "text": " 1. 下次进入应用时，跟随设置收起在二级页，新增设置入口显示<br>2. 用户可以通过新的设置入口访问跟随设置 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 1452,
                                "data": {
                                    "text": " 用户未打开全部跟随设置，检查跟随设置的展示逻辑是否与之前一致 "
                                },
                                "children": [
                                    {
                                        "id": 806731,
                                        "data": {
                                            "text": " 1. 跟随设置的展示逻辑与之前版本一致，没有变化<br>2. 用户可以正常查看和修改各个跟随设置 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 992257,
                                "data": {
                                    "text": " 安卓用户打开了全部跟随设置，退出应用再进入，检查跟随设置是否收起，并包含端外跟随设置 "
                                },
                                "children": [
                                    {
                                        "id": 542854,
                                        "data": {
                                            "text": " 1. 下次进入应用时，跟随设置收起在二级页，包含端内和端外跟随设置<br>2. 新增的设置入口可以访问所有跟随设置 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 274623,
                                "data": {
                                    "text": " iOS用户打开了全部跟随设置，退出应用再进入，检查跟随设置是否收起 "
                                },
                                "children": [
                                    {
                                        "id": 612063,
                                        "data": {
                                            "text": " 1. 下次进入应用时，跟随设置收起在二级页<br>2. 新增设置入口仅包含端内跟随设置 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 920719,
                                "data": {
                                    "text": " 用户打开全部跟随设置后，立即杀掉进程，再次进入应用，检查跟随设置是否收起 "
                                },
                                "children": [
                                    {
                                        "id": 929662,
                                        "data": {
                                            "text": " 1. 下次进入应用时，跟随设置收起在二级页 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 257133,
                                "data": {
                                    "text": " 用户打开跟随设置时，网络断开，设置未保存，检查跟随设置状态 "
                                },
                                "children": [
                                    {
                                        "id": 4201,
                                        "data": {
                                            "text": " 1. 应用提示网络错误，设置未保存<br>2. 跟随设置仍保持原状态 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 977733,
                                "data": {
                                    "text": " 用户快速增、删、改跟随设置，查看设置是否正常生效 "
                                },
                                "children": [
                                    {
                                        "id": 881021,
                                        "data": {
                                            "text": " 1. 应用正确保存用户的设置修改<br>2. 设置界面实时刷新，显示最新的设置状态 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 948124,
                                "data": {
                                    "text": " 用户打开全部跟随设置，但下次进入时，跟随设置未收起，检查是否存在异常 "
                                },
                                "children": [
                                    {
                                        "id": 764830,
                                        "data": {
                                            "text": " 1. 系统应当将跟随设置收起到二级页<br>2. 若未收起，需反馈给开发人员处理 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "id": 572211,
                "data": {
                    "text": "4.3 聊天优化"
                },
                "children": [
                    {
                        "id": 768919,
                        "data": {
                            "text": " 聊天页面优化 "
                        },
                        "children": [
                            {
                                "id": 69054,
                                "data": {
                                    "text": " 用户收到AI发送的语音消息，能够正常播放。 "
                                },
                                "children": [
                                    {
                                        "id": 424631,
                                        "data": {
                                            "text": " 1. AI语音消息可以正常播放。<br>2. 声音清晰，无卡顿。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 440459,
                                "data": {
                                    "text": " 用户在无声模式下收到AI语音消息，播放时是否遵循系统音量设置。 "
                                },
                                "children": [
                                    {
                                        "id": 364053,
                                        "data": {
                                            "text": " 1. 在无声模式下，AI语音消息播放时遵循系统设置，不会发出声音。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 352177,
                                "data": {
                                    "text": " 用户在收到AI语音消息时，网络中断，是否能正常播放。 "
                                },
                                "children": [
                                    {
                                        "id": 18507,
                                        "data": {
                                            "text": " 1. 已接收的AI语音消息，网络中断后仍可正常播放。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 23940,
                                "data": {
                                    "text": " AI语音消息较长时，能否完整播放。 "
                                },
                                "children": [
                                    {
                                        "id": 584534,
                                        "data": {
                                            "text": " 1. AI语音消息均可完整播放，无论长度。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 941982,
                                "data": {
                                    "text": " 用户录制一段语音消息并发送，AI能够接收到并做出正确的回复。 "
                                },
                                "children": [
                                    {
                                        "id": 210436,
                                        "data": {
                                            "text": " 1. 用户录音成功，发送成功。<br>2. AI正确识别并回复。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 602946,
                                "data": {
                                    "text": " 用户录制语音时，中途取消，是否有正确的提示。 "
                                },
                                "children": [
                                    {
                                        "id": 633217,
                                        "data": {
                                            "text": " 1. 用户取消录音，提示录音已取消，不发送消息。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 909546,
                                "data": {
                                    "text": " 用户录制超长语音消息，是否有录音时长的限制提示。 "
                                },
                                "children": [
                                    {
                                        "id": 262996,
                                        "data": {
                                            "text": " 1. 超过录音时长限制时，提示录音已达到最大时长。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 977072,
                                "data": {
                                    "text": " 用户发送语音消息时，网络中断，是否有相应提示或重试机制。 "
                                },
                                "children": [
                                    {
                                        "id": 206680,
                                        "data": {
                                            "text": " 1. 发送失败，提示网络异常，提供重试选项。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 137257,
                                "data": {
                                    "text": " 用户录制环境嘈杂，AI能否正确识别。 "
                                },
                                "children": [
                                    {
                                        "id": 697390,
                                        "data": {
                                            "text": " 1. AI尽可能准确识别语音，无法识别时给出合理提示。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 275542,
                                "data": {
                                    "text": " 用户发送消息后，立即显示AI正在回复的「...」动效。 "
                                },
                                "children": [
                                    {
                                        "id": 718275,
                                        "data": {
                                            "text": " 1. 用户发送消息后，立即显示「...」动效，表示AI正在输入中。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 334053,
                                "data": {
                                    "text": " AI回复较快时，「...」动效是否会短暂闪现或省略。 "
                                },
                                "children": [
                                    {
                                        "id": 350927,
                                        "data": {
                                            "text": " 1. AI回复较快时，「...」动效短暂显示或直接显示回复。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 418015,
                                "data": {
                                    "text": " 用户发送消息后，网络中断，是否有相应提示，动效是否停止。 "
                                },
                                "children": [
                                    {
                                        "id": 433500,
                                        "data": {
                                            "text": " 1. 网络中断时，提示发送失败，「...」动效消失。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 317603,
                                "data": {
                                    "text": " 多次快速发送消息，是否能正确显示「...」动效。 "
                                },
                                "children": [
                                    {
                                        "id": 755420,
                                        "data": {
                                            "text": " 1. 每次发送消息后，「...」动效正确显示，不重叠或混乱。 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 492737,
                        "data": {
                            "text": " 桌宠主页-聊天功能优化 "
                        },
                        "children": [
                            {
                                "id": 589787,
                                "data": {
                                    "text": " 用户在桌宠主页发送消息后，桌宠头上出现冒泡提示。 "
                                },
                                "children": [
                                    {
                                        "id": 569746,
                                        "data": {
                                            "text": " 1. 用户发送消息后，桌宠头上出现冒泡提示。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 820402,
                                "data": {
                                    "text": " 冒泡提示展示1秒后消失。 "
                                },
                                "children": [
                                    {
                                        "id": 125393,
                                        "data": {
                                            "text": " 1. 冒泡提示展示1秒后自动消失。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 335124,
                                "data": {
                                    "text": " 收到AI回复后，思考中的提示消失，展示回复。 "
                                },
                                "children": [
                                    {
                                        "id": 229664,
                                        "data": {
                                            "text": " 1. AI回复后，思考提示消失，显示AI回复内容。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 511039,
                                "data": {
                                    "text": " 用户快速连续发送消息，冒泡提示是否能正确显示。 "
                                },
                                "children": [
                                    {
                                        "id": 241281,
                                        "data": {
                                            "text": " 1. 冒泡提示按每次消息发送正确显示，不重叠或遗漏。 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 99608,
                                "data": {
                                    "text": " 用户发送消息后，网络中断，冒泡提示状态如何变化。 "
                                },
                                "children": [
                                    {
                                        "id": 934316,
                                        "data": {
                                            "text": " 1. 网络中断时，冒泡提示消失，提示发送失败。 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "id": 971534,
                "data": {
                    "text": "4.2 悬浮小人-新增交互"
                },
                "children": [
                    {
                        "id": 440225,
                        "data": {
                            "text": " 双击悬浮小人，弹出键盘进行对话 "
                        },
                        "children": [
                            {
                                "id": 382906,
                                "data": {
                                    "text": " 用户正常双击悬浮小人，键盘弹出，可以输入消息与小人对话 "
                                },
                                "children": [
                                    {
                                        "id": 65358,
                                        "data": {
                                            "text": " 1. 键盘成功弹出<br>2. 用户可以输入并发送消息 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 730071,
                                "data": {
                                    "text": " 用户单击悬浮小人，观察是否有反应 "
                                },
                                "children": [
                                    {
                                        "id": 704233,
                                        "data": {
                                            "text": " 1. 单击无反应<br>2. 键盘不弹出 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 200322,
                                "data": {
                                    "text": " 用户快速多次点击悬浮小人，观察是否导致异常 "
                                },
                                "children": [
                                    {
                                        "id": 521133,
                                        "data": {
                                            "text": " 1. 系统稳定，无异常<br>2. 键盘正常弹出或收起 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 196499,
                                "data": {
                                    "text": " 在悬浮小人未获得悬浮权限时，双击小人 "
                                },
                                "children": [
                                    {
                                        "id": 510785,
                                        "data": {
                                            "text": " 1. 悬浮小人不显示<br>2. 双击无反应，键盘不弹出 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 527542,
                                "data": {
                                    "text": " 在不同界面（主屏、App内、锁屏）双击悬浮小人 "
                                },
                                "children": [
                                    {
                                        "id": 46055,
                                        "data": {
                                            "text": " 1. 主屏和App内键盘正常弹出<br>2. 锁屏界面键盘不弹出 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 636674,
                                "data": {
                                    "text": " 系统资源不足时，双击悬浮小人 "
                                },
                                "children": [
                                    {
                                        "id": 134667,
                                        "data": {
                                            "text": " 1. 系统提示资源不足<br>2. 键盘不弹出 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 413769,
                                "data": {
                                    "text": " 断网情况下，双击悬浮小人 "
                                },
                                "children": [
                                    {
                                        "id": 767130,
                                        "data": {
                                            "text": " 1. 键盘成功弹出<br>2. 发送消息时提示网络异常 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 574800,
                                "data": {
                                    "text": " 测试双击手势识别的准确性 "
                                },
                                "children": [
                                    {
                                        "id": 669203,
                                        "data": {
                                            "text": " 1. 双击手势识别准确<br>2. 不同速度下双击均可弹出键盘 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 773687,
                        "data": {
                            "text": " 键盘的功能特性 "
                        },
                        "children": [
                            {
                                "id": 111442,
                                "data": {
                                    "text": " 用户点击空白处，键盘收起 "
                                },
                                "children": [
                                    {
                                        "id": 684919,
                                        "data": {
                                            "text": " 1. 点击空白处，键盘收起<br>2. 界面恢复悬浮小人状态 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 861110,
                                "data": {
                                    "text": " 用户点击键盘以外的其他区域，键盘不收起 "
                                },
                                "children": [
                                    {
                                        "id": 405465,
                                        "data": {
                                            "text": " 1. 点击非空白区域，键盘不收起<br>2. 用户可继续输入 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 165103,
                                "data": {
                                    "text": " 键盘上显示对应形象的趴趴动画 "
                                },
                                "children": [
                                    {
                                        "id": 580554,
                                        "data": {
                                            "text": " 1. 键盘上正确显示趴趴动画<br>2. 动画与当前小人形象一致 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 346892,
                                "data": {
                                    "text": " 验证趴趴动画的正常显示和播放 "
                                },
                                "children": [
                                    {
                                        "id": 487822,
                                        "data": {
                                            "text": " 1. 动画正常播放，无卡顿<br>2. 动画循环正确 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 942319,
                                "data": {
                                    "text": " 屏蔽挂件小人仔，验证其被屏蔽 "
                                },
                                "children": [
                                    {
                                        "id": 422863,
                                        "data": {
                                            "text": " 1. 挂件小人仔不显示<br>2. 不影响其他功能 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 544727,
                                "data": {
                                    "text": " 快速点击空白处，验证键盘收起是否异常 "
                                },
                                "children": [
                                    {
                                        "id": 572260,
                                        "data": {
                                            "text": " 1. 键盘迅速收起<br>2. 无延迟或异常情况 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 324710,
                                "data": {
                                    "text": " 多任务切换时，键盘的状态变化 "
                                },
                                "children": [
                                    {
                                        "id": 576981,
                                        "data": {
                                            "text": " 1. 切换至其他应用，键盘自动收起<br>2. 返回应用，键盘不自动弹出 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 312515,
                        "data": {
                            "text": " 发送消息后的交互效果 "
                        },
                        "children": [
                            {
                                "id": 155900,
                                "data": {
                                    "text": " 用户发送消息成功后，键盘和蒙层收起 "
                                },
                                "children": [
                                    {
                                        "id": 283295,
                                        "data": {
                                            "text": " 1. 消息发送成功<br>2. 键盘和蒙层自动收起 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 747502,
                                "data": {
                                    "text": " 用户发送消息失败时，键盘和蒙层的状态 "
                                },
                                "children": [
                                    {
                                        "id": 891355,
                                        "data": {
                                            "text": " 1. 消息发送失败提示<br>2. 键盘和蒙层保持，用户可重新编辑 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 846323,
                                "data": {
                                    "text": " 小人头顶出现冒泡的泡，显示发送中效果 "
                                },
                                "children": [
                                    {
                                        "id": 616621,
                                        "data": {
                                            "text": " 1. 消息发送后，小人头顶出现冒泡<br>2. 冒泡显示发送中状态 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 43538,
                                "data": {
                                    "text": " 收到小人回复后，展示回复内容，泡泡消失 "
                                },
                                "children": [
                                    {
                                        "id": 617800,
                                        "data": {
                                            "text": " 1. 小人回复内容展示<br>2. 冒泡自动消失 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 725250,
                                "data": {
                                    "text": " 用户发送空消息，系统处理 "
                                },
                                "children": [
                                    {
                                        "id": 230778,
                                        "data": {
                                            "text": " 1. 不允许发送空消息<br>2. 提示用户输入内容 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 638737,
                                "data": {
                                    "text": " 用户频繁发送消息，系统处理 "
                                },
                                "children": [
                                    {
                                        "id": 145271,
                                        "data": {
                                            "text": " 1. 系统能正常处理<br>2. 无崩溃或卡顿 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 359278,
                                "data": {
                                    "text": " 网络异常时，发送消息后的交互 "
                                },
                                "children": [
                                    {
                                        "id": 109308,
                                        "data": {
                                            "text": " 1. 提示网络异常<br>2. 键盘和蒙层根据设计处理 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 232126,
                                "data": {
                                    "text": " 用户尝试发送语音消息 "
                                },
                                "children": [
                                    {
                                        "id": 371987,
                                        "data": {
                                            "text": " 1. 无法发送语音消息<br>2. 界面不提供语音发送功能 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 962066,
                        "data": {
                            "text": " 对话内容计入上下文 "
                        },
                        "children": [
                            {
                                "id": 257875,
                                "data": {
                                    "text": " 用户多次对话，验证上下文计入情况 "
                                },
                                "children": [
                                    {
                                        "id": 534773,
                                        "data": {
                                            "text": " 1. 小人回复包含上下文<br>2. 对话连贯 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 81374,
                                "data": {
                                    "text": " 清除缓存或重启App后，验证上下文 "
                                },
                                "children": [
                                    {
                                        "id": 157897,
                                        "data": {
                                            "text": " 1. 根据设计，上下文保留或清除<br>2. 符合预期 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 509049,
                                "data": {
                                    "text": " 多设备登录，验证上下文共享情况 "
                                },
                                "children": [
                                    {
                                        "id": 124231,
                                        "data": {
                                            "text": " 1. 上下文是否同步符合设计<br>2. 无异常 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 522401,
                                "data": {
                                    "text": " 对话上下文超长时，系统处理 "
                                },
                                "children": [
                                    {
                                        "id": 156782,
                                        "data": {
                                            "text": " 1. 系统截断或清理旧上下文<br>2. 小人回复正常 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 781078,
                                "data": {
                                    "text": " 用户清除对话记录后，再次对话 "
                                },
                                "children": [
                                    {
                                        "id": 515348,
                                        "data": {
                                            "text": " 1. 上下文被重置<br>2. 小人回复不包含之前内容 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 371387,
                                "data": {
                                    "text": " 不同网络下，上下文同步情况 "
                                },
                                "children": [
                                    {
                                        "id": 352036,
                                        "data": {
                                            "text": " 1. 上下文同步正常<br>2. 网络切换无影响 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "id": 553171,
                "data": {
                    "text": "4.1 悬浮小人-首次出现新增交互引导"
                },
                "children": [
                    {
                        "id": 646381,
                        "data": {
                            "text": " 悬浮小人首次出现时展示引导 "
                        },
                        "children": [
                            {
                                "id": 484075,
                                "data": {
                                    "text": " 用户首次打开悬浮小人功能，是否正确展示引导流程 "
                                },
                                "children": [
                                    {
                                        "id": 270014,
                                        "data": {
                                            "text": " 1. 用户的悬浮小人首次出现时，展示首次引导流程，从引导1开始 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 405383,
                                "data": {
                                    "text": " 用户非首次打开悬浮小人功能，是否不会再次展示引导 "
                                },
                                "children": [
                                    {
                                        "id": 940270,
                                        "data": {
                                            "text": " 1. 用户已完成引导流程，非首次打开悬浮小人，不再展示引导 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 752288,
                                "data": {
                                    "text": " 首次打开但引导记录缺失（如清除缓存后），是否再次展示引导 "
                                },
                                "children": [
                                    {
                                        "id": 560908,
                                        "data": {
                                            "text": " 1. 引导记录缺失，视为已完成引导，不再展示首次引导 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 55567,
                        "data": {
                            "text": " 引导1：拖动 "
                        },
                        "children": [
                            {
                                "id": 885154,
                                "data": {
                                    "text": " 用户按照指引拖动小人至屏幕边缘，是否正确进入下一步 "
                                },
                                "children": [
                                    {
                                        "id": 671965,
                                        "data": {
                                            "text": " 1. 用户拖动小人至屏幕边缘后，引导1结束，展示完成提示2秒，进入引导2 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 125319,
                                "data": {
                                    "text": " 用户拖动小人到屏幕其他位置（非贴边），是否能进入下一步 "
                                },
                                "children": [
                                    {
                                        "id": 717852,
                                        "data": {
                                            "text": " 1. 系统识别用户的拖动操作，要求继续拖动至屏幕边缘，或直接进入下一步 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 648636,
                                "data": {
                                    "text": " 用户在10秒内未进行任何操作，是否自动跳转下一步 "
                                },
                                "children": [
                                    {
                                        "id": 687334,
                                        "data": {
                                            "text": " 1. 10秒内无指定操作，系统自动跳转到引导2 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 772016,
                                "data": {
                                    "text": " 用户点击跳过，是否直接进入下一步引导 "
                                },
                                "children": [
                                    {
                                        "id": 299196,
                                        "data": {
                                            "text": " 1. 用户点击跳过，引导1结束，直接进入引导2 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 864590,
                                "data": {
                                    "text": " 引导过程中杀进程，下次打开是否不再出现引导 "
                                },
                                "children": [
                                    {
                                        "id": 972870,
                                        "data": {
                                            "text": " 1. 引导未完整结束，杀进程后下次不再触发引导 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 457856,
                        "data": {
                            "text": " 引导2：长按 "
                        },
                        "children": [
                            {
                                "id": 247960,
                                "data": {
                                    "text": " 用户长按小人，出现工具栏，是否正确进入下一步 "
                                },
                                "children": [
                                    {
                                        "id": 767059,
                                        "data": {
                                            "text": " 1. 用户长按小人，工具栏出现，引导2结束，展示完成提示2秒，进入引导3 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 345494,
                                "data": {
                                    "text": " 用户点击跳过，是否直接进入引导3 "
                                },
                                "children": [
                                    {
                                        "id": 479090,
                                        "data": {
                                            "text": " 1. 用户点击跳过，引导2结束，直接进入引导3 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 22644,
                                "data": {
                                    "text": " 用户在10秒内未进行任何操作，是否自动跳转下一步 "
                                },
                                "children": [
                                    {
                                        "id": 170079,
                                        "data": {
                                            "text": " 1. 10秒内无指定操作，系统自动跳转到引导3 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 850887,
                                "data": {
                                    "text": " 用户未长按，而是进行其他操作，手指消失，引导文案不消失 "
                                },
                                "children": [
                                    {
                                        "id": 688945,
                                        "data": {
                                            "text": " 1. 用户未执行指定操作，引导手指消失，引导文案不消失，等待用户操作 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 347603,
                                "data": {
                                    "text": " 引导过程中杀进程，下次打开是否不再出现引导 "
                                },
                                "children": [
                                    {
                                        "id": 404737,
                                        "data": {
                                            "text": " 1. 引导未完整结束，杀进程后下次不再触发引导 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 895364,
                        "data": {
                            "text": " 引导3：双击 "
                        },
                        "children": [
                            {
                                "id": 332616,
                                "data": {
                                    "text": " 用户双击小人，拉起键盘，是否正确结束引导 "
                                },
                                "children": [
                                    {
                                        "id": 775706,
                                        "data": {
                                            "text": " 1. 用户双击小人，拉起键盘，引导3结束，展示完成提示2秒，全部引导结束，蒙层消失 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 266675,
                                "data": {
                                    "text": " 用户点击跳过，是否直接结束引导 "
                                },
                                "children": [
                                    {
                                        "id": 376067,
                                        "data": {
                                            "text": " 1. 用户点击跳过，引导3结束，全部引导结束，蒙层消失 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 122579,
                                "data": {
                                    "text": " 用户在10秒内未进行任何操作，是否自动结束引导 "
                                },
                                "children": [
                                    {
                                        "id": 235832,
                                        "data": {
                                            "text": " 1. 10秒内无指定操作，系统自动结束引导，蒙层消失 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 509472,
                                "data": {
                                    "text": " 用户未双击，而是进行其他操作，手指消失，引导文案不消失 "
                                },
                                "children": [
                                    {
                                        "id": 268326,
                                        "data": {
                                            "text": " 1. 用户未执行指定操作，引导手指消失，引导文案不消失，等待用户操作 "
                                        },
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 481405,
                                "data": {
                                    "text": " 引导过程中杀进程，下次打开是否不再出现引导 "
                                },
                                "children": [
                                    {
                                        "id": 202644,
                                        "data": {
                                            "text": " 1. 引导未完整结束，杀进程后下次不再触发引导 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 587559,
                        "data": {
                            "text": " 引导过程中操作异常 "
                        },
                        "children": [
                            {
                                "id": 980210,
                                "data": {
                                    "text": " 用户在引导过程中戳到桌宠，手指消失，引导文案不消失 "
                                },
                                "children": [
                                    {
                                        "id": 744438,
                                        "data": {
                                            "text": " 1. 用户戳到桌宠，引导手指消失，引导文案不消失，等待用户执行指定操作 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 843531,
                        "data": {
                            "text": " 引导进行中弹窗处理（iOS） "
                        },
                        "children": [
                            {
                                "id": 524484,
                                "data": {
                                    "text": " iOS系统在引导进行中拦截全部弹窗，是否在引导结束后弹出 "
                                },
                                "children": [
                                    {
                                        "id": 937330,
                                        "data": {
                                            "text": " 1. iOS系统在引导进行中拦截所有弹窗，引导结束后弹出被拦截的弹窗 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 321471,
                        "data": {
                            "text": " 引导进行中弹窗处理（安卓） "
                        },
                        "children": [
                            {
                                "id": 25075,
                                "data": {
                                    "text": " 安卓系统在引导进行中是否拦截发放玩伴/房间下发弹窗 "
                                },
                                "children": [
                                    {
                                        "id": 192589,
                                        "data": {
                                            "text": " 1. 安卓系统在引导进行中不显示发放玩伴/房间下发弹窗 "
                                        },
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
}`
)

func TestCreateTestCase(t *testing.T) {
	type args struct {
		baseUrl string
		in      CreateBlackBoxTestCaseReq
	}

	input := &CreateBlackBoxTestCaseReq{}

	err := jsonx.UnmarshalFromString(testCreateTestCaseRequest, input)
	if err != nil {
		t.Fatal(err)
	}

	input.CaseID = "case_1000x" // 每次更新

	tests := []struct {
		name string
		args args
		//want    ResponseData
		wantErr bool
	}{
		{"test1",
			args{"http://probe-test.ttyuyin.com:8000", *input}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CreateTestCase(tt.args.baseUrl, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateTestCase() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			jsonx.MarshalIgnoreError(got.Data)

			t.Log("res:", string(jsonx.MarshalIgnoreError(got.Data)))
		})
	}
}

func TestGetDocumentSize(t *testing.T) {
	type args struct {
		baseUrl string
		in      GetDocumentSizeReq
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				baseUrl: "http://probe-test.ttyuyin.com:8000",
				in: GetDocumentSizeReq{
					CaseRefId: "ai:case_ref_id:mRphUFwsT05D6dQ0u98V1",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetDocumentSize(tt.args.baseUrl, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDocumentSize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Printf("got:%+v", got.Data)
		})
	}
}

func TestGetFuncCoverage(t *testing.T) {
	type args struct {
		baseUrl string
		in      GetFuncCoverageReq
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				baseUrl: "http://probe-test.ttyuyin.com:8000",
				in: GetFuncCoverageReq{
					CaseRefId: "ai:case_ref_id:AVx0i1pEsMwXOSwRqOqwB",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetFuncCoverage(tt.args.baseUrl, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFuncCoverage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			fmt.Printf("got:%+v", got.Data)
		})
	}
}

func TestGetUpdatedCaseAddUpdateCount(t *testing.T) {
	type args struct {
		baseUrl string
		in      GetUpdatedCaseAddUpdateCountReq
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				baseUrl: "http://probe-test.ttyuyin.com:8000",
				in: GetUpdatedCaseAddUpdateCountReq{
					CaseRefId: "ai:case_ref_id:nPKmKlEeP9Q0XsunEVilk",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUpdatedCaseAddUpdateCount(tt.args.baseUrl, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpdatedCaseAddUpdateCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Printf("got:%+v", got.Data)
		})
	}
}
