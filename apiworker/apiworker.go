package main

import (
	"github.com/spf13/cobra"
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/cmd"
	mqc "gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/server"
)

var configFile = new(string)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		sg := service.NewServiceGroup()
		defer sg.Stop()

		ss, err := server.NewCombineServer(
			[][]server.Option{
				mqc.Options(*configFile),
			},
		)
		if err != nil {
			return err
		}

		for _, s := range ss {
			sg.Add(s)
		}

		sg.Start()

		return nil
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.StringVarP(configFile, "mqc-config", "f", "mqc/etc/apiworker.yaml", "the config file of mqc service")

	cobra.CheckErr(root.Execute())
}
