package tt

import "strconv"

// Generated by gen_error tool (go plugin).
// If you make any local change, they will be lost.
// source: errors.def

// status codes
const (
	// 切记!切记!切记: 不要轻易使用1000 以内的错误码

	// common 使用的 appsvr/common/core/iErrno.h

	Success        = 0 // 成功
	ErrKeyNotFound = 1

	// Common Error
	ErrSys               = -2 // 系统错误
	ErrDatabaseOperation = -3
	ErrSystemBusy        = -4  // 系统繁忙
	ErrRequestTimeout    = -5  // 处理超时
	ErrBadRequest        = -6  // 无效的请求
	ErrTrafficAdmin      = -8  // 流量管制
	ErrTrafficFrequently = -14 // 请求太频繁
	ErrPbParseError      = -21

	// 客户端错误码 从－100000开始 begin ---------------
	ErrTimeout                                     = -100001
	ErrPbParse                                     = -100003
	ErrParam                                       = -100004 // 参数错误
	ErrNetwork                                     = -100005
	ErrDbError                                     = -100006
	ErrNetservice                                  = -100007
	ErrNotLogin                                    = -100008
	ErrFileNotFound                                = -100009
	ErrStillWaitingResponse                        = -100010
	ErrImageDecodeError                            = -100011
	ErrImageDownloadError                          = -100012
	ErrDummyErrorCode                              = -100020 // 该错误不应该显示给用户
	ErrNoMoreDataErrorCode                         = -100021
	ErrPureErrorCode                               = -100022 // 该错误码不应该显示给用户
	ErrFaceBigDownloading                          = -100100
	ErrImUploadAttachmentFailed                    = -100201
	ErrImDownloadAttachmentFailed                  = -100202
	ErrGameNoLocalPackage                          = -100600
	ErrGameDownloadHttpError                       = -100601
	ErrGameDownloadApkError                        = -100602
	ErrGameCircleSendTopicCompressImgFailed        = -100700
	ErrGameCircleSendTopicUploadImgFailed          = -100701
	ErrDeviceNotSupportTeamVoice                   = -100801
	ErrNotInChannel                                = -100901
	ErrPluginLoad                                  = -101001
	ErrPluginNotExists                             = -101002
	ErrPluginFileNotExists                         = -101003
	ErrPluginDisable                               = -101004
	ErrPluginInterfaceNotSupport                   = -101005
	ErrChannelNeedsQuitCurrentChannel              = -101101
	ErrChannelNeedsRegister                        = -101102
	ErrShareFailedWechatNotInstalled               = -101201
	ErrShareFailedIllegalParameter                 = -101202
	ErrShareFailedNotSupportShareType              = -101203
	ErrSendPresentSelectNothingToSend              = -101301
	ErrSendPresentFailToGetTargetUserInfo          = -101302
	ErrSendPresentFailToGetCurrentChannelInfo      = -101303
	ErrCaptchaErrParameter                         = -101401
	ErrCaptchaNotReady                             = -101402
	ErrCaptchaValidateErr                          = -101403
	ErrCaptchaOtherErr                             = -101404
	ErrNormalVerifyTooOftenErr                     = -101501 // 获取短信验证码太频繁
	ErrChannelInGameNotSupportChangeMic            = -101601 // 房间在游戏中，不支持上下麦
	ErrOperateTooOften                             = -101701 // 操作太频繁，频率限制
	ErrTgameNoButtonAction                         = -101801 // 不支持游戏中该按钮的点击回调
	ErrTgameForbidImChatWithMicMate                = -101802 // 该游戏不支持跟麦上成员进行im聊天
	ErrTgameForbidSendPresentWhenGameStart         = -101803 // 游戏已开始，为了保证游戏公平性，暂时不能给Ta送礼哦
	ErrTgameForbidSendMagicExpressionWhenGameStart = -101804 // 游戏已开始，为了保证游戏公平性，暂时不能发送魔法表情哦
	ErrPlayTogetherAnotherStillExist               = -101901 // 与该用户存在另外一个 一起玩的邀请未失效
	ErrPlayTogetherQuitChannelError                = -101902 // 退出当前房间失败
	ErrPlayTogetherQuitChannelCancel               = -101903 // 拒绝退出当前房间
	ErrFrescoRequestFail                           = -102001 // //FRESCO下载图片失败
	ErrFrescoRequestCancel                         = -102002 // //FRESCO下载图片取
	ErrDownloadResourceFail                        = -102003 // //下载资源失败

	// 客户端错误码 end --------------
	ErrBaseAccount = -100
	ErrBaseSendMsg = -200
	ErrBaseSyncMsg = -300
	ErrBaseFriend  = -400
	ErrSystemError = -10000

	// ACCOUNT -100
	ErrAccountPhoneExist                        = -101 // 该手机号码已被注册
	ErrAccountUsernameExist                     = -102 // 该用户名已存在
	ErrAccountNotExist                          = -103 // 账号不存在
	ErrAccountPasswordWrong                     = -104 // 密码或用户名错误
	ErrAccountAliasModified                     = -105 // 账号已经修改过了
	ErrAccountCompleted                         = -106 // 用户资料已经完善过了
	ErrAccountNoHeadImage                       = -107 // 用户没有上传头像
	ErrAccountVerifyCodeWrong                   = -108 // 验证码输入有误，请重新输入
	ErrAccountPasswordSame                      = -109 // 新密码不能与旧密码相同
	ErrAccountPhoneFormatWrong                  = -110 // 手机号码格式不对
	ErrAccountUsernameFormatWrong               = -111 // 用户名不能包含特殊字符
	ErrAccountPermissionDenied                  = -112 // 权限不足
	ErrAccountHasNoGuild                        = -113 // 用户未加入公会
	ErrAccountVerifyTooFreq                     = -114 // 验证短信一段时间内只能下发一条， 请稍后再试
	ErrAccountExist                             = -115 // 账号已存在
	ErrAccountVerifyInvalidUsage                = -116
	ErrAccountMibaoNotSet                       = -117 // 用户未设置密保
	ErrAccountMibaoAnswerErr                    = -118 // 密保答案错误
	ErrAccountMibaoQuestionErr                  = -119 // 密保问题错误
	ErrAccountNicknameSensitive                 = -120 // 你输入的昵称包含敏感词，修改失败
	ErrAccountSignatureSensitive                = -121 // 你输入的签名包含敏感词，修改失败
	ErrAccountMibaoAlreadyInit                  = -122 // 用户已经设置过密保
	ErrAccountResetClose                        = -123 // 注册失败，同一个手机号只能注册一个帐号
	ErrAccountBanned                            = -124 // 该帐号由于安全问题已被暂时冻结，请联系客服
	ErrAccountRegFrequenceLimit                 = -125 // 同设备不能注册多个
	ErrAccountOpenidExist                       = -126 // Openid已存在
	ErrAccountPasswordNotEnough                 = -127
	ErrAccountInvalidQueryType                  = -128
	ErrAccountNoLoginHistory                    = -129
	ErrAccountOperationNotAllowed               = -130
	ErrAccountUserPhoneAlreadyBinded            = -131 // 该手机号已经绑定到你的账号，无需再次绑定
	ErrAccountUserBindedAnotherPhone            = -132 // 你当前已经绑定到了另一个手机号，无法绑定
	ErrAccountPhoneBindedToAnother              = -133 // 该手机号已被绑定过，请更新到最新版本中绑定，在【我】-【关于TT语音】-【检查更新】中更新版本
	ErrAccountBindedPhoheNotMatch               = -134
	ErrAccountRequireNewPassword                = -135 // 需要设置一个密码
	ErrAccountSetTaillightMedalidInvalid        = -136 // 你没有该勋章或已过期 不能将其设置为昵称后显示
	ErrAccountMedalNotallowSettaillight         = -137 // 昵称后不能显示该类型的勋章
	ErrAccountWatcherPermissionLimit            = -138 // 权限不足
	ErrAccountToLoginCaptcha                    = -139 // 需要登录验证
	ErrAccountVerifyLoginCaptcha                = -140 // 登录验证码错误
	ErrAccountNoNeedCaptcha                     = -141 // 不需要图片验证码
	ErrAccountNoWechatUnionId                   = -142
	ErrAccountUnionIdExist                      = -143
	ErrAccountBanRegFrequenceLimit              = -144 // 该帐号由于安全问题已被限制注册，请联系客服
	ErrAccountBanAuthFrequenceLimit             = -145 // 该帐号由于安全问题已被限制登录，请联系客服
	ErrAccountInvalid                           = -146 // 账号异常
	ErrAccountRegIpFrequenceLimit               = -147 // 注册帐号IP频率限制
	ErrAccountSearchNewRegContactFrequenceLimit = -148 // 由于安全问题已被限制操作，请联系客服
	ErrAccountSmsThreshholdExceed               = -149 // 发送短信过多，请明天再试
	ErrAccountNoBindedPhoneToUser               = -150 // 该账号未绑定任何手机号
	ErrAccountRebindVerifiedPhoneInvalid        = -151 // 已绑定的手机验证失败
	ErrAccountThirdpartyDetachDisabled          = -152 // 该功能已经下线
	ErrAccountPhoneFormatBanned                 = -153 // 手机号码格式不对
	ErrAccountRegLimitedByClientVersion         = -154 // 请升级客户端至最新版
	ErrAccountThirdpartyIdNotDetachable         = -155 // 该账号无法进行解绑
	ErrAccountDeviceFormatBanned                = -156 // 设备异常
	ErrAccountLoginNeedBindPhone                = -157 // 该账号需要绑定手机号才能登陆，请升级客户端版本至最新
	ErrAccountModifyPwdNeedVerifycode           = -158 // 修改密码需要进行手机验证，请升级客户端版本至最新
	ErrAccountDeviceUnusual                     = -159 // 您的版本过旧，为确保账号安全，需更新为最新版才可继续使用
	ErrAccountDiffAccTryPwdTooMuch              = -160 // 由于该设备登陆密码错误次数过多，该设备5小时内不能登陆
	ErrAccountSameAccTryPwdTooMuch              = -161 // 由于之前一段时间该账户密码错误次数超过5次，该账号5小时内不能登陆
	ErrAccountPwdNullErr                        = -162 // 未设置密码
	ErrAccountBannedForever                     = -163 // 该帐号已被冻结
	ErrAccountLoginNxLimit                      = -164 // 请勿尝试同时登录多设备
	ErrAccountRegNeedBindPhone                  = -165 // 需要绑定手机号才能注册，请升级客户端版本至最新
	ErrAccountLoginNeedBindRealnameauthPhone    = -166 // 根据国家相关政策要求,登录需要填写手机号，请升级客户端版本至最新
	ErrAccountUnregister                        = -167 // 用户已注销
	ErrAccountLoginBindRealnameauthPhoneLimit   = -168 // 此手机号已认证十个账号，请使用其它手机号进行认证
	ErrAccountBindLoginPhoneIsExpected          = -169 // 此手机号未绑定至任何账户，请重试并绑定为登录手机
	ErrAccountBindSecurePhoneIsExpected         = -170 // 此手机号已经绑定至其他账户，请重试并绑定为安全手机
	ErrAccountBindSecurePhoneLimit              = -171 // 此手机号已被10个账号绑定，请使用其它手机号操作
	ErrAccountRegBannedByPolicy                 = -172 // 您的手机号码或IP所属区域暂不支持注册
	ErrAccountBannedWithInfo                    = -173 // 该帐号由于安全问题已被暂时冻结，请联系客服

	// IM_MSG -200
	ErrSendMsgTargetNotExist                 = -201
	ErrSendMsgTargetNotFriend                = -202
	ErrSendMsgTimelineSvrFailed              = -203
	ErrSendMsgUploadAttachmentSvrFailed      = -204
	ErrSendMsgDownlAttachmentSvrFailed       = -205
	ErrSendMsgDownlAttachmentExceed          = -206 // 附件已失效
	ErrSendMsgAttachmentInvalid              = -207
	ErrSendMsgAttachmentInvalidUploadAlready = -208
	ErrSendMsgAttachmentGenSmallImgFailed    = -209
	ErrSendMsgNotGroupMember                 = -210 // 你不在该群，发送失败
	ErrSendMsgUpdateGroupMsgFailed           = -211
	ErrSendMsgParasErr                       = -212 // 发消息参数不对
	ErrSendMsgMute                           = -213 // 你被禁言了，发送失败
	ErrSendMsgAllMute                        = -214 // 全员禁言中，只有管理员可以发言
	ErrSendMsgBuildSystemMsgErr              = -215
	ErrSendMsgGuildLimit                     = -216 // 客官你今天公会内的自由聊天人数已经用完，想要继续搭讪请先添加玩伴吧~
	ErrSendMsgQuitGuildFar                   = -217 // 对不起，已超出挽回聊天有效时间，发起会话失败。
	ErrSendMsgUpdatePulibMsgFailed           = -218
	ErrSendMsgGenerateAttachmentKeyFailed    = -219
	ErrSendMsgAtEveryoneNotAllow             = -220 // 该用户没有发送@全体消息的权限
	ErrSendMsgAtEveryoneTimesToomuch         = -221 // 该用户发送@全体消息的次数超过限制
	ErrSendMsgStrangerTimesToomuch           = -222 // 对方未回复前只能发送5条消息哦~
	ErrSendMsgStrangerTargetsToomuch         = -223 // 你今天发起的聊天有点多哦~先和刚认识的小伙伴聊聊吧
	ErrSendMsgStrangerUnusualAccount         = -224 // 你的账号异常，无法和陌生人打招呼呢~
	ErrSendMsgStrangerIpTargetsToomuch       = -225 // 你的IP已达到陌生人打招呼次数上限
	ErrSendMsgCantTalkWithGmHelper           = -226 // 你不是会长，不能和会长小秘聊啦~
	ErrSendMsgIsBlockByTarget                = -227 // 发送失败，对方已拒收
	ErrSendMsgBlockTarget                    = -228 // 你已拉黑对方，对方无法接收你的信息
	ErrSendMsgSensitive                      = -229 // 发送失败，不符合平台管理规则
	ErrUserAntispamAccountBannedPost         = -230 // 您的账号目前处于禁言期，请稍后再尝试
	ErrSendMsgStrangerTimesToomuchTimeLimit  = -231 // 发送失败，你最近发起聊天过于频繁，请稍后再试
	ErrSendMsgStrangerTargetRevToomuch       = -232 // 发送失败~今天有很多人找Ta聊天啦~
	ErrSendMsgStrangerReportedToomany        = -233 // 由于你被频繁举报，账号存在异常，60分钟内不能发起私聊，如有疑问，请联系客服
	ErrSendMsgTooManyUsersChat               = -234 // 发送失败，今天有很多用户找ta聊天啦

	// 师徒关系相关
	ErrSendMsgMasterapprenticeYoualreadybound     = -235 // 你已经和对方绑定啦～
	ErrSendMsgMasterapprenticeUpperlimit          = -236 // 发送失败，你绑定的徒弟数已达到上限啦
	ErrSendMsgMasterapprenticeAccountabnormal     = -237 // 对方账号异常，不能绑定为徒弟哟
	ErrSendMsgMasterapprenticeAccountnotsatisfied = -238 // 对方不满足收徒条件，不能绑定为徒弟，您可到<徒弟大厅>中收徒
	ErrSendMsgMasterapprenticeTargetalreadybound  = -239 // 绑定失败，对方已被绑定其他师父啦，您可到<徒弟大厅>中收徒
	ErrSendMsgMasterapprenticeIndev               = -240 // 此功能维护中，敬请期待
	ErrSendMsgMasterapprenticeAlreadyfollow       = -241 // 你已经关注Ta啦
	ErrSendMsgMasterapprenticeEntranceclosed      = -242 // 入口已关闭
	ErrSendMsgMasterapprenticeAlreadyinvited      = -243 // 你刚刚已邀请过Ta啦
	ErrSendMsgMasterapprenticeAlreadyapprentice   = -244 // 已成功收Ta为徒啦，快去完成每日互动任务吧
	ErrSendMsgMasterapprenticeOver                = -245 // 五日师徒任务已完成，Ta已经出师啦，不能重复收徒哦
	ErrSendMsgMasterapprenticeTerminal            = -246 // 你们没有连续完成每日互动任务，师徒关系已解绑，不能再次收Ta为徒哦
	ErrSendMsgMasterapprenticeNotnewdevice        = -247 // 对方不是新设备，不能绑定为徒弟哦

	// SYNC_MSG -300
	ErrSyncMsgTypeErr       = -301
	ErrSyncMsgEmptyMessages = -302

	// FRIEND -400
	ErrFriendAlreadyFriend        = -401
	ErrFriendTargetNotFriend      = -402 // 对方还不是你的好友
	ErrFriendVerifyFriendFailed   = -403
	ErrFriendVerifyMsgExceed      = -404
	ErrFriendDelNotSupported      = -405
	ErrFriendCannotAddYoursefl    = -406
	ErrFriendUpdateRemarkFailed   = -407
	ErrFriendLimit                = -408 // 客官今天发出了很多添加玩伴的请求了，添加玩伴的体力用完了哦~
	ErrFriendKefuLimit            = -409 // 客服帐号不能加好友
	ErrFriendGonghuiKefuLimit     = -410 // 公会客服帐号不能加好友
	ErrFriendBaned                = -411 // 由于对方的设置，你不能添加对方为玩伴
	ErrFriendTotalSizeLimit       = -412 // 你的好友数量已达到500人上限
	ErrFriendTargetTotalSizeLimit = -413 // 对方的好友数量已达到500人上限
	ErrFriendIpLimit              = -414 // 客官你的IP今天发出了很多添加玩伴的请求了，添加玩伴的体力用完了哦~

	// GUILD -500
	ErrGuildNameExist                   = -500 // 会长大人，该名字已经被抢注，赶紧换个更霸气的名字吧。
	ErrGuildNotExist                    = -501 // 公会不存在
	ErrGuildShortIdSet                  = -502 // 该公会已经设置过短号，不能重新设置
	ErrGuildShortIdExist                = -503 // 该短号已被使用
	ErrGuildUserHave                    = -504 // 用户已加入公会了
	ErrGuildNoPermission                = -505 // 无权限操作
	ErrGuildApplyExist                  = -506 // 用户已发起加入公会的申请
	ErrGuildApplyHaveReviewed           = -507 // 已被其他管理员审核通过
	ErrGuildApplyHaveConfirmed          = -508 // 此申请已经确认通过了
	ErrGuildMemberNotExist              = -509 // 非公会成员
	ErrGuildApplyNotExist               = -510 // 用户未发起对加入公会的申请
	ErrGroupMemberNotExist              = -511 // 不是群成员
	ErrGroupMemberExist                 = -512 // 已经是群成员
	ErrGroupNotExist                    = -513 // 群不存在
	ErrGroupOwnerCannotBeRemoved        = -514 // 群主不能退群
	ErrGuildOwnerCannotBeRemoved        = -515 // 会长不能退出公会
	ErrGroupCannotJoin                  = -516 // 此群禁止任何人加入
	ErrGroupApplyExist                  = -517 // 用户已发起加入群的申请
	ErrGroupApplyNotExist               = -518 // 用户未发起对加入群的申请
	ErrGroupApplyHaveReviewed           = -519 // 已被其他管理员审核通过
	ErrGroupUserHaveBeenOwner           = -520 // 用户已经是群主
	ErrGuildAddGameExist                = -521 // 该游戏已经是群游戏了
	ErrGroupDeleteMemberSelf            = -522 // 踢出群用户列表中包含自己
	ErrGuildDeleteMemberSelf            = -523 // 踢出公会用户列表中包含自己
	ErrGroupMuteMemberSelf              = -524 // 禁言用户列表不能包含自己
	ErrGroupUnmuteMemberSelf            = -525 // 恢复发言用户列表不能包含自己
	ErrGroupCannotExitGuildMainGroup    = -526 // 不能退出公会总群
	ErrGroupParamErr                    = -527 // 参数错误
	ErrGuildGameNotExist                = -528 // 非公会主玩游戏
	ErrGuildNoticeDuplicateDelete       = -529
	ErrGroupUserNotOwner                = -530 // 此用户不是群主
	ErrGuildCantDismissMemberToMuch     = -531 // 解散公会，必须公会成员总数少于10人
	ErrGuildAlreadyJoin                 = -532 // 用户已经加入公会
	ErrGuildSetGameUrlInvalid           = -533 // 设置游戏链接，URL不正确
	ErrGuildIdNotMatch                  = -534 // 公会id不正确
	ErrGuildSetGameUrlCantReach         = -535 // 已设置成功，但您设置的链接可能无法访问，请确认哦。
	ErrGuildSetGameUrlNotApk            = -536 // 已设置成功，但该链接可能不是安卓游戏下载链接，请确认哦。
	ErrGuildApplyUserJoinOtherGuild     = -537 // 申请已失效，或用户已加入其它公会
	ErrGuildApplyPartedSendFailed       = -538
	ErrGuildMainGroupNoOwner            = -539 // 总群不能设置群主
	ErrGuildPunishPinNoPermission       = -540 // 无权限发布图钉广播
	ErrGuildNameSensitive               = -541 // 你输入的公会名包含敏感词，修改失败
	ErrGuildGroupNameSensitive          = -542 // 你输入的群名称包含敏感词，修改失败
	ErrGuildDescriptionSensitive        = -543 // 你输入的群简介包含敏感词，修改失败
	ErrGuildMemberCardSensitive         = -544 // 你输入的群名片包含敏感词，修改失败
	ErrGuildNameContainsGameName        = -545 // 你输入的公会名称中包含游戏名，无法创建
	ErrGuildNameAllDigit                = -546 // 无法使用纯数字作为公会名
	ErrGuildHadOnceJoinGuild            = -547 // 快速加入失败
	ErrGuildAddGameExceedLimit          = -548 // 公会游戏已达上限
	ErrGuildNameNotMatch                = -549 // 公会名称不对
	ErrGuildNameMapErr                  = -550 // 公会名称映射错误
	ErrGroupMemberCountLimit            = -551 // 临时群已满员
	ErrGuildNameInvalid                 = -552 // 公会名称含有非法字符
	ErrGuildUserHavebeenHighadminlevel  = -553 // 目标用户已经被设置为更高权限
	ErrGroupMemberCardOffline           = -554 // 修改群名片失败
	ErrGroupAddmemberLimit              = -555 // 每次拉人加群不能超过50人上限
	ErrGroupApplyExceed                 = -556 // 该入群申请已过期
	ErrGuildCreateFrequenceLimit        = -557 // 当天不能创建多个公会
	ErrGuildNotCheckin                  = -558 // 今天未签到
	ErrGuildCheckinSupplement           = -559 // 没有补签天数
	ErrGroupDismissed                   = -560 // 该群已被解散
	ErrGuildRefuseQuitJointimeShort     = -561 // 加入公会满30分钟后，才可以退出公会哦~~ 在公会里多玩一会儿吧，小伙伴们舍不得你走呢
	ErrGuildAlreadyDonate               = -562 // 今天已捐献
	ErrGuildExtraGameExceedLimit        = -563 // 额外扩充游戏数量已达上限，公会等级提升后可扩充更多数量
	ErrGuildContributionNotEnough       = -564 // 公会可用贡献值不足
	ErrGuildUserIsOfficial              = -565 // 用户已经是官员
	ErrGuildUserNotOfficial             = -566 // 用户不是官员
	ErrGuildOfficialNotExist            = -567 // 职位不存在
	ErrGuildMemberListRankTypeErr       = -568
	ErrGuildStoragePNotShelve           = -569 // 商品还没上架
	ErrGuildStoragePSoldout             = -570 // 商品已经卖完
	ErrGuildStoragePHadModify           = -571 // 商品价格已发生变化，请刷新再试
	ErrGuildStoragePTotalError          = -572 // 商品的总数与礼物列表不匹配
	ErrGuildStorageHasNotExamineRecord  = -573 // 找不到审查记录
	ErrGuildStorageAddPError            = -574 // 添加商品失败
	ErrGuildDeductMemContributionFail   = -575 // 扣除个人贡献失败
	ErrGuildReturnMemContributionFail   = -576 // 返回个人贡献失败
	ErrGuildStorageAddGiftBoxFail       = -577 // 放入个人宝箱失败
	ErrGuildStoragePNotEnough           = -578 // 商品数量不足，快去【礼包中心】补货吧~
	ErrGuildStoragePUserCanNotGain      = -579 // 你还没有领取资格
	ErrGuildAlreadyCheckIn              = -580 // 今天已签到
	ErrGuildStoragePAllotInvalid        = -581 // 分配操作无效
	ErrGuildStoragePPassDu              = -582 // 领取失败，礼包领取尚未开始或者已经过期
	ErrGuildMemberCardGuildLimit        = -583 // 该成员已退出公会
	ErrGuildPrefixIsTooLong             = -584 // 马甲长度超过限制
	ErrGuildMemberOptCdUid              = -585 // 用户已达到当天操作次数上限
	ErrGuildMemberOptCdIp               = -586 // IP已达到本时段内操作次数上限
	ErrGuildMemberOptCdDevice           = -587 // 设备已达到当天操作次数上限
	ErrGuildOfficialExist               = -588 // 职位已经存在
	ErrGuildSpiltGiftCardValueOverLimit = -589 // 切分的礼品卡单张面额超过了限制
	ErrGuildStorageVoucherPermission    = -590 // 此商品只有会长才能操作
	ErrGuildStoragePNotExist            = -591 // 商品不存在
	ErrGuildStoragePDeleteConditionFail = -592 // 只能删除已过期的商品
	ErrGuildGiftCardBalanceNotEnough    = -593 // 礼品卡余额不足
	ErrGuildGiftCardCantNotUnshelve     = -594 // 本版本不支持代金券下架操作，请先检查升级
	ErrGuildGiftCardFoundNot            = -595 // 找不到礼品卡
	ErrGuildGiftCardStatus              = -596 // 礼品卡状态异常
	ErrGuildGiftCardRemoveFail          = -597 // 只能删除过期和状态异常的礼品卡
	ErrGuildGiftCardRepeat              = -598 // 该代金券每个用户只能领取一次
	ErrGuildManifestoSensitive          = -599

	//  Game [-600, -649]
	ErrGameNotExist              = -600 // 游戏不存在
	ErrGameFuzzyMatchNotReady    = -601
	ErrGameServerNotExist        = -602
	ErrGameTopgameNotExist       = -603
	ErrGameTopgameExist          = -604
	ErrGameZoneNotExist          = -605
	ErrGameBindedOfficialTgroup  = -606
	ErrGameZoneSetNotEnoughParam = -607
	ErrGameTagNumLimit           = -608

	//  Guild [-650, -699]
	ErrGuildOfficialMemberReachLimit = -650 // 您只能设置50个公会管理
	ErrGuildMemberReachLimit         = -651 // 公会成员已经达上限，无法加入
	ErrGuildModifyPrefixLimit        = -652 // 30分钟内只能修改一次成员马甲，请稍候再试。
	ErrGuildSensitive                = -653 // 输入的内容包含敏感词，修改失败

	// ACCOUNT -700
	ErrAuthAutoLoginAlreadyOnline = -700 // 该帐号已在其它设备登录，请手动登录
	ErrUploadFaceAccountTypeErr   = -701 // 上传头像，帐号类型错误
	ErrAuthAutoLoginFailed        = -702 // 自动登录失败，请手动登录
	ErrThirdPartyAccessTokenErr   = -703 // 第三方账号token验证失败
	ErrImageInvalidErr            = -704 // 图片涉嫌违规
	ErrChinaMobileAccessTokenErr  = -705 // 中国移动一键登录token验证失败
	ErrChinaMobileNotfindPhone    = -706 // 中国移动一键登录没有匹配到手机号
	ErrChinaUnicomAccessTokenErr  = -707 // 中国联通一键登录token验证失败
	ErrChinaUnicomNotfindPhone    = -708 // 中国联通一键登录没有匹配到手机号
	ErrChuangLanAccessTokenErr    = -709 // 创蓝token验证失败
	ErrChuangLanNotfindPhone      = -710 // 创蓝没有匹配到手机号

	// album -800
	ErrAlbumNotExist               = -800 // 相册不存在
	ErrPhotoNotExist               = -801 // 相片不存在
	ErrAlbumDefaultAlbumCantDelete = -802 // 默认相册不允许删除
	ErrNoPermissionDeleteAlbum     = -803 // 你没有权限删除该相册
	ErrNoPermissionDeletePhoto     = -804 // 你没有权限删除该照片
	ErrAlbumNoPermission           = -805 // 没权限修改相册

	// giftpkg -900
	ErrGiftpkgGuildPkgNotEnough      = -900 // 公会礼包不足
	ErrGiftpkgPkgNotFound            = -901 // 找不到这种礼包
	ErrGiftpkgAlreadyApplying        = -902 // 这种礼包公会已经在申请中
	ErrGiftpkgApplyidDoneOrNotExist  = -903 // 该申请已经处理过，或者不存在
	ErrGiftpkgApplyidNotExist        = -904 // 该申请不存在
	ErrGiftpkgApplyidAlreadyDone     = -905 // 该申请已处理过
	ErrGiftpkgPkgNotEnough           = -906 // 后台礼包不足
	ErrGiftpkgApplyNoPermission      = -907 // 无权限申请礼包
	ErrGiftpkgUserHadFetedThisPkg    = -908 // 已经领取过这种礼包
	ErrGiftpkgTaohaoNoUsableId       = -909 // 淘号，已经没有可用的序列号了
	ErrGiftpkgRedPkgEmpty            = -910 // 红包已经被领完了
	ErrGiftpkgStorageSerialNotEnough = -911 // 仓库序列号不足
	ErrGiftpkgApplyHasNoThisGame     = -912 // 公会没有这款游戏，不能申请该礼包
	ErrGiftpkgNotFoundRedPkg         = -913 // 找不到该红包
	ErrGiftpkgRedPkgTargetErr        = -914 // 红包只能发到公会群或者公会好友
	ErrGiftpkgRedPkgGuildErr         = -915 // 你不是该公会的成员，不能领取该公会的红包
	ErrGiftpkgAlreadyFetchRedpkg     = -916 // 已经领取过这个红包
	ErrGiftpkgNotGuildOwner          = -917 // 用户不是会长
	ErrGiftpkgPkgNotUsed             = -918 // 该礼包已经下架
	ErrGiftpkgApplyMemberNotEnough   = -919 // 该款礼包需要公会人数达到5人以上才能申请，你的公会人数不够，赶紧邀请更多的小伙伴加入你的公会吧!
	ErrGiftpkgUserHadFetchedToday    = -920 // 你今天领礼包次数已达到上限，请明天签到后再来吧
	ErrGiftpkgUserNotCheckinToday    = -921 // 公会签到后才可以领礼包哦
	ErrGiftpkgDeviceHadFetchedToday  = -922 // 你今天领礼包次数已达到上限，请明天签到后再来吧
	ErrGiftpkgApplyCdNotReady        = -923 // 申请过于频繁，请稍后再试
	ErrGiftpkgNotMatchPkgSource      = -924
	ErrGiftpkgNotBelongToTheGuild    = -925
	ErrGiftpkgUnableToDelete         = -926
	ErrGiftpkgAlreadyDeleted         = -927
	ErrGiftpkgExceedFetchFailed      = -928 // 领取失败，该礼包已过期
	ErrGiftpkgHadFetedThisSerial     = -929
	ErrGiftpkgOrderidNotFound        = -930
	ErrGiftpkgOrderidDuplicate       = -931
	ErrGiftpkgPriceUpdated           = -932 // 礼包价格已更新
	ErrGiftpkgPriceRangeErr          = -933
	ErrGiftpkgDeviceHadFetched       = -934
	ErrGiftpkgCollectionNotExist     = -935

	// verifycode
	ErrVerifycodeForSensitiveOp   = -936 // 敏感操作需要输入短信验证码，请更新至最新版本进行操作
	ErrNoPhoneBind                = -937 // 该操作需要绑定手机
	ErrVerifycodeSessionChanged   = -938 // 登录信息变化,请重新获取验证码
	ErrVerifycodeNoCode           = -939 // 验证码未生成,请重新获取验证码
	ErrVerifycodeWrongCode        = -940 // 验证码不正确,请重新输入
	ErrGiftpkgDevHadFetedThisPkg  = -941 // 设备已经领取过这种礼包
	ErrVerifycodeParamErr         = -942 // 验证码操作的请求参数有误
	ErrNeedPhoneBind              = -943 // 该操作需要绑定手机,请到【我】-【隐私与安全】中绑定手机或者升级客户端
	ErrVerifycodeWrongCodeToomuch = -944 // 验证码错误过多, 请重新获取验证码
	ErrVerifycodeExceedLimit      = -945 // 今日接收验证码次数已达上限

	//	circle
	ErrCircleNotFound                       = -1000 // 游戏圈不存在
	ErrCircleJoinAlreadyJoined              = -1001 // 已经加入了游戏圈
	ErrCircleTopicDeleted                   = -1002 // 该主题已被删除
	ErrCircleTopicNotExists                 = -1003 // 该主题不存在
	ErrCircleCommentDeleted                 = -1004 // 该评论已被删除
	ErrCircleCommentNotExists               = -1005 // 该评论不存在
	ErrCircleLikeAlreadyLiked               = -1006 // 你已经点过赞了
	ErrCircleLikeNotExists                  = -1007 // 还未赞过
	ErrCircleNotCreator                     = -1008 // 该操作只允许作者进行
	ErrCircleTopicTitleSensitive            = -1009 // 你输入的标题包含敏感词，发布失败
	ErrCircleTopicContentSensitive          = -1010 // 你输入的内容包含敏感词，发布失败
	ErrCircleCommentSensitive               = -1011 // 你输入的内容包含敏感词，发布失败
	ErrCircleNoPermissionMute               = -1012 // 无权限禁言
	ErrCirclePostTopicCoolingdown           = -1013 // 你的操作过于频繁, 请稍后再试
	ErrCirclePostCommentCoolingdown         = -1014 // 你的操作过于频繁, 请稍后再试
	ErrCirclePostSimilarTopic               = -1015 // 你的操作过于频繁, 请稍后再试
	ErrCircleHaveNotJoinCircle              = -1016 // 未加入圈子
	ErrCircleCanNotQuitActivity             = -1017 // 无法退出活动圈
	ErrCircleNoPermissionHightlight         = -1018 // 无权限加精
	ErrCircleNoPermissionCancleHightlight   = -1019 // 无权限删精
	ErrCircleNoPermissionDelTopicComment    = -1020 // 无权限删评论
	ErrCircleUserMuteComment                = -1021 // 你已被禁言，暂时无法发送评论
	ErrCircleMarkReadedFailed               = -1022
	ErrCircleTopicContentEmpty              = -1023
	ErrCircleCommentContentEmpty            = -1024
	ErrCircleUserMuteTopic                  = -1025 // 你已被禁言，暂时无法发送主题
	ErrCircleContentLenOverlimit            = -1026 // 你发的内容太多了，伦家受不了呐
	ErrCircleTitleLenOverlimit              = -1027 // 你标题都这么长，吓死我了，嘤嘤嘤~~~
	ErrCircleAnnouncementSendTopic          = -1028 // 公告圈禁止发主题
	ErrCircleAnnouncementQuit               = -1029 // 禁止退出公告圈
	ErrCircleTopicPictureSensitive          = -1030 // 主题图片包含敏感信息
	ErrCircleAnnouncementJoin               = -1031 // 禁止关注公告圈
	ErrCircleAnnouncementVersion            = -1032 // 当前版本不支持该操作，请更新版本
	ErrCircleTopicNotDeleted                = -1033 // 该主题未被删除
	ErrCircleIsNowReadonly                  = -1034 // T^T游戏圈帖子只支持查看，不支持发帖、点赞、评论等操作哟
	ErrRecruitNotExist                      = -1035 // 招募不存在或者已经结束
	ErrRecruitPostContentSensitive          = -1036 // 招募内容包含敏感词汇
	ErrRecruitPostReddiamonNotEnough        = -1037 // 红钻不够 不能招募
	ErrRecruitPostGuildAlreadyPost          = -1038 // 该公会在这个游戏发过招募还没有结束
	ErrRecruitGuildNotUsercurrent           = -1039 // 您已经不在创建该招募的公会了
	ErrRecruitSupportAlreadyDo              = -1040 // 每天只能为自己公会顶一次
	ErrRecruitOpNoPermission                = -1041 // 没有操作权限
	ErrRecruitPostGuildcontrbutionNotEnough = -1042 // 贡献值不够 不能招募

	// Grow
	ErrGrowMissionNotExists               = -1100
	ErrGrowMissionNotBelongToUser         = -1101
	ErrGrowUserMissionNotExists           = -1102
	ErrGrowUserMissionNotFinished         = -1103
	ErrGrowUserMissionCollected           = -1104 // 奖励已领取
	ErrGrowExpAdded                       = -1105
	ErrGrowCurrencyAdded                  = -1106 // 用户红钻已经添加，不能重复添加
	ErrGrowCurrencyNotEnough              = -1107 // 你的红钻数量不足，赶紧去做任务领红钻吧～
	ErrGrowExpZero                        = -1108
	ErrGrowCurrencyZero                   = -1109
	ErrGrowUserMissionExpired             = -1110
	ErrGrowLevelNotEnough                 = -1111
	ErrGrowMissionNotTimeLimit            = -1112
	ErrGrowMissionAccepted                = -1113 // 任务已接受
	ErrGrowMissionUnacceptable            = -1114 // 任务不可接受
	ErrGrowMissionNotAccepted             = -1115
	ErrGrowMissionNotEffect               = -1116
	ErrGrowMissionEventNoOneCare          = -1117
	ErrGrowMissionSameDayOper             = -1118
	ErrReddiamondRewardAboveLimit         = -1119
	ErrReddiamondNotEnoughInStock         = -1120 // 红钻池红钻不足
	ErrReddiamondFreezeDuplicateOrder     = -1121
	ErrReddiamondFreezeNoSuchOrder        = -1122
	ErrReddiamondFreezeConfirmNotAllowed  = -1123
	ErrReddiamondFreezeConfirmAlreadyDone = -1124
	ErrTimeLimitMissionConfigNotExist     = -1171 // 限时任务不存在
	ErrTimeLimitMissionEventNotExist      = -1172 // 限时任务类型不存在
	ErrTimeLimitMissionConfigParam        = -1173 // 限时任务配置参数异常
	ErrTimeLimitMissionAcceptLimit        = -1174 // 该任务的领取名额已满
	ErrTimeLimitUserMissionNotExist       = -1175 // 用户限时任务不存在
	ErrTimeLimitUserMissionAccepted       = -1176 // 用户限时任务已接受

	//	medal
	ErrMedalConfitNotExists      = -1200
	ErrMedalUserMedalNotExists   = -1201
	ErrMedalUserMedalOrderExists = -1202 // 发勋章订单重复

	// push_message
	ErrPushMessageNotExists         = -1300 // 推送信息不存在
	ErrPushMessageReviewed          = -1301 // 消息之前已经审核过了
	ErrPushMessageRefused           = -1302 // 消息已经被拒绝
	ErrPushMessageDeleted           = -1303 // 消息已被删除
	ErrPushMessageSent              = -1304
	ErrPushMessageOperationUndefine = -1305
	ErrPushParamError               = -1306 // 推送参数错误
	ErrPushWithoutBroadcast         = -1320 // 无推送筛选条件，如需广播请指定广播模式

	// public account
	ErrPublicAccountNotExists         = -1400
	ErrPublicAccountInvalidBizType    = -1401
	ErrPublicNoConfig                 = -1402
	ErrPublicCanNotUnsubSystemAccount = -1403 // 无法取消关注该公众号

	// session (room)
	ErrSessionRoomNotExists        = -1500 // 房间不存在
	ErrSessionRoomUserInOtherRoom  = -1501 // 用户已经在其它房间
	ErrSessionRoomUserNotInRoom    = -1502 // 用户不在此房间内
	ErrSessionRoomUserNotInAnyRoom = -1503 // 用户不在任何房间
	ErrSession1v1NotFriend         = -1504 // 和对方不是好友
	ErrSessionCallinWaitCoolDown   = -1505 // 召集令冷却中
	ErrSessionGroupNoCallin        = -1506 // 这个群当前没有召集令
	ErrSessionAlreadyAcceptCallin  = -1507 // 用户已经接受了召集了
	ErrSessionCallinNotEnd         = -1508 // 召集令尚未结束
	ErrSessionCallinTooFrequency   = -1509 // 发起召集令过于频繁
	ErrSessionRoomUserExceed       = -1510 // 当前开黑人数已满

	// token
	ErrTokenUnsupportedBiz      = -1600
	ErrTokenBadToken            = -1601 // token无效或者已经过期, 请重新登录
	ErrTokenNoValidSeed         = -1602
	ErrTokenIsRequired          = -1603 // token无效, 请重新登录
	ErrTokenWasExpired          = -1604 // token已过期, 请重新登录
	ErrTokenInvalidRefreshToken = -1605 // 无效的refresh token

	// tgroup
	ErrTgroupCreateLimitExceed      = -1700 // 用户创建的兴趣群数量已经超过上限
	ErrTgroupInvalidTgroupAccount   = -1701 // 无效的群帐号
	ErrTgroupNoPermission           = -1702 // 无权限操作
	ErrTgroupOwnerCanntQuit         = -1703 // 群主不允许退群
	ErrTgroupCreateNeedInviteFriend = -1704 // 需要邀请一个好友才能创建群组
	ErrTgroupTargetInvalid          = -1705 // 操作对象错误
	ErrTgroupDisplayIdNotExist      = -1706 // 群不存在
	ErrTgroupJoinGroupLimit         = -1707 // 用户加入的兴趣群过多
	ErrTroupUserBeenKicked          = -1708
	ErrTgroupInvalidInvite          = -1709 // 非法的邀请
	ErrTgroupSensitive              = -1710 // 输入的内容包含敏感词，修改失败

	// presence
	ErrPresExist               = -1800
	ErrSessionNotExist         = -1801 // 会话不存在
	ErrSessionExpired          = -1802 // 会话过期
	ErrPresInvalidProxyAddress = -1803 // 错误的proxy地址

	// UserSetting
	ErrUserSetting   = -1900
	ErrSettingEmpty  = -1901
	ErrResultUidDiff = -1902
	ErrUnknowType    = -1903

	// Mall
	ErrMall                                  = -2000
	ErrMallNoProduct                         = -2001 // 找不到商品
	ErrMallItemTypeMismatch                  = -2002 // 物品类型不匹配
	ErrMallInvalidCategory                   = -2003 // 无效的Category
	ErrMallInvalidItemType                   = -2004 // 无效的物品类型
	ErrMallNoEnoughStorage                   = -2005 // 库存不足
	ErrMallActivityTimeOverlappedWithAnother = -2006 // 活动与另一个活动冲突
	ErrMallNoSuchActivity                    = -2007 // 找不到相应的活动
	ErrMallActivityCanNotBeUpdated           = -2008 // 活动当前无法被修改
	ErrMallInvalidConfig                     = -2009 // 活动配置有问题
	ErrMallCurrentActivityNone               = -2010 // 当前没有活动
	ErrMallNotCurrentActivity                = -2011 // 非当前活动
	ErrMallActivityNotOpen                   = -2012 // 活动还未开始
	ErrMallActivityClosed                    = -2013 // 活动已经结束
	ErrMallNoSuchProductItem                 = -2014 // 找不到相应的物品
	ErrMallCurrentActivityInProgress         = -2015 // 当前有活动在进行
	ErrMallFailedToParseProductItem          = -2016 // 无法解析物品数据
	ErrMallFailedToParseOrderId              = -2017
	ErrMallUserHadMedal                      = -2018 // 你已经拥有该勋章
	ErrMallNoSuchOrderId                     = -2019
	ErrMallNoLotteryChance                   = -2020
	ErrMallUserHaveAlreadyPurchasedProduct   = -2021 // 你已经在本次活动中购买过该商品了

	// Channel
	ErrChannel                              = -2100
	ErrChannelNameLenError                  = -2101 // 房间名称长度不正确
	ErrChannelBindtypeInvalid               = -2102 // 创建的房间类型不匹配
	ErrChannelNotExist                      = -2103 // 房间已经被删除
	ErrChannelBindinfoNothaveChannel        = -2104
	ErrChannelCacheErr                      = -2105
	ErrChannelMicrophoneOverlimit           = -2106 // 上麦人数达到上限
	ErrChannelNameSensitive                 = -2107 // 不能使用敏感词作为房间名
	ErrChannelNameAllDigit                  = -2108 // 无法使用纯数字作为房间名
	ErrChannelBindGuildIdErr                = -2109
	ErrChannelBindGuildNotMember            = -2110 // 用户不是该房间所属公会成员
	ErrChannelNoPermission                  = -2111 // 权限不足
	ErrChannelMemberOverlimit               = -2112 // 房间内成员数目达到上限
	ErrChannelGuildChannelSizeOverlimit     = -2113 // 公会内开黑房间总数达到上限
	ErrChannelRegRelationFailed             = -2114
	ErrChannelUserAlreadySetMuted           = -2115 // 用户已经被禁言
	ErrChannelUserAlreadyHoldMicrophone     = -2116 // 用户已经在麦上
	ErrChannelUserChannelMuted              = -2117 // 你已经被禁言
	ErrChannelUserNotJoinAnyChannel         = -2118 // 用户没有加入任何房间
	ErrChannelUserNotJoinThisChannel        = -2119 // 用户不是该房间成员,请尝试退出房间后重新进入
	ErrChannelMicrophoneEntryAlreadyDisable = -2120 // 麦位已经被关闭
	ErrChannelMicrophoneEntryAlreadyEnabled = -2121 // 麦位已经被启用
	ErrChannelInLockScreenStat              = -2122 // 房间公屏处于锁定状态 不允许发公屏消息
	ErrChannelUserNotHoldMicrophone         = -2123 // 用户没有上麦
	ErrChannelMicmodeNotsupportOper         = -2124 // 房间当前模式不支持该操作
	ErrChannelTopicinfoSensitive            = -2125 // 房间话题包含敏感词
	ErrChannelMicrophoneDisable             = -2126 // 麦位已经被管理员禁用
	ErrChannelUserMicrophoneKickPunishTime  = -2127 // 你被管理员设为旁听 暂时无法上麦
	ErrChannelUserChannelKickPunishTime     = -2128 // 你被管理员踢出房间 暂时无法进入
	ErrChannelMuteTargetHaveduty            = -2129 // 用户是公会管理员 不能被禁言
	ErrChannelMicrophoneMute                = -2130 // 你已经被禁言 暂时无法上麦
	ErrChannelUnmuteTargetHaveduty          = -2131 // 普通管理员没有权限对其他管理员解除禁言
	ErrChannelPwdInvaild                    = -2132

	// 密码错: android 使用CHANNEL_PWD_WRONG, ios使用CHANNEL_HAVE_ALREAD_SET_PWD
	ErrChannelPwdWrong                    = -2133 // 该房间已经上锁，当前版本不支持房间密码输入，请更新APP
	ErrChannelHaveAlreadSetPwd            = -2134
	ErrChannelMicModeNotSupport           = -2135 // 该房间处于当前版本不支持的模式下,请将应用升级到最新版本
	ErrChannelMusicNotHoldMic             = -2136 // 上麦后才能播放背景音乐哦
	ErrChannelSearchTimeOverlimit         = -2137 // 哎呀您慢点啊~~ 手速这么快~~伦家受不了的哟
	ErrChannelMicModeAlreadySet           = -2138 // 您已经处于该模式下了哦
	ErrChannelBindtypeClientNotSupport    = -2139 // 您当前版本不支持进入该类型房间,请升级到最新版本
	ErrChannelOperPermissonUnderTarget    = -2140 // 这里的人等级都好高～伦家打不过啦
	ErrChannelMusicKuwoApiErr             = -2141 // 战歌服务正在努力修复中
	ErrChannelPwdInvalid                  = -2142 // 输入的房间密码无效
	ErrChannelMsgAttachmentExpire         = -2143 // 数据已经过期
	ErrChannelMsgAttachmentFormatInvalid  = -2144 // 房间不支持发送该格式数据或者数据大小超过限制
	ErrChannelConveneCdLmt                = -2145 // 房间召集CD中
	ErrChannelConvening                   = -2146 // 房间正在召集
	ErrChannelNotConvening                = -2147 // 当前房间没有召集哦
	ErrChannelMemberConfirmStatus         = -2148 // 响应召集失败
	ErrNoUserChannelConvene               = -2149 // 没有在召集的房间哦
	ErrChannelMicrSpaceIdInvalid          = -2150 // 麦位信息有误
	ErrChannelMicrSpaceInfoEmpty          = -2151 // 找不到麦位数据
	ErrChannelMicrSpaceNotallowHold       = -2152 // 该麦位被关闭或麦上有人
	ErrChannelMicModeChangeFromFun        = -2153 // 娱乐房人数超过500人时，不能切换为开黑房哦
	ErrChannelPersonalAdminCntLimit       = -2154 // 管理员数量已经达到上限
	ErrChannelTagIdIsReserved             = -2155 // 该标签指暂不允许设置
	ErrChannelCollectNumOverLimit         = -2156 // 你收藏的房间太多了
	ErrChannelTrafficAdmin                = -2158 // 房间当前人数过多，请稍后再试
	ErrChannelSetBackgroundFail           = -2159 // 房间背景设置失败
	ErrChannelCollectGuildOwner           = -2168 // 你是会长，不能取消收藏哦
	ErrChannelCollectManager              = -2169 // 你是房间管理，不能取消收藏哦
	ErrChannelMusicListEmpty              = -2170 // 歌单为空
	ErrChannelMusicCountLimit             = -2171 // 已经达到歌单上限
	ErrChannelMusicCannotShare            = -2172 // 房间当前禁止分享音乐
	ErrChannelMusicHoldNomic              = -2173 // 分享失败,你当前不在麦上
	ErrChannelMusicMusicNotExist          = -2174 // 歌曲不存在,可能已经被删除
	ErrChannelFungameAlreadyStart         = -2180 // 房间已经在进行小游戏
	ErrChannelInFunMicMode                = -2181 // 房间处于娱乐模式,请升级到最新版本才可以操作
	ErrChannelMicrophoneEntryAlreadyMute  = -2182 // 麦位已经被闭麦
	ErrChannelInvalidTagId                = -2183 // 不存在此标签
	ErrChannelRefreshCd                   = -2184 // 刷新太快，请稍后
	ErrChannelTmpAllocPoolEmpty           = -2185 // 临时房间不足
	ErrChannelTmpAllocTypeInvalid         = -2186 // 不能分配该类型的临时房间
	ErrChannelTmpAllocNotExist            = -2187 // 临时房间不存在或者已经解散
	ErrChannelMicModeVersionNotSupport    = -2188 // 房间处于新版布局中,请升级到最新版本才可以操作
	ErrChannelSwitchAttachmentMsg         = -2189 // 该房间暂时不允许发图片哦
	ErrChannelSwitchLevelLmt              = -2190 // 该房间暂时不允许新人发言哦
	ErrChannelSwitchLiveConnectMicLmt     = -2191 // 当前主播没有开启连麦哦
	ErrChannelLiveConnectMicApplyCountLmt = -2192 // 当前申请连麦人数已经达到上限，不能继续申请哦
	ErrChannelLiveConnectMicCountLmt      = -2193 // 当前连麦人数已经达到上限，不能继续连麦哦
	ErrChannelLiveNotStarting             = -2194 // 别急嘛,主播还没有开播哦
	ErrChannelHcNoHoldmic                 = -2195 // HC频道不支持上麦操作
	ErrChannelInDatinggameMicMode         = -2196 // 房间处于相亲模式,请升级到最新版本才可以操作
	ErrChannelIsRecommendOpLimit          = -2197 // 房间被加入推荐库中，不能执行该操作
	ErrChannelOpCdOverlimit               = -2198 // 哎呀您慢点啊~~ 手速这么快~~伦家受不了的哟
	ErrChannelCollectCd                   = -2199 // 操作太快啦，一会儿再来试试

	// SDK
	ErrSdkConfigNotValid = -2200
	ErrSdkConfigNotFound = -2201

	//
	ErrChannelQueueUpMicSwitchLmt             = -2250 // 房间排麦功能没有开启哦
	ErrChannelQueueUpMicApplyCountLmt         = -2251 // 当前申请排麦的人数已经达到上限，不能继续申请哦
	ErrChannelQueueUpMicApplyAlready          = -2252 // 你已经申请过排麦了，请等待主持人的处理
	ErrChannelLockedConveneUnavailable        = -2253 // 锁房状态下不可召集
	ErrChannelMiniGameUnavailable             = -2254 // 当前版本不支持小游戏玩法，请更新到最新版本体验酷炫好玩的房间小游戏
	ErrChannelSwitchMiniGameUnavailable       = -2255 // 切换小游戏失败，请更新至最新版本体验酷炫好玩的房间小游戏哟
	ErrChannelSwitchWerewolvesGameUnavailable = -2256 // 切换失败，请更新至最新版本体验酷炫好玩的狼人杀小游戏哟
	ErrChannelPublicWerewolvesGameUnavailable = -2257 // 发布失败，请更新至最新版本体验酷炫好玩的狼人杀小游戏哟

	// Activity 活动
	ErrActivityErrcodeBase        = -2300
	ErrActivitySvrFalied          = -2301
	ErrActivityNameEmpty          = -2302
	ErrActivityTimeError          = -2303
	ErrActivityDberror            = -2304
	ErrActivityRecordNotexist     = -2305
	ErrActivityParameterErr       = -2306
	ErrActivityNotEnoughPrizeTale = -2307 // 没有足够的奖励可领取

	// apns
	ErrApnsInvalidRegisterRequest = -2400
	ErrApnsNoDeviceToken          = -2401
	ErrApnsInvalidDeviceToken     = -2402
	ErrApnsNotificationExpired    = -2403

	// first voucher activity
	ErrGameFirstVoucherNotExist        = -2500
	ErrGameFirstVoucherUpdateNoParam   = -2501
	ErrGameFirstVoucherProductHaveItem = -2502
	ErrGameFirstVoucherProductPastDue  = -2503
	ErrGameFirstVoucherItemNotEnough   = -2504
	ErrGameFirstVoucherUserNoBuyAuth   = -2505

	// Redpacket
	ErrRedpacketAlreadyMassguildbuff        = -2600
	ErrRedpacketStageNotFound               = -2601
	ErrRedpacketExchangeGiftAccountNotExist = -2603
	ErrRedpacketNotAllowed                  = -2604
	ErrRedpacketActivityNotFound            = -2605
	ErrRedpacketGuildbuffNotFound           = -2606
	ErrRedpacketStagetimeOverlay            = -2607 // 修改的阶段时间和已有配置重合
	ErrRedpacketStagetimeTooLate            = -2608 // 修改的阶段时间必须在当前时间前2小时

	// 活动
	ErrCeremony2018StageError              = -2650 // 年度盛典当前阶段不能执行该操作
	ErrCeremony2018SignupSexErr            = -2651 // 您的性别不符合报名要求，请修改
	ErrCeremony2018SignupAlready           = -2652 // 您已经报过名了 不能重复报名
	ErrCeremony2018JoinTeamAlready         = -2653 // 您已经加入了一个多人队伍
	ErrCeremony2018NotTeamMember           = -2654 // 您不是该队伍成员
	ErrCeremony2018NotTeamCreater          = -2655 // 您不是该队伍的创建者，没有权限
	ErrCeremony2018TeamNotExist            = -2656 // 该队伍不存在或者已经解散
	ErrCeremony2018TeamMemberSizeOverlimit = -2657 // 该队伍已经满员,无法加入
	ErrNewyearBeat2019GameNotStart         = -2658 // 游戏尚未开始
	ErrNewyearBeat2019GameAlreadyPlay      = -2659 // 本轮游戏您已经参与过了
	ErrNewyearBeat2019GameAlreadyEnd       = -2660 // 本轮游戏已结束
	ErrNewyearBeat2019LotteryError         = -2661 // 游戏抽奖错误

	// Commission
	ErrCommissionMoneyNotEnough100   = -2670 // 金额小于100，不可提现
	ErrCommissionAccountIsFrost      = -2671 // 你的账户已被冻结，不可提现。如有问题，请联系客服
	ErrCommissionGuildLvTooLow       = -2672 // 公会星级达到Lv.2以上才能提现
	ErrCommissionGetMoneyTimeErr     = -2673 // 每月的4日~6日才可以申请提现
	ErrCommissionUserInfoIdCardErr   = -2674 // 身份证填写格式有误, 请核对后重试
	ErrCommissionUserInfoBankIdErr   = -2675 // 开户行填写格式有误，请核对后重试
	ErrCommissionUserInfoPhoneErr    = -2676 // 手机号码填写格式有误, 请核对后重试
	ErrCommissionUserInfoBankCardErr = -2677 // 银行卡号填写格式有误，请核对后重试
	ErrCommissionMoneyNotEnough      = -2678 // 提现金额超过余额，请重新申请
	ErrCommissionUserInfoErr         = -2679
	ErrCommissionApiTimeout          = -2680 // 佣金系统接口超时
	ErrCommissionApiRespError        = -2681 // 佣金系统操作不成功
	ErrCommissionUserBankInfoError   = -2682 // 上次提现银行卡账户信息不正确，请重新设置银行卡

	// Gold Diamond
	ErrGoldDiamondOrderIdExist      = -2700
	ErrGoldDiamondDetailEmpty       = -2701
	ErrGoldDiamondSqlSumFailed      = -2702
	ErrGoldDiamondIncomeNotEqualSum = -2703
	ErrGoldDiamondTimeError         = -2704

	// numericsvr 2750- 2799
	ErrNumericsUnknownRankType             = -2750 // 未知的排行榜类型
	ErrNumericsPresentRecordNotsupportType = -2751 // 财富魅力值接口不支持该类型礼物
	ErrLbsJsonParseErr                     = -2800 // json解析失败
	ErrLbsSsdbOperErr                      = -2801 // ssdb操作失败
	ErrLbsIpNotFound                       = -2802 // IP地址无效
	ErrLbsLocationNotFound                 = -2803 // 经纬度无效
	ErrLbsLocationOutOfDate                = -2804 // 经纬度数据过期

	// Guild storage [2900 - 2999]
	ErrGuildJoinTimeLimit = -2900 // 加入公会时间不足

	// 开放平台 oauth2:   [ -3000 --- -4999 ]
	ErrOauth2Base                    = -3000
	ErrOauth2InvalidRequest          = -3001
	ErrOauth2UnauthorizedClient      = -3002
	ErrOauth2AccessDenied            = -3003
	ErrOauth2UnsupportedResponseType = -3004
	ErrOauth2InvalidScope            = -3005
	ErrOauth2ServerError             = -3006
	ErrOauth2TemprarilyUnavailable   = -3007
	ErrOauth2InvalidClient           = -3008
	ErrOauth2InvalidGrant            = -3009
	ErrOauth2UnsupportedGrantType    = -3010
	ErrOauth2InvalidUser             = -3011
	ErrOauth2InvalidCode             = -3012
	ErrOauth2InvalidAccessToken      = -3013
	ErrOauth2InvalidRefreshToken     = -3014
	ErrOauth2InvalidRedirectUri      = -3015
	ErrOauth2NoPermissionToAccess    = -3016
	ErrOauth2TokenSignatureMismatch  = -3040
	ErrOauth2InvalidTokenFormat      = -3041

	// Team svr
	ErrTeamSvrUserAlreadyHaveTeam = -3101 // 用户已有战队
	ErrTeamSvrTeamNotExist        = -3102 // 战队不存在
	ErrTeamSvrUserNoVoteChance    = -3103 // 每天只能给出一个支持哦~
	ErrTeamSvrApproved            = -3104 // 战队已批准，禁止再修改
	ErrTeamNameAlreadyUse         = -3105
	ErrTeamMemberSignupRepeat     = -3106
	ErrTeamNotFollow              = -3107

	// league svr
	ErrLeagueSvrUploadImg = -3201 // 当前阶段不可上传截图

	// game preorder
	ErrGamePreorderNotExist = -3250 // 预约不存在

	// userrecommendsvr
	ErrUserReportPhonelistInvalid = -3260 // 上报手机列表出错

	// cityrecommendsvr
	ErrCityRecommendAddrNotExist = -3265 // 用户地址不存在

	// recommend card
	ErrGameRecommendCardTagExist              = -3301
	ErrGameRecommendCardToplistNameExist      = -3302
	ErrGameRecommendCardToplistTagExist       = -3303
	ErrGameRecommendCardTagNotExist           = -3304
	ErrGameRecommendCardNotExist              = -3305
	ErrGameRecommendCardActivityBindGameExist = -3306
	ErrGameRecommendCardActivityExpire        = -3307
	ErrGameRecommendCardTagCardExist          = -3308

	// 安全中心 Security -3401 ~ -3499
	ErrSecurityOk                            = -3401
	ErrSecurityAccountWrong                  = -3402
	ErrSecuritySystemErr                     = -3403
	ErrSecurityAccountNotExist               = -3404
	ErrSecurityAuthRequired                  = -3405
	ErrSecurityAccessTokenExpired            = -3406
	ErrSecurityPhoneFormatWrong              = -3407
	ErrSecurityWrongMissingInput             = -3408
	ErrSecurityPhoneBindedByAnother          = -3409
	ErrSecuritySmsVerfiycodeValidateFail     = -3410
	ErrSecuritySessionRequired               = -3411
	ErrSecuritySessionExpired                = -3412
	ErrSecuritySessionError                  = -3413
	ErrSecurityDberror                       = -3414
	ErrSecurityAnswerNotMatch                = -3415
	ErrSecurityAnswerWrongFrequently         = -3416
	ErrSecurityWrongOldPassword              = -3417
	ErrSecuritySmsVerfiycodeFailExeccedDaily = -3418
	ErrSecuritySmsSendTooFreq                = -3419
	ErrSecuritySmsSendExeccedDaily           = -3420
	ErrSecurityAlreadyBindPhone              = -3421 // 你已经绑定了手机

	// gamelotto svr
	ErrGamelottoSvrNoEnoughChance                = -3501 // 抱歉 剩余抽奖次数为0
	ErrGamelottoSvrReachLimit                    = -3502 // 已达到当日抽奖次数限制
	ErrGamelottoSvrNeedReddiamond                = -3503 // 继续抽奖需要消耗红钻
	ErrGamelottoRechargeOrderidDuplicate         = -3504
	ErrGamelottoRechargeParamErrMingiftOverlimit = -3505
	ErrGamelottoGameChannelUnkown                = -3506
	ErrGamelottoGameChannelNotValid              = -3507
	ErrGamelottoRechargeInvalid                  = -3508
	ErrGamelottoRechargeParamErrRebateInvalid    = -3509
	ErrGamelottoRechargeParamLack                = -3510
	ErrGamelottoRechargeNeverRecharge            = -3511
	ErrGamelottoRechargeFixPoolLack              = -3512
	ErrGamelottoRechargeAdditionPoolLack         = -3513
	ErrGamelottoRechargeNoGiftRange              = -3514
	ErrGamelottoRechargeParamErr                 = -3515
	ErrGamelottoUserGameCpNotExist               = -3540
	ErrGamelottoNoEnoughReddiamond               = -3550 // 红钻不足
	ErrGamelottoPoolEmpty                        = -3551 // 库存已空
	ErrGamelottoConsumeReddiamondFailed          = -3552 // 红钻扣除失败
	ErrGamelottFirstLottoHasDone                 = -3553 // 首抽已经领取
	ErrGamelottoLoginToGetFreeChance             = -3554 // 红钻抽奖次数已到达上线,登录获取免费抽奖次数
	ErrGamelottoNoSuchOrder                      = -3555 // 无效的订单号
	ErrGamelottoFragmentNotEnough                = -3556 // 碎片不足

	// audit svr 3600 ~ 3699
	ErrAuditOk               = -3600
	ErrAuditGeneralErr       = -3601
	ErrAuditMongodbException = -3602
	ErrAuditDbErr            = -3603
	ErrAuditQueryParseErr    = -3604

	// guildmemberlv svr
	ErrGuildmemberlvSupplementParam     = -3701
	ErrGuildmemberlvInvalidDonateOption = -3702 // 捐献值错误
	ErrGuildmemberlvContributionLack    = -3703 // 贡献值不足
	ErrGuildmemberlvAddContributionZero = -3704
	ErrGuildmemberlvOperType            = -3705
	ErrGuildmemberlvOrderExist          = -3706
	ErrGuildmemberlvOrderEmpty          = -3708
	ErrGuildmemberlvNoUidList           = -3709

	// guildstorge svr
	ErrGuildStorgeProductOverLimit     = -3740 // 公会商品数量超过限制
	ErrGuildStorgeProductItemOverLimit = -3741 // 商品兑换码超过限制svn

	// guildcircle svr
	ErrGuildcircleTopTopicCountLimitExceeded = -3750 // 置顶帖数量超过限制
	ErrGuildcircleTitleIsEmpty               = -3751 // 标题不能为空

	// ttgiftcenter svr
	ErrTtGiftCenterNotRankSubType   = -3801 // 改子礼包无法排序
	ErrTtGiftCenterNotEnoughStorage = -3802 // 库存不足
	ErrTtGiftNotExistGift           = -3803 // 礼包不存在
	ErrTtGiftNotEnoughCurrency      = -3804 // 货币不足
	ErrTtGiftSpendCurrencyFailed    = -3805 // 扣费失败
	ErrTtGiftNotAllowToPick         = -3806 // 未开放淘号
	ErrTtGiftNotValidCount          = -3809
	ErrTtGiftNotForSdk              = -3810
	ErrTtGiftNotthingCanClaim       = -3811 // 没有可领取的礼包
	ErrTtGiftLimitRegisterAt        = -3812 // 礼包限定了可领取用户的注册时间

	// findfriend_match
	ErrFindFriendMatchingNull      = -3830 // 没有匹配的用户
	ErrFindFriendExamIdNotExist    = -3831 // 该测试项不存在~
	ErrFindFriendMatchCntOverLimit = -3832 // 今天召唤了很多个小可爱啦，明天再试试吧

	// userpresent
	ErrUserPresentConfigNotExist        = -3851 // 礼物不存在
	ErrUserPresentLmtEffectedBegin      = -3852 // 礼物未上架
	ErrUserPresentLmtEffectedEnd        = -3853 // 礼物已下架
	ErrUserPresentConfigOverdue         = -3854 // 礼物配置已过期
	ErrUserPresentConfigParam           = -3855 // 礼物配置参数异常
	ErrUserPresentBuyFailed             = -3857 // 购买礼物失败
	ErrUserPresentInvalidItemCount      = -3858 // 礼物数量异常
	ErrUserPresentOrderNotExist         = -3859 // 礼物订单不存在
	ErrUserPresentTagChannelDating      = -3860 // 此房间不可送出相亲房礼物
	ErrUserPresentInvalidTargetUserSize = -3861 // 送礼失败
	ErrUserPresentUnableSendUserPresent = -3862 // 无法给该用户送礼
	ErrUserPresentFlowConfigNotExist    = -3865 // 礼物流光配置不存在
	ErrUserPresentSpendOverLimit        = -3866 // 单笔赠送金额不能超过2万元
	ErrScenePresentOrderExist           = -3870 // 送礼场景的礼物订单号已存在
	ErrUserScoreConfigNotExist          = -3871 // 积分配置不存在
	ErrUserScoreNotEnough               = -3872 // 积分不足
	ErrUserScoreOrderExist              = -3873 // 积分订单号已存在
	ErrUserScoreChangeReasonInvalid     = -3874 // 非法积分变化原因
	ErrUserScoreOrderRollback           = -3875 // 积分已回滚

	// anti 3900 - 3949
	ErrAntiOk                 = -3900
	ErrAntiDbErr              = -3901 // 访问DB错误
	ErrAntiNotExist           = -3902 // 记录不存在
	ErrAntiRedisErr           = -3903 // 访问Redis错误
	ErrAntiCmdOpBanForbid     = -3904 // 当前处于系统维护升级中，该功能暂不可用
	ErrAntiCmdOpUserBanForbid = -3905 // 系统升级中，该功能暂不可用

	// cooldown 3950 - 3999
	ErrCooldownOk       = -3950
	ErrCooldownRedisErr = -3951
	ErrCooldownNotExist = -3952

	// channelvotepk
	ErrChannelVotePkGetLockFail    = -3310 // 获取锁失败
	ErrChannelVotePkRedisFail      = -3311 // 系统错误
	ErrChannelVotePkExist          = -3312 // 发起投票失败，该房间正在投票中
	ErrChannelVotePkArgsFail       = -3313 // 参数错误
	ErrChannelVotePkNotExist       = -3314 // PK已经结束
	ErrChannelVotePkNotMember      = -3315 // 不是PK成员
	ErrChannelVotePkNotAdmin       = -3316 // 需要管理员权限
	ErrChannelVotePkCommonFail     = -3317 // 投票失败
	ErrChannelVotePkNotLeftVote    = -3318 // 你的票数已经用完啦，感谢你的支持哟
	ErrChannelVotePkNameInvalid    = -3319 // 投票话题中包含敏感词，请重新输入
	ErrChannelVotePkNotChannelMode = -3320 // 只有娱乐模式才能开启投票哟

	// channeldrawgame
	ErrChannelDrawGameStatusOff    = -3321 // 涂鸦小画板已经关闭，请联系房管打开画板功能
	ErrChannelDrawGameLineCntMax   = -3322 // 当前画板操作次数过多，请清屏后再画吧
	ErrChannelDrawGameNotAdmin     = -3323 // 需要管理员权限
	ErrChannelDrawGameLineNotExist = -3324 // 线不存在
	ErrChannelDraeGamePointCntMax  = -3325 // 笔画太长啦，松手再继续吧~

	// 领取礼物碎片
	ErrGetFriendDebrisGiftTimeOut        = -3330 // 消息已过期
	ErrGetFriendDebrisGiftRepeat         = -3331 // 礼物已经领取
	ErrSendFriendDebrisDayCntLimit       = -3332 // 今日送礼次数达到上限
	ErrReceiveFriendDebrisDayCntLimit    = -3333 // 对方今日收礼次数达到上限
	ErrSendFriendTargetUidErr            = -3334 // 不能领取不是送给自己的礼物
	ErrYouReceiveFriendDebrisDayCntLimit = -3335 // 今日收礼次数已达上限，明天再试吧

	// httplogic 4000 - 4099
	ErrHttplogicSmsThreshold = -4001

	// misssion 4100 - 4199
	ErrUpdateMissionStatus = -4101

	// friendonline 4200 - 4220
	ErrOnlineUnknownStatus            = -4201
	ErrOnlineChannelFollowAuthClose   = -4202 // 您的玩伴未开启“跟随进房”功能，快去让他开启吧
	ErrOnlineChannelFollowAuthNotOpen = -4203 // 用户跟随进房间设置未打开
	ErrOnlineChannelFollowNotEnter    = -4204 // 您跟随的玩伴现在没有在房间内

	// user tag 4221 - 4299
	ErrUsertagConfNotExist            = -4221 // 标签不存在
	ErrUsertagConfDel                 = -4222 // 该标签已经被删除 不可设置
	ErrUsertagGameOptConfDel          = -4223 // 游戏标签的该选项已经被删除 不可设置
	ErrUsertagSetCountOverlimit       = -4224 // 设置的标签数量超过限制
	ErrUsertagSetGametagNickSensitive = -4225 // 游戏卡片中游戏昵称包含敏感词
	ErrUsertagSetBirthdayFormatErr    = -4226 // 无效的生日标签格式

	// search 4300 - 4349
	ErrSearchOk           = -4300
	ErrSearchParameterErr = -4301
	ErrSearchSphinxErr    = -4302 // Sphinx错误

	// clientversion 4350 - 4399
	ErrClientversionRedisErr        = -4351
	ErrClientversionNoVersionCfg    = -4352
	ErrClientversionNoValidVersion  = -4353
	ErrClientversionUserVerNotExist = -4354

	// kefu 4400 - 4450
	ErrUserAlreadyBindKefu = -4400 // 该用户当前由其他客服绑定服务
	ErrKefuAllOffline      = -4401 // 没有在线客服

	// 绿色爸爸的制裁
	ErrGreenbabaSanctionChannel    = -4421 // 该房间暂时无法使用
	ErrGreenbabaSanctionOpUser     = -4422 // 您挑战了绿色爸爸的力量 正在被制裁 (说人话就是您被封了)
	ErrGreenbabaSanctionTargetUser = -4423 // 对方挑战了绿色爸爸的力量 正在被制裁(说人话就是对方被封了)

	// exchange 4450 - 4500
	ErrExchangeItemPriceNotUpToDate    = -4450 // 兑换价格不是最新
	ErrExchangeItemInvalidCurrencyType = -4451 // 无效的兑换类型
	ErrExchangeDuplicateOrderId        = -4452 // 重复操作
	ErrExchangeTbeanInsufficientFunds  = -4453 // 今天豆豆已经抢光了哟，请明天再来吧～
	ErrExchangePointsSettling          = -4454 // 正在结算积分中，请稍候再来吧~
	ErrExchangeFuseProtection          = -4455 // 积分兑换T豆库存不足，请稍后再试
	ErrExchangeFuseProtectionProcess   = -4456 // 积分兑换T豆库存达到警戒线

	// tbean 4500 - 4520
	ErrTbeanDuplicateOrder     = -4500 // 重复的订单号
	ErrTbeanNoEnoughBalance    = -4501 // T豆余额不足
	ErrTbeanIllegalOrderStatus = -4502
	ErrTbeanSystemError        = -4503 // T豆系统错误

	// unified pay 4520 - 4540
	ErrUnifiedPayDuplicateOrder     = -4520 // 重复的订单号
	ErrUnifiedPayIllegalOrderStatus = -4521
	ErrUnifiedPayNoSuchOrder        = -4522 // 无此订单

	// rush  4541 - 4550
	ErrRushInQueue = -4541 // 正在排队中，请稍候

	// find_friends 4551 - 4599
	ErrFindFriendsNoRegistered             = -4551 // 你未在扩圈中登记资料，请先完善资料
	ErrAnotherQuickMatchIsAfoot            = -4552 // 你有另一个匹配正在进行中，请别着急~
	ErrQuickMatchCurrentChannelIsSupplying = -4553 // 当前房间正在补位中，请别着急~
	ErrQuickMatchGameNotSupport            = -4554 // 你选择的游戏已经不再支持匹配，请重新选择~
	ErrQuickMatchPunishedForDeserter       = -4555 // 由于你最近从匹配房间中秒退，请稍后再进行匹配~
	ErrFindFriendsReachedDailyLikeLimit    = -4556 // 今天已经喜欢了太多人了，不要那么花心哦

	// headwear
	ErrHeadwearNotFound       = -4570 // 不存在的麦位框
	ErrHeadwearNotInUse       = -4571 // 用户没有使用麦位框
	ErrHeadwearNotHave        = -4572 // 用户没有该麦位框
	ErrHeadwearExpired        = -4573 // 麦位框已经过期
	ErrHeadwearNotSameCp      = -4574 // 与该麦位框绑定的CP不一致
	ErrHeadwearOrderidExist   = -4575 // 麦位框订单已存在
	ErrHeadwearCpTypeNotCpUid = -4576 // CP麦位框缺少CP对象UID
	ErrHeadwearCanNotUse      = -4577 // 无法使用该麦位框，请升级版本

	// activity_present 4600 - 4619
	ErrActivityPresentEnded = -4600 // 活动已结束

	// Trivia Game 4620 - 4680
	ErrChannelTriviaGameNotSupport                     = -4620 // 当前版本不支持答题，请升级版本
	ErrChannelTriviaGameActNotExist                    = -4621 // 答题活动不存在或者尚未开始
	ErrChannelTriviaGameQuestionNotExist               = -4622 // 题目不存在
	ErrChannelTriviaGameQuestionExpire                 = -4623 // 题目过期或者尚未开始答题
	ErrChannelTriviaGameAnswerNoQualify                = -4624 // 您已经被淘汰了,不能继续答题
	ErrChannelTriviaGameNotQuestionPhase               = -4625 // 非答题阶段
	ErrChannelTriviaGameShowSolutionEarly              = -4626 // 答案公布太早了
	ErrChannelTriviaGameErrPhase                       = -4627 // 阶段顺序异常
	ErrChannelTriviaGameNoLivesToResurrect             = -4628 // 复活卡不足
	ErrChannelTriviaGameNoResurrectChancesInThisPeriod = -4629 // 本轮已经无法使用复活机会
	ErrChannelTriviaGameNotStart                       = -4630 // 活动未开始
	ErrChannelTriviaGameAlreadyEnd                     = -4631 // 活动已经结束
	ErrChannelTriviaGameAlreadyShowSolution            = -4632 // 已经公布过答案
	ErrChannelTriviaGameAlreadyAnswer                  = -4633 // 不能重复答题

	// esgw 4680 - 4700
	ErrEsgwNotFound = -4686 // 不存在

	// guildrecommend 4701 - 4720
	ErrGuildGameCfgNotExist = -4701 // 非公会游戏
	ErrGuildGameAddLimit    = -4702 // 同时添加的公会主打游戏太多咯

	// game recruit 4800- 4850
	ErrGameRecruitExist          = -4800 // 游戏招募已存在
	ErrGameRecruitNotHaveTeam    = -4801 // 没有加入任何队伍
	ErrGameRecruitNotExist       = -4802 // 该游戏组队已结束
	ErrGameRecruitFullMember     = -4803 // 该游戏组队已满员
	ErrGameRecruitNotInChannel   = -4804 // 用户不在频道中
	ErrGameRecruitFrequenceLimit = -4805 // 频率限制

	// presentlogic 4851 - 4880
	ErrPresentSourceInvalid = -4851 // 礼物不存在

	// backpack 背包 4900 - 4949
	ErrBackpackPackageItemNotExist       = -4900 // 包裹不存在该包裹项配置
	ErrBackpackUserNotEnoughItem         = -4901 // 背包物品不足
	ErrBackpackPackageItemTimeout        = -4902 // 背包物品已过期
	ErrBackpackFuncCardAlreadyUse        = -4903 // 已使用同类型卡片
	ErrBackpackUserItemNotFind           = -4904 // 用户背包找不到该项
	ErrBackpackUseSourceIdErr            = -4905 // 使用物品源id不一致
	ErrBackpackUseItemTypeErr            = -4906 // 使用物品类型不一致
	ErrBackpackUseFragmentSendErr        = -4907 // 碎片不支持赠送
	ErrBackpackUseFreezeOrderConfilctErr = -4908 // order_id 冲突
	ErrBackpackUseFreezeOrderNonexistErr = -4909 // order_id 不存在
	ErrBackpackUseFreezeOrderFinishedErr = -4910 // order_id 已经完成
	ErrBackpackUseFunccardLevelLimit     = -4911 // 不可使用比当前低倍或相同倍数的加速卡
	ErrBackpackUseFreezeTypeInvalidErr   = -4912 // 该类型不支持冻结
	ErrBackpackOrderExist                = -4913 // 订单号已存在
	ErrBackpackNumLimit                  = -4914 // 超过单次发包裹数量
	ErrBackpackOrderNotExist             = -4915 // 订单不存在
	ErrBackpackTimestampInvalid          = -4916 // 时间戳参数错误
	ErrBackpackIsDeleted                 = -4917 // 包裹不存在

	// channel dating game 房间相亲游戏 4950 - 5000
	ErrChannelDatinggameEntryNotOpen          = -4950 // 该房间暂无相亲游戏模式权限
	ErrChannelDatinggamePhaseError            = -4951 // 相亲游戏阶段设置错误
	ErrChannelDatinggameApplyMicUserOverLimit = -4952 // 相亲游戏申请上麦人数超过限制
	ErrChannelDatinggameUserNotSelectLikeObj  = -4953 // 用户没有选择心动对象
	ErrChannelDatinggameCanNotOpenUser        = -4954 // 不能公布心动对象
	ErrChannelDatinggameErrStageOperation     = -4955 // 该阶段不能进行此操作
	ErrChannelDatinggameNotVip                = -4956 // 不可上土豪王座
	ErrChannelDatinggamePhaseVipError         = -4957 // 此相亲阶段不可上土豪王座
	ErrChannelDatinggameOpenUserCoolDown      = -4958 // 公布心动对象冷却中~
	ErrChannelDatinggameAlreadyOpenUser       = -4959 // 已经公布过该用户心动对象~

	// checkin 5001 - 5020
	ErrUserAlreadyCheckin    = -5001 // 今天已签到
	ErrCheckinAwardNotExist  = -5002 // 签到奖励不存在
	ErrCheckinConfigNotExist = -5003 // 签到配置不存在

	// 实名认证 5021 - 5040
	ErrRealnameNotSetingErr        = -5021 // 需要进行实名认证
	ErrRealnameNotFinished         = -5022 // 实名认证没有完成
	ErrRealnameAlreadyFinished     = -5023 // 实名认证已经完成
	ErrRealnameUnknownStatus       = -5024 // 未知的实名认证状态
	ErrRealnameNotIdentityInfo     = -5025 // 没有该用户的身份证信息
	ErrRealnameNotStatusInfo       = -5026 // 没有认证信息
	ErrRealnameBindPhoneLimit      = -5027 // 此手机号已经认证十个账号，请用其它手机号认证
	ErrRealnameBindPhoneLimitTt    = -5028 // 绑定的手机号已经认证十个账号，请联系客服
	ErrRealnameBindIdentityLimit   = -5029 // 此身份证已实名认证了十个账号,请使用其他身份信息验证
	ErrRealnameInvalidParameter    = -5030 // 参数错误
	ErrRealnameInvalidIdentityNum  = -5031 // 无效的身份证号码
	ErrRealnameInvalidIdentityName = -5032 // 名字错误
	ErrRealnameUpgradeStopService  = -5033 // 实名认证系统升级中，暂不可用

	// faceid 失败，走 web 实名流程
	ErrRealnameNeedManualCheck     = -5034 // 认证失败，请升级客户端走人工审核通道
	ErrRealnameInvalidIdentityInfo = -5035 // 身份信息有误，请检查姓名或身份证号并重新输入
	ErrRealnameCheckFaceFailLimit  = -5036 // 当天刷脸认证次数过多，请明天再试
	ErrRealnameCntLimit            = -5037 // 今日实名认证次数已达上限，请明日再来

	// 房间送礼统计 5041 - 5060
	ErrCountIsOffErr = -5041 // 已经关闭了开关
	ErrCountIsOnErr  = -5042 // 已经开启了开关
	ErrCountOnMicErr = -5043 // 上麦事件消费错误
	ErrCountIsOff    = -5044 // 计数器未开启

	// 反垃圾 5061 ~ 5099
	ErrAntispamNeedVerifyCode        = -5061 // 需要弹验证码进行行为验证
	ErrAntispamVerifyCodeCheckFailed = -5062 // 验证码验证错误
	ErrAntispamTokenNotExist         = -5071 // 反垃圾token不存在

	// 表情包 5100 ~ 5120
	ErrEmojiMaxLimit            = -5100 // 你添加的表情数量已经达到上限，请删除部分表情再尝试添加
	ErrEmojiAlreadyAdd          = -5101 // 你已经添加过这个表情了哦
	ErrEmojiUploadError         = -5105 // 上传失败
	ErrEmojiUploadFileTooLarge  = -5106 // 文件大小超出系统最大限制
	ErrEmojiPkgPermissionDenied = -5107 // 无权限获取或修改表情包
	ErrEmojiTargetNotExists     = -5108 // 该表情不存在

	// channel-personalization 5120 ~ 5139
	ErrChannelPslUnknownDecorationType     = -5120 // 不支持的装饰类型
	ErrChannelPslGrantingExpiredDecoration = -5121 // 该装饰已经过期
	ErrChannelPslGrantingInvalidDecoration = -5122 // 请求无效
	ErrChannelPslGrantingOrderIdDuplicate  = -5123 // 发放装饰的订单号重复
	ErrChannelPslConfigDuplicate           = -5124 // 装饰配置重复
	ErrChannelPslConfigNotExist            = -5125 // 装饰配置不存在
	ErrChannelPslConfigError               = -5126 // 其他装饰配置错误

	// 家长监护模式 5140 ~ 5159
	ErrGuardianPwdErr     = -5141 // 密码错误
	ErrGuardianOnErr      = -5142 // 已经打开家长监护模式
	ErrGuardianOffErr     = -5143 // 已经关闭家长监护模式
	ErrGuardianPwdNullErr = -5144 // 密码不能为空
	ErrGuardianDbErr      = -5145 // 数据库访问异常
	ErrGuardianUnonErr    = -5146 // 未开启家长监护模式

	// 家长监控模式忘记密码申诉时扫脸验证
	ErrGuaidianCheckIdentityByFaceNotPass = -5147 // 扫脸验证未通过
	ErrGuaidianCheckCountIsOverLimit      = -5148 // 今天已经申诉很多次了，明天再试吧
	ErrGuaidianYoungOffDenied             = -5149 // 您是未成年人，不能关闭青少年模式

	// 5160 ~ 5250 异步内容
	ErrUgcTopicNotExists                = -5160 // 该话题不存在
	ErrUgcTopicDisable                  = -5161 // 该主题还没上线
	ErrUgcTopicCreateDuplicateName      = -5162 // 主题名称同名冲突
	ErrUgcTopicCreateDuplicateBindUid   = -5163 // 主题绑定的官方账号已经被使用
	ErrUgcTopicBindedParent             = -5164 // 话题绑定的圈子失败
	ErrUgcHadLike                       = -5170 // 已经点过赞了
	ErrUgcHadNotLike                    = -5171 // 已经取消赞了
	ErrUgcFriendshipFollowLimited       = -5172 // 关注失败，你的关注已达到上限
	ErrUgcFriendshipAntispamHit         = -5173 // 关注失败，对方已开启免打扰
	ErrUgcFriendshipBatchFollowLimited  = -5174 // 关注失败，批量关注达到上限
	ErrUgcInteractivePermissionDenied   = -5175 // 无权限操作
	ErrUgcInvalidFeedGroup              = -5176 // 无效的feed 类型
	ErrUgcInvalidVisitRecordType        = -5177 // 非法的记录类型
	ErrUgcHotRecordNotExists            = -5180 // 该热度记录不存在
	ErrUgcNotAllowToFollowYourself      = -5181 // 不允许关注你自己哦
	ErrUgcPostNotExists                 = -5190 // 帖子不存在
	ErrUgcCommentNotExists              = -5191 // 评论不存在
	ErrUgcPermissionDenied              = -5192 // 权限不足
	ErrUgcAttachmentStatusInvalid       = -5193 // 附件状态无效
	ErrUgcPostBanned                    = -5194 // 该动态已被屏蔽
	ErrUgcPostDeleted                   = -5195 // 该动态已被删除
	ErrUgcCommentBanned                 = -5196 // 评论已被屏蔽
	ErrUgcCommentDeleted                = -5197 // 评论已被删除
	ErrUgcPostPostCooldown              = -5198 // 手速太快啦，歇一会再来吧
	ErrUgcPostCommentCooldown           = -5199 // 哎呀，手速太快啦，人家接受不了惹
	ErrUgcPostOpInBlackList             = -5200 // 你已拉黑对方，需移出黑名单后才可操作
	ErrUgcPostOpInBlackListForbid       = -5201 // 由于对方的设置，你暂时无法操作
	ErrUgcFriendshipOpInBlackListForbid = -5202 // 你已拉黑对方，需移出黑名单后才可重新关注
	ErrUgcFollowOpInBlackListForbid     = -5203 // 由于对方的设置，你暂无法关注
	ErrUgcStickyPostCountLimit          = -5204 // 置顶动态已达上限
	ErrUgcStickyCommentCountLimit       = -5205 // 置顶评论已达上限
	ErrUgcPostPrivacyPolicyPrivate      = -5206 // opps~这条动态被隐藏起来啦
	ErrUgcCannotMarkPostSticky          = -5207 // 置顶失败，私密动态不支持置顶哦~
	ErrUgcCelebrityNotExisted           = -5210 // UID对应的用户并非优质用户
	ErrUgcCelebrityExisted              = -5211 // 用户已是优质用户

	// audio post 5220
	ErrScriptShouldUni = -5220 // 台本重复

	// 用户进房特效 5251 - 5260
	ErrInvalidUserDecorationType = -5251 // 类型错误
	ErrUserDecorationNotExist    = -5252 // 座驾不存在

	// 用户服务协议 5261 - 5270
	ErrUserContractUnknown        = -5261 // 用户服务协议状态异常
	ErrUserContractInvalidVersion = -5262 // 用户服务协议版本错误

	// 用户认证 5300-5320
	ErrUnmatchPushSample = -5300 // 推送参数个数与模板对不上

	//
	ErrCarVersionNotSupport = -5321 // 当前车载版本不支持该功能 请使用APP版本来操作

	// 新人开黑推荐房，房主设置展示 5331 - 5340
	ErrNoviceRecommendUnknownStatus    = -5331 // 状态类型不受支持
	ErrNoviceRecommendAlreadyDisplay   = -5332 // 展示已打开
	ErrNoviceRecommendAlreadyHide      = -5333 // 展示已关闭
	ErrNoviceRecommendPermissionDenied = -5334 // 没有权限改变展示状态
	ErrNoviceRecommendNotRecommend     = -5335 // 不是开黑新人推荐房

	// 主题房间 5341-5360
	ErrTopicChannelCreateChannelLevelNotAllow         = -5341 // 您的等级未达到10级哦~请先到大厅挑挑吧~
	ErrTopicChannelCreateChannelTagRequired           = -5342 // 完善个人标签才能创建房间哟~
	ErrTopicChannelCreateChannelRecommendTypeNowAllow = -5343 // 推荐房暂时不支持发布到约玩大厅哦~
	ErrTopicChannelNotFound                           = -5344 // 主题房间不存在~
	ErrTopicChannelChannelLocked                      = -5345 // 房间已上锁，解锁房间后才能发起哦~
	ErrTopicChannelTabNotFound                        = -5346 // 未找到对应标签哦~
	ErrTopicChannelNameInvalid                        = -5347 // 房间名含有非法字符~
	ErrTopicChannelNameIsNull                         = -5348 // 房间名不能为空~
	ErrTopicChannelNameFormattedInvalid               = -5349 // 请输入15个字以内的有效房间名~
	ErrTopicChannelPermissionDenied                   = -5350 // 无权限操作
	ErrTopicChannelNameSensitive                      = -5351 // 房间名中包含敏感词，若多次发布包含敏感词的房间名，将面临封号危险
	ErrTopicChannelNameSensitiveForHighRisk           = -5352 // 服务器无响应，发布失败
	ErrTopicChannelCreateChannelGameTagRequired       = -5353 // 完善游戏卡才能创建房间哟~
	ErrTopicChannelSetRoomNameFormatInvalid           = -5354 // 设置房间名参数错误
	ErrTopicChannelNameNoExist                        = -5355 // 房间名不存在
	ErrTopicChannelNameConfigVersionInvalid           = -5356 // 房间名配置版本旧了
	ErrTopicChannelSetRoomNameVersionInvalid          = -5357 // 设置房间名版本号错误
	ErrTopicChannelNoPermissionChange                 = -5358 // 发布了游戏类房间需升级到最新版哟~快去升级吧~
	ErrTopicChannelCanNotUseTab                       = -5359 // 小可爱您好，为了给您提供更好的房间体验，非游戏房间暂不开放，我们正在积极优化升级敬请期待。
	ErrTopicChannelNotAllowModifyName                 = -5360 // 发布公开房间暂时不支持自定义房间名哟
	ErrTopicChannelReleaseTooManyTimes                = -5362 // 发布房间操作过于频繁，请明天再来吧
	ErrTopicChannelReleaseBorderOnLimit               = -5363 // 温馨提示：频繁发布房间可能会导致发布功能受限

	// 标签匹配 5361 - 5400
	ErrUsermatchMatchOverLimit = -5361 // 今天已经找了很多人了哟，先和他们聊聊吧~

	// 直播业务调整 5401 - 5440
	ErrExchangeLiveBroNoBegin         = -5401 // 现在不是可兑换的时间啊~
	ErrExchangeLiveBroNoLiveUser      = -5402 // 没有可兑换的直播收益~
	ErrExchangeLiveBroExchangeAlready = -5403 // 已经兑换过了~

	// 敲门  5441-5460
	ErrKnockDb               = -5441 // 敲门时数据库错误
	ErrKnockNotUidExist      = -5442 // 处理敲门时没有操作者uid信息
	ErrKnockWrongRoomType    = -5443 // 错误房间类型
	ErrKnockRedisFail        = -5444 // redis报错
	ErrKnockOneUserOneMinute = -5445 // 一分钟内对同一玩家请求数只能一次
	ErrKnockTimeOut          = -5446 // 同意拒绝超过timeout时间（10s）
	ErrKnockTicketTimeout    = -5447 // 敲门进房只有5分钟的有效期哦，已经过期请重新敲门
	ErrKnockTicketError      = -5448 // 敲门信息错误，不要伪装成别人进房哟
	ErrKnockPeopleInroom     = -5449 // 敲门者在房间内
	ErrKnockAdminEverHandle  = -5450 // 其它房管已处理

	// 用户拉黑 5461 - 5480
	ErrUserBlackListAddSelf            = -5461 // 无法拉黑自己
	ErrUserBlackListAddOfficialAccount = -5462 // 官方账号无法拉黑
	ErrUserBlackListWasAdded           = -5463 // 对方已在黑名单

	// 主播签约 5481 - 5530
	ErrContractApplyNoBindPhone        = -5481 // 你的账号未绑定未绑定手机号码，无法签约
	ErrContractApplyNoRealname         = -5482 // 你尚未完成实名认证，无法签约
	ErrContractApplyUnderAge           = -5483 // 你不满足签约年龄要求，无法签约
	ErrContractNonexist                = -5484 // 合约不存在
	ErrContractExist                   = -5485 // 合约已存在
	ErrContractApplyIdentityLimit      = -5486 // 身份证已经绑定合约
	ErrContractApplyHaveContract       = -5487 // 已经签约
	ErrContractApplyTodayLimit         = -5489 // 次日0点之后才可以重新签约哟
	ErrContractApplyAlready            = -5490 // 重复申请
	ErrContractHandleConflict          = -5491 // 已被其它公会签约
	ErrContractHandleTimeout           = -5492 // 超时的申请，已失效
	ErrContractHandleInvalid           = -5493 // 无效的申请
	ErrContractApplysignNonexist       = -5494 // 签约申请不存在
	ErrContractExtensionExist          = -5495 // 已邀请续约
	ErrContractExtensionNonexist       = -5496 // 续约不存在
	ErrContractApplyLimit              = -5497 // 申请次数超限
	ErrContractApplyIdentityApplyLimit = -5498 // 同一实名账号已申请
	ErrContractExtensionCannot         = -5499 // 未达到续约条件
	ErrContractYearSignLimit           = -5500 // 您本年度的签约次数已达上限，暂时无法签约新的公会
	ErrContractApplyIdentityTypeLimit  = -5501 // 您正在申请其他身份
	ErrContractApplyHaveIdentityType   = -5502 // 已拥有该身份身份

	// 砸蛋活动 5531 - 5540
	ErrSmashEggChanceNotEnough = -5531 // 魔力球不足
	ErrSmashEggReachDailyLimit = -5532 // 转转今日已达上限，明日再试试吧

	// 反垃圾/作弊/黑产 错误码 5541-5550
	ErrBlacklistCheckNotPass = -5541 // 账号异常，该功能不可用

	// 主题房间2 5551-5650
	ErrTopicChannelFreeze                            = -5551 // 您的账号处于禁言状态，暂不可以可进行此操作
	ErrTopicChannelQuickMatchVersionMiniGameRequired = -5552 // 发起速配失败，请升级至最新版本才可以匹配到小游戏房间哦
	ErrTopicChannelNameCanNotDiy                     = -5553 // 因系统升级，暂不支持修改房间名称，请重新选择房间名称发布

	// 首页开黑tab广告位 5670 - 5680
	ErrGetGangupAdvConfFailed     = -5670 // 获取开黑tab广告位配置失败
	ErrGetGangupAdvUserInfoFailed = -5671 // 获取开黑tab广告位用户信息失败
	ErrNonValidGangupAdvConf      = -5672 // 无有效开黑tab广告位配置

	// 小游戏 5681 - 5700
	ErrOpengameChangeGameTooOfen  = -5681 // 修改房间游戏太频繁
	ErrOpengameTemporaryPlayernum = -5682 // 小游戏匹配人数为0
	ErrMiniGameMaintain           = -5683 // 游戏维护中,请稍后匹配

	// 贵族体系 5701 - 5704
	ErrNobilityExclusiveMagicexpression = -5701 // 成为贵族即可使用该表情
	ErrNonNobiltiyExclusivePresent      = -5702 // 没有贵族专属礼物特权
	ErrNonNobiltiyNotPrivilege          = -5703 // 没有使用此特权权限
	ErrNobilitySensitiveTimeRange       = -5704 // 暂不能发送小喇叭消息
	ErrNobilityEqulLevel                = -5705 // 充值后等级小于等于原等级
	ErrNobilityInvisibleTakeHoldMic     = -5706 // 该用户不在房间内

	// 语音直播
	ErrChannelLiveNotAuthority     = -5780 // 没有语音直播权限
	ErrChannelLiveIdInvalid        = -5781 // 过期的直播ID
	ErrChannelLiveNotOpen          = -5782 // 主播还未开播哦
	ErrFansGroupNameNotStanderd    = -5783 // 名称设置不规范，请文明起名
	ErrFansGroupNameIsExist        = -5784 // 团名已被占用
	ErrFansGroupNameVerifying      = -5785 // 团名正在审核，审核结果将助手推送
	ErrFansGroupNameMemberCntLimit = -5786 // 粉丝团人数达到10可自定义团名
	ErrFansGroupNameFontCntLimit   = -5787 // 粉丝团名称最多3个字
	ErrFansSetGroupNameCntLimit    = -5788 // 团名暂不支持修改，需修改请联系官方

	// PK相关
	ErrChannelLivePkRepeatedApply  = -5789 // 不能重复申请
	ErrChannelLivePkIng            = -5790 // 对方正在PK
	ErrChannelLiveNotPkAuth        = -5791 // 暂无PK权限
	ErrChannelLiveCloseLimit       = -5792 // PK中不能结束直播哦~
	ErrChannelLivePkMatchInvalidTy = -5793 // 匹配类型跟服务端不一致

	// 打龙

	// 主题房渠道号相关
	ErrDistributorInvalid = -5800 // 无效渠道号

	// 蒙面聊天 5800 - 5810
	ErrMaskedCallClosing          = -5801 // 不在开放时间
	ErrMaskedCallNoMoreTicket     = -5802 // 匹配次数不足
	ErrMaskedCallTooMuchTipOff    = -5803 // 被举报次数过多
	ErrEnterOtherChannelInLive    = -5811 // 不允许在直播时进入其他房间
	ErrUserChannelPeopleOverlimit = -5812 // 该房间人数已达上限

	// usermodification 5820-5821
	ErrMaxModifySexLimit   = -5820 // 已修改过性别，不能再修改咯
	ErrOldVersionModifySex = -5821 // 当前版本不允许修改性别，请升级到最新版本

	// 房间切换玩法相关 5900 - 5990
	ErrTopicChannelNotAllowSwitchPlay          = -5900 // 该主题房不支持切换玩法
	ErrTopicChannelFrequentlySwitchPlay        = -5901 // 操作过于频繁
	ErrTopicChannelCannotSwitchPlayWhenRelease = -5902 // 主题房发布过程中不可以修改小队信息和玩法或重复发布哦
	ErrTopicChannelReleaseFreezing             = -5903 // 发布房间正在冷却中，请稍后再试
	ErrTopicChannelNotOwnerSwitchPlay          = -5904 // 房间正在发布中，仅房主可切换房间玩法
	ErrTopicChannelChangeFreezing              = -5905 // 修改主题房CD中

	// tab配置后台 -6000-6100
	ErrConfigTabDeleteWarn            = -6000 // 存在分类与主题绑定，不能删除
	ErrUpdateCategoryPlatformTypeWarn = -6001 // 分类的展示平台属性无法修改
	ErrConfigTabConflictWarn          = -6002 // 配置展示平台和分类冲突

	// 临时房 6010 - 6030
	ErrTempchannelInvalidChannelid = -6010 // 临时房id无效

	// 房间密码错误限制
	ErrChannelPwdErrLimit = -6031 // 错误操作频繁，24小时内您将无法再通过输入密码进入该房间

	// 礼物合成 6041 - 6050
	ErrConversionComposeGiftConfInvalid      = -6041 // 无效的合成礼物配置
	ErrConversionMaterialTotalPriceNotEnough = -6042 // 原料价值不足以合成目标礼物

	// 背包风控服务
	ErrRiskControlBackpackTbeanLimit       = -6080 // 风控限制,业务T豆余额不足
	ErrRiskControlBackpackCountLimit       = -6081 // 风控限制,包裹数量额度不足
	ErrRiskControlBackpackSignalCountLimit = -6082 // 风控限制，单次包裹数量限制
	ErrRiskControlBackpackSignalTbeanLimit = -6083 // 风控限制,单次T豆价值限制
	ErrRiskControlBackpackConfigNotFound   = -6084 // 找不到对应业务风控配置
	ErrRiskControlBackpackOrderidInvalid   = -6085 // 风控限制,订单格式错误
	ErrRiskControlBackpackBusinessNotFound = -6086 // 风控限制,找不到对应业务配置
	ErrRiskControlBackpackAuthCheckFail    = -6087 // 风控限制,密钥检查错误
	ErrRiskControlBackpackSysFail          = -6088 // 风控系统错误
	ErrRiskControlBackpackDuplicateOrderid = -6089 // 订单重复

	// 发奖平台风控
	ErrRiskControlAwardCenterOrderExist       = -6095 // 订单已存在
	ErrRiskControlAwardCenterOrderInvalid     = -6096 // 订单号格式有误
	ErrRiskControlAwardCenterSignalCountLimit = -6097 // 风控限制，单次发放数量限制
	ErrRiskControlAwardCenterConfigInvalid    = -6098 // 业务配置有误

	// 昵称头像上传推送
	ErrUserNicknameViolate  = -6110 // 用户昵称违规
	ErrUserProfileViolate   = -6111 // 用户头像违规
	ErrChannelNameViolate   = -6112 // 房间名违规
	ErrChannelProfilViolate = -6113 // 房间头像违规

	// 处罚禁止进入房间
	ErrBannedEnterChannel = -6120 // 禁止进房

	// 踢号失败，请重试
	ErrBannedKickUserFail = -6121 // 踢号失败，请重试

	// 房间小队 6130-6140
	ErrChannelTeamNotFount = -6130 // 数据不存在
	ErrChannelTeamToast    = -6131 // 处理信息有误

	// 游戏雷达相关
	ErrGameRadarConfigUpdated      = -6141 // 游戏雷达配置已更新，请重新获取
	ErrGameRadarGameCardIncomplete = -6142 // 游戏卡缺少必填信息无法打开雷达，请先将信息填写完整哦
	ErrGameRadarIncomplete         = -6143 // 雷达信息缺少必填选项，请先将信息填写完整哦
	ErrGameRadarTooManyCharacter   = -6144 // 字数太多啦
	ErrGameRadarQuickFailed        = -6145 // 无法快速开启雷达
	ErrGameRadarModelIncorrect     = -6146 // 无正确雷达游戏模式
	ErrGameRadarIsClosed           = -6147 // 雷达已按时关闭，点击再次开启
	ErrGameRadarInviteExceed       = -6148 // 你已经约玩很多人啦，耐心等待对方回应吧～
	ErrGameRadarIsAlreadyOpened    = -6149 // 雷达已经开启

	// 房间抽奖 6150-6160
	ErrChannelLotteryBeginFail      = -6150 // 房间抽奖开始失败
	ErrChannelLotteryTextErr        = -6151 // 敏感词
	ErrChannelLotteryTimeViolate    = -6152 // 时间不对
	ErrChannelLotteryJoinLotteryErr = -6153 // 参与抽奖失败
	ErrChannelLotteryInfoErr        = -6154 // 处理信息有误

	// 房间踢人
	ErrChannelKickoutNotInChannel = -6160 // 该用户不在房间

	// SYSTEM_ERR -10000   ([-20000, -10000] 错误码会归入系统失败调用统计)
	ErrSendMsgFriendVerifyFailed             = -10001
	ErrSendMsgWriteMsgFailed                 = -10002
	ErrSendMsgGetNickFailed                  = -10003
	ErrSendMsgGetUseraccountFailed           = -10004
	ErrFriendSystemErr                       = -10005
	ErrAccountSystemErr                      = -10006
	ErrSeqGenSystemErr                       = -10007
	ErrHeadImageSystemErr                    = -10008
	ErrCheckFriendSystemErr                  = -10009
	ErrTimelineSvrSystemErr                  = -10010
	ErrAttachmentSvrSystemErr                = -10011
	ErrGuildTimelineSvrSystemErr             = -10012
	ErrSearchSvrSystemErr                    = -10013
	ErrGuildSvrSystemErr                     = -10014
	ErrGameSvrSystemErr                      = -10015
	ErrAlbumSvrSystemErr                     = -10016
	ErrGiftpkgSvrSystemErr                   = -10017
	ErrCircleTimelinesvrErr                  = -10018
	ErrContainsSensitiveWord                 = -10019
	ErrCirclesvrErr                          = -10020
	ErrMissionTimelinesvrErr                 = -10021
	ErrAntisvrErr                            = -10022
	ErrPublicErr                             = -10023
	ErrGenerateTimelineErr                   = -10024
	ErrObsessionErr                          = -10025
	ErrSdkMsgErr                             = -10026
	ErrChannelImErr                          = -10027
	ErrEventcenterErr                        = -10028
	ErrCircletimelineErr                     = -10029
	ErrTbeanApiErr                           = -10030
	ErrPointsApiErr                          = -10031
	ErrCurrencyApiErr                        = -10032
	ErrCurrencyExchangeApiErr                = -10033
	ErrGuildTransNotFinish                   = -10034
	ErrChannelmicSvrErr                      = -10035
	ErrChannelolSvrErr                       = -10036
	ErrChannelHoldmicUserNotInCurrentChannel = -10037
	ErrAntispamSvrErr                        = -10038
	ErrUgcFriendshipSvrErr                   = -10039
	ErrAntispamlogicSvrErr                   = -10040
	ErrAntispamTimeoutErr                    = -10041
	ErrAntispamcxtsvrSvrError                = -10042
	ErrHttpUnmarshalError                    = -10050
	ErrHttpParaError                         = -10051
	ErrIsNotMasterError                      = -10052
) // status codes

var CodeMessageMap = map[int]string{
	Success:                                "成功",
	ErrSys:                                 "系统错误",
	ErrSystemBusy:                          "系统繁忙",
	ErrRequestTimeout:                      "处理超时",
	ErrBadRequest:                          "无效的请求",
	ErrTrafficAdmin:                        "流量管制",
	ErrTrafficFrequently:                   "请求太频繁",
	ErrParam:                               "参数错误",
	ErrDummyErrorCode:                      "该错误不应该显示给用户",
	ErrPureErrorCode:                       "该错误码不应该显示给用户",
	ErrNormalVerifyTooOftenErr:             "获取短信验证码太频繁",
	ErrChannelInGameNotSupportChangeMic:    "房间在游戏中，不支持上下麦",
	ErrOperateTooOften:                     "操作太频繁，频率限制",
	ErrTgameNoButtonAction:                 "不支持游戏中该按钮的点击回调",
	ErrTgameForbidImChatWithMicMate:        "该游戏不支持跟麦上成员进行im聊天",
	ErrTgameForbidSendPresentWhenGameStart: "游戏已开始，为了保证游戏公平性，暂时不能给Ta送礼哦",
	ErrTgameForbidSendMagicExpressionWhenGameStart:     "游戏已开始，为了保证游戏公平性，暂时不能发送魔法表情哦",
	ErrPlayTogetherAnotherStillExist:                   "与该用户存在另外一个 一起玩的邀请未失效",
	ErrPlayTogetherQuitChannelError:                    "退出当前房间失败",
	ErrPlayTogetherQuitChannelCancel:                   "拒绝退出当前房间",
	ErrFrescoRequestFail:                               "FRESCO下载图片失败",
	ErrFrescoRequestCancel:                             "FRESCO下载图片取",
	ErrDownloadResourceFail:                            "下载资源失败",
	ErrAccountPhoneExist:                               "该手机号码已被注册",
	ErrAccountUsernameExist:                            "该用户名已存在",
	ErrAccountNotExist:                                 "账号不存在",
	ErrAccountPasswordWrong:                            "密码或用户名错误",
	ErrAccountAliasModified:                            "账号已经修改过了",
	ErrAccountCompleted:                                "用户资料已经完善过了",
	ErrAccountNoHeadImage:                              "用户没有上传头像",
	ErrAccountVerifyCodeWrong:                          "验证码输入有误，请重新输入",
	ErrAccountPasswordSame:                             "新密码不能与旧密码相同",
	ErrAccountPhoneFormatWrong:                         "手机号码格式不对",
	ErrAccountUsernameFormatWrong:                      "用户名不能包含特殊字符",
	ErrAccountPermissionDenied:                         "权限不足",
	ErrAccountHasNoGuild:                               "用户未加入公会",
	ErrAccountVerifyTooFreq:                            "验证短信一段时间内只能下发一条， 请稍后再试",
	ErrAccountExist:                                    "账号已存在",
	ErrAccountMibaoNotSet:                              "用户未设置密保",
	ErrAccountMibaoAnswerErr:                           "密保答案错误",
	ErrAccountMibaoQuestionErr:                         "密保问题错误",
	ErrAccountNicknameSensitive:                        "你输入的昵称包含敏感词，修改失败",
	ErrAccountSignatureSensitive:                       "你输入的签名包含敏感词，修改失败",
	ErrAccountMibaoAlreadyInit:                         "用户已经设置过密保",
	ErrAccountResetClose:                               "注册失败，同一个手机号只能注册一个帐号",
	ErrAccountBanned:                                   "该帐号由于安全问题已被暂时冻结，请联系客服",
	ErrAccountRegFrequenceLimit:                        "同设备不能注册多个",
	ErrAccountOpenidExist:                              "Openid已存在",
	ErrAccountUserPhoneAlreadyBinded:                   "该手机号已经绑定到你的账号，无需再次绑定",
	ErrAccountUserBindedAnotherPhone:                   "你当前已经绑定到了另一个手机号，无法绑定",
	ErrAccountPhoneBindedToAnother:                     "该手机号已被绑定过，请更新到最新版本中绑定，在【我】-【关于TT语音】-【检查更新】中更新版本",
	ErrAccountRequireNewPassword:                       "需要设置一个密码",
	ErrAccountSetTaillightMedalidInvalid:               "你没有该勋章或已过期 不能将其设置为昵称后显示",
	ErrAccountMedalNotallowSettaillight:                "昵称后不能显示该类型的勋章",
	ErrAccountWatcherPermissionLimit:                   "权限不足",
	ErrAccountToLoginCaptcha:                           "需要登录验证",
	ErrAccountVerifyLoginCaptcha:                       "登录验证码错误",
	ErrAccountNoNeedCaptcha:                            "不需要图片验证码",
	ErrAccountBanRegFrequenceLimit:                     "该帐号由于安全问题已被限制注册，请联系客服",
	ErrAccountBanAuthFrequenceLimit:                    "该帐号由于安全问题已被限制登录，请联系客服",
	ErrAccountInvalid:                                  "账号异常",
	ErrAccountRegIpFrequenceLimit:                      "注册帐号IP频率限制",
	ErrAccountSearchNewRegContactFrequenceLimit:        "由于安全问题已被限制操作，请联系客服",
	ErrAccountSmsThreshholdExceed:                      "发送短信过多，请明天再试",
	ErrAccountNoBindedPhoneToUser:                      "该账号未绑定任何手机号",
	ErrAccountRebindVerifiedPhoneInvalid:               "已绑定的手机验证失败",
	ErrAccountThirdpartyDetachDisabled:                 "该功能已经下线",
	ErrAccountPhoneFormatBanned:                        "手机号码格式不对",
	ErrAccountRegLimitedByClientVersion:                "请升级客户端至最新版",
	ErrAccountThirdpartyIdNotDetachable:                "该账号无法进行解绑",
	ErrAccountDeviceFormatBanned:                       "设备异常",
	ErrAccountLoginNeedBindPhone:                       "该账号需要绑定手机号才能登陆，请升级客户端版本至最新",
	ErrAccountModifyPwdNeedVerifycode:                  "修改密码需要进行手机验证，请升级客户端版本至最新",
	ErrAccountDeviceUnusual:                            "您的版本过旧，为确保账号安全，需更新为最新版才可继续使用",
	ErrAccountDiffAccTryPwdTooMuch:                     "由于该设备登陆密码错误次数过多，该设备5小时内不能登陆",
	ErrAccountSameAccTryPwdTooMuch:                     "由于之前一段时间该账户密码错误次数超过5次，该账号5小时内不能登陆",
	ErrAccountPwdNullErr:                               "未设置密码",
	ErrAccountBannedForever:                            "该帐号已被冻结",
	ErrAccountLoginNxLimit:                             "请勿尝试同时登录多设备",
	ErrAccountRegNeedBindPhone:                         "需要绑定手机号才能注册，请升级客户端版本至最新",
	ErrAccountLoginNeedBindRealnameauthPhone:           "根据国家相关政策要求,登录需要填写手机号，请升级客户端版本至最新",
	ErrAccountUnregister:                               "用户已注销",
	ErrAccountLoginBindRealnameauthPhoneLimit:          "此手机号已认证十个账号，请使用其它手机号进行认证",
	ErrAccountBindLoginPhoneIsExpected:                 "此手机号未绑定至任何账户，请重试并绑定为登录手机",
	ErrAccountBindSecurePhoneIsExpected:                "此手机号已经绑定至其他账户，请重试并绑定为安全手机",
	ErrAccountBindSecurePhoneLimit:                     "此手机号已被10个账号绑定，请使用其它手机号操作",
	ErrAccountRegBannedByPolicy:                        "您的手机号码或IP所属区域暂不支持注册",
	ErrAccountBannedWithInfo:                           "该帐号由于安全问题已被暂时冻结，请联系客服",
	ErrSendMsgDownlAttachmentExceed:                    "附件已失效",
	ErrSendMsgNotGroupMember:                           "你不在该群，发送失败",
	ErrSendMsgParasErr:                                 "发消息参数不对",
	ErrSendMsgMute:                                     "你被禁言了，发送失败",
	ErrSendMsgAllMute:                                  "全员禁言中，只有管理员可以发言",
	ErrSendMsgGuildLimit:                               "客官你今天公会内的自由聊天人数已经用完，想要继续搭讪请先添加玩伴吧~",
	ErrSendMsgQuitGuildFar:                             "对不起，已超出挽回聊天有效时间，发起会话失败。",
	ErrSendMsgAtEveryoneNotAllow:                       "该用户没有发送@全体消息的权限",
	ErrSendMsgAtEveryoneTimesToomuch:                   "该用户发送@全体消息的次数超过限制",
	ErrSendMsgStrangerTimesToomuch:                     "对方未回复前只能发送5条消息哦~",
	ErrSendMsgStrangerTargetsToomuch:                   "你今天发起的聊天有点多哦~先和刚认识的小伙伴聊聊吧",
	ErrSendMsgStrangerUnusualAccount:                   "你的账号异常，无法和陌生人打招呼呢~",
	ErrSendMsgStrangerIpTargetsToomuch:                 "你的IP已达到陌生人打招呼次数上限",
	ErrSendMsgCantTalkWithGmHelper:                     "你不是会长，不能和会长小秘聊啦~",
	ErrSendMsgIsBlockByTarget:                          "发送失败，对方已拒收",
	ErrSendMsgBlockTarget:                              "你已拉黑对方，对方无法接收你的信息",
	ErrSendMsgSensitive:                                "发送失败，不符合平台管理规则",
	ErrUserAntispamAccountBannedPost:                   "您的账号目前处于禁言期，请稍后再尝试",
	ErrSendMsgStrangerTimesToomuchTimeLimit:            "发送失败，你最近发起聊天过于频繁，请稍后再试",
	ErrSendMsgStrangerTargetRevToomuch:                 "发送失败~今天有很多人找Ta聊天啦~",
	ErrSendMsgStrangerReportedToomany:                  "由于你被频繁举报，账号存在异常，60分钟内不能发起私聊，如有疑问，请联系客服",
	ErrSendMsgTooManyUsersChat:                         "发送失败，今天有很多用户找ta聊天啦",
	ErrSendMsgMasterapprenticeYoualreadybound:          "你已经和对方绑定啦～",
	ErrSendMsgMasterapprenticeUpperlimit:               "发送失败，你绑定的徒弟数已达到上限啦",
	ErrSendMsgMasterapprenticeAccountabnormal:          "对方账号异常，不能绑定为徒弟哟",
	ErrSendMsgMasterapprenticeAccountnotsatisfied:      "对方不满足收徒条件，不能绑定为徒弟，您可到<徒弟大厅>中收徒",
	ErrSendMsgMasterapprenticeTargetalreadybound:       "绑定失败，对方已被绑定其他师父啦，您可到<徒弟大厅>中收徒",
	ErrSendMsgMasterapprenticeIndev:                    "此功能维护中，敬请期待",
	ErrSendMsgMasterapprenticeAlreadyfollow:            "你已经关注Ta啦",
	ErrSendMsgMasterapprenticeEntranceclosed:           "入口已关闭",
	ErrSendMsgMasterapprenticeAlreadyinvited:           "你刚刚已邀请过Ta啦",
	ErrSendMsgMasterapprenticeAlreadyapprentice:        "已成功收Ta为徒啦，快去完成每日互动任务吧",
	ErrSendMsgMasterapprenticeOver:                     "五日师徒任务已完成，Ta已经出师啦，不能重复收徒哦",
	ErrSendMsgMasterapprenticeTerminal:                 "你们没有连续完成每日互动任务，师徒关系已解绑，不能再次收Ta为徒哦",
	ErrSendMsgMasterapprenticeNotnewdevice:             "对方不是新设备，不能绑定为徒弟哦",
	ErrFriendTargetNotFriend:                           "对方还不是你的好友",
	ErrFriendLimit:                                     "客官今天发出了很多添加玩伴的请求了，添加玩伴的体力用完了哦~",
	ErrFriendKefuLimit:                                 "客服帐号不能加好友",
	ErrFriendGonghuiKefuLimit:                          "公会客服帐号不能加好友",
	ErrFriendBaned:                                     "由于对方的设置，你不能添加对方为玩伴",
	ErrFriendTotalSizeLimit:                            "你的好友数量已达到500人上限",
	ErrFriendTargetTotalSizeLimit:                      "对方的好友数量已达到500人上限",
	ErrFriendIpLimit:                                   "客官你的IP今天发出了很多添加玩伴的请求了，添加玩伴的体力用完了哦~",
	ErrGuildNameExist:                                  "会长大人，该名字已经被抢注，赶紧换个更霸气的名字吧。",
	ErrGuildNotExist:                                   "公会不存在",
	ErrGuildShortIdSet:                                 "该公会已经设置过短号，不能重新设置",
	ErrGuildShortIdExist:                               "该短号已被使用",
	ErrGuildUserHave:                                   "用户已加入公会了",
	ErrGuildNoPermission:                               "无权限操作",
	ErrGuildApplyExist:                                 "用户已发起加入公会的申请",
	ErrGuildApplyHaveReviewed:                          "已被其他管理员审核通过",
	ErrGuildApplyHaveConfirmed:                         "此申请已经确认通过了",
	ErrGuildMemberNotExist:                             "非公会成员",
	ErrGuildApplyNotExist:                              "用户未发起对加入公会的申请",
	ErrGroupMemberNotExist:                             "不是群成员",
	ErrGroupMemberExist:                                "已经是群成员",
	ErrGroupNotExist:                                   "群不存在",
	ErrGroupOwnerCannotBeRemoved:                       "群主不能退群",
	ErrGuildOwnerCannotBeRemoved:                       "会长不能退出公会",
	ErrGroupCannotJoin:                                 "此群禁止任何人加入",
	ErrGroupApplyExist:                                 "用户已发起加入群的申请",
	ErrGroupApplyNotExist:                              "用户未发起对加入群的申请",
	ErrGroupApplyHaveReviewed:                          "已被其他管理员审核通过",
	ErrGroupUserHaveBeenOwner:                          "用户已经是群主",
	ErrGuildAddGameExist:                               "该游戏已经是群游戏了",
	ErrGroupDeleteMemberSelf:                           "踢出群用户列表中包含自己",
	ErrGuildDeleteMemberSelf:                           "踢出公会用户列表中包含自己",
	ErrGroupMuteMemberSelf:                             "禁言用户列表不能包含自己",
	ErrGroupUnmuteMemberSelf:                           "恢复发言用户列表不能包含自己",
	ErrGroupCannotExitGuildMainGroup:                   "不能退出公会总群",
	ErrGroupParamErr:                                   "参数错误",
	ErrGuildGameNotExist:                               "非公会主玩游戏",
	ErrGroupUserNotOwner:                               "此用户不是群主",
	ErrGuildCantDismissMemberToMuch:                    "解散公会，必须公会成员总数少于10人",
	ErrGuildAlreadyJoin:                                "用户已经加入公会",
	ErrGuildSetGameUrlInvalid:                          "设置游戏链接，URL不正确",
	ErrGuildIdNotMatch:                                 "公会id不正确",
	ErrGuildSetGameUrlCantReach:                        "已设置成功，但您设置的链接可能无法访问，请确认哦。",
	ErrGuildSetGameUrlNotApk:                           "已设置成功，但该链接可能不是安卓游戏下载链接，请确认哦。",
	ErrGuildApplyUserJoinOtherGuild:                    "申请已失效，或用户已加入其它公会",
	ErrGuildMainGroupNoOwner:                           "总群不能设置群主",
	ErrGuildPunishPinNoPermission:                      "无权限发布图钉广播",
	ErrGuildNameSensitive:                              "你输入的公会名包含敏感词，修改失败",
	ErrGuildGroupNameSensitive:                         "你输入的群名称包含敏感词，修改失败",
	ErrGuildDescriptionSensitive:                       "你输入的群简介包含敏感词，修改失败",
	ErrGuildMemberCardSensitive:                        "你输入的群名片包含敏感词，修改失败",
	ErrGuildNameContainsGameName:                       "你输入的公会名称中包含游戏名，无法创建",
	ErrGuildNameAllDigit:                               "无法使用纯数字作为公会名",
	ErrGuildHadOnceJoinGuild:                           "快速加入失败",
	ErrGuildAddGameExceedLimit:                         "公会游戏已达上限",
	ErrGuildNameNotMatch:                               "公会名称不对",
	ErrGuildNameMapErr:                                 "公会名称映射错误",
	ErrGroupMemberCountLimit:                           "临时群已满员",
	ErrGuildNameInvalid:                                "公会名称含有非法字符",
	ErrGuildUserHavebeenHighadminlevel:                 "目标用户已经被设置为更高权限",
	ErrGroupMemberCardOffline:                          "修改群名片失败",
	ErrGroupAddmemberLimit:                             "每次拉人加群不能超过50人上限",
	ErrGroupApplyExceed:                                "该入群申请已过期",
	ErrGuildCreateFrequenceLimit:                       "当天不能创建多个公会",
	ErrGuildNotCheckin:                                 "今天未签到",
	ErrGuildCheckinSupplement:                          "没有补签天数",
	ErrGroupDismissed:                                  "该群已被解散",
	ErrGuildRefuseQuitJointimeShort:                    "加入公会满30分钟后，才可以退出公会哦~~ 在公会里多玩一会儿吧，小伙伴们舍不得你走呢",
	ErrGuildAlreadyDonate:                              "今天已捐献",
	ErrGuildExtraGameExceedLimit:                       "额外扩充游戏数量已达上限，公会等级提升后可扩充更多数量",
	ErrGuildContributionNotEnough:                      "公会可用贡献值不足",
	ErrGuildUserIsOfficial:                             "用户已经是官员",
	ErrGuildUserNotOfficial:                            "用户不是官员",
	ErrGuildOfficialNotExist:                           "职位不存在",
	ErrGuildStoragePNotShelve:                          "商品还没上架",
	ErrGuildStoragePSoldout:                            "商品已经卖完",
	ErrGuildStoragePHadModify:                          "商品价格已发生变化，请刷新再试",
	ErrGuildStoragePTotalError:                         "商品的总数与礼物列表不匹配",
	ErrGuildStorageHasNotExamineRecord:                 "找不到审查记录",
	ErrGuildStorageAddPError:                           "添加商品失败",
	ErrGuildDeductMemContributionFail:                  "扣除个人贡献失败",
	ErrGuildReturnMemContributionFail:                  "返回个人贡献失败",
	ErrGuildStorageAddGiftBoxFail:                      "放入个人宝箱失败",
	ErrGuildStoragePNotEnough:                          "商品数量不足，快去【礼包中心】补货吧~",
	ErrGuildStoragePUserCanNotGain:                     "你还没有领取资格",
	ErrGuildAlreadyCheckIn:                             "今天已签到",
	ErrGuildStoragePAllotInvalid:                       "分配操作无效",
	ErrGuildStoragePPassDu:                             "领取失败，礼包领取尚未开始或者已经过期",
	ErrGuildMemberCardGuildLimit:                       "该成员已退出公会",
	ErrGuildPrefixIsTooLong:                            "马甲长度超过限制",
	ErrGuildMemberOptCdUid:                             "用户已达到当天操作次数上限",
	ErrGuildMemberOptCdIp:                              "IP已达到本时段内操作次数上限",
	ErrGuildMemberOptCdDevice:                          "设备已达到当天操作次数上限",
	ErrGuildOfficialExist:                              "职位已经存在",
	ErrGuildSpiltGiftCardValueOverLimit:                "切分的礼品卡单张面额超过了限制",
	ErrGuildStorageVoucherPermission:                   "此商品只有会长才能操作",
	ErrGuildStoragePNotExist:                           "商品不存在",
	ErrGuildStoragePDeleteConditionFail:                "只能删除已过期的商品",
	ErrGuildGiftCardBalanceNotEnough:                   "礼品卡余额不足",
	ErrGuildGiftCardCantNotUnshelve:                    "本版本不支持代金券下架操作，请先检查升级",
	ErrGuildGiftCardFoundNot:                           "找不到礼品卡",
	ErrGuildGiftCardStatus:                             "礼品卡状态异常",
	ErrGuildGiftCardRemoveFail:                         "只能删除过期和状态异常的礼品卡",
	ErrGuildGiftCardRepeat:                             "该代金券每个用户只能领取一次",
	ErrGameNotExist:                                    "游戏不存在",
	ErrGuildOfficialMemberReachLimit:                   "您只能设置50个公会管理",
	ErrGuildMemberReachLimit:                           "公会成员已经达上限，无法加入",
	ErrGuildModifyPrefixLimit:                          "30分钟内只能修改一次成员马甲，请稍候再试。",
	ErrGuildSensitive:                                  "输入的内容包含敏感词，修改失败",
	ErrAuthAutoLoginAlreadyOnline:                      "该帐号已在其它设备登录，请手动登录",
	ErrUploadFaceAccountTypeErr:                        "上传头像，帐号类型错误",
	ErrAuthAutoLoginFailed:                             "自动登录失败，请手动登录",
	ErrThirdPartyAccessTokenErr:                        "第三方账号token验证失败",
	ErrImageInvalidErr:                                 "图片涉嫌违规",
	ErrChinaMobileAccessTokenErr:                       "中国移动一键登录token验证失败",
	ErrChinaMobileNotfindPhone:                         "中国移动一键登录没有匹配到手机号",
	ErrChinaUnicomAccessTokenErr:                       "中国联通一键登录token验证失败",
	ErrChinaUnicomNotfindPhone:                         "中国联通一键登录没有匹配到手机号",
	ErrChuangLanAccessTokenErr:                         "创蓝token验证失败",
	ErrChuangLanNotfindPhone:                           "创蓝没有匹配到手机号",
	ErrAlbumNotExist:                                   "相册不存在",
	ErrPhotoNotExist:                                   "相片不存在",
	ErrAlbumDefaultAlbumCantDelete:                     "默认相册不允许删除",
	ErrNoPermissionDeleteAlbum:                         "你没有权限删除该相册",
	ErrNoPermissionDeletePhoto:                         "你没有权限删除该照片",
	ErrAlbumNoPermission:                               "没权限修改相册",
	ErrGiftpkgGuildPkgNotEnough:                        "公会礼包不足",
	ErrGiftpkgPkgNotFound:                              "找不到这种礼包",
	ErrGiftpkgAlreadyApplying:                          "这种礼包公会已经在申请中",
	ErrGiftpkgApplyidDoneOrNotExist:                    "该申请已经处理过，或者不存在",
	ErrGiftpkgApplyidNotExist:                          "该申请不存在",
	ErrGiftpkgApplyidAlreadyDone:                       "该申请已处理过",
	ErrGiftpkgPkgNotEnough:                             "后台礼包不足",
	ErrGiftpkgApplyNoPermission:                        "无权限申请礼包",
	ErrGiftpkgUserHadFetedThisPkg:                      "已经领取过这种礼包",
	ErrGiftpkgTaohaoNoUsableId:                         "淘号，已经没有可用的序列号了",
	ErrGiftpkgRedPkgEmpty:                              "红包已经被领完了",
	ErrGiftpkgStorageSerialNotEnough:                   "仓库序列号不足",
	ErrGiftpkgApplyHasNoThisGame:                       "公会没有这款游戏，不能申请该礼包",
	ErrGiftpkgNotFoundRedPkg:                           "找不到该红包",
	ErrGiftpkgRedPkgTargetErr:                          "红包只能发到公会群或者公会好友",
	ErrGiftpkgRedPkgGuildErr:                           "你不是该公会的成员，不能领取该公会的红包",
	ErrGiftpkgAlreadyFetchRedpkg:                       "已经领取过这个红包",
	ErrGiftpkgNotGuildOwner:                            "用户不是会长",
	ErrGiftpkgPkgNotUsed:                               "该礼包已经下架",
	ErrGiftpkgApplyMemberNotEnough:                     "该款礼包需要公会人数达到5人以上才能申请，你的公会人数不够，赶紧邀请更多的小伙伴加入你的公会吧!",
	ErrGiftpkgUserHadFetchedToday:                      "你今天领礼包次数已达到上限，请明天签到后再来吧",
	ErrGiftpkgUserNotCheckinToday:                      "公会签到后才可以领礼包哦",
	ErrGiftpkgDeviceHadFetchedToday:                    "你今天领礼包次数已达到上限，请明天签到后再来吧",
	ErrGiftpkgApplyCdNotReady:                          "申请过于频繁，请稍后再试",
	ErrGiftpkgExceedFetchFailed:                        "领取失败，该礼包已过期",
	ErrGiftpkgPriceUpdated:                             "礼包价格已更新",
	ErrVerifycodeForSensitiveOp:                        "敏感操作需要输入短信验证码，请更新至最新版本进行操作",
	ErrNoPhoneBind:                                     "该操作需要绑定手机",
	ErrVerifycodeSessionChanged:                        "登录信息变化,请重新获取验证码",
	ErrVerifycodeNoCode:                                "验证码未生成,请重新获取验证码",
	ErrVerifycodeWrongCode:                             "验证码不正确,请重新输入",
	ErrGiftpkgDevHadFetedThisPkg:                       "设备已经领取过这种礼包",
	ErrVerifycodeParamErr:                              "验证码操作的请求参数有误",
	ErrNeedPhoneBind:                                   "该操作需要绑定手机,请到【我】-【隐私与安全】中绑定手机或者升级客户端",
	ErrVerifycodeWrongCodeToomuch:                      "验证码错误过多, 请重新获取验证码",
	ErrVerifycodeExceedLimit:                           "今日接收验证码次数已达上限",
	ErrCircleNotFound:                                  "游戏圈不存在",
	ErrCircleJoinAlreadyJoined:                         "已经加入了游戏圈",
	ErrCircleTopicDeleted:                              "该主题已被删除",
	ErrCircleTopicNotExists:                            "该主题不存在",
	ErrCircleCommentDeleted:                            "该评论已被删除",
	ErrCircleCommentNotExists:                          "该评论不存在",
	ErrCircleLikeAlreadyLiked:                          "你已经点过赞了",
	ErrCircleLikeNotExists:                             "还未赞过",
	ErrCircleNotCreator:                                "该操作只允许作者进行",
	ErrCircleTopicTitleSensitive:                       "你输入的标题包含敏感词，发布失败",
	ErrCircleTopicContentSensitive:                     "你输入的内容包含敏感词，发布失败",
	ErrCircleCommentSensitive:                          "你输入的内容包含敏感词，发布失败",
	ErrCircleNoPermissionMute:                          "无权限禁言",
	ErrCirclePostTopicCoolingdown:                      "你的操作过于频繁, 请稍后再试",
	ErrCirclePostCommentCoolingdown:                    "你的操作过于频繁, 请稍后再试",
	ErrCirclePostSimilarTopic:                          "你的操作过于频繁, 请稍后再试",
	ErrCircleHaveNotJoinCircle:                         "未加入圈子",
	ErrCircleCanNotQuitActivity:                        "无法退出活动圈",
	ErrCircleNoPermissionHightlight:                    "无权限加精",
	ErrCircleNoPermissionCancleHightlight:              "无权限删精",
	ErrCircleNoPermissionDelTopicComment:               "无权限删评论",
	ErrCircleUserMuteComment:                           "你已被禁言，暂时无法发送评论",
	ErrCircleUserMuteTopic:                             "你已被禁言，暂时无法发送主题",
	ErrCircleContentLenOverlimit:                       "你发的内容太多了，伦家受不了呐",
	ErrCircleTitleLenOverlimit:                         "你标题都这么长，吓死我了，嘤嘤嘤~~~",
	ErrCircleAnnouncementSendTopic:                     "公告圈禁止发主题",
	ErrCircleAnnouncementQuit:                          "禁止退出公告圈",
	ErrCircleTopicPictureSensitive:                     "主题图片包含敏感信息",
	ErrCircleAnnouncementJoin:                          "禁止关注公告圈",
	ErrCircleAnnouncementVersion:                       "当前版本不支持该操作，请更新版本",
	ErrCircleTopicNotDeleted:                           "该主题未被删除",
	ErrCircleIsNowReadonly:                             "T^T游戏圈帖子只支持查看，不支持发帖、点赞、评论等操作哟",
	ErrRecruitNotExist:                                 "招募不存在或者已经结束",
	ErrRecruitPostContentSensitive:                     "招募内容包含敏感词汇",
	ErrRecruitPostReddiamonNotEnough:                   "红钻不够 不能招募",
	ErrRecruitPostGuildAlreadyPost:                     "该公会在这个游戏发过招募还没有结束",
	ErrRecruitGuildNotUsercurrent:                      "您已经不在创建该招募的公会了",
	ErrRecruitSupportAlreadyDo:                         "每天只能为自己公会顶一次",
	ErrRecruitOpNoPermission:                           "没有操作权限",
	ErrRecruitPostGuildcontrbutionNotEnough:            "贡献值不够 不能招募",
	ErrGrowUserMissionCollected:                        "奖励已领取",
	ErrGrowCurrencyAdded:                               "用户红钻已经添加，不能重复添加",
	ErrGrowCurrencyNotEnough:                           "你的红钻数量不足，赶紧去做任务领红钻吧～",
	ErrGrowMissionAccepted:                             "任务已接受",
	ErrGrowMissionUnacceptable:                         "任务不可接受",
	ErrReddiamondNotEnoughInStock:                      "红钻池红钻不足",
	ErrTimeLimitMissionConfigNotExist:                  "限时任务不存在",
	ErrTimeLimitMissionEventNotExist:                   "限时任务类型不存在",
	ErrTimeLimitMissionConfigParam:                     "限时任务配置参数异常",
	ErrTimeLimitMissionAcceptLimit:                     "该任务的领取名额已满",
	ErrTimeLimitUserMissionNotExist:                    "用户限时任务不存在",
	ErrTimeLimitUserMissionAccepted:                    "用户限时任务已接受",
	ErrMedalUserMedalOrderExists:                       "发勋章订单重复",
	ErrPushMessageNotExists:                            "推送信息不存在",
	ErrPushMessageReviewed:                             "消息之前已经审核过了",
	ErrPushMessageRefused:                              "消息已经被拒绝",
	ErrPushMessageDeleted:                              "消息已被删除",
	ErrPushParamError:                                  "推送参数错误",
	ErrPushWithoutBroadcast:                            "无推送筛选条件，如需广播请指定广播模式",
	ErrPublicCanNotUnsubSystemAccount:                  "无法取消关注该公众号",
	ErrSessionRoomNotExists:                            "房间不存在",
	ErrSessionRoomUserInOtherRoom:                      "用户已经在其它房间",
	ErrSessionRoomUserNotInRoom:                        "用户不在此房间内",
	ErrSessionRoomUserNotInAnyRoom:                     "用户不在任何房间",
	ErrSession1v1NotFriend:                             "和对方不是好友",
	ErrSessionCallinWaitCoolDown:                       "召集令冷却中",
	ErrSessionGroupNoCallin:                            "这个群当前没有召集令",
	ErrSessionAlreadyAcceptCallin:                      "用户已经接受了召集了",
	ErrSessionCallinNotEnd:                             "召集令尚未结束",
	ErrSessionCallinTooFrequency:                       "发起召集令过于频繁",
	ErrSessionRoomUserExceed:                           "当前开黑人数已满",
	ErrTokenBadToken:                                   "token无效或者已经过期, 请重新登录",
	ErrTokenIsRequired:                                 "token无效, 请重新登录",
	ErrTokenWasExpired:                                 "token已过期, 请重新登录",
	ErrTokenInvalidRefreshToken:                        "无效的refresh token",
	ErrTgroupCreateLimitExceed:                         "用户创建的兴趣群数量已经超过上限",
	ErrTgroupInvalidTgroupAccount:                      "无效的群帐号",
	ErrTgroupNoPermission:                              "无权限操作",
	ErrTgroupOwnerCanntQuit:                            "群主不允许退群",
	ErrTgroupCreateNeedInviteFriend:                    "需要邀请一个好友才能创建群组",
	ErrTgroupTargetInvalid:                             "操作对象错误",
	ErrTgroupDisplayIdNotExist:                         "群不存在",
	ErrTgroupJoinGroupLimit:                            "用户加入的兴趣群过多",
	ErrTgroupInvalidInvite:                             "非法的邀请",
	ErrTgroupSensitive:                                 "输入的内容包含敏感词，修改失败",
	ErrSessionNotExist:                                 "会话不存在",
	ErrSessionExpired:                                  "会话过期",
	ErrPresInvalidProxyAddress:                         "错误的proxy地址 ",
	ErrMallNoProduct:                                   "找不到商品",
	ErrMallItemTypeMismatch:                            "物品类型不匹配",
	ErrMallInvalidCategory:                             "无效的Category",
	ErrMallInvalidItemType:                             "无效的物品类型",
	ErrMallNoEnoughStorage:                             "库存不足",
	ErrMallActivityTimeOverlappedWithAnother:           "活动与另一个活动冲突",
	ErrMallNoSuchActivity:                              "找不到相应的活动",
	ErrMallActivityCanNotBeUpdated:                     "活动当前无法被修改",
	ErrMallInvalidConfig:                               "活动配置有问题",
	ErrMallCurrentActivityNone:                         "当前没有活动",
	ErrMallNotCurrentActivity:                          "非当前活动",
	ErrMallActivityNotOpen:                             "活动还未开始",
	ErrMallActivityClosed:                              "活动已经结束",
	ErrMallNoSuchProductItem:                           "找不到相应的物品",
	ErrMallCurrentActivityInProgress:                   "当前有活动在进行",
	ErrMallFailedToParseProductItem:                    "无法解析物品数据",
	ErrMallUserHadMedal:                                "你已经拥有该勋章",
	ErrMallUserHaveAlreadyPurchasedProduct:             "你已经在本次活动中购买过该商品了",
	ErrChannelNameLenError:                             "房间名称长度不正确",
	ErrChannelBindtypeInvalid:                          "创建的房间类型不匹配",
	ErrChannelNotExist:                                 "房间已经被删除",
	ErrChannelMicrophoneOverlimit:                      "上麦人数达到上限",
	ErrChannelNameSensitive:                            "不能使用敏感词作为房间名",
	ErrChannelNameAllDigit:                             "无法使用纯数字作为房间名",
	ErrChannelBindGuildNotMember:                       "用户不是该房间所属公会成员",
	ErrChannelNoPermission:                             "权限不足",
	ErrChannelMemberOverlimit:                          "房间内成员数目达到上限",
	ErrChannelGuildChannelSizeOverlimit:                "公会内开黑房间总数达到上限",
	ErrChannelUserAlreadySetMuted:                      "用户已经被禁言",
	ErrChannelUserAlreadyHoldMicrophone:                "用户已经在麦上",
	ErrChannelUserChannelMuted:                         "你已经被禁言",
	ErrChannelUserNotJoinAnyChannel:                    "用户没有加入任何房间",
	ErrChannelUserNotJoinThisChannel:                   "用户不是该房间成员,请尝试退出房间后重新进入",
	ErrChannelMicrophoneEntryAlreadyDisable:            "麦位已经被关闭",
	ErrChannelMicrophoneEntryAlreadyEnabled:            "麦位已经被启用",
	ErrChannelInLockScreenStat:                         "房间公屏处于锁定状态 不允许发公屏消息",
	ErrChannelUserNotHoldMicrophone:                    "用户没有上麦",
	ErrChannelMicmodeNotsupportOper:                    "房间当前模式不支持该操作",
	ErrChannelTopicinfoSensitive:                       "房间话题包含敏感词",
	ErrChannelMicrophoneDisable:                        "麦位已经被管理员禁用",
	ErrChannelUserMicrophoneKickPunishTime:             "你被管理员设为旁听 暂时无法上麦",
	ErrChannelUserChannelKickPunishTime:                "你被管理员踢出房间 暂时无法进入",
	ErrChannelMuteTargetHaveduty:                       "用户是公会管理员 不能被禁言",
	ErrChannelMicrophoneMute:                           "你已经被禁言 暂时无法上麦",
	ErrChannelUnmuteTargetHaveduty:                     "普通管理员没有权限对其他管理员解除禁言",
	ErrChannelPwdWrong:                                 "该房间已经上锁，当前版本不支持房间密码输入，请更新APP",
	ErrChannelMicModeNotSupport:                        "该房间处于当前版本不支持的模式下,请将应用升级到最新版本",
	ErrChannelMusicNotHoldMic:                          "上麦后才能播放背景音乐哦",
	ErrChannelSearchTimeOverlimit:                      "哎呀您慢点啊~~ 手速这么快~~伦家受不了的哟",
	ErrChannelMicModeAlreadySet:                        "您已经处于该模式下了哦",
	ErrChannelBindtypeClientNotSupport:                 "您当前版本不支持进入该类型房间,请升级到最新版本",
	ErrChannelOperPermissonUnderTarget:                 "这里的人等级都好高～伦家打不过啦",
	ErrChannelMusicKuwoApiErr:                          "战歌服务正在努力修复中",
	ErrChannelPwdInvalid:                               "输入的房间密码无效",
	ErrChannelMsgAttachmentExpire:                      "数据已经过期",
	ErrChannelMsgAttachmentFormatInvalid:               "房间不支持发送该格式数据或者数据大小超过限制",
	ErrChannelConveneCdLmt:                             "房间召集CD中",
	ErrChannelConvening:                                "房间正在召集",
	ErrChannelNotConvening:                             "当前房间没有召集哦",
	ErrChannelMemberConfirmStatus:                      "响应召集失败",
	ErrNoUserChannelConvene:                            "没有在召集的房间哦",
	ErrChannelMicrSpaceIdInvalid:                       "麦位信息有误",
	ErrChannelMicrSpaceInfoEmpty:                       "找不到麦位数据",
	ErrChannelMicrSpaceNotallowHold:                    "该麦位被关闭或麦上有人",
	ErrChannelMicModeChangeFromFun:                     "娱乐房人数超过500人时，不能切换为开黑房哦",
	ErrChannelPersonalAdminCntLimit:                    "管理员数量已经达到上限",
	ErrChannelTagIdIsReserved:                          "该标签指暂不允许设置",
	ErrChannelCollectNumOverLimit:                      "你收藏的房间太多了",
	ErrChannelTrafficAdmin:                             "房间当前人数过多，请稍后再试",
	ErrChannelSetBackgroundFail:                        "房间背景设置失败",
	ErrChannelCollectGuildOwner:                        "你是会长，不能取消收藏哦",
	ErrChannelCollectManager:                           "你是房间管理，不能取消收藏哦",
	ErrChannelMusicListEmpty:                           "歌单为空",
	ErrChannelMusicCountLimit:                          "已经达到歌单上限",
	ErrChannelMusicCannotShare:                         "房间当前禁止分享音乐",
	ErrChannelMusicHoldNomic:                           "分享失败,你当前不在麦上",
	ErrChannelMusicMusicNotExist:                       "歌曲不存在,可能已经被删除",
	ErrChannelFungameAlreadyStart:                      "房间已经在进行小游戏",
	ErrChannelInFunMicMode:                             "房间处于娱乐模式,请升级到最新版本才可以操作",
	ErrChannelMicrophoneEntryAlreadyMute:               "麦位已经被闭麦",
	ErrChannelInvalidTagId:                             "不存在此标签",
	ErrChannelRefreshCd:                                "刷新太快，请稍后",
	ErrChannelTmpAllocPoolEmpty:                        "临时房间不足",
	ErrChannelTmpAllocTypeInvalid:                      "不能分配该类型的临时房间",
	ErrChannelTmpAllocNotExist:                         "临时房间不存在或者已经解散",
	ErrChannelMicModeVersionNotSupport:                 "房间处于新版布局中,请升级到最新版本才可以操作",
	ErrChannelSwitchAttachmentMsg:                      "该房间暂时不允许发图片哦",
	ErrChannelSwitchLevelLmt:                           "该房间暂时不允许新人发言哦",
	ErrChannelSwitchLiveConnectMicLmt:                  "当前主播没有开启连麦哦",
	ErrChannelLiveConnectMicApplyCountLmt:              "当前申请连麦人数已经达到上限，不能继续申请哦",
	ErrChannelLiveConnectMicCountLmt:                   "当前连麦人数已经达到上限，不能继续连麦哦",
	ErrChannelLiveNotStarting:                          "别急嘛,主播还没有开播哦",
	ErrChannelHcNoHoldmic:                              "HC频道不支持上麦操作",
	ErrChannelInDatinggameMicMode:                      "房间处于相亲模式,请升级到最新版本才可以操作",
	ErrChannelIsRecommendOpLimit:                       "房间被加入推荐库中，不能执行该操作",
	ErrChannelOpCdOverlimit:                            "哎呀您慢点啊~~ 手速这么快~~伦家受不了的哟",
	ErrChannelCollectCd:                                "操作太快啦，一会儿再来试试",
	ErrChannelQueueUpMicSwitchLmt:                      "房间排麦功能没有开启哦",
	ErrChannelQueueUpMicApplyCountLmt:                  "当前申请排麦的人数已经达到上限，不能继续申请哦",
	ErrChannelQueueUpMicApplyAlready:                   "你已经申请过排麦了，请等待主持人的处理",
	ErrChannelLockedConveneUnavailable:                 "锁房状态下不可召集",
	ErrChannelMiniGameUnavailable:                      "当前版本不支持小游戏玩法，请更新到最新版本体验酷炫好玩的房间小游戏",
	ErrChannelSwitchMiniGameUnavailable:                "切换小游戏失败，请更新至最新版本体验酷炫好玩的房间小游戏哟",
	ErrChannelSwitchWerewolvesGameUnavailable:          "切换失败，请更新至最新版本体验酷炫好玩的狼人杀小游戏哟",
	ErrChannelPublicWerewolvesGameUnavailable:          "发布失败，请更新至最新版本体验酷炫好玩的狼人杀小游戏哟",
	ErrActivityNotEnoughPrizeTale:                      "没有足够的奖励可领取",
	ErrRedpacketStagetimeOverlay:                       "修改的阶段时间和已有配置重合",
	ErrRedpacketStagetimeTooLate:                       "修改的阶段时间必须在当前时间前2小时",
	ErrCeremony2018StageError:                          "年度盛典当前阶段不能执行该操作",
	ErrCeremony2018SignupSexErr:                        "您的性别不符合报名要求，请修改",
	ErrCeremony2018SignupAlready:                       "您已经报过名了 不能重复报名",
	ErrCeremony2018JoinTeamAlready:                     "您已经加入了一个多人队伍",
	ErrCeremony2018NotTeamMember:                       "您不是该队伍成员",
	ErrCeremony2018NotTeamCreater:                      "您不是该队伍的创建者，没有权限",
	ErrCeremony2018TeamNotExist:                        "该队伍不存在或者已经解散",
	ErrCeremony2018TeamMemberSizeOverlimit:             "该队伍已经满员,无法加入",
	ErrNewyearBeat2019GameNotStart:                     "游戏尚未开始",
	ErrNewyearBeat2019GameAlreadyPlay:                  "本轮游戏您已经参与过了",
	ErrNewyearBeat2019GameAlreadyEnd:                   "本轮游戏已结束",
	ErrNewyearBeat2019LotteryError:                     "游戏抽奖错误",
	ErrCommissionMoneyNotEnough100:                     "金额小于100，不可提现",
	ErrCommissionAccountIsFrost:                        "你的账户已被冻结，不可提现。如有问题，请联系客服",
	ErrCommissionGuildLvTooLow:                         "公会星级达到Lv.2以上才能提现",
	ErrCommissionGetMoneyTimeErr:                       "每月的4日~6日才可以申请提现",
	ErrCommissionUserInfoIdCardErr:                     "身份证填写格式有误, 请核对后重试",
	ErrCommissionUserInfoBankIdErr:                     "开户行填写格式有误，请核对后重试",
	ErrCommissionUserInfoPhoneErr:                      "手机号码填写格式有误, 请核对后重试",
	ErrCommissionUserInfoBankCardErr:                   "银行卡号填写格式有误，请核对后重试",
	ErrCommissionMoneyNotEnough:                        "提现金额超过余额，请重新申请",
	ErrCommissionApiTimeout:                            "佣金系统接口超时",
	ErrCommissionApiRespError:                          "佣金系统操作不成功",
	ErrCommissionUserBankInfoError:                     "上次提现银行卡账户信息不正确，请重新设置银行卡",
	ErrNumericsUnknownRankType:                         "未知的排行榜类型",
	ErrNumericsPresentRecordNotsupportType:             "财富魅力值接口不支持该类型礼物",
	ErrLbsJsonParseErr:                                 "json解析失败",
	ErrLbsSsdbOperErr:                                  "ssdb操作失败",
	ErrLbsIpNotFound:                                   "IP地址无效",
	ErrLbsLocationNotFound:                             "经纬度无效",
	ErrLbsLocationOutOfDate:                            "经纬度数据过期",
	ErrGuildJoinTimeLimit:                              "加入公会时间不足",
	ErrTeamSvrUserAlreadyHaveTeam:                      "用户已有战队",
	ErrTeamSvrTeamNotExist:                             "战队不存在",
	ErrTeamSvrUserNoVoteChance:                         "每天只能给出一个支持哦~",
	ErrTeamSvrApproved:                                 "战队已批准，禁止再修改",
	ErrLeagueSvrUploadImg:                              "当前阶段不可上传截图",
	ErrGamePreorderNotExist:                            "预约不存在",
	ErrUserReportPhonelistInvalid:                      "上报手机列表出错",
	ErrCityRecommendAddrNotExist:                       "用户地址不存在",
	ErrSecurityAlreadyBindPhone:                        "你已经绑定了手机",
	ErrGamelottoSvrNoEnoughChance:                      "抱歉 剩余抽奖次数为0",
	ErrGamelottoSvrReachLimit:                          "已达到当日抽奖次数限制",
	ErrGamelottoSvrNeedReddiamond:                      "继续抽奖需要消耗红钻",
	ErrGamelottoNoEnoughReddiamond:                     "红钻不足",
	ErrGamelottoPoolEmpty:                              "库存已空",
	ErrGamelottoConsumeReddiamondFailed:                "红钻扣除失败",
	ErrGamelottFirstLottoHasDone:                       "首抽已经领取",
	ErrGamelottoLoginToGetFreeChance:                   "红钻抽奖次数已到达上线,登录获取免费抽奖次数",
	ErrGamelottoNoSuchOrder:                            "无效的订单号",
	ErrGamelottoFragmentNotEnough:                      "碎片不足",
	ErrGuildmemberlvInvalidDonateOption:                "捐献值错误",
	ErrGuildmemberlvContributionLack:                   "贡献值不足",
	ErrGuildStorgeProductOverLimit:                     "公会商品数量超过限制",
	ErrGuildStorgeProductItemOverLimit:                 "商品兑换码超过限制svn",
	ErrGuildcircleTopTopicCountLimitExceeded:           "置顶帖数量超过限制",
	ErrGuildcircleTitleIsEmpty:                         "标题不能为空",
	ErrTtGiftCenterNotRankSubType:                      "改子礼包无法排序",
	ErrTtGiftCenterNotEnoughStorage:                    "库存不足",
	ErrTtGiftNotExistGift:                              "礼包不存在",
	ErrTtGiftNotEnoughCurrency:                         "货币不足",
	ErrTtGiftSpendCurrencyFailed:                       "扣费失败",
	ErrTtGiftNotAllowToPick:                            "未开放淘号",
	ErrTtGiftNotthingCanClaim:                          "没有可领取的礼包",
	ErrTtGiftLimitRegisterAt:                           "礼包限定了可领取用户的注册时间",
	ErrFindFriendMatchingNull:                          "没有匹配的用户",
	ErrFindFriendExamIdNotExist:                        "该测试项不存在~",
	ErrFindFriendMatchCntOverLimit:                     "今天召唤了很多个小可爱啦，明天再试试吧",
	ErrUserPresentConfigNotExist:                       "礼物不存在",
	ErrUserPresentLmtEffectedBegin:                     "礼物未上架",
	ErrUserPresentLmtEffectedEnd:                       "礼物已下架",
	ErrUserPresentConfigOverdue:                        "礼物配置已过期",
	ErrUserPresentConfigParam:                          "礼物配置参数异常",
	ErrUserPresentBuyFailed:                            "购买礼物失败",
	ErrUserPresentInvalidItemCount:                     "礼物数量异常",
	ErrUserPresentOrderNotExist:                        "礼物订单不存在",
	ErrUserPresentTagChannelDating:                     "此房间不可送出相亲房礼物",
	ErrUserPresentInvalidTargetUserSize:                "送礼失败",
	ErrUserPresentUnableSendUserPresent:                "无法给该用户送礼",
	ErrUserPresentFlowConfigNotExist:                   "礼物流光配置不存在",
	ErrUserPresentSpendOverLimit:                       "单笔赠送金额不能超过2万元",
	ErrScenePresentOrderExist:                          "送礼场景的礼物订单号已存在",
	ErrUserScoreConfigNotExist:                         "积分配置不存在",
	ErrUserScoreNotEnough:                              "积分不足",
	ErrUserScoreOrderExist:                             "积分订单号已存在",
	ErrUserScoreChangeReasonInvalid:                    "非法积分变化原因",
	ErrUserScoreOrderRollback:                          "积分已回滚",
	ErrAntiDbErr:                                       "访问DB错误",
	ErrAntiNotExist:                                    "记录不存在",
	ErrAntiRedisErr:                                    "访问Redis错误",
	ErrAntiCmdOpBanForbid:                              "当前处于系统维护升级中，该功能暂不可用",
	ErrAntiCmdOpUserBanForbid:                          "系统升级中，该功能暂不可用",
	ErrChannelVotePkGetLockFail:                        "获取锁失败",
	ErrChannelVotePkRedisFail:                          "系统错误",
	ErrChannelVotePkExist:                              "发起投票失败，该房间正在投票中",
	ErrChannelVotePkArgsFail:                           "参数错误",
	ErrChannelVotePkNotExist:                           "PK已经结束",
	ErrChannelVotePkNotMember:                          "不是PK成员",
	ErrChannelVotePkNotAdmin:                           "需要管理员权限",
	ErrChannelVotePkCommonFail:                         "投票失败",
	ErrChannelVotePkNotLeftVote:                        "你的票数已经用完啦，感谢你的支持哟",
	ErrChannelVotePkNameInvalid:                        "投票话题中包含敏感词，请重新输入",
	ErrChannelVotePkNotChannelMode:                     "只有娱乐模式才能开启投票哟",
	ErrChannelDrawGameStatusOff:                        "涂鸦小画板已经关闭，请联系房管打开画板功能",
	ErrChannelDrawGameLineCntMax:                       "当前画板操作次数过多，请清屏后再画吧",
	ErrChannelDrawGameNotAdmin:                         "需要管理员权限",
	ErrChannelDrawGameLineNotExist:                     "线不存在",
	ErrChannelDraeGamePointCntMax:                      "笔画太长啦，松手再继续吧~",
	ErrGetFriendDebrisGiftTimeOut:                      "消息已过期",
	ErrGetFriendDebrisGiftRepeat:                       "礼物已经领取",
	ErrSendFriendDebrisDayCntLimit:                     "今日送礼次数达到上限",
	ErrReceiveFriendDebrisDayCntLimit:                  "对方今日收礼次数达到上限",
	ErrSendFriendTargetUidErr:                          "不能领取不是送给自己的礼物",
	ErrYouReceiveFriendDebrisDayCntLimit:               "今日收礼次数已达上限，明天再试吧",
	ErrOnlineChannelFollowAuthClose:                    "您的玩伴未开启“跟随进房”功能，快去让他开启吧",
	ErrOnlineChannelFollowAuthNotOpen:                  "用户跟随进房间设置未打开",
	ErrOnlineChannelFollowNotEnter:                     "您跟随的玩伴现在没有在房间内",
	ErrUsertagConfNotExist:                             "标签不存在",
	ErrUsertagConfDel:                                  "该标签已经被删除 不可设置",
	ErrUsertagGameOptConfDel:                           "游戏标签的该选项已经被删除 不可设置",
	ErrUsertagSetCountOverlimit:                        "设置的标签数量超过限制",
	ErrUsertagSetGametagNickSensitive:                  "游戏卡片中游戏昵称包含敏感词",
	ErrUsertagSetBirthdayFormatErr:                     "无效的生日标签格式",
	ErrSearchSphinxErr:                                 "Sphinx错误",
	ErrUserAlreadyBindKefu:                             "该用户当前由其他客服绑定服务",
	ErrKefuAllOffline:                                  "没有在线客服",
	ErrGreenbabaSanctionChannel:                        "该房间暂时无法使用",
	ErrGreenbabaSanctionOpUser:                         "您挑战了绿色爸爸的力量 正在被制裁 (说人话就是您被封了)",
	ErrGreenbabaSanctionTargetUser:                     "对方挑战了绿色爸爸的力量 正在被制裁(说人话就是对方被封了)",
	ErrExchangeItemPriceNotUpToDate:                    "兑换价格不是最新",
	ErrExchangeItemInvalidCurrencyType:                 "无效的兑换类型",
	ErrExchangeDuplicateOrderId:                        "重复操作",
	ErrExchangeTbeanInsufficientFunds:                  "今天豆豆已经抢光了哟，请明天再来吧～",
	ErrExchangePointsSettling:                          "正在结算积分中，请稍候再来吧~",
	ErrExchangeFuseProtection:                          "积分兑换T豆库存不足，请稍后再试",
	ErrExchangeFuseProtectionProcess:                   "积分兑换T豆库存达到警戒线",
	ErrTbeanDuplicateOrder:                             "重复的订单号",
	ErrTbeanNoEnoughBalance:                            "T豆余额不足",
	ErrTbeanSystemError:                                "T豆系统错误",
	ErrUnifiedPayDuplicateOrder:                        "重复的订单号",
	ErrUnifiedPayNoSuchOrder:                           "无此订单",
	ErrRushInQueue:                                     "正在排队中，请稍候",
	ErrFindFriendsNoRegistered:                         "你未在扩圈中登记资料，请先完善资料",
	ErrAnotherQuickMatchIsAfoot:                        "你有另一个匹配正在进行中，请别着急~",
	ErrQuickMatchCurrentChannelIsSupplying:             "当前房间正在补位中，请别着急~",
	ErrQuickMatchGameNotSupport:                        "你选择的游戏已经不再支持匹配，请重新选择~",
	ErrQuickMatchPunishedForDeserter:                   "由于你最近从匹配房间中秒退，请稍后再进行匹配~",
	ErrFindFriendsReachedDailyLikeLimit:                "今天已经喜欢了太多人了，不要那么花心哦",
	ErrHeadwearNotFound:                                "不存在的麦位框",
	ErrHeadwearNotInUse:                                "用户没有使用麦位框",
	ErrHeadwearNotHave:                                 "用户没有该麦位框",
	ErrHeadwearExpired:                                 "麦位框已经过期",
	ErrHeadwearNotSameCp:                               "与该麦位框绑定的CP不一致",
	ErrHeadwearOrderidExist:                            "麦位框订单已存在",
	ErrHeadwearCpTypeNotCpUid:                          "CP麦位框缺少CP对象UID",
	ErrHeadwearCanNotUse:                               "无法使用该麦位框，请升级版本",
	ErrActivityPresentEnded:                            "活动已结束",
	ErrChannelTriviaGameNotSupport:                     "当前版本不支持答题，请升级版本",
	ErrChannelTriviaGameActNotExist:                    "答题活动不存在或者尚未开始",
	ErrChannelTriviaGameQuestionNotExist:               "题目不存在",
	ErrChannelTriviaGameQuestionExpire:                 "题目过期或者尚未开始答题",
	ErrChannelTriviaGameAnswerNoQualify:                "您已经被淘汰了,不能继续答题",
	ErrChannelTriviaGameNotQuestionPhase:               "非答题阶段",
	ErrChannelTriviaGameShowSolutionEarly:              "答案公布太早了",
	ErrChannelTriviaGameErrPhase:                       "阶段顺序异常",
	ErrChannelTriviaGameNoLivesToResurrect:             "复活卡不足",
	ErrChannelTriviaGameNoResurrectChancesInThisPeriod: "本轮已经无法使用复活机会",
	ErrChannelTriviaGameNotStart:                       "活动未开始",
	ErrChannelTriviaGameAlreadyEnd:                     "活动已经结束",
	ErrChannelTriviaGameAlreadyShowSolution:            "已经公布过答案",
	ErrChannelTriviaGameAlreadyAnswer:                  "不能重复答题",
	ErrEsgwNotFound:                                    "不存在",
	ErrGuildGameCfgNotExist:                            "非公会游戏",
	ErrGuildGameAddLimit:                               "同时添加的公会主打游戏太多咯",
	ErrGameRecruitExist:                                "游戏招募已存在",
	ErrGameRecruitNotHaveTeam:                          "没有加入任何队伍",
	ErrGameRecruitNotExist:                             "该游戏组队已结束",
	ErrGameRecruitFullMember:                           "该游戏组队已满员",
	ErrGameRecruitNotInChannel:                         "用户不在频道中",
	ErrGameRecruitFrequenceLimit:                       "频率限制",
	ErrPresentSourceInvalid:                            "礼物不存在",
	ErrBackpackPackageItemNotExist:                     "包裹不存在该包裹项配置",
	ErrBackpackUserNotEnoughItem:                       "背包物品不足",
	ErrBackpackPackageItemTimeout:                      "背包物品已过期  ",
	ErrBackpackFuncCardAlreadyUse:                      "已使用同类型卡片",
	ErrBackpackUserItemNotFind:                         "用户背包找不到该项",
	ErrBackpackUseSourceIdErr:                          "使用物品源id不一致",
	ErrBackpackUseItemTypeErr:                          "使用物品类型不一致",
	ErrBackpackUseFragmentSendErr:                      "碎片不支持赠送",
	ErrBackpackUseFreezeOrderConfilctErr:               "order_id 冲突",
	ErrBackpackUseFreezeOrderNonexistErr:               "order_id 不存在",
	ErrBackpackUseFreezeOrderFinishedErr:               "order_id 已经完成",
	ErrBackpackUseFunccardLevelLimit:                   "不可使用比当前低倍或相同倍数的加速卡",
	ErrBackpackUseFreezeTypeInvalidErr:                 "该类型不支持冻结",
	ErrBackpackOrderExist:                              "订单号已存在",
	ErrBackpackNumLimit:                                "超过单次发包裹数量",
	ErrBackpackOrderNotExist:                           "订单不存在",
	ErrBackpackTimestampInvalid:                        "时间戳参数错误",
	ErrBackpackIsDeleted:                               "包裹不存在",
	ErrChannelDatinggameEntryNotOpen:                   "该房间暂无相亲游戏模式权限",
	ErrChannelDatinggamePhaseError:                     "相亲游戏阶段设置错误",
	ErrChannelDatinggameApplyMicUserOverLimit:          "相亲游戏申请上麦人数超过限制",
	ErrChannelDatinggameUserNotSelectLikeObj:           "用户没有选择心动对象",
	ErrChannelDatinggameCanNotOpenUser:                 "不能公布心动对象",
	ErrChannelDatinggameErrStageOperation:              "该阶段不能进行此操作",
	ErrChannelDatinggameNotVip:                         "不可上土豪王座",
	ErrChannelDatinggamePhaseVipError:                  "此相亲阶段不可上土豪王座",
	ErrChannelDatinggameOpenUserCoolDown:               "公布心动对象冷却中~",
	ErrChannelDatinggameAlreadyOpenUser:                "已经公布过该用户心动对象~",
	ErrUserAlreadyCheckin:                              "今天已签到",
	ErrCheckinAwardNotExist:                            "签到奖励不存在",
	ErrCheckinConfigNotExist:                           "签到配置不存在",
	ErrRealnameNotSetingErr:                            "需要进行实名认证",
	ErrRealnameNotFinished:                             "实名认证没有完成",
	ErrRealnameAlreadyFinished:                         "实名认证已经完成",
	ErrRealnameUnknownStatus:                           "未知的实名认证状态",
	ErrRealnameNotIdentityInfo:                         "没有该用户的身份证信息",
	ErrRealnameNotStatusInfo:                           "没有认证信息",
	ErrRealnameBindPhoneLimit:                          "此手机号已经认证十个账号，请用其它手机号认证",
	ErrRealnameBindPhoneLimitTt:                        "绑定的手机号已经认证十个账号，请联系客服",
	ErrRealnameBindIdentityLimit:                       "此身份证已实名认证了十个账号,请使用其他身份信息验证",
	ErrRealnameInvalidParameter:                        "参数错误",
	ErrRealnameInvalidIdentityNum:                      "无效的身份证号码",
	ErrRealnameInvalidIdentityName:                     "名字错误 ",
	ErrRealnameUpgradeStopService:                      "实名认证系统升级中，暂不可用 ",
	ErrRealnameNeedManualCheck:                         "认证失败，请升级客户端走人工审核通道",
	ErrRealnameInvalidIdentityInfo:                     "身份信息有误，请检查姓名或身份证号并重新输入",
	ErrRealnameCheckFaceFailLimit:                      "当天刷脸认证次数过多，请明天再试",
	ErrRealnameCntLimit:                                "今日实名认证次数已达上限，请明日再来",
	ErrCountIsOffErr:                                   "已经关闭了开关",
	ErrCountIsOnErr:                                    "已经开启了开关",
	ErrCountOnMicErr:                                   "上麦事件消费错误",
	ErrCountIsOff:                                      "计数器未开启",
	ErrAntispamNeedVerifyCode:                          "需要弹验证码进行行为验证",
	ErrAntispamVerifyCodeCheckFailed:                   "验证码验证错误",
	ErrAntispamTokenNotExist:                           "反垃圾token不存在",
	ErrEmojiMaxLimit:                                   "你添加的表情数量已经达到上限，请删除部分表情再尝试添加",
	ErrEmojiAlreadyAdd:                                 "你已经添加过这个表情了哦",
	ErrEmojiUploadError:                                "上传失败",
	ErrEmojiUploadFileTooLarge:                         "文件大小超出系统最大限制",
	ErrEmojiPkgPermissionDenied:                        "无权限获取或修改表情包",
	ErrEmojiTargetNotExists:                            "该表情不存在",
	ErrChannelPslUnknownDecorationType:                 "不支持的装饰类型",
	ErrChannelPslGrantingExpiredDecoration:             "该装饰已经过期",
	ErrChannelPslGrantingInvalidDecoration:             "请求无效",
	ErrChannelPslGrantingOrderIdDuplicate:              "发放装饰的订单号重复",
	ErrChannelPslConfigDuplicate:                       "装饰配置重复",
	ErrChannelPslConfigNotExist:                        "装饰配置不存在",
	ErrChannelPslConfigError:                           "其他装饰配置错误",
	ErrGuardianPwdErr:                                  "密码错误",
	ErrGuardianOnErr:                                   "已经打开家长监护模式",
	ErrGuardianOffErr:                                  "已经关闭家长监护模式",
	ErrGuardianPwdNullErr:                              "密码不能为空",
	ErrGuardianDbErr:                                   "数据库访问异常",
	ErrGuardianUnonErr:                                 "未开启家长监护模式",
	ErrGuaidianCheckIdentityByFaceNotPass:              "扫脸验证未通过",
	ErrGuaidianCheckCountIsOverLimit:                   "今天已经申诉很多次了，明天再试吧",
	ErrGuaidianYoungOffDenied:                          "您是未成年人，不能关闭青少年模式",
	ErrUgcTopicNotExists:                               "该话题不存在",
	ErrUgcTopicDisable:                                 "该主题还没上线",
	ErrUgcTopicCreateDuplicateName:                     "主题名称同名冲突",
	ErrUgcTopicCreateDuplicateBindUid:                  "主题绑定的官方账号已经被使用",
	ErrUgcTopicBindedParent:                            "话题绑定的圈子失败",
	ErrUgcHadLike:                                      "已经点过赞了",
	ErrUgcHadNotLike:                                   "已经取消赞了",
	ErrUgcFriendshipFollowLimited:                      "关注失败，你的关注已达到上限",
	ErrUgcFriendshipAntispamHit:                        "关注失败，对方已开启免打扰",
	ErrUgcFriendshipBatchFollowLimited:                 "关注失败，批量关注达到上限",
	ErrUgcInteractivePermissionDenied:                  "无权限操作",
	ErrUgcInvalidFeedGroup:                             "无效的feed 类型",
	ErrUgcInvalidVisitRecordType:                       "非法的记录类型",
	ErrUgcHotRecordNotExists:                           "该热度记录不存在",
	ErrUgcNotAllowToFollowYourself:                     "不允许关注你自己哦",
	ErrUgcPostNotExists:                                "帖子不存在",
	ErrUgcCommentNotExists:                             "评论不存在",
	ErrUgcPermissionDenied:                             "权限不足",
	ErrUgcAttachmentStatusInvalid:                      "附件状态无效",
	ErrUgcPostBanned:                                   "该动态已被屏蔽",
	ErrUgcPostDeleted:                                  "该动态已被删除",
	ErrUgcCommentBanned:                                "评论已被屏蔽",
	ErrUgcCommentDeleted:                               "评论已被删除",
	ErrUgcPostPostCooldown:                             "手速太快啦，歇一会再来吧",
	ErrUgcPostCommentCooldown:                          "哎呀，手速太快啦，人家接受不了惹",
	ErrUgcPostOpInBlackList:                            "你已拉黑对方，需移出黑名单后才可操作",
	ErrUgcPostOpInBlackListForbid:                      "由于对方的设置，你暂时无法操作",
	ErrUgcFriendshipOpInBlackListForbid:                "你已拉黑对方，需移出黑名单后才可重新关注",
	ErrUgcFollowOpInBlackListForbid:                    "由于对方的设置，你暂无法关注",
	ErrUgcStickyPostCountLimit:                         "置顶动态已达上限",
	ErrUgcStickyCommentCountLimit:                      "置顶评论已达上限",
	ErrUgcPostPrivacyPolicyPrivate:                     "opps~这条动态被隐藏起来啦",
	ErrUgcCannotMarkPostSticky:                         "置顶失败，私密动态不支持置顶哦~",
	ErrUgcCelebrityNotExisted:                          "UID对应的用户并非优质用户",
	ErrUgcCelebrityExisted:                             "用户已是优质用户",
	ErrScriptShouldUni:                                 "台本重复",
	ErrInvalidUserDecorationType:                       "类型错误",
	ErrUserDecorationNotExist:                          "座驾不存在",
	ErrUserContractUnknown:                             "用户服务协议状态异常",
	ErrUserContractInvalidVersion:                      "用户服务协议版本错误",
	ErrUnmatchPushSample:                               "推送参数个数与模板对不上",
	ErrCarVersionNotSupport:                            "当前车载版本不支持该功能 请使用APP版本来操作",
	ErrNoviceRecommendUnknownStatus:                    "状态类型不受支持",
	ErrNoviceRecommendAlreadyDisplay:                   "展示已打开",
	ErrNoviceRecommendAlreadyHide:                      "展示已关闭",
	ErrNoviceRecommendPermissionDenied:                 "没有权限改变展示状态",
	ErrNoviceRecommendNotRecommend:                     "不是开黑新人推荐房",
	ErrTopicChannelCreateChannelLevelNotAllow:          "您的等级未达到10级哦~请先到大厅挑挑吧~",
	ErrTopicChannelCreateChannelTagRequired:            "完善个人标签才能创建房间哟~",
	ErrTopicChannelCreateChannelRecommendTypeNowAllow:  "推荐房暂时不支持发布到约玩大厅哦~",
	ErrTopicChannelNotFound:                            "主题房间不存在~",
	ErrTopicChannelChannelLocked:                       "房间已上锁，解锁房间后才能发起哦~",
	ErrTopicChannelTabNotFound:                         "未找到对应标签哦~",
	ErrTopicChannelNameInvalid:                         "房间名含有非法字符~",
	ErrTopicChannelNameIsNull:                          "房间名不能为空~",
	ErrTopicChannelNameFormattedInvalid:                "请输入15个字以内的有效房间名~",
	ErrTopicChannelPermissionDenied:                    "无权限操作",
	ErrTopicChannelNameSensitive:                       "房间名中包含敏感词，若多次发布包含敏感词的房间名，将面临封号危险",
	ErrTopicChannelNameSensitiveForHighRisk:            "服务器无响应，发布失败",
	ErrTopicChannelCreateChannelGameTagRequired:        "完善游戏卡才能创建房间哟~",
	ErrTopicChannelSetRoomNameFormatInvalid:            "设置房间名参数错误",
	ErrTopicChannelNameNoExist:                         "房间名不存在",
	ErrTopicChannelNameConfigVersionInvalid:            "房间名配置版本旧了",
	ErrTopicChannelSetRoomNameVersionInvalid:           "设置房间名版本号错误",
	ErrTopicChannelNoPermissionChange:                  "发布了游戏类房间需升级到最新版哟~快去升级吧~",
	ErrTopicChannelCanNotUseTab:                        "小可爱您好，为了给您提供更好的房间体验，非游戏房间暂不开放，我们正在积极优化升级敬请期待。",
	ErrTopicChannelNotAllowModifyName:                  "发布公开房间暂时不支持自定义房间名哟",
	ErrTopicChannelReleaseTooManyTimes:                 "发布房间操作过于频繁，请明天再来吧",
	ErrTopicChannelReleaseBorderOnLimit:                "温馨提示：频繁发布房间可能会导致发布功能受限",
	ErrUsermatchMatchOverLimit:                         "今天已经找了很多人了哟，先和他们聊聊吧~",
	ErrExchangeLiveBroNoBegin:                          "现在不是可兑换的时间啊~",
	ErrExchangeLiveBroNoLiveUser:                       "没有可兑换的直播收益~",
	ErrExchangeLiveBroExchangeAlready:                  "已经兑换过了~",
	ErrKnockDb:                                         "敲门时数据库错误",
	ErrKnockNotUidExist:                                "处理敲门时没有操作者uid信息",
	ErrKnockWrongRoomType:                              "错误房间类型",
	ErrKnockRedisFail:                                  "redis报错",
	ErrKnockOneUserOneMinute:                           "一分钟内对同一玩家请求数只能一次",
	ErrKnockTimeOut:                                    "同意拒绝超过timeout时间（10s）",
	ErrKnockTicketTimeout:                              "敲门进房只有5分钟的有效期哦，已经过期请重新敲门",
	ErrKnockTicketError:                                "敲门信息错误，不要伪装成别人进房哟",
	ErrKnockPeopleInroom:                               "敲门者在房间内",
	ErrKnockAdminEverHandle:                            "其它房管已处理",
	ErrUserBlackListAddSelf:                            "无法拉黑自己",
	ErrUserBlackListAddOfficialAccount:                 "官方账号无法拉黑",
	ErrUserBlackListWasAdded:                           "对方已在黑名单",
	ErrContractApplyNoBindPhone:                        "你的账号未绑定未绑定手机号码，无法签约",
	ErrContractApplyNoRealname:                         "你尚未完成实名认证，无法签约",
	ErrContractApplyUnderAge:                           "你不满足签约年龄要求，无法签约",
	ErrContractNonexist:                                "合约不存在",
	ErrContractExist:                                   "合约已存在",
	ErrContractApplyIdentityLimit:                      "身份证已经绑定合约",
	ErrContractApplyHaveContract:                       "已经签约",
	ErrContractApplyTodayLimit:                         "次日0点之后才可以重新签约哟",
	ErrContractApplyAlready:                            "重复申请",
	ErrContractHandleConflict:                          "已被其它公会签约",
	ErrContractHandleTimeout:                           "超时的申请，已失效",
	ErrContractHandleInvalid:                           "无效的申请",
	ErrContractApplysignNonexist:                       "签约申请不存在",
	ErrContractExtensionExist:                          "已邀请续约",
	ErrContractExtensionNonexist:                       "续约不存在",
	ErrContractApplyLimit:                              "申请次数超限",
	ErrContractApplyIdentityApplyLimit:                 "同一实名账号已申请",
	ErrContractExtensionCannot:                         "未达到续约条件",
	ErrContractYearSignLimit:                           "您本年度的签约次数已达上限，暂时无法签约新的公会",
	ErrContractApplyIdentityTypeLimit:                  "您正在申请其他身份",
	ErrContractApplyHaveIdentityType:                   "已拥有该身份身份",
	ErrSmashEggChanceNotEnough:                         "魔力球不足",
	ErrSmashEggReachDailyLimit:                         "转转今日已达上限，明日再试试吧",
	ErrBlacklistCheckNotPass:                           "账号异常，该功能不可用",
	ErrTopicChannelFreeze:                              "您的账号处于禁言状态，暂不可以可进行此操作",
	ErrTopicChannelQuickMatchVersionMiniGameRequired:   "发起速配失败，请升级至最新版本才可以匹配到小游戏房间哦",
	ErrTopicChannelNameCanNotDiy:                       "因系统升级，暂不支持修改房间名称，请重新选择房间名称发布",
	ErrGetGangupAdvConfFailed:                          "获取开黑tab广告位配置失败",
	ErrGetGangupAdvUserInfoFailed:                      "获取开黑tab广告位用户信息失败",
	ErrNonValidGangupAdvConf:                           "无有效开黑tab广告位配置",
	ErrOpengameChangeGameTooOfen:                       "修改房间游戏太频繁",
	ErrOpengameTemporaryPlayernum:                      "小游戏匹配人数为0",
	ErrMiniGameMaintain:                                "游戏维护中,请稍后匹配",
	ErrNobilityExclusiveMagicexpression:                "成为贵族即可使用该表情",
	ErrNonNobiltiyExclusivePresent:                     "没有贵族专属礼物特权",
	ErrNonNobiltiyNotPrivilege:                         "没有使用此特权权限",
	ErrNobilitySensitiveTimeRange:                      "暂不能发送小喇叭消息",
	ErrNobilityEqulLevel:                               "充值后等级小于等于原等级",
	ErrNobilityInvisibleTakeHoldMic:                    "该用户不在房间内",
	ErrChannelLiveNotAuthority:                         "没有语音直播权限",
	ErrChannelLiveIdInvalid:                            "过期的直播ID",
	ErrChannelLiveNotOpen:                              "主播还未开播哦",
	ErrFansGroupNameNotStanderd:                        "名称设置不规范，请文明起名",
	ErrFansGroupNameIsExist:                            "团名已被占用",
	ErrFansGroupNameVerifying:                          "团名正在审核，审核结果将助手推送",
	ErrFansGroupNameMemberCntLimit:                     "粉丝团人数达到10可自定义团名",
	ErrFansGroupNameFontCntLimit:                       "粉丝团名称最多3个字",
	ErrFansSetGroupNameCntLimit:                        "团名暂不支持修改，需修改请联系官方",
	ErrChannelLivePkRepeatedApply:                      "不能重复申请",
	ErrChannelLivePkIng:                                "对方正在PK",
	ErrChannelLiveNotPkAuth:                            "暂无PK权限",
	ErrChannelLiveCloseLimit:                           "PK中不能结束直播哦~",
	ErrChannelLivePkMatchInvalidTy:                     "匹配类型跟服务端不一致",
	ErrDistributorInvalid:                              "无效渠道号",
	ErrMaskedCallClosing:                               "不在开放时间",
	ErrMaskedCallNoMoreTicket:                          "匹配次数不足",
	ErrMaskedCallTooMuchTipOff:                         "被举报次数过多",
	ErrEnterOtherChannelInLive:                         "不允许在直播时进入其他房间",
	ErrUserChannelPeopleOverlimit:                      "该房间人数已达上限",
	ErrMaxModifySexLimit:                               "已修改过性别，不能再修改咯",
	ErrOldVersionModifySex:                             "当前版本不允许修改性别，请升级到最新版本",
	ErrTopicChannelNotAllowSwitchPlay:                  "该主题房不支持切换玩法",
	ErrTopicChannelFrequentlySwitchPlay:                "操作过于频繁",
	ErrTopicChannelCannotSwitchPlayWhenRelease:         "主题房发布过程中不可以修改小队信息和玩法或重复发布哦",
	ErrTopicChannelReleaseFreezing:                     "发布房间正在冷却中，请稍后再试",
	ErrTopicChannelNotOwnerSwitchPlay:                  "房间正在发布中，仅房主可切换房间玩法",
	ErrTopicChannelChangeFreezing:                      "修改主题房CD中",
	ErrConfigTabDeleteWarn:                             "存在分类与主题绑定，不能删除",
	ErrUpdateCategoryPlatformTypeWarn:                  "分类的展示平台属性无法修改",
	ErrConfigTabConflictWarn:                           "配置展示平台和分类冲突",
	ErrTempchannelInvalidChannelid:                     "临时房id无效",
	ErrChannelPwdErrLimit:                              "错误操作频繁，24小时内您将无法再通过输入密码进入该房间",
	ErrConversionComposeGiftConfInvalid:                "无效的合成礼物配置",
	ErrConversionMaterialTotalPriceNotEnough:           "原料价值不足以合成目标礼物",
	ErrRiskControlBackpackTbeanLimit:                   "风控限制,业务T豆余额不足",
	ErrRiskControlBackpackCountLimit:                   "风控限制,包裹数量额度不足",
	ErrRiskControlBackpackSignalCountLimit:             "风控限制，单次包裹数量限制",
	ErrRiskControlBackpackSignalTbeanLimit:             "风控限制,单次T豆价值限制",
	ErrRiskControlBackpackConfigNotFound:               "找不到对应业务风控配置",
	ErrRiskControlBackpackOrderidInvalid:               "风控限制,订单格式错误",
	ErrRiskControlBackpackBusinessNotFound:             "风控限制,找不到对应业务配置",
	ErrRiskControlBackpackAuthCheckFail:                "风控限制,密钥检查错误",
	ErrRiskControlBackpackSysFail:                      "风控系统错误",
	ErrRiskControlBackpackDuplicateOrderid:             "订单重复",
	ErrRiskControlAwardCenterOrderExist:                "订单已存在",
	ErrRiskControlAwardCenterOrderInvalid:              "订单号格式有误",
	ErrRiskControlAwardCenterSignalCountLimit:          "风控限制，单次发放数量限制",
	ErrRiskControlAwardCenterConfigInvalid:             "业务配置有误",
	ErrUserNicknameViolate:                             "用户昵称违规",
	ErrUserProfileViolate:                              "用户头像违规",
	ErrChannelNameViolate:                              "房间名违规",
	ErrChannelProfilViolate:                            "房间头像违规",
	ErrBannedEnterChannel:                              "禁止进房",
	ErrBannedKickUserFail:                              "踢号失败，请重试",
	ErrChannelTeamNotFount:                             "数据不存在",
	ErrChannelTeamToast:                                "处理信息有误",
	ErrGameRadarConfigUpdated:                          "游戏雷达配置已更新，请重新获取",
	ErrGameRadarGameCardIncomplete:                     "游戏卡缺少必填信息无法打开雷达，请先将信息填写完整哦",
	ErrGameRadarIncomplete:                             "雷达信息缺少必填选项，请先将信息填写完整哦",
	ErrGameRadarTooManyCharacter:                       "字数太多啦",
	ErrGameRadarQuickFailed:                            "无法快速开启雷达",
	ErrGameRadarModelIncorrect:                         "无正确雷达游戏模式",
	ErrGameRadarIsClosed:                               "雷达已按时关闭，点击再次开启",
	ErrGameRadarInviteExceed:                           "你已经约玩很多人啦，耐心等待对方回应吧～",
	ErrGameRadarIsAlreadyOpened:                        "雷达已经开启",
	ErrChannelLotteryBeginFail:                         "房间抽奖开始失败",
	ErrChannelLotteryTextErr:                           "敏感词",
	ErrChannelLotteryTimeViolate:                       "时间不对",
	ErrChannelLotteryJoinLotteryErr:                    "参与抽奖失败",
	ErrChannelLotteryInfoErr:                           "处理信息有误",
	ErrChannelKickoutNotInChannel:                      "该用户不在房间",
}

// MessageFromCode get message associated with the code
func MessageFromCode(code int) string {
	if m, ok := CodeMessageMap[code]; ok {
		return m
	}

	return strconv.Itoa(code)
}
