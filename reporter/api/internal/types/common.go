package types

type DevicePerfDataSeries struct {
	Name string   `json:"name"` // 指标名称
	Y    []string `json:"y"`    // Y轴数据
}

type DevicePerfData struct {
	DataType string                  `json:"data_type"` // 数据类型
	Unit     string                  `json:"unit"`      // 单位
	X        []string                `json:"x"`         // X轴数据
	Series   []*DevicePerfDataSeries `json:"series"`    // 数据
}

type DeviceActivity struct {
	Name    string `json:"name"`    // 活动名称
	Covered bool   `json:"covered"` // 是否覆盖
}
