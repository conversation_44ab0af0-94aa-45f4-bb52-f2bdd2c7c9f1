package perfReporter

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	converters []utils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		converters: []utils.TypeConverter{
			usercommon.StringToUserInfo(ctx, svcCtx.UserRpc, nil),

			commonpb.TriggerModeToString(),
			commonpb.ProtocolToString(),
			commonpb.TargetEnvironmentToString(),
			commonpb.PerfTaskTypeToString(),
			commonpb.PerfTaskExecutionModeToString(),
			commonpb.PerfCaseStepTypeToString(),
			commonpb.MonitorUrlTypeToString(),
			usercommon.StringToUserInfo(ctx, svcCtx.UserRpc, nil),
			logic.ApiAPIMetricToRpcAPIMetric(),
			logic.RpcAPIMetricToApiAPIMetric(),
			logic.ApiErrorMessageToRpcErrorMessage(),
			logic.RpcErrorMessageToApiErrorMessage(),
		},
	}
}
