package stabilityReporter

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetStabilityDevicePerfDataLogic struct {
	*BaseLogic
}

func NewGetStabilityDevicePerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetStabilityDevicePerfDataLogic {
	return &GetStabilityDevicePerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetStabilityDevicePerfDataLogic) GetStabilityDevicePerfData(req *types.GetStabilityDevicePerfDataReq) (
	resp *types.GetStabilityDevicePerfDataResp, err error,
) {
	in := &pb.GetStabilityDevicePerfDataReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}
	out, err := l.svcCtx.StabilityReporter.GetStabilityDevicePerfData(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.GetStabilityDevicePerfDataResp{
		DevicePerfData: &types.DevicePerfData{
			X:      []string{},
			Series: []*types.DevicePerfDataSeries{},
		},
	}
	if err = utils.Copy(resp.DevicePerfData, out.GetData(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
