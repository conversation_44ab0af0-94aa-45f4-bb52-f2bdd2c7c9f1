package svc

import (
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userservice "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/wiseEyes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	perfReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/perfreporter"
	reporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
	stabilityReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/stabilityreporter"
	uiReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uireporter"
)

type ServiceContext struct {
	Config config.Config

	Validator *utils.Validator

	ExecutionRecordModel          model.ExecutionRecordModel
	InterfaceExecutionRecordModel model.InterfaceExecutionRecordModel
	PerfCaseExecutionRecordModel  model.PerfCaseExecutionRecordModel

	ReporterRpc       reporter.Reporter
	UIReporterRpc     uiReporter.UIReporter
	PerfReporter      perfReporter.PerfReporter
	StabilityReporter stabilityReporter.StabilityReporter
	UserRpc           userservice.UserService

	WiseEyesClient *wiseEyes.Client
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	return &ServiceContext{
		Config: c,

		Validator: utils.NewValidator(c.Validator.Locale),

		ExecutionRecordModel:          model.NewExecutionRecordModel(sqlConn),
		InterfaceExecutionRecordModel: model.NewInterfaceExecutionRecordModel(sqlConn),
		PerfCaseExecutionRecordModel:  model.NewPerfCaseExecutionRecordModel(sqlConn),

		ReporterRpc: reporter.NewReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		UIReporterRpc: uiReporter.NewUIReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		PerfReporter: perfReporter.NewPerfReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		StabilityReporter: stabilityReporter.NewStabilityReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		UserRpc: userservice.NewUserService(
			zrpc.MustNewClient(
				c.User, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),

		WiseEyesClient: wiseEyes.NewClient(c.WiseEyes),
	}
}
