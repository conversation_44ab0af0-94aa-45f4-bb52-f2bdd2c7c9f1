syntax = "v1"

info(
    title: "质量平台测试报告服务类型定义"
    desc: "质量平台测试报告服务中API接口涉及的类型定义"
    author: "hejian<PERSON>"
    email: "hejian<PERSON>@52tt.com"
    version: "0.1.0"
)

// 用户信息
type FullUserInfo {
    Account string `json:"account"`
    Fullname string `json:"fullname"`
    DeptId string `json:"dept_id"`
    DeptName string `json:"dept_name"`
    FullDeptName string `json:"full_dept_name"`
    Email string `json:"email"`
    Mobile string `json:"mobile"`
    Photo string `json:"photo"`
    Enabled bool `json:"enabled"`
}

type Condition {
    Single *SingleCondition `json:"single,omitempty,optional"`
    Group  *GroupCondition  `json:"group,omitempty,optional"`
}

type GroupCondition {
    Relationship string       `json:"relationship" validate:"oneof=AND OR"`
    Conditions   []*Condition `json:"conditions"`
}

type Between {
    Start string `json:"start"`
    End   string `json:"end"`
}

type Other {
    Value string `json:"value"`
}

type SingleCondition {
    Field   string   `json:"field" validate:"required"`
    Compare string   `json:"compare,default=EQ" validate:"oneof=EQ NE LT LE GT GE LIKE IN BETWEEN"`
    In      []string `json:"in,omitempty,optional"`
    Between *Between `json:"between,omitempty,optional"`
    Other   *Other   `json:"other,omitempty,optional"`
}

// 分页
type Pagination {
    CurrentPage uint64 `json:"current_page"`
    PageSize uint64 `json:"page_size"`
}

// 字段排序
type SortField {
    Field string `json:"field"`
    Order string `json:"order"`
}

// 组件执行记录详情
type (
    viewComponentRecordReq {
        TaskId string `form:"task_id"`
        ProjectId string `form:"project_id"`
        ExecuteId string `form:"execute_id"`
        ComponentExecuteId string `form:"component_execute_id"`
        Times int64 `form:"times"`
    }
    viewComponentRecordResp {
        content string `json:"content"`
    }
)

// 组件组`调试`记录列表
type (
    listComponentGroupRecordReq {
        ProjectId string `json:"project_id"`
        ComponentGroupId string `json:"component_group_id"`
        Version string `json:"version"`
        Pagination *Pagination `json:"pagination,omitempty,optional"`
        Sort []*SortField `json:"sort,omitempty,optional"`
    }
    listComponentGroupRecordResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*ComponentGroupRecordItem `json:"items"`
    }
    ComponentGroupRecordItem {
        TaskId string `json:"task_id"`
        ProjectId string `json:"project_id"`
        ExecuteId string `json:"execute_id"`
        ComponentGroupExecuteId string `json:"component_group_execute_id"`
        Times int64 `json:"times"`
        Version string `json:"version"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
        Cleaned int64 `json:"cleaned"`
    }
)


// 执行信息摘要
type (
    ExecuteSummary {
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }
)

// 组件组执行记录详情
type (
    GetComponentGroupRecordReq {
        TaskId string `form:"task_id"`
        ProjectId string `form:"project_id"`
        ExecuteId string `form:"execute_id"`
        ComponentExecuteId string `form:"component_execute_id"`
        Times int64 `form:"times"`
    }

    GetComponentGroupRecordResp {
        ExecuteSummary *ExecuteSummary `json:"execute_summary"`
        Items []*ComponentRecordItem `json:"items"`
    }

    ComponentGroupExecuteSummary {

    }

    ComponentRecordItem {
        TaskId string `json:"task_id"`
        ProjectId string `json:"project_id"`
        ExecuteId string `json:"execute_id"`
        ComponentId string `json:"component_id"`
        ComponentExecuteId string `json:"component_execute_id"`
        ParentComponentId string `json:"parent_component_id"`
        ParentComponentExecuteId string `json:"parent_component_execute_id"`
        Version string `json:"version"`
        Times int64 `json:"times"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }
)

// 用例`调试`记录列表
type (
    ListCaseRecordReq {
        ProjectId string `json:"project_id"`
        CaseId string `json:"case_id"`
        Version string `json:"version"`
        Pagination *Pagination `json:"pagination,omitempty,optional"`
        Sort []*SortField `json:"sort,omitempty,optional"`
    }

    ListCaseRecordResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*CaseRecordItem `json:"items"`
    }

    CaseRecordItem {
        TaskId string `json:"task_id"`
        ProjectId string `json:"project_id"`
        ExecuteId string `json:"execute_id"`
        CaseExecuteId string `json:"case_execute_id"`
        Version string `json:"version"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
        Cleaned int64 `json:"cleaned"`
    }
)

// 用例执行记录详情
type (
    GetCaseRecordReq {
        TaskId string `form:"task_id"`
        ProjectId string `form:"project_id"`
        ExecuteId string `form:"execute_id"`
        CaseExecuteId string `form:"case_execute_id"`
    }

    GetCaseRecordResp {
        ExecuteSummary *ExecuteSummary `json:"execute_summary"`
        GeneralConfig string `json:"general_config"`
        AccountConfig string `json:"account_config"`
        Items []*CaseAllLayerComponentRecordItem `json:"items"`
    }

    CaseAllLayerComponentRecordItem {
        TaskId string `json:"task_id"`
        ProjectId string `json:"project_id"`
        ExecuteId string `json:"execute_id"`
        ComponentId string `json:"component_id"`
        ComponentExecuteId string `json:"component_execute_id"`
        ParentComponentId string `json:"parent_component_id"`
        ParentComponentExecuteId string `json:"parent_component_execute_id"`
        Version string `json:"version"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }
)

// 接口执行记录列表
type (
    ListInterfaceRecordReq {
        ProjectId string `json:"project_id"`
        InterfaceId string `json:"interface_id"`
        Pagination *Pagination `json:"pagination,omitempty,optional"`
        Sort []*SortField `json:"sort,omitempty,optional"`
    }

    ListInterfaceRecordResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*InterfaceCaseRecordItem `json:"items"`
    }

    InterfaceCaseRecordItem {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        InterfaceExecuteId string `json:"interface_execute_id"`
        InterfaceId string `json:"interface_id"`
        InterfaceName string `json:"interface_name"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        Status string `json:"status"`
        Cleaned int64 `json:"cleaned"`
    }
)

// 接口执行记录详情
type (
    GetInterfaceRecordReq {
        TaskId string `form:"task_id"`
        ProjectId string `form:"project_id"`
        ExecuteId string `form:"execute_id"`
        InterfaceExecuteId string `form:"interface_execute_id"`
    }

    GetInterfaceRecordResp {
        CostTime int64 `json:"cost_time"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        InterfaceId string `json:"interface_id"`
        InterfaceName string `json:"interface_name"`
        Content string `json:"content"`
        GeneralConfig string `json:"general_config"`
        AccountConfig string `json:"account_config"`
        CaseItems []*InterfaceCaseRecordItem2 `json:"case_items"`
    }

    InterfaceCaseRecordItem2 {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        CaseExecuteId string `json:"case_execute_id"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        MaintainedBy *FullUserInfo `json:"maintained_by"`
        CaseId string `json:"case_id"`
        CaseName string `json:"case_name"`
        Version string `json:"version"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }
)

// 集合执行记录列表
type (
    ListSuiteRecordReq {
        ProjectId string `json:"project_id"`
        SuiteId string `json:"suite_id"`
        Pagination *Pagination `json:"pagination,omitempty,optional"`
        Sort []*SortField `json:"sort,omitempty,optional"`
    }

    ListSuiteRecordResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*SuiteCaseRecordItem `json:"items"`
    }

    SuiteCaseRecordItem {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        SuiteExecuteId string `json:"suite_execute_id"`
        SuiteId string `json:"suite_id"`
        SuiteName string `json:"suite_name"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        Status string `json:"status"`
        Cleaned int64 `json:"cleaned"`
    }
)

// 集合执行记录详情
type (
    GetSuiteRecordReq {
        TaskId string `form:"task_id"`
        ProjectId string `form:"project_id"`
        ExecuteId string `form:"execute_id"`
        SuiteExecuteId string `form:"suite_execute_id"`
    }

    GetSuiteRecordResp {
        CostTime int64 `json:"cost_time"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        SuiteId string `json:"suite_id"`
        SuiteName string `json:"suite_name"`
        Content string `json:"content"`
        GeneralConfig string `json:"general_config"`
        AccountConfig string `json:"account_config"`
        CaseItems []*SuiteCaseRecordItem2 `json:"case_items"`
    }

    SuiteCaseRecordItem2 {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        CaseExecuteId string `json:"case_execute_id"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        MaintainedBy *FullUserInfo `json:"maintained_by"`
        CaseType string `json:"case_type"`
        CaseId string `json:"case_id"`
        CaseName string `json:"case_name"`
        Version string `json:"version"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }
)


// 服务执行记录列表
type (
    ListServiceRecordReq {
        ProjectId string `json:"project_id"`
        ServiceId string `json:"service_id"`
        Pagination *Pagination `json:"pagination,omitempty,optional"`
        Sort []*SortField `json:"sort,omitempty,optional"`
    }

    ListServiceRecordResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*ServiceCaseRecordItem `json:"items"`
    }

    ServiceCaseRecordItem {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        ServiceExecuteId string `json:"service_execute_id"`
        ServiceId string `json:"service_id"`
        ServiceName string `json:"service_name"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        Status string `json:"status"`
        Cleaned int64 `json:"cleaned"`
    }
)

// 精准测试服务执行记录详情
type (
    GetServiceRecordReq {
        TaskId string `form:"task_id"`
        ProjectId string `form:"project_id"`
        ExecuteId string `form:"execute_id"`
        ServiceExecuteId string `form:"service_execute_id"`
    }

    GetServiceRecordResp {
        CostTime int64 `json:"cost_time"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        ServiceId string `json:"service_id"`
        ServiceName string `json:"service_name"`
        Content string `json:"content"`
        GeneralConfig string `json:"general_config"`
        AccountConfig string `json:"account_config"`
        CaseItems []*ServiceCaseRecordItem2 `json:"case_items"`
    }

    ServiceCaseRecordItem2 {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        CaseExecuteId string `json:"case_execute_id"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        MaintainedBy *FullUserInfo `json:"maintained_by"`
        CaseId string `json:"case_id"`
        CaseName string `json:"case_name"`
        Version string `json:"version"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }
)

// 计划执行记录列表
type (
    ListPlanRecordReq {
        ProjectId string `json:"project_id"`
        PlanId string `json:"plan_id"`
        Pagination *Pagination `json:"pagination,omitempty,optional"`
        Sort []*SortField `json:"sort,omitempty,optional"`
    }

    ListPlanRecordResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*PlanRecordItem `json:"items"`
    }

    PlanRecordItem {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        PlanExecuteId string `json:"plan_execute_id"`
        Version string `json:"version"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        TotalSuite int64 `json:"total_suite"`
        SuccessSuite int64 `json:"success_suite"`
        FailureSuite int64 `json:"failure_suite"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
        Cleaned int64 `json:"cleaned"`
        PurposeType string `json:"purpose_type"`
        Type string `json:"type"`
    }
)

type (
    ListFailCaseRecordForPlanReq {
        ProjectId string `json:"project_id"`
        // PlanId     string       `json:"plan_id"`
        CaseId string `json:"case_id"`
        CaseType string `json:"case_type"`
        Pagination *Pagination `json:"pagination,omitempty,optional"`
        // Sort       []*SortField `json:"sort,omitempty,optional"`
    }

    ListFailCaseRecordForPlanResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*FailCaseItemForPlanRecord `json:"items"`
    }

    FailCaseItemForPlanRecord {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        PlanId string `json:"plan_id"`
        PlanName string `json:"plan_name"`
        PlanExecuteId string `json:"plan_execute_id"`
        Version string `json:"version"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        TotalSuite int64 `json:"total_suite"`
        SuccessSuite int64 `json:"success_suite"`
        FailureSuite int64 `json:"failure_suite"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
        Cleaned int64 `json:"cleaned"`
        PurposeType string `json:"purpose_type"`
        Type string `json:"type"`
    }
)

// 计划执行记录详情
type (
    GetPlanRecordReq {
        TaskId string `form:"task_id"`
        ProjectId string `form:"project_id"`
        ExecuteId string `form:"execute_id"`
        PlanExecuteId string `form:"plan_execute_id"`
    }

    GetPlanRecordResp {
        CostTime int64 `json:"cost_time"`
        ExecutedBy *FullUserInfo `json:"executed_by"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        TotalSuite int64 `json:"total_suite"`
        SuccessSuite int64 `json:"success_suite"`
        FailureSuite int64 `json:"failure_suite"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        PlanId string `json:"plan_id"`
        PlanName string `json:"plan_name"`
        PurposeType string `json:"purpose_type"`
        Content string `json:"content"`
        GeneralConfig string `json:"general_config"`
        AccountConfig string `json:"account_config"`
        SuiteItems []*SuiteItem `json:"suite_items"`
        InterfaceDocumentItems []*InterfaceDocumentItem `json:"interface_document_items"`
        ServiceItems []*ServiceItem `json:"service_items"`
        Type string `json:"type"`
    }

    SuiteItem {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        SuiteExecuteId string `json:"suite_execute_id"`
        SuiteId string `json:"suite_id"`
        SuiteName string `json:"suite_name"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }

    InterfaceDocumentItem {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        InterfaceExecuteId string `json:"interface_execute_id"`
        InterfaceId string `json:"interface_id"`
        InterfaceName string `json:"interface_name"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }

    ServiceItem {
        TaskId string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        ProjectId string `json:"project_id"`
        ServiceExecuteId string `json:"service_execute_id"`
        ServiceId string `json:"service_id"`
        ServiceName string `json:"service_name"`
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        FailureCase int64 `json:"failure_case"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }
)

// 计划执行详情查看其下各集合用例执行时间刻度信息
type (
    GetPlanTimeScaleReq {
        TaskId string `form:"task_id"`
        ProjectId string `form:"project_id"`
        ExecuteId string `form:"execute_id"`
        PlanExecuteId string `form:"plan_execute_id"`
    }

    GetPlanTimeScaleResp {
        SuiteItems []*SuiteItem2 `json:"suite_items"`
    }

    SuiteItem2 {
        SuiteId string `json:"suite_id"`
        SuiteName string `json:"suite_name"`
        CaseItems []*CaseItem2 `json:"case_items"`
    }

    CaseItem2 {
        CaseId string `json:"case_id"`
        CaseName string `json:"case_name"`
        StartedAt int64 `json:"started_at"`
        EndedAt int64 `json:"ended_at"`
        CostTime int64 `json:"cost_time"`
        Status string `json:"status"`
    }
)

// 获取测试计划执行报告（ci/cd专用）
type (
    GetPlanSummaryReq {
        ProjectId string `form:"project_id"`
        TaskId string `form:"task_id"`
        ExecuteId string `form:"execute_id"`
        PlanType int64 `json:"plan_type,omitempty,optional,default=4" zh:"执行对象的类型"`
    }

    GetPlanSummaryResp {
        Status string `json:"status"`
        Record *Record `json:"record"`
    }

    Record {
        TotalCase int64 `json:"total_case"`
        SuccessCase int64 `json:"success_case"`
        PassRate string `json:"pass_rate"`
        PassingRate string `json:"passing_rate"`
        ReportPath string `json:"report_path"`
    }
)

// 设备性能数据
type (
    DevicePerfDataSeries {
        Name string   `json:"name"` // 指标名称
        Y    []string `json:"y"`    // Y轴数据
    }
    DevicePerfData {
        DataType string                  `json:"data_type"` // 数据类型
        Unit     string                  `json:"unit"`      // 单位
        X        []string                `json:"x"`         // X轴数据
        Series   []*DevicePerfDataSeries `json:"series"`    // 数据
    }
)

// 设备Activity
type (
    DeviceActivity struct {
        Name    string `json:"name"`    // 活动名称
        Covered bool   `json:"covered"` // 是否覆盖
    }
)