syntax = "v1"

import (
	"perf_types.api"
)

@server (
	prefix: reporter/v1
	group: perfReporter
)

service reporter {
	@handler SearchPerfPlanRecord
	post /record/perf_plan/search (SearchPerfPlanRecordReq) returns (SearchPerfPlanRecordResp)

    @handler GetPerfPlanRecord
    get /record/perf_plan/get (GetPerfPlanRecordReq) returns (GetPerfPlanRecordResp)

    @handler GetPerfPlanRecordV2
    get /record/perf_plan/get_v2 (GetPerfPlanRecordV2Req) returns (GetPerfPlanRecordV2Resp)

	@handler SearchPerfCaseRecord
    post /record/perf_plan/case/search (SearchPerfCaseRecordReq) returns (SearchPerfCaseRecordResp)

	@handler GetPerfCaseLog
	get /record/perf_case/log/get (GetPerfCaseLogReq) returns (GetPerfCaseLogResp)

	@handler ListPerfPlanRecordHandler
	post /record/perf_plan/list (ListPerfPlanRecordReq) returns (ListPerfPlanRecordResp)
}
