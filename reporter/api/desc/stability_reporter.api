syntax = "v1"

import (
	"stability_types.api"
)

@server (
	prefix: reporter/v1
	group: stabilityReporter
)

service reporter {
    @doc "获取稳测计划的执行记录"
    @handler ListStabilityPlanRecord
    post /record/stability_plan/list (ListStabilityPlanRecordReq) returns (ListStabilityPlanRecordResp)

    @doc "搜索稳测的执行记录"
    @handler SearchStabilityPlanRecord
    post /record/stability_plan/search (SearchStabilityPlanRecordReq) returns (SearchStabilityPlanRecordResp)

    @doc "获取稳测执行报告的计划信息"
    @handler GetStabilityPlanRecord
    get /record/stability_plan/get (GetStabilityPlanRecordReq) returns (GetStabilityPlanRecordResp)

    @doc "搜索稳测执行报告的总览设备"
    @handler SearchStabilityDeviceRecord
    post /record/stability_device/search (SearchStabilityDeviceRecordReq) returns (SearchStabilityDeviceRecordResp)

    @doc "获取稳测执行报告的设备步骤日志"
    @handler ListStabilityDeviceStep
    post /record/stability_device/step/list (ListStabilityDeviceStepReq) returns (ListStabilityDeviceStepResp)

    @doc "获取稳测执行报告的设备性能数据"
    @handler GetStabilityDevicePerfData
    get /record/stability_device/perf_data/get (GetStabilityDevicePerfDataReq) returns (GetStabilityDevicePerfDataResp)

    @doc "获取稳测执行报告的设备Activity统计"
    @handler GetStabilityDeviceActivity
    get /record/stability_device/activity/get (GetStabilityDeviceActivityReq) returns (GetStabilityDeviceActivityResp)
}