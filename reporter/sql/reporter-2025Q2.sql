-- UI测试支持指定设备执行
ALTER TABLE `ui_suite_execution_record` MODIFY `execute_id` VARCHAR(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '集合执行id, 作为用例执行表的suite_execute_id';
ALTER TABLE `ui_suite_execution_record` MODIFY `suite_id` VARCHAR(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '集合ID';
ALTER TABLE `ui_suite_execution_record` MODIFY `suite_name` VARCHAR(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '集合名称';
ALTER TABLE `ui_suite_execution_record` MODIFY `plan_execute_id` VARCHAR(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划执行id, 关联表ui_plan_execution_record的execute_id';
ALTER TABLE `ui_suite_execution_record` MODIFY `status` VARCHAR(64) COLLATE utf8mb4_general_ci NULL COMMENT '执行状态（结果）';
ALTER TABLE `ui_suite_execution_record` MODIFY `executed_by` VARCHAR(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行人';
ALTER TABLE `ui_suite_execution_record` COLLATE = utf8mb4_general_ci;

ALTER TABLE `ui_case_execution_record` ADD `udid` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '设备编号' AFTER `suite_execute_id`;

CREATE INDEX `ix_user_task_plan_project` ON `ui_suite_execution_record` (`task_id`, `plan_execute_id`, `project_id`);
CREATE INDEX `ix_ucer_task_project_udid_suite` ON `ui_case_execution_record` (`task_id`, `project_id`, `udid`, `suite_execute_id`);

DROP INDEX `unique_parent_record_index` ON `ui_case_execution_record`;
CREATE INDEX `ix_ucer_parent_record_index` ON `ui_case_execution_record` (`task_id`, `project_id`, `suite_execute_id`);

DROP INDEX `unique_record_index` ON `ui_case_execution_record`;
CREATE UNIQUE INDEX `uix_ucer_record_index` ON `ui_case_execution_record` (`task_id`, `project_id`, `execute_id`);

DROP INDEX `unique_record_index` ON `ui_suite_execution_record`;
CREATE UNIQUE INDEX `uix_user_record_index` ON `ui_suite_execution_record` (`task_id`, `project_id`, `execute_id`);

DROP INDEX `unique_record_index` ON `ui_plan_execution_record`;
CREATE UNIQUE INDEX `uix_uper_record_index` ON `ui_plan_execution_record` (`task_id`, `project_id`, `execute_id`);

ALTER TABLE `ui_case_execution_step` ADD `index` TINYINT NOT NULL DEFAULT 0 COMMENT '步骤索引' AFTER `stage`;

DROP INDEX `uix_es_step_id` ON `ui_case_execution_step`;
CREATE UNIQUE INDEX `uix_uces_step_id` ON `ui_case_execution_step` (`step_id`);

DROP INDEX `ix_es_task_id_execute_id_project_id_case_id_stage_name` ON `ui_case_execution_step`;
CREATE INDEX `ix_uces_task_id_execute_id_project_id_case_id_stage_name` ON `ui_case_execution_step` (`task_id`, `execute_id`, `project_id`, `case_id`, `stage`, `name`);

CREATE INDEX `ix_uces_task_id_execute_id_project_id_stage_index` ON `ui_case_execution_step` (`task_id`, `execute_id`, `project_id`, `stage`, `index`);

DROP INDEX `uix_esc_step_id_index` ON `ui_case_execution_step_content`;
CREATE UNIQUE INDEX `uix_ucesc_step_id_index` ON `ui_case_execution_step_content` (`step_id`, `index`);

DROP INDEX `ix_esc_task_id_step_id` ON `ui_case_execution_step_content`;
CREATE INDEX `ix_ucesc_task_id_step_id_index` ON `ui_case_execution_step_content` (`task_id`, `step_id`, `index`);



-- UI测试报告增加性能图表的展示
CREATE TABLE IF NOT EXISTS `ui_device_perf_data`
(
    `id`         INT         NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`    VARCHAR(64) NOT NULL COMMENT '任务ID',
    `execute_id` VARCHAR(64) NOT NULL COMMENT '执行ID',
    `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
    `udid`       VARCHAR(64) NOT NULL COMMENT '设备编号',
    `data_type`  VARCHAR(16) NOT NULL COMMENT '数据类型',
    `interval`   INT         NOT NULL COMMENT '采集间隔，单位毫秒',
    `series`     VARCHAR(16) NOT NULL COMMENT '指标名称',
    `unit`       VARCHAR(8)  NOT NULL DEFAULT '' COMMENT '单位',
    `x`          VARCHAR(64) NOT NULL COMMENT 'X轴数据',
    `y`          VARCHAR(64) NOT NULL COMMENT 'Y轴数据',
    `deleted`    TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_udpd_task_id_execute_id_project_id_udid_data_type_series_x` (`task_id`, `execute_id`, `project_id`, `udid`, `data_type`, `series`, `x`),
    KEY `ix_udpd_task_id_execute_id_project_id_data_type` (`task_id`, `execute_id`, `project_id`, `data_type`),
    KEY `ix_udpd_task_id_project_id_udid_data_type` (`task_id`, `project_id`, `udid`, `data_type`),
    KEY `ix_udpd_task_id_project_id_data_type` (`task_id`, `project_id`, `data_type`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='UI设备性能数据表';

CREATE TABLE IF NOT EXISTS `stability_plan_execution_record`
(
    `id`              INT           NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`      VARCHAR(64)   NOT NULL COMMENT '项目ID',
    `plan_id`         VARCHAR(64)   NOT NULL COMMENT '计划ID',
    `plan_name`       VARCHAR(64)   NOT NULL COMMENT '计划名称',
    `trigger_mode`    VARCHAR(64)   NOT NULL COMMENT '触发模式',
    `task_id`         VARCHAR(64)   NOT NULL COMMENT '任务ID',
    `execute_id`      VARCHAR(64)   NOT NULL COMMENT '执行ID',
    `status`          VARCHAR(64)   NULL     COMMENT '执行状态（结果）',
    `cost_time`       BIGINT        NULL     DEFAULT 0 COMMENT '执行耗时（单位为毫秒）',
    `target_duration` INT           NULL     DEFAULT 0 COMMENT '目标运行时长（单位为分钟）',
    `execute_data`    JSON          NULL     COMMENT '执行数据',
    `executed_by`     VARCHAR(64)   NOT NULL COMMENT '执行者的用户ID',
    `success_device`  INT           NOT NULL DEFAULT 0 COMMENT '执行成功的测试设备数',
    `failure_device`  INT           NOT NULL DEFAULT 0 COMMENT '执行失败的测试设备数',
    `total_device`    INT           NOT NULL DEFAULT 0 COMMENT '测试设备总数',
    `started_at`      TIMESTAMP     NULL     COMMENT '开始时间',
    `ended_at`        TIMESTAMP     NULL     COMMENT '结束时间',
    `err_msg`         JSON          NULL     COMMENT '计划执行错误信息',
    `cleaned`         TINYINT       NOT NULL DEFAULT 0 COMMENT '清理标识（未清理、已清理）',
    `deleted`         TINYINT       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64)   NULL     COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP     NULL     COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_sper_project_id_task_id_execute_id` (`project_id`, `task_id`, `execute_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '稳测计划执行记录表';

CREATE TABLE IF NOT EXISTS `stability_device_execution_record`
(
    `id`               INT         NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`          VARCHAR(64) NOT NULL COMMENT '任务ID',
    `execute_id`       VARCHAR(64) NOT NULL COMMENT '执行ID',
    `project_id`       VARCHAR(64) NOT NULL COMMENT '项目ID',
    `plan_execute_id`  VARCHAR(64) NOT NULL COMMENT '计划执行ID',
    `udid`             VARCHAR(64) NOT NULL COMMENT '设备编号',
    `device`           JSON        NULL     COMMENT '设备执行信息',
    `status`           VARCHAR(64) NULL     COMMENT '执行状态（结果）',
    `cost_time`        BIGINT      NULL     DEFAULT 0 COMMENT '执行耗时（单位为毫秒）',
    `crash_count`      INT         NOT NULL DEFAULT 0 COMMENT 'crash数量',
    `anr_count`        INT         NOT NULL DEFAULT 0 COMMENT 'anr数量',
    `executed_by`      VARCHAR(64) NOT NULL COMMENT '执行者的用户ID',
    `started_at`       TIMESTAMP   NULL     COMMENT '开始时间',
    `ended_at`         TIMESTAMP   NULL     COMMENT '结束时间',
    `err_msg`          JSON        NULL     COMMENT '用例执行错误信息',
    `cleaned`          TINYINT     NOT NULL DEFAULT 0 COMMENT '清理标识（未清理、已清理）',
    `deleted`          TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`       VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`       VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`       VARCHAR(64) NULL     COMMENT '删除者的用户ID',
    `created_at`       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       TIMESTAMP   NULL     COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_sder_project_id_task_id_execute_id` (`project_id`, `task_id`, `execute_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '稳测设备执行记录表';

CREATE TABLE IF NOT EXISTS `stability_device_execution_step`
(
    `id`         INT           NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`    VARCHAR(64)   NOT NULL COMMENT '任务ID',
    `execute_id` VARCHAR(64)   NOT NULL COMMENT '用例执行ID',
    `project_id` VARCHAR(64)   NOT NULL COMMENT '项目ID',
    `udid`       VARCHAR(64)   NOT NULL COMMENT '设备编号',
    `step_id`    VARCHAR(64)   NOT NULL COMMENT '步骤ID',
    `stage`      TINYINT       NOT NULL DEFAULT 0 COMMENT '阶段（前置步骤、测试步骤、后置步骤）',
    `index`      TINYINT       NOT NULL DEFAULT 0 COMMENT '步骤索引',
    `name`       VARCHAR(64)   NOT NULL COMMENT '步骤名称',
    `status`     VARCHAR(64)   NOT NULL COMMENT '状态（成功、失败）',
    `started_at` TIMESTAMP     NOT NULL COMMENT '开始时间',
    `ended_at`   TIMESTAMP     NOT NULL COMMENT '结束时间',
    `deleted`    TINYINT       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64)   NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP     NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_sdes_step_id` (`step_id`)
) ENGINE = InnoDB 
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '稳测设备执行步骤表';

CREATE TABLE IF NOT EXISTS `stability_device_execution_step_content`
(
    `id`         INT           NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`    VARCHAR(64)   NOT NULL COMMENT '任务ID',
    `step_id`    VARCHAR(64)   NOT NULL COMMENT '步骤ID',
    `content`    VARCHAR(1024) NULL COMMENT '步骤分片内容',
    `index`      INT           NOT NULL DEFAULT 1 COMMENT '步骤分片内容索引',
    `deleted`    TINYINT       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64)   NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP     NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_sdesc_step_id_index` (`step_id`, `index`)
) ENGINE = InnoDB 
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '稳测设备执行步骤内容表';

CREATE TABLE IF NOT EXISTS `stability_device_perf_data`
(
    `id`         INT         NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`    VARCHAR(64) NOT NULL COMMENT '任务ID',
    `execute_id` VARCHAR(64) NOT NULL COMMENT '执行ID',
    `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
    `udid`       VARCHAR(64) NOT NULL COMMENT '设备编号',
    `data_type`  VARCHAR(16) NOT NULL COMMENT '数据类型',
    `interval`   INT         NOT NULL COMMENT '采集间隔，单位毫秒',
    `series`     VARCHAR(16) NOT NULL COMMENT '指标名称',
    `unit`       VARCHAR(8)  NOT NULL DEFAULT '' COMMENT '单位',
    `x`          VARCHAR(64) NOT NULL COMMENT 'X轴数据',
    `y`          VARCHAR(64) NOT NULL COMMENT 'Y轴数据',
    `deleted`    TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_sdpd_task_id_execute_id_project_id_udid_data_type_series_x` (`task_id`, `execute_id`, `project_id`, `udid`, `data_type`, `series`, `x`),
    KEY `ix_sdpd_task_id_execute_id_project_id_data_type` (`task_id`, `execute_id`, `project_id`, `data_type`),
    KEY `ix_sdpd_task_id_project_id_udid_data_type` (`task_id`, `project_id`, `udid`, `data_type`),
    KEY `ix_sdpd_task_id_project_id_data_type` (`task_id`, `project_id`, `data_type`)
) ENGINE = InnoDB 
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='稳测设备性能数据表';

CREATE TABLE IF NOT EXISTS `stability_device_activity_record`
(
    `id`         INT         NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `task_id`    VARCHAR(64) NOT NULL COMMENT '任务ID',
    `execute_id` VARCHAR(64) NOT NULL COMMENT '执行ID',
    `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
    `udid`       VARCHAR(64) NOT NULL COMMENT '设备编号',
    `name`       VARCHAR(64) NOT NULL COMMENT 'activity名称',
    `covered`    TINYINT     NOT NULL DEFAULT 0 COMMENT '是否被覆盖（未覆盖，已覆盖）',
    `deleted`    TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_sdar_task_id_execute_id_project_id_udid` (`task_id`, `execute_id`, `project_id`, `udid`)
) ENGINE = InnoDB 
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='稳测设备activity记录表';