package saveDevicePerfDataTask

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
)

type Logic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLogic(ctx context.Context, svcCtx *svc.ServiceContext) *Logic {
	return &Logic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *Logic) SaveDevicePerfData(info *commonpb.SaveDevicePerfDataTaskInfo) error {
	switch info.GetUsage() {
	case commonpb.DeviceUsage_UI_TESTING:
		return l.saveUIDevicePerfData(info)
	case commonpb.DeviceUsage_STABILITY_TESTING:
		// TODO: 待实现
		return nil
	default:
		return errors.Errorf("unknown device usage: %s", protobuf.GetEnumStringOf(info.GetUsage()))
	}
}

func (l *Logic) saveUIDevicePerfData(info *commonpb.SaveDevicePerfDataTaskInfo) error {
	key := fmt.Sprintf(
		"%s:%s:%s:%s:%s:%s",
		common.ConstLockUIDevicePerfDataTaskIDExecuteIDProjectIDUDIDDataTypePrefix,
		info.GetTaskId(), info.GetExecuteId(), info.GetProjectId(), info.GetUdid(),
		protobuf.GetEnumStringOf(info.GetDataType()),
	)
	fn := func() error {
		record, err := l.svcCtx.UIDevicePerfDataModel.FindOneByX(
			l.ctx, info.GetTaskId(), info.GetExecuteId(), info.GetProjectId(), info.GetUdid(),
			protobuf.GetEnumStringOf(info.GetDataType()), info.GetSeries(), info.GetX(),
		)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find ui device perf data, info: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(info), err,
			)
		}

		now := time.Now()
		if record == nil {
			record = &model.UiDevicePerfData{
				TaskId:    info.GetTaskId(),
				ExecuteId: info.GetExecuteId(),
				ProjectId: info.GetProjectId(),
				Udid:      info.GetUdid(),
				DataType:  protobuf.GetEnumStringOf(info.GetDataType()),
				Interval:  info.GetInterval(),
				Series:    info.GetSeries(),
				Unit:      info.GetUnit(),
				X:         info.GetX(),
				Y:         info.GetY(),
				CreatedBy: info.GetExecutedBy(),
				UpdatedBy: info.GetExecutedBy(),
				CreatedAt: now,
				UpdatedAt: now,
			}
			if _, err = l.svcCtx.UIDevicePerfDataModel.Insert(l.ctx, nil, record); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIDevicePerfDataModel.Table(), jsonx.MarshalIgnoreError(record), err,
				)
			}
		} else {
			record.Interval = info.GetInterval()
			record.Unit = info.GetUnit()
			record.Y = info.GetY()
			record.UpdatedBy = info.GetExecutedBy()
			record.UpdatedAt = now
			if _, err = l.svcCtx.UIDevicePerfDataModel.Update(l.ctx, nil, record); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIDevicePerfDataModel.Table(), jsonx.MarshalIgnoreError(record), err,
				)
			}
		}

		return nil
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
