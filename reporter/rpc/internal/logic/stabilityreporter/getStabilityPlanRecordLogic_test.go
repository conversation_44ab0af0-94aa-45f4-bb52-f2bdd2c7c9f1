package stabilityreporterlogic_test

import (
	"testing"

	"google.golang.org/protobuf/types/known/structpb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

func TestGetStabilityPlanRecordLogic(t *testing.T) {
	component := managerpb.StabilityPlanComponent{
		ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
		PlanId:      "stability_plan_id:4RtFnvfyFmw-Wc3YZo8uo",
		Name:        "稳定性测试计划 - D",
		Description: "稳定性测试计划 - D 描述",
		Type:        commonpb.TriggerMode_MANUAL,
		MetaData: &managerpb.StabilityPlanMetaData{
			AccountConfig: &commonpb.AccountConfig{
				ProjectId:    "project_id:Kqllt5-9fA-I5UOdhjA5d",
				ConfigId:     "account_config_id:fwxPKaEKF0a4gxJL-Dftx",
				Name:         "测开验证环境_hjh",
				Description:  "",
				ProductType:  17,
				ProductName:  "TT_APP",
				PoolEnvTable: "t_tt_1648447543597",
				PoolEnvName:  "测开验证环境_hjh",
			},
			LarkChats: []*commonpb.LarkChat{
				{
					ChatId: "oc_abb0dfb67442732f489c56844e798d52",
					Name:   "探星飞书通知群",
				},
			},
			DeviceType:   commonpb.DeviceType_REAL_PHONE,
			PlatformType: commonpb.PlatformType_ANDROID,
			Devices: &commonpb.StabilityCustomDevices{
				Devices: &commonpb.StabilityCustomDevices_Udids{
					Udids: &structpb.ListValue{
						Values: []*structpb.Value{
							{
								Kind: &structpb.Value_StringValue{
									StringValue: "PXUYD22628002359",
								},
							},
							{
								Kind: &structpb.Value_StringValue{
									StringValue: "LZYTYLZT9HFI6DLN",
								},
							},
						},
					},
				},
			},
			PackageName:     "com.yiyou.ga",
			AppDownloadLink: "http://d-internal.ttyuyin.com/d/app/GAClient-master/download?code=Ni42Ni4zQDQ3MTcz",
			Activities:      []string{"com.yiyou.ga.client.BlankActivity"},
			CustomScript: &commonpb.StabilityCustomScript{
				Script: &commonpb.StabilityCustomScript_Image{
					Image: "custom_script_image:latest",
				},
			},
			Duration: 120,
		},
		State:        managerpb.CommonState_CS_ENABLE,
		MaintainedBy: "T1704",
		CreatedBy:    "T5210",
		UpdatedBy:    "T1704",
	}
	s, err := protobuf.MarshalJSONToString(&component)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(s)
}
