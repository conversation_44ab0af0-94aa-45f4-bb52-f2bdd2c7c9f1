package stabilityreporterlogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListStabilityPlanRecordLogic struct {
	*BaseLogic
}

func NewListStabilityPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListStabilityPlanRecordLogic {
	return &ListStabilityPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ListStabilityPlanRecord 获取稳测计划的执行记录
func (l *ListStabilityPlanRecordLogic) ListStabilityPlanRecord(in *pb.ListStabilityPlanRecordReq) (
	out *pb.ListStabilityPlanRecordResp, err error,
) {
	out = &pb.ListStabilityPlanRecordResp{}

	selectBuilder, countBuilder := l.svcCtx.StabilityPlanExecutionRecordModel.GenerateSearchStabilityPlanExecutionRecordSqlBuilder(
		model.SearchStabilityPlanExecutionRecordReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID:  in.GetProjectId(),
				Condition:  in.GetCondition(),
				Pagination: in.GetPagination(),
				Sort:       rpc.ConvertSortFields(in.GetSort()),
			},
			PlanID: in.GetPlanId(),
		},
	)

	count, err := l.svcCtx.StabilityPlanExecutionRecordModel.FindCountStabilityPlanExecutionRecords(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count stability plan execution records, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	records, err := l.svcCtx.StabilityPlanExecutionRecordModel.FindStabilityPlanExecutionRecords(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find stability plan execution records, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.StabilityPlanRecordItem, 0, len(records))
	for _, record := range records {
		item := &pb.StabilityPlanRecordItem{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy stability plan execution record to response, record: %+v, error: %+v",
				record, err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = uint64(math.Ceil(float64(out.TotalCount) / float64(out.PageSize)))
	}

	return out, nil
}
