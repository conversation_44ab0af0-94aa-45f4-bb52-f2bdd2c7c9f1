package stabilityreporterlogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetStabilityPlanRecordLogic struct {
	*BaseLogic
}

func NewGetStabilityPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetStabilityPlanRecordLogic {
	return &GetStabilityPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetStabilityPlanRecord 获取稳测执行报告的计划信息
func (l *GetStabilityPlanRecordLogic) GetStabilityPlanRecord(in *pb.GetStabilityPlanRecordReq) (
	out *pb.GetStabilityPlanRecordResp, err error,
) {
	record, err := l.svcCtx.StabilityPlanExecutionRecordModel.FindOneByTaskIDExecuteIDProjectID(
		l.ctx, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find stability plan execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the stability plan execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
					in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
				),
			)
		}
	}

	out = &pb.GetStabilityPlanRecordResp{
		Item:     &pb.StabilityPlanRecordItem{},
		MetaData: &pb.StabilityPlanMetaData{},
	}
	if err = utils.Copy(out.Item, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy stability plan execution record to response, record: %+v, error: %+v",
			record, err,
		)
	}

	if record.ExecuteData.Valid {
		var component managerpb.StabilityPlanComponent
		if err = protobuf.UnmarshalJSONFromString(record.ExecuteData.String, &component); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to unmarshal stability plan component, component: %+v, error: %+v",
				record.ExecuteData.String, err,
			)
		}
		if err = utils.Copy(out.MetaData, component.GetMetaData(), l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy stability plan component metadata to response, metadata: %+v, error: %+v",
				component.GetMetaData(), err,
			)
		}
	}

	return out, nil
}
