package stabilityreporterlogic_test

import (
	"database/sql"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

func TestSearchStabilityPlanRecordLogic(t *testing.T) {
	resp := model.StabilityPlanExecutionRecord{
		PlanId: "123",
		Status: sql.NullString{
			String: "success",
			Valid:  true,
		},
		StartedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		Cleaned:   1,
		CreatedAt: time.Now(),
	}
	t.Logf("%+v", resp)

	converters := []utils.TypeConverter{}

	out := &pb.StabilityPlanRecordItem{}
	if err := utils.Copy(out, resp, converters...); err != nil {
		t.Fatalf(
			"failed to copy resp to out, resp: %s, error: %+v",
			jsonx.MarshalIgnoreError(resp), err,
		)
	}
	t.Logf("%+v", out)
}
