package stabilityreporterlogic_test

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/config"
)

func TestGetStabilityDevicePerfDataLogic(t *testing.T) {
	var c config.Config
	conf.MustLoad("../../../etc/reporter.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	uiDevicePerfDataModel := model.NewUiDevicePerfDataModel(sqlConn)
	stabilityDevicePerfDataModel := model.NewStabilityDevicePerfDataModel(sqlConn)

	taskID := "task_id:gfknYnBVgtDDA6VbEt1tR"

	items, err := uiDevicePerfDataModel.FindNoCacheByQuery(ctx, uiDevicePerfDataModel.SelectBuilder().Where("`task_id` = ?", taskID))
	if err != nil {
		t.Fatal(err)
	}

	for _, item := range items {
		_, err = stabilityDevicePerfDataModel.Insert(ctx, sqlConn, &model.StabilityDevicePerfData{
			TaskId:    "task_id:kBaJ_s2Td7SmanLX2LIJD",
			ExecuteId: "execute_id:9KXRTwmXmoDeB-czJ8vXj",
			ProjectId: item.ProjectId,
			Udid:      item.Udid,
			DataType:  item.DataType,
			Interval:  item.Interval,
			Series:    item.Series,
			Unit:      item.Unit,
			X:         item.X,
			Y:         item.Y,
			CreatedBy: "T1704",
			UpdatedBy: "T1704",
		})
		if err != nil {
			t.Fatal(err)
		}
	}
}
