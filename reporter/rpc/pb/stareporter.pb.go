// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: reporter/stareporter.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StabilityPlanRecord struct {
	state           protoimpl.MessageState     `protogen:"open.v1"`
	TaskId          string                     `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                              // 任务ID
	ExecuteId       string                     `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                     // 稳定性测试计划执行ID
	ProjectId       string                     `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                    // 项目ID
	PlanId          string                     `protobuf:"bytes,12,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                             // 计划ID
	PlanName        string                     `protobuf:"bytes,13,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                       // 计划名称
	Type            pb.TriggerMode             `protobuf:"varint,14,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                      // 计划类型（手动、定时、接口）
	AccountConfig   *pb.AccountConfig          `protobuf:"bytes,21,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                        // 账号池配置
	DeviceType      pb.DeviceType              `protobuf:"varint,31,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`         // 设备类型（真机、云手机）
	PlatformType    pb.PlatformType            `protobuf:"varint,32,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、iOS）
	Devices         *pb.StabilityCustomDevices `protobuf:"bytes,23,opt,name=devices,proto3" json:"devices,omitempty"`                                                         // 自定义设备
	AppDownloadLink string                     `protobuf:"bytes,41,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                // App下载地址
	Activities      []string                   `protobuf:"bytes,42,rep,name=activities,proto3" json:"activities,omitempty"`                                                   // 指定的Activity列表
	CustomScript    *pb.StabilityCustomScript  `protobuf:"bytes,43,opt,name=custom_script,json=customScript,proto3" json:"custom_script,omitempty"`                           // 自定义脚本
	CostTime        int64                      `protobuf:"varint,51,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                                      // 执行耗时（单位为毫秒）
	StartedAt       int64                      `protobuf:"varint,52,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                                   // 开始时间
	EndedAt         int64                      `protobuf:"varint,53,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                                         // 结束时间
	ExecutedBy      string                     `protobuf:"bytes,54,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`                                 // 执行者
	CreatedBy       string                     `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                    // 创建者
	UpdatedBy       string                     `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                    // 更新者
	CreatedAt       int64                      `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                   // 创建时间
	UpdatedAt       int64                      `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                   // 更新时间
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StabilityPlanRecord) Reset() {
	*x = StabilityPlanRecord{}
	mi := &file_reporter_stareporter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityPlanRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityPlanRecord) ProtoMessage() {}

func (x *StabilityPlanRecord) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_stareporter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityPlanRecord.ProtoReflect.Descriptor instead.
func (*StabilityPlanRecord) Descriptor() ([]byte, []int) {
	return file_reporter_stareporter_proto_rawDescGZIP(), []int{0}
}

func (x *StabilityPlanRecord) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StabilityPlanRecord) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *StabilityPlanRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StabilityPlanRecord) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *StabilityPlanRecord) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *StabilityPlanRecord) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *StabilityPlanRecord) GetAccountConfig() *pb.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *StabilityPlanRecord) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *StabilityPlanRecord) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *StabilityPlanRecord) GetDevices() *pb.StabilityCustomDevices {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *StabilityPlanRecord) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *StabilityPlanRecord) GetActivities() []string {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *StabilityPlanRecord) GetCustomScript() *pb.StabilityCustomScript {
	if x != nil {
		return x.CustomScript
	}
	return nil
}

func (x *StabilityPlanRecord) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *StabilityPlanRecord) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *StabilityPlanRecord) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *StabilityPlanRecord) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

func (x *StabilityPlanRecord) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *StabilityPlanRecord) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *StabilityPlanRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *StabilityPlanRecord) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type StabilityCaseStep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`           // 任务ID
	StepId        string                 `protobuf:"bytes,2,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`           // 步骤ID
	Stage         pb.TestStage           `protobuf:"varint,3,opt,name=stage,proto3,enum=common.TestStage" json:"stage,omitempty"`    // 阶段
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                             // 步骤名称
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                         // 执行状态（结果）
	Content       string                 `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`                       // 步骤内容
	StartedAt     int64                  `protobuf:"varint,7,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"` // 开始时间
	EndedAt       int64                  `protobuf:"varint,8,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`       // 结束时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityCaseStep) Reset() {
	*x = StabilityCaseStep{}
	mi := &file_reporter_stareporter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityCaseStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityCaseStep) ProtoMessage() {}

func (x *StabilityCaseStep) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_stareporter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityCaseStep.ProtoReflect.Descriptor instead.
func (*StabilityCaseStep) Descriptor() ([]byte, []int) {
	return file_reporter_stareporter_proto_rawDescGZIP(), []int{1}
}

func (x *StabilityCaseStep) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StabilityCaseStep) GetStepId() string {
	if x != nil {
		return x.StepId
	}
	return ""
}

func (x *StabilityCaseStep) GetStage() pb.TestStage {
	if x != nil {
		return x.Stage
	}
	return pb.TestStage(0)
}

func (x *StabilityCaseStep) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StabilityCaseStep) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StabilityCaseStep) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *StabilityCaseStep) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *StabilityCaseStep) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

type StabilityPlanRecordItem struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                  // 项目ID
	PlanId         string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                           // 计划ID
	PlanName       string                 `protobuf:"bytes,3,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                     // 计划名称
	TriggerMode    string                 `protobuf:"bytes,4,opt,name=trigger_mode,json=triggerMode,proto3" json:"trigger_mode,omitempty"`            // 触发方式
	TaskId         string                 `protobuf:"bytes,5,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                           // 任务ID
	ExecuteId      string                 `protobuf:"bytes,6,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                  // 执行ID
	Status         string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`                                         // 执行状态（结果）
	SuccessDevice  int64                  `protobuf:"varint,8,opt,name=success_device,json=successDevice,proto3" json:"success_device,omitempty"`     // 成功的设备数
	FailureDevice  int64                  `protobuf:"varint,9,opt,name=failure_device,json=failureDevice,proto3" json:"failure_device,omitempty"`     // 失败的设备数
	TotalDevice    int64                  `protobuf:"varint,10,opt,name=total_device,json=totalDevice,proto3" json:"total_device,omitempty"`          // 测试设备总数
	CostTime       int64                  `protobuf:"varint,11,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                   // 实际执行时长（单位：秒）
	TargetDuration int64                  `protobuf:"varint,12,opt,name=target_duration,json=targetDuration,proto3" json:"target_duration,omitempty"` // 目标运行时长（单位：分）
	ExecutedBy     string                 `protobuf:"bytes,13,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`              // 执行者
	StartedAt      int64                  `protobuf:"varint,14,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                // 开始时间
	EndedAt        int64                  `protobuf:"varint,15,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                      // 结束时间
	CreatedAt      int64                  `protobuf:"varint,16,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                // 创建时间
	UpdatedAt      int64                  `protobuf:"varint,17,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                // 更新时间
	Cleaned        bool                   `protobuf:"varint,18,opt,name=cleaned,proto3" json:"cleaned,omitempty"`                                     // 是否被清理
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *StabilityPlanRecordItem) Reset() {
	*x = StabilityPlanRecordItem{}
	mi := &file_reporter_stareporter_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityPlanRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityPlanRecordItem) ProtoMessage() {}

func (x *StabilityPlanRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_stareporter_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityPlanRecordItem.ProtoReflect.Descriptor instead.
func (*StabilityPlanRecordItem) Descriptor() ([]byte, []int) {
	return file_reporter_stareporter_proto_rawDescGZIP(), []int{2}
}

func (x *StabilityPlanRecordItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StabilityPlanRecordItem) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *StabilityPlanRecordItem) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *StabilityPlanRecordItem) GetTriggerMode() string {
	if x != nil {
		return x.TriggerMode
	}
	return ""
}

func (x *StabilityPlanRecordItem) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StabilityPlanRecordItem) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *StabilityPlanRecordItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StabilityPlanRecordItem) GetSuccessDevice() int64 {
	if x != nil {
		return x.SuccessDevice
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetFailureDevice() int64 {
	if x != nil {
		return x.FailureDevice
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetTotalDevice() int64 {
	if x != nil {
		return x.TotalDevice
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetTargetDuration() int64 {
	if x != nil {
		return x.TargetDuration
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

func (x *StabilityPlanRecordItem) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *StabilityPlanRecordItem) GetCleaned() bool {
	if x != nil {
		return x.Cleaned
	}
	return false
}

type StabilityPlanMetaData struct {
	state           protoimpl.MessageState     `protogen:"open.v1"`
	AccountConfig   *pb.AccountConfig          `protobuf:"bytes,1,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                         // 池账号配置
	LarkChats       []*pb.LarkChat             `protobuf:"bytes,2,rep,name=lark_chats,json=larkChats,proto3" json:"lark_chats,omitempty"`                                     // 飞书群组列表
	DeviceType      pb.DeviceType              `protobuf:"varint,11,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`         // 设备类型（真机、云手机）
	PlatformType    pb.PlatformType            `protobuf:"varint,12,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、iOS）
	Devices         *pb.StabilityCustomDevices `protobuf:"bytes,13,opt,name=devices,proto3" json:"devices,omitempty"`                                                         // 自定义设备
	PackageName     string                     `protobuf:"bytes,21,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                              // App包名
	AppDownloadLink string                     `protobuf:"bytes,22,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                // App下载地址
	Activities      []string                   `protobuf:"bytes,23,rep,name=activities,proto3" json:"activities,omitempty"`                                                   // 指定的Activity列表
	CustomScript    *pb.StabilityCustomScript  `protobuf:"bytes,24,opt,name=custom_script,json=customScript,proto3" json:"custom_script,omitempty"`                           // 自定义脚本
	Duration        uint32                     `protobuf:"varint,31,opt,name=duration,proto3" json:"duration,omitempty"`                                                      // 执行时长
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StabilityPlanMetaData) Reset() {
	*x = StabilityPlanMetaData{}
	mi := &file_reporter_stareporter_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityPlanMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityPlanMetaData) ProtoMessage() {}

func (x *StabilityPlanMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_stareporter_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityPlanMetaData.ProtoReflect.Descriptor instead.
func (*StabilityPlanMetaData) Descriptor() ([]byte, []int) {
	return file_reporter_stareporter_proto_rawDescGZIP(), []int{3}
}

func (x *StabilityPlanMetaData) GetAccountConfig() *pb.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *StabilityPlanMetaData) GetLarkChats() []*pb.LarkChat {
	if x != nil {
		return x.LarkChats
	}
	return nil
}

func (x *StabilityPlanMetaData) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *StabilityPlanMetaData) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *StabilityPlanMetaData) GetDevices() *pb.StabilityCustomDevices {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *StabilityPlanMetaData) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *StabilityPlanMetaData) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *StabilityPlanMetaData) GetActivities() []string {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *StabilityPlanMetaData) GetCustomScript() *pb.StabilityCustomScript {
	if x != nil {
		return x.CustomScript
	}
	return nil
}

func (x *StabilityPlanMetaData) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type StabilityDeviceRecordItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`               // 项目ID
	TaskId        string                 `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                        // 任务ID
	ExecuteId     string                 `protobuf:"bytes,3,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`               // 执行ID
	PlanExecuteId string                 `protobuf:"bytes,4,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"` // 计划执行ID
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                                      // 执行状态（结果）
	CrashCount    int64                  `protobuf:"varint,6,opt,name=crash_count,json=crashCount,proto3" json:"crash_count,omitempty"`           // crash数量
	AnrCount      int64                  `protobuf:"varint,7,opt,name=anr_count,json=anrCount,proto3" json:"anr_count,omitempty"`                 // anr数量
	CostTime      int64                  `protobuf:"varint,8,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                 // 实际执行时长（单位：秒）
	StartedAt     int64                  `protobuf:"varint,9,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`              // 开始时间
	EndedAt       int64                  `protobuf:"varint,10,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                   // 结束时间
	// Device
	Device        *pb1.Device `protobuf:"bytes,99,opt,name=device,proto3" json:"device,omitempty"` // 设备信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityDeviceRecordItem) Reset() {
	*x = StabilityDeviceRecordItem{}
	mi := &file_reporter_stareporter_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityDeviceRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityDeviceRecordItem) ProtoMessage() {}

func (x *StabilityDeviceRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_stareporter_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityDeviceRecordItem.ProtoReflect.Descriptor instead.
func (*StabilityDeviceRecordItem) Descriptor() ([]byte, []int) {
	return file_reporter_stareporter_proto_rawDescGZIP(), []int{4}
}

func (x *StabilityDeviceRecordItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StabilityDeviceRecordItem) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StabilityDeviceRecordItem) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *StabilityDeviceRecordItem) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *StabilityDeviceRecordItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StabilityDeviceRecordItem) GetCrashCount() int64 {
	if x != nil {
		return x.CrashCount
	}
	return 0
}

func (x *StabilityDeviceRecordItem) GetAnrCount() int64 {
	if x != nil {
		return x.AnrCount
	}
	return 0
}

func (x *StabilityDeviceRecordItem) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *StabilityDeviceRecordItem) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *StabilityDeviceRecordItem) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *StabilityDeviceRecordItem) GetDevice() *pb1.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type StabilityDeviceStep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`           // 任务ID
	StepId        string                 `protobuf:"bytes,2,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`           // 步骤ID
	Stage         pb.TestStage           `protobuf:"varint,3,opt,name=stage,proto3,enum=common.TestStage" json:"stage,omitempty"`    // 阶段
	Index         int64                  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`                          // 步骤索引
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                             // 步骤名称
	Status        string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`                         // 执行状态（结果）
	Content       string                 `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`                       // 步骤内容
	StartedAt     int64                  `protobuf:"varint,8,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"` // 开始时间
	EndedAt       int64                  `protobuf:"varint,9,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`       // 结束时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityDeviceStep) Reset() {
	*x = StabilityDeviceStep{}
	mi := &file_reporter_stareporter_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityDeviceStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityDeviceStep) ProtoMessage() {}

func (x *StabilityDeviceStep) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_stareporter_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityDeviceStep.ProtoReflect.Descriptor instead.
func (*StabilityDeviceStep) Descriptor() ([]byte, []int) {
	return file_reporter_stareporter_proto_rawDescGZIP(), []int{5}
}

func (x *StabilityDeviceStep) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StabilityDeviceStep) GetStepId() string {
	if x != nil {
		return x.StepId
	}
	return ""
}

func (x *StabilityDeviceStep) GetStage() pb.TestStage {
	if x != nil {
		return x.Stage
	}
	return pb.TestStage(0)
}

func (x *StabilityDeviceStep) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *StabilityDeviceStep) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StabilityDeviceStep) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StabilityDeviceStep) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *StabilityDeviceStep) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *StabilityDeviceStep) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

var File_reporter_stareporter_proto protoreflect.FileDescriptor

var file_reporter_stareporter_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c,
	0x61, 0x72, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x68, 0x75, 0x62, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xb7, 0x06, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50,
	0x6c, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a,
	0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a,
	0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x07,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x34, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x35, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x36, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xee, 0x01, 0x0a, 0x11,
	0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65,
	0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74,
	0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x65,
	0x70, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x22, 0xcb, 0x04, 0x0a,
	0x17, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x65, 0x64, 0x22, 0xff, 0x03, 0x0a, 0x15, 0x53,
	0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x2f, 0x0a, 0x0a, 0x6c, 0x61, 0x72, 0x6b, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4c, 0x61, 0x72, 0x6b, 0x43, 0x68, 0x61, 0x74, 0x52, 0x09, 0x6c, 0x61, 0x72, 0x6b, 0x43, 0x68,
	0x61, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x52, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2a, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1e, 0x0a, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0d,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf2, 0x02, 0x0a,
	0x19, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x6e, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x6e, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x63, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68,
	0x75, 0x62, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x22, 0x86, 0x02, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x65, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x65, 0x70, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x42, 0x42, 0x5a, 0x40, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_reporter_stareporter_proto_rawDescOnce sync.Once
	file_reporter_stareporter_proto_rawDescData = file_reporter_stareporter_proto_rawDesc
)

func file_reporter_stareporter_proto_rawDescGZIP() []byte {
	file_reporter_stareporter_proto_rawDescOnce.Do(func() {
		file_reporter_stareporter_proto_rawDescData = protoimpl.X.CompressGZIP(file_reporter_stareporter_proto_rawDescData)
	})
	return file_reporter_stareporter_proto_rawDescData
}

var file_reporter_stareporter_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_reporter_stareporter_proto_goTypes = []any{
	(*StabilityPlanRecord)(nil),       // 0: reporter.StabilityPlanRecord
	(*StabilityCaseStep)(nil),         // 1: reporter.StabilityCaseStep
	(*StabilityPlanRecordItem)(nil),   // 2: reporter.StabilityPlanRecordItem
	(*StabilityPlanMetaData)(nil),     // 3: reporter.StabilityPlanMetaData
	(*StabilityDeviceRecordItem)(nil), // 4: reporter.StabilityDeviceRecordItem
	(*StabilityDeviceStep)(nil),       // 5: reporter.StabilityDeviceStep
	(pb.TriggerMode)(0),               // 6: common.TriggerMode
	(*pb.AccountConfig)(nil),          // 7: common.AccountConfig
	(pb.DeviceType)(0),                // 8: common.DeviceType
	(pb.PlatformType)(0),              // 9: common.PlatformType
	(*pb.StabilityCustomDevices)(nil), // 10: common.StabilityCustomDevices
	(*pb.StabilityCustomScript)(nil),  // 11: common.StabilityCustomScript
	(pb.TestStage)(0),                 // 12: common.TestStage
	(*pb.LarkChat)(nil),               // 13: common.LarkChat
	(*pb1.Device)(nil),                // 14: devicehub.Device
}
var file_reporter_stareporter_proto_depIdxs = []int32{
	6,  // 0: reporter.StabilityPlanRecord.type:type_name -> common.TriggerMode
	7,  // 1: reporter.StabilityPlanRecord.account_config:type_name -> common.AccountConfig
	8,  // 2: reporter.StabilityPlanRecord.device_type:type_name -> common.DeviceType
	9,  // 3: reporter.StabilityPlanRecord.platform_type:type_name -> common.PlatformType
	10, // 4: reporter.StabilityPlanRecord.devices:type_name -> common.StabilityCustomDevices
	11, // 5: reporter.StabilityPlanRecord.custom_script:type_name -> common.StabilityCustomScript
	12, // 6: reporter.StabilityCaseStep.stage:type_name -> common.TestStage
	7,  // 7: reporter.StabilityPlanMetaData.account_config:type_name -> common.AccountConfig
	13, // 8: reporter.StabilityPlanMetaData.lark_chats:type_name -> common.LarkChat
	8,  // 9: reporter.StabilityPlanMetaData.device_type:type_name -> common.DeviceType
	9,  // 10: reporter.StabilityPlanMetaData.platform_type:type_name -> common.PlatformType
	10, // 11: reporter.StabilityPlanMetaData.devices:type_name -> common.StabilityCustomDevices
	11, // 12: reporter.StabilityPlanMetaData.custom_script:type_name -> common.StabilityCustomScript
	14, // 13: reporter.StabilityDeviceRecordItem.device:type_name -> devicehub.Device
	12, // 14: reporter.StabilityDeviceStep.stage:type_name -> common.TestStage
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_reporter_stareporter_proto_init() }
func file_reporter_stareporter_proto_init() {
	if File_reporter_stareporter_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_reporter_stareporter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_reporter_stareporter_proto_goTypes,
		DependencyIndexes: file_reporter_stareporter_proto_depIdxs,
		MessageInfos:      file_reporter_stareporter_proto_msgTypes,
	}.Build()
	File_reporter_stareporter_proto = out.File
	file_reporter_stareporter_proto_rawDesc = nil
	file_reporter_stareporter_proto_goTypes = nil
	file_reporter_stareporter_proto_depIdxs = nil
}
