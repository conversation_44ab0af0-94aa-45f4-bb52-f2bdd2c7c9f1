// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: reporter/reporter.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Reporter_CreateRecord_FullMethodName               = "/reporter.reporter/createRecord"
	Reporter_ModifyRecord_FullMethodName               = "/reporter.reporter/modifyRecord"
	Reporter_GetExecuteRecord_FullMethodName           = "/reporter.reporter/getExecuteRecord"
	Reporter_GetParentRecord_FullMethodName            = "/reporter.reporter/getParentRecord"
	Reporter_GetChildrenRecord_FullMethodName          = "/reporter.reporter/getChildrenRecord"
	Reporter_CreateInterfaceRecord_FullMethodName      = "/reporter.reporter/createInterfaceRecord"
	Reporter_ModifyInterfaceRecord_FullMethodName      = "/reporter.reporter/modifyInterfaceRecord"
	Reporter_GetCaseLatestRecord_FullMethodName        = "/reporter.reporter/getCaseLatestRecord"
	Reporter_ListInterfaceRecord_FullMethodName        = "/reporter.reporter/listInterfaceRecord"
	Reporter_GetInterfaceRecord_FullMethodName         = "/reporter.reporter/getInterfaceRecord"
	Reporter_CreateSuiteRecord_FullMethodName          = "/reporter.reporter/createSuiteRecord"
	Reporter_ModifySuiteRecord_FullMethodName          = "/reporter.reporter/modifySuiteRecord"
	Reporter_ListSuiteRecord_FullMethodName            = "/reporter.reporter/listSuiteRecord"
	Reporter_GetSuiteRecord_FullMethodName             = "/reporter.reporter/getSuiteRecord"
	Reporter_CreateServiceRecord_FullMethodName        = "/reporter.reporter/createServiceRecord"
	Reporter_ModifyServiceRecord_FullMethodName        = "/reporter.reporter/modifyServiceRecord"
	Reporter_ListServiceRecord_FullMethodName          = "/reporter.reporter/listServiceRecord"
	Reporter_GetServiceRecord_FullMethodName           = "/reporter.reporter/getServiceRecord"
	Reporter_CreatePlanRecord_FullMethodName           = "/reporter.reporter/createPlanRecord"
	Reporter_ModifyPlanRecord_FullMethodName           = "/reporter.reporter/modifyPlanRecord"
	Reporter_ListPlanRecord_FullMethodName             = "/reporter.reporter/listPlanRecord"
	Reporter_GetPlanRecord_FullMethodName              = "/reporter.reporter/getPlanRecord"
	Reporter_GetPlanTimeScale_FullMethodName           = "/reporter.reporter/getPlanTimeScale"
	Reporter_GetPlanSummary_FullMethodName             = "/reporter.reporter/getPlanSummary"
	Reporter_GetPlanCasesInfo_FullMethodName           = "/reporter.reporter/getPlanCasesInfo"
	Reporter_ListFailCaseForPlanRecord_FullMethodName  = "/reporter.reporter/listFailCaseForPlanRecord"
	Reporter_DelCaseFailStatForPlan_FullMethodName     = "/reporter.reporter/delCaseFailStatForPlan"
	Reporter_CountFailedCaseInLastNDays_FullMethodName = "/reporter.reporter/CountFailedCaseInLastNDays"
)

// ReporterClient is the client API for Reporter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReporterClient interface {
	// 组件组及用例记录 RPC接口
	CreateRecord(ctx context.Context, in *PutRecordRequest, opts ...grpc.CallOption) (*CreateRecordResponse, error)
	ModifyRecord(ctx context.Context, in *PutRecordRequest, opts ...grpc.CallOption) (*ModifyRecordResponse, error)
	// 查询自身的RPC接口
	GetExecuteRecord(ctx context.Context, in *GetExecuteRecordRequest, opts ...grpc.CallOption) (*GetExecuteRecordResponse, error)
	// 查询Parent的RPC接口
	GetParentRecord(ctx context.Context, in *GetParentRecordRequest, opts ...grpc.CallOption) (*GetParentRecordResponse, error)
	// 查询Children的RPC接口
	GetChildrenRecord(ctx context.Context, in *GetChildrenRecordRequest, opts ...grpc.CallOption) (*GetChildrenRecordResponse, error)
	// `接口`
	// 创建接口记录
	CreateInterfaceRecord(ctx context.Context, in *PutInterfaceRecordRequest, opts ...grpc.CallOption) (*CreateInterfaceRecordResponse, error)
	// 修改接口记录
	ModifyInterfaceRecord(ctx context.Context, in *PutInterfaceRecordRequest, opts ...grpc.CallOption) (*ModifyInterfaceRecordResponse, error)
	// 获取接口中用例最新一次执行记录
	GetCaseLatestRecord(ctx context.Context, in *GetCaseLatestRecordRequest, opts ...grpc.CallOption) (*GetCaseLatestRecordResponse, error)
	// 接口`调试`记录列表
	ListInterfaceRecord(ctx context.Context, in *ListInterfaceRecordRequest, opts ...grpc.CallOption) (*ListInterfaceRecordResponse, error)
	// 接口执行记录详情
	GetInterfaceRecord(ctx context.Context, in *GetInterfaceRecordRequest, opts ...grpc.CallOption) (*GetInterfaceRecordResponse, error)
	// `集合`
	// 创建集合记录
	CreateSuiteRecord(ctx context.Context, in *PutSuiteRecordRequest, opts ...grpc.CallOption) (*CreateSuiteRecordResponse, error)
	// 修改集合记录
	ModifySuiteRecord(ctx context.Context, in *PutSuiteRecordRequest, opts ...grpc.CallOption) (*ModifySuiteRecordResponse, error)
	// 集合`调试`记录列表
	ListSuiteRecord(ctx context.Context, in *ListSuiteRecordRequest, opts ...grpc.CallOption) (*ListSuiteRecordResponse, error)
	// 集合执行记录详情
	GetSuiteRecord(ctx context.Context, in *GetSuiteRecordRequest, opts ...grpc.CallOption) (*GetSuiteRecordResponse, error)
	// `精准测试服务`
	// 创建精准测试服务记录
	CreateServiceRecord(ctx context.Context, in *PutServiceRecordRequest, opts ...grpc.CallOption) (*CreateServiceRecordResponse, error)
	// 修改精准测试服务记录
	ModifyServiceRecord(ctx context.Context, in *PutServiceRecordRequest, opts ...grpc.CallOption) (*ModifyServiceRecordResponse, error)
	// 精准测试服务`调试`记录列表
	ListServiceRecord(ctx context.Context, in *ListServiceRecordRequest, opts ...grpc.CallOption) (*ListServiceRecordResponse, error)
	// 精准测试服务执行记录详情
	GetServiceRecord(ctx context.Context, in *GetServiceRecordRequest, opts ...grpc.CallOption) (*GetServiceRecordResponse, error)
	// `计划`
	// 创建计划记录
	CreatePlanRecord(ctx context.Context, in *PutPlanRecordRequest, opts ...grpc.CallOption) (*CreatePlanRecordResponse, error)
	// 修改计划记录
	ModifyPlanRecord(ctx context.Context, in *PutPlanRecordRequest, opts ...grpc.CallOption) (*ModifyPlanRecordResponse, error)
	// 计划执行记录列表
	ListPlanRecord(ctx context.Context, in *ListPlanRecordRequest, opts ...grpc.CallOption) (*ListPlanRecordResponse, error)
	// 计划执行记录详情
	GetPlanRecord(ctx context.Context, in *GetPlanRecordRequest, opts ...grpc.CallOption) (*GetPlanRecordResponse, error)
	// 计划执行详情查看其下各集合用例执行时间刻度信息
	GetPlanTimeScale(ctx context.Context, in *GetPlanTimeScaleRequest, opts ...grpc.CallOption) (*GetPlanTimeScaleResponse, error)
	// 获取测试计划执行报告（ci/cd专用）
	GetPlanSummary(ctx context.Context, in *GetPlanSummaryRequest, opts ...grpc.CallOption) (*GetPlanSummaryResponse, error)
	// 获取API计划关联用例信息
	GetPlanCasesInfo(ctx context.Context, in *GetPlanCasesInfoRequest, opts ...grpc.CallOption) (*GetPlanCasesInfoResponse, error)
	// `用例`
	// 用例失败管理计划列表
	ListFailCaseForPlanRecord(ctx context.Context, in *ListFailCaseRecordForPlanRequest, opts ...grpc.CallOption) (*ListFailCaseRecordForPlanResponse, error)
	DelCaseFailStatForPlan(ctx context.Context, in *DelCaseFailStatForPlanReq, opts ...grpc.CallOption) (*DelCaseFailStatForPlanResp, error)
	// CountFailedCaseInLastNDays 统计最近N天指定用例的失败数
	CountFailedCaseInLastNDays(ctx context.Context, in *CountFailedCaseInLastNDaysReq, opts ...grpc.CallOption) (*CountFailedCaseInLastNDaysResp, error)
}

type reporterClient struct {
	cc grpc.ClientConnInterface
}

func NewReporterClient(cc grpc.ClientConnInterface) ReporterClient {
	return &reporterClient{cc}
}

func (c *reporterClient) CreateRecord(ctx context.Context, in *PutRecordRequest, opts ...grpc.CallOption) (*CreateRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_CreateRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ModifyRecord(ctx context.Context, in *PutRecordRequest, opts ...grpc.CallOption) (*ModifyRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ModifyRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetExecuteRecord(ctx context.Context, in *GetExecuteRecordRequest, opts ...grpc.CallOption) (*GetExecuteRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetExecuteRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_GetExecuteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetParentRecord(ctx context.Context, in *GetParentRecordRequest, opts ...grpc.CallOption) (*GetParentRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetParentRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_GetParentRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetChildrenRecord(ctx context.Context, in *GetChildrenRecordRequest, opts ...grpc.CallOption) (*GetChildrenRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetChildrenRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_GetChildrenRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) CreateInterfaceRecord(ctx context.Context, in *PutInterfaceRecordRequest, opts ...grpc.CallOption) (*CreateInterfaceRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateInterfaceRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_CreateInterfaceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ModifyInterfaceRecord(ctx context.Context, in *PutInterfaceRecordRequest, opts ...grpc.CallOption) (*ModifyInterfaceRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyInterfaceRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ModifyInterfaceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetCaseLatestRecord(ctx context.Context, in *GetCaseLatestRecordRequest, opts ...grpc.CallOption) (*GetCaseLatestRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCaseLatestRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_GetCaseLatestRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ListInterfaceRecord(ctx context.Context, in *ListInterfaceRecordRequest, opts ...grpc.CallOption) (*ListInterfaceRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListInterfaceRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ListInterfaceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetInterfaceRecord(ctx context.Context, in *GetInterfaceRecordRequest, opts ...grpc.CallOption) (*GetInterfaceRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetInterfaceRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_GetInterfaceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) CreateSuiteRecord(ctx context.Context, in *PutSuiteRecordRequest, opts ...grpc.CallOption) (*CreateSuiteRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSuiteRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_CreateSuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ModifySuiteRecord(ctx context.Context, in *PutSuiteRecordRequest, opts ...grpc.CallOption) (*ModifySuiteRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifySuiteRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ModifySuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ListSuiteRecord(ctx context.Context, in *ListSuiteRecordRequest, opts ...grpc.CallOption) (*ListSuiteRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSuiteRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ListSuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetSuiteRecord(ctx context.Context, in *GetSuiteRecordRequest, opts ...grpc.CallOption) (*GetSuiteRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSuiteRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_GetSuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) CreateServiceRecord(ctx context.Context, in *PutServiceRecordRequest, opts ...grpc.CallOption) (*CreateServiceRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_CreateServiceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ModifyServiceRecord(ctx context.Context, in *PutServiceRecordRequest, opts ...grpc.CallOption) (*ModifyServiceRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyServiceRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ModifyServiceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ListServiceRecord(ctx context.Context, in *ListServiceRecordRequest, opts ...grpc.CallOption) (*ListServiceRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServiceRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ListServiceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetServiceRecord(ctx context.Context, in *GetServiceRecordRequest, opts ...grpc.CallOption) (*GetServiceRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_GetServiceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) CreatePlanRecord(ctx context.Context, in *PutPlanRecordRequest, opts ...grpc.CallOption) (*CreatePlanRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePlanRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_CreatePlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ModifyPlanRecord(ctx context.Context, in *PutPlanRecordRequest, opts ...grpc.CallOption) (*ModifyPlanRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPlanRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ModifyPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ListPlanRecord(ctx context.Context, in *ListPlanRecordRequest, opts ...grpc.CallOption) (*ListPlanRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListPlanRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_ListPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetPlanRecord(ctx context.Context, in *GetPlanRecordRequest, opts ...grpc.CallOption) (*GetPlanRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPlanRecordResponse)
	err := c.cc.Invoke(ctx, Reporter_GetPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetPlanTimeScale(ctx context.Context, in *GetPlanTimeScaleRequest, opts ...grpc.CallOption) (*GetPlanTimeScaleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPlanTimeScaleResponse)
	err := c.cc.Invoke(ctx, Reporter_GetPlanTimeScale_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetPlanSummary(ctx context.Context, in *GetPlanSummaryRequest, opts ...grpc.CallOption) (*GetPlanSummaryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPlanSummaryResponse)
	err := c.cc.Invoke(ctx, Reporter_GetPlanSummary_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) GetPlanCasesInfo(ctx context.Context, in *GetPlanCasesInfoRequest, opts ...grpc.CallOption) (*GetPlanCasesInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPlanCasesInfoResponse)
	err := c.cc.Invoke(ctx, Reporter_GetPlanCasesInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) ListFailCaseForPlanRecord(ctx context.Context, in *ListFailCaseRecordForPlanRequest, opts ...grpc.CallOption) (*ListFailCaseRecordForPlanResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListFailCaseRecordForPlanResponse)
	err := c.cc.Invoke(ctx, Reporter_ListFailCaseForPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) DelCaseFailStatForPlan(ctx context.Context, in *DelCaseFailStatForPlanReq, opts ...grpc.CallOption) (*DelCaseFailStatForPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DelCaseFailStatForPlanResp)
	err := c.cc.Invoke(ctx, Reporter_DelCaseFailStatForPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reporterClient) CountFailedCaseInLastNDays(ctx context.Context, in *CountFailedCaseInLastNDaysReq, opts ...grpc.CallOption) (*CountFailedCaseInLastNDaysResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountFailedCaseInLastNDaysResp)
	err := c.cc.Invoke(ctx, Reporter_CountFailedCaseInLastNDays_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReporterServer is the server API for Reporter service.
// All implementations must embed UnimplementedReporterServer
// for forward compatibility.
type ReporterServer interface {
	// 组件组及用例记录 RPC接口
	CreateRecord(context.Context, *PutRecordRequest) (*CreateRecordResponse, error)
	ModifyRecord(context.Context, *PutRecordRequest) (*ModifyRecordResponse, error)
	// 查询自身的RPC接口
	GetExecuteRecord(context.Context, *GetExecuteRecordRequest) (*GetExecuteRecordResponse, error)
	// 查询Parent的RPC接口
	GetParentRecord(context.Context, *GetParentRecordRequest) (*GetParentRecordResponse, error)
	// 查询Children的RPC接口
	GetChildrenRecord(context.Context, *GetChildrenRecordRequest) (*GetChildrenRecordResponse, error)
	// `接口`
	// 创建接口记录
	CreateInterfaceRecord(context.Context, *PutInterfaceRecordRequest) (*CreateInterfaceRecordResponse, error)
	// 修改接口记录
	ModifyInterfaceRecord(context.Context, *PutInterfaceRecordRequest) (*ModifyInterfaceRecordResponse, error)
	// 获取接口中用例最新一次执行记录
	GetCaseLatestRecord(context.Context, *GetCaseLatestRecordRequest) (*GetCaseLatestRecordResponse, error)
	// 接口`调试`记录列表
	ListInterfaceRecord(context.Context, *ListInterfaceRecordRequest) (*ListInterfaceRecordResponse, error)
	// 接口执行记录详情
	GetInterfaceRecord(context.Context, *GetInterfaceRecordRequest) (*GetInterfaceRecordResponse, error)
	// `集合`
	// 创建集合记录
	CreateSuiteRecord(context.Context, *PutSuiteRecordRequest) (*CreateSuiteRecordResponse, error)
	// 修改集合记录
	ModifySuiteRecord(context.Context, *PutSuiteRecordRequest) (*ModifySuiteRecordResponse, error)
	// 集合`调试`记录列表
	ListSuiteRecord(context.Context, *ListSuiteRecordRequest) (*ListSuiteRecordResponse, error)
	// 集合执行记录详情
	GetSuiteRecord(context.Context, *GetSuiteRecordRequest) (*GetSuiteRecordResponse, error)
	// `精准测试服务`
	// 创建精准测试服务记录
	CreateServiceRecord(context.Context, *PutServiceRecordRequest) (*CreateServiceRecordResponse, error)
	// 修改精准测试服务记录
	ModifyServiceRecord(context.Context, *PutServiceRecordRequest) (*ModifyServiceRecordResponse, error)
	// 精准测试服务`调试`记录列表
	ListServiceRecord(context.Context, *ListServiceRecordRequest) (*ListServiceRecordResponse, error)
	// 精准测试服务执行记录详情
	GetServiceRecord(context.Context, *GetServiceRecordRequest) (*GetServiceRecordResponse, error)
	// `计划`
	// 创建计划记录
	CreatePlanRecord(context.Context, *PutPlanRecordRequest) (*CreatePlanRecordResponse, error)
	// 修改计划记录
	ModifyPlanRecord(context.Context, *PutPlanRecordRequest) (*ModifyPlanRecordResponse, error)
	// 计划执行记录列表
	ListPlanRecord(context.Context, *ListPlanRecordRequest) (*ListPlanRecordResponse, error)
	// 计划执行记录详情
	GetPlanRecord(context.Context, *GetPlanRecordRequest) (*GetPlanRecordResponse, error)
	// 计划执行详情查看其下各集合用例执行时间刻度信息
	GetPlanTimeScale(context.Context, *GetPlanTimeScaleRequest) (*GetPlanTimeScaleResponse, error)
	// 获取测试计划执行报告（ci/cd专用）
	GetPlanSummary(context.Context, *GetPlanSummaryRequest) (*GetPlanSummaryResponse, error)
	// 获取API计划关联用例信息
	GetPlanCasesInfo(context.Context, *GetPlanCasesInfoRequest) (*GetPlanCasesInfoResponse, error)
	// `用例`
	// 用例失败管理计划列表
	ListFailCaseForPlanRecord(context.Context, *ListFailCaseRecordForPlanRequest) (*ListFailCaseRecordForPlanResponse, error)
	DelCaseFailStatForPlan(context.Context, *DelCaseFailStatForPlanReq) (*DelCaseFailStatForPlanResp, error)
	// CountFailedCaseInLastNDays 统计最近N天指定用例的失败数
	CountFailedCaseInLastNDays(context.Context, *CountFailedCaseInLastNDaysReq) (*CountFailedCaseInLastNDaysResp, error)
	mustEmbedUnimplementedReporterServer()
}

// UnimplementedReporterServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedReporterServer struct{}

func (UnimplementedReporterServer) CreateRecord(context.Context, *PutRecordRequest) (*CreateRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRecord not implemented")
}
func (UnimplementedReporterServer) ModifyRecord(context.Context, *PutRecordRequest) (*ModifyRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyRecord not implemented")
}
func (UnimplementedReporterServer) GetExecuteRecord(context.Context, *GetExecuteRecordRequest) (*GetExecuteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExecuteRecord not implemented")
}
func (UnimplementedReporterServer) GetParentRecord(context.Context, *GetParentRecordRequest) (*GetParentRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParentRecord not implemented")
}
func (UnimplementedReporterServer) GetChildrenRecord(context.Context, *GetChildrenRecordRequest) (*GetChildrenRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChildrenRecord not implemented")
}
func (UnimplementedReporterServer) CreateInterfaceRecord(context.Context, *PutInterfaceRecordRequest) (*CreateInterfaceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInterfaceRecord not implemented")
}
func (UnimplementedReporterServer) ModifyInterfaceRecord(context.Context, *PutInterfaceRecordRequest) (*ModifyInterfaceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyInterfaceRecord not implemented")
}
func (UnimplementedReporterServer) GetCaseLatestRecord(context.Context, *GetCaseLatestRecordRequest) (*GetCaseLatestRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCaseLatestRecord not implemented")
}
func (UnimplementedReporterServer) ListInterfaceRecord(context.Context, *ListInterfaceRecordRequest) (*ListInterfaceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInterfaceRecord not implemented")
}
func (UnimplementedReporterServer) GetInterfaceRecord(context.Context, *GetInterfaceRecordRequest) (*GetInterfaceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInterfaceRecord not implemented")
}
func (UnimplementedReporterServer) CreateSuiteRecord(context.Context, *PutSuiteRecordRequest) (*CreateSuiteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSuiteRecord not implemented")
}
func (UnimplementedReporterServer) ModifySuiteRecord(context.Context, *PutSuiteRecordRequest) (*ModifySuiteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifySuiteRecord not implemented")
}
func (UnimplementedReporterServer) ListSuiteRecord(context.Context, *ListSuiteRecordRequest) (*ListSuiteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSuiteRecord not implemented")
}
func (UnimplementedReporterServer) GetSuiteRecord(context.Context, *GetSuiteRecordRequest) (*GetSuiteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSuiteRecord not implemented")
}
func (UnimplementedReporterServer) CreateServiceRecord(context.Context, *PutServiceRecordRequest) (*CreateServiceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceRecord not implemented")
}
func (UnimplementedReporterServer) ModifyServiceRecord(context.Context, *PutServiceRecordRequest) (*ModifyServiceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyServiceRecord not implemented")
}
func (UnimplementedReporterServer) ListServiceRecord(context.Context, *ListServiceRecordRequest) (*ListServiceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceRecord not implemented")
}
func (UnimplementedReporterServer) GetServiceRecord(context.Context, *GetServiceRecordRequest) (*GetServiceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceRecord not implemented")
}
func (UnimplementedReporterServer) CreatePlanRecord(context.Context, *PutPlanRecordRequest) (*CreatePlanRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlanRecord not implemented")
}
func (UnimplementedReporterServer) ModifyPlanRecord(context.Context, *PutPlanRecordRequest) (*ModifyPlanRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPlanRecord not implemented")
}
func (UnimplementedReporterServer) ListPlanRecord(context.Context, *ListPlanRecordRequest) (*ListPlanRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlanRecord not implemented")
}
func (UnimplementedReporterServer) GetPlanRecord(context.Context, *GetPlanRecordRequest) (*GetPlanRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanRecord not implemented")
}
func (UnimplementedReporterServer) GetPlanTimeScale(context.Context, *GetPlanTimeScaleRequest) (*GetPlanTimeScaleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanTimeScale not implemented")
}
func (UnimplementedReporterServer) GetPlanSummary(context.Context, *GetPlanSummaryRequest) (*GetPlanSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanSummary not implemented")
}
func (UnimplementedReporterServer) GetPlanCasesInfo(context.Context, *GetPlanCasesInfoRequest) (*GetPlanCasesInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanCasesInfo not implemented")
}
func (UnimplementedReporterServer) ListFailCaseForPlanRecord(context.Context, *ListFailCaseRecordForPlanRequest) (*ListFailCaseRecordForPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFailCaseForPlanRecord not implemented")
}
func (UnimplementedReporterServer) DelCaseFailStatForPlan(context.Context, *DelCaseFailStatForPlanReq) (*DelCaseFailStatForPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelCaseFailStatForPlan not implemented")
}
func (UnimplementedReporterServer) CountFailedCaseInLastNDays(context.Context, *CountFailedCaseInLastNDaysReq) (*CountFailedCaseInLastNDaysResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountFailedCaseInLastNDays not implemented")
}
func (UnimplementedReporterServer) mustEmbedUnimplementedReporterServer() {}
func (UnimplementedReporterServer) testEmbeddedByValue()                  {}

// UnsafeReporterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReporterServer will
// result in compilation errors.
type UnsafeReporterServer interface {
	mustEmbedUnimplementedReporterServer()
}

func RegisterReporterServer(s grpc.ServiceRegistrar, srv ReporterServer) {
	// If the following call pancis, it indicates UnimplementedReporterServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Reporter_ServiceDesc, srv)
}

func _Reporter_CreateRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).CreateRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_CreateRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).CreateRecord(ctx, req.(*PutRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ModifyRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ModifyRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ModifyRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ModifyRecord(ctx, req.(*PutRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetExecuteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExecuteRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetExecuteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetExecuteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetExecuteRecord(ctx, req.(*GetExecuteRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetParentRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetParentRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetParentRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetParentRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetParentRecord(ctx, req.(*GetParentRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetChildrenRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChildrenRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetChildrenRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetChildrenRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetChildrenRecord(ctx, req.(*GetChildrenRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_CreateInterfaceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutInterfaceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).CreateInterfaceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_CreateInterfaceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).CreateInterfaceRecord(ctx, req.(*PutInterfaceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ModifyInterfaceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutInterfaceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ModifyInterfaceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ModifyInterfaceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ModifyInterfaceRecord(ctx, req.(*PutInterfaceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetCaseLatestRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCaseLatestRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetCaseLatestRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetCaseLatestRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetCaseLatestRecord(ctx, req.(*GetCaseLatestRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ListInterfaceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInterfaceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ListInterfaceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ListInterfaceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ListInterfaceRecord(ctx, req.(*ListInterfaceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetInterfaceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInterfaceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetInterfaceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetInterfaceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetInterfaceRecord(ctx, req.(*GetInterfaceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_CreateSuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutSuiteRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).CreateSuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_CreateSuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).CreateSuiteRecord(ctx, req.(*PutSuiteRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ModifySuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutSuiteRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ModifySuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ModifySuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ModifySuiteRecord(ctx, req.(*PutSuiteRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ListSuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSuiteRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ListSuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ListSuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ListSuiteRecord(ctx, req.(*ListSuiteRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetSuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSuiteRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetSuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetSuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetSuiteRecord(ctx, req.(*GetSuiteRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_CreateServiceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutServiceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).CreateServiceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_CreateServiceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).CreateServiceRecord(ctx, req.(*PutServiceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ModifyServiceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutServiceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ModifyServiceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ModifyServiceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ModifyServiceRecord(ctx, req.(*PutServiceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ListServiceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ListServiceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ListServiceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ListServiceRecord(ctx, req.(*ListServiceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetServiceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetServiceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetServiceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetServiceRecord(ctx, req.(*GetServiceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_CreatePlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutPlanRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).CreatePlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_CreatePlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).CreatePlanRecord(ctx, req.(*PutPlanRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ModifyPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutPlanRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ModifyPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ModifyPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ModifyPlanRecord(ctx, req.(*PutPlanRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ListPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPlanRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ListPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ListPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ListPlanRecord(ctx, req.(*ListPlanRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetPlanRecord(ctx, req.(*GetPlanRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetPlanTimeScale_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanTimeScaleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetPlanTimeScale(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetPlanTimeScale_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetPlanTimeScale(ctx, req.(*GetPlanTimeScaleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetPlanSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetPlanSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetPlanSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetPlanSummary(ctx, req.(*GetPlanSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_GetPlanCasesInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanCasesInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).GetPlanCasesInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_GetPlanCasesInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).GetPlanCasesInfo(ctx, req.(*GetPlanCasesInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_ListFailCaseForPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFailCaseRecordForPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).ListFailCaseForPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_ListFailCaseForPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).ListFailCaseForPlanRecord(ctx, req.(*ListFailCaseRecordForPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_DelCaseFailStatForPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCaseFailStatForPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).DelCaseFailStatForPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_DelCaseFailStatForPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).DelCaseFailStatForPlan(ctx, req.(*DelCaseFailStatForPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reporter_CountFailedCaseInLastNDays_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountFailedCaseInLastNDaysReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReporterServer).CountFailedCaseInLastNDays(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reporter_CountFailedCaseInLastNDays_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReporterServer).CountFailedCaseInLastNDays(ctx, req.(*CountFailedCaseInLastNDaysReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Reporter_ServiceDesc is the grpc.ServiceDesc for Reporter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Reporter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "reporter.reporter",
	HandlerType: (*ReporterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createRecord",
			Handler:    _Reporter_CreateRecord_Handler,
		},
		{
			MethodName: "modifyRecord",
			Handler:    _Reporter_ModifyRecord_Handler,
		},
		{
			MethodName: "getExecuteRecord",
			Handler:    _Reporter_GetExecuteRecord_Handler,
		},
		{
			MethodName: "getParentRecord",
			Handler:    _Reporter_GetParentRecord_Handler,
		},
		{
			MethodName: "getChildrenRecord",
			Handler:    _Reporter_GetChildrenRecord_Handler,
		},
		{
			MethodName: "createInterfaceRecord",
			Handler:    _Reporter_CreateInterfaceRecord_Handler,
		},
		{
			MethodName: "modifyInterfaceRecord",
			Handler:    _Reporter_ModifyInterfaceRecord_Handler,
		},
		{
			MethodName: "getCaseLatestRecord",
			Handler:    _Reporter_GetCaseLatestRecord_Handler,
		},
		{
			MethodName: "listInterfaceRecord",
			Handler:    _Reporter_ListInterfaceRecord_Handler,
		},
		{
			MethodName: "getInterfaceRecord",
			Handler:    _Reporter_GetInterfaceRecord_Handler,
		},
		{
			MethodName: "createSuiteRecord",
			Handler:    _Reporter_CreateSuiteRecord_Handler,
		},
		{
			MethodName: "modifySuiteRecord",
			Handler:    _Reporter_ModifySuiteRecord_Handler,
		},
		{
			MethodName: "listSuiteRecord",
			Handler:    _Reporter_ListSuiteRecord_Handler,
		},
		{
			MethodName: "getSuiteRecord",
			Handler:    _Reporter_GetSuiteRecord_Handler,
		},
		{
			MethodName: "createServiceRecord",
			Handler:    _Reporter_CreateServiceRecord_Handler,
		},
		{
			MethodName: "modifyServiceRecord",
			Handler:    _Reporter_ModifyServiceRecord_Handler,
		},
		{
			MethodName: "listServiceRecord",
			Handler:    _Reporter_ListServiceRecord_Handler,
		},
		{
			MethodName: "getServiceRecord",
			Handler:    _Reporter_GetServiceRecord_Handler,
		},
		{
			MethodName: "createPlanRecord",
			Handler:    _Reporter_CreatePlanRecord_Handler,
		},
		{
			MethodName: "modifyPlanRecord",
			Handler:    _Reporter_ModifyPlanRecord_Handler,
		},
		{
			MethodName: "listPlanRecord",
			Handler:    _Reporter_ListPlanRecord_Handler,
		},
		{
			MethodName: "getPlanRecord",
			Handler:    _Reporter_GetPlanRecord_Handler,
		},
		{
			MethodName: "getPlanTimeScale",
			Handler:    _Reporter_GetPlanTimeScale_Handler,
		},
		{
			MethodName: "getPlanSummary",
			Handler:    _Reporter_GetPlanSummary_Handler,
		},
		{
			MethodName: "getPlanCasesInfo",
			Handler:    _Reporter_GetPlanCasesInfo_Handler,
		},
		{
			MethodName: "listFailCaseForPlanRecord",
			Handler:    _Reporter_ListFailCaseForPlanRecord_Handler,
		},
		{
			MethodName: "delCaseFailStatForPlan",
			Handler:    _Reporter_DelCaseFailStatForPlan_Handler,
		},
		{
			MethodName: "CountFailedCaseInLastNDays",
			Handler:    _Reporter_CountFailedCaseInLastNDays_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "reporter/reporter.proto",
}

const (
	UIReporter_CreateUICaseRecord_FullMethodName   = "/reporter.UIReporter/CreateUICaseRecord"
	UIReporter_ModifyUICaseRecord_FullMethodName   = "/reporter.UIReporter/ModifyUICaseRecord"
	UIReporter_CreateUISuiteRecord_FullMethodName  = "/reporter.UIReporter/CreateUISuiteRecord"
	UIReporter_ModifyUISuiteRecord_FullMethodName  = "/reporter.UIReporter/ModifyUISuiteRecord"
	UIReporter_CreateUIPlanRecord_FullMethodName   = "/reporter.UIReporter/CreateUIPlanRecord"
	UIReporter_ModifyUIPlanRecord_FullMethodName   = "/reporter.UIReporter/ModifyUIPlanRecord"
	UIReporter_ViewUIPlanRecord_FullMethodName     = "/reporter.UIReporter/ViewUIPlanRecord"
	UIReporter_ListUIPlanRecord_FullMethodName     = "/reporter.UIReporter/ListUIPlanRecord"
	UIReporter_GetUIPlanCasesInfo_FullMethodName   = "/reporter.UIReporter/GetUIPlanCasesInfo"
	UIReporter_GetUIPlanRecord_FullMethodName      = "/reporter.UIReporter/GetUIPlanRecord"
	UIReporter_SearchUISuiteRecord_FullMethodName  = "/reporter.UIReporter/SearchUISuiteRecord"
	UIReporter_SearchUICaseRecord_FullMethodName   = "/reporter.UIReporter/SearchUICaseRecord"
	UIReporter_GetUICaseRecord_FullMethodName      = "/reporter.UIReporter/GetUICaseRecord"
	UIReporter_ListUICaseStep_FullMethodName       = "/reporter.UIReporter/ListUICaseStep"
	UIReporter_GetUICaseStep_FullMethodName        = "/reporter.UIReporter/GetUICaseStep"
	UIReporter_SearchUIDeviceRecord_FullMethodName = "/reporter.UIReporter/SearchUIDeviceRecord"
	UIReporter_SaveUIDevicePerfData_FullMethodName = "/reporter.UIReporter/SaveUIDevicePerfData"
	UIReporter_GetUIDevicePerfData_FullMethodName  = "/reporter.UIReporter/GetUIDevicePerfData"
)

// UIReporterClient is the client API for UIReporter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UIReporterClient interface {
	//CreateUICaseRecord 创建UI用例执行记录
	CreateUICaseRecord(ctx context.Context, in *PutUICaseRecordRequest, opts ...grpc.CallOption) (*CreateUICaseRecordResponse, error)
	//ModifyUICaseRecord 修改UI用例执行记录
	ModifyUICaseRecord(ctx context.Context, in *PutUICaseRecordRequest, opts ...grpc.CallOption) (*ModifyUICaseRecordResponse, error)
	//CreateUISuiteRecord 创建UI集合执行记录
	CreateUISuiteRecord(ctx context.Context, in *PutUISuiteRecordRequest, opts ...grpc.CallOption) (*CreateUISuiteRecordResponse, error)
	//ModifyUISuiteRecord 修改UI集合执行记录
	ModifyUISuiteRecord(ctx context.Context, in *PutUISuiteRecordRequest, opts ...grpc.CallOption) (*ModifyUISuiteRecordResponse, error)
	//CreateUIPlanRecord 创建UI计划执行记录
	CreateUIPlanRecord(ctx context.Context, in *PutUIPlanRecordRequest, opts ...grpc.CallOption) (*CreateUIPlanRecordResponse, error)
	//ModifyUIPlanRecord 修改UI计划执行记录
	ModifyUIPlanRecord(ctx context.Context, in *PutUIPlanRecordRequest, opts ...grpc.CallOption) (*ModifyUIPlanRecordResponse, error)
	//ViewUIPlanRecord 查看UI计划执行记录
	ViewUIPlanRecord(ctx context.Context, in *ViewUIPlanRecordRequest, opts ...grpc.CallOption) (*ViewUIPlanRecordResponse, error)
	//ListUIPlanRecord UI计划执行记录列表
	ListUIPlanRecord(ctx context.Context, in *ListUIPlanRecordRequest, opts ...grpc.CallOption) (*ListUIPlanRecordResponse, error)
	//GetUIPlanCasesInfo 获取UI计划关联用例信息
	GetUIPlanCasesInfo(ctx context.Context, in *GetUIPlanCasesInfoRequest, opts ...grpc.CallOption) (*GetUIPlanCasesInfoResponse, error)
	//GetUIPlanRecord 获取UI计划执行记录
	GetUIPlanRecord(ctx context.Context, in *GetUIPlanRecordReq, opts ...grpc.CallOption) (*GetUIPlanRecordResp, error)
	//SearchUISuiteRecord 搜索UI计划执行记录下的UI集合执行记录
	SearchUISuiteRecord(ctx context.Context, in *SearchUISuiteRecordReq, opts ...grpc.CallOption) (*SearchUISuiteRecordResp, error)
	//SearchUICaseRecord 搜索UI集合执行记录下的UI用例执行记录
	SearchUICaseRecord(ctx context.Context, in *SearchUICaseRecordReq, opts ...grpc.CallOption) (*SearchUICaseRecordResp, error)
	//GetUICaseRecord 获取UI用例执行记录
	GetUICaseRecord(ctx context.Context, in *GetUICaseRecordReq, opts ...grpc.CallOption) (*GetUICaseRecordResp, error)
	//ListUICaseStep 获取UI用例执行步骤列表
	ListUICaseStep(ctx context.Context, in *ListUICaseStepReq, opts ...grpc.CallOption) (*ListUICaseStepResp, error)
	//GetUICaseStep 获取UI用例执行步骤
	GetUICaseStep(ctx context.Context, in *GetUICaseStepReq, opts ...grpc.CallOption) (*GetUICaseStepResp, error)
	//SearchUIDeviceRecord 搜索UI计划执行记录下的设备记录
	SearchUIDeviceRecord(ctx context.Context, in *SearchUIDeviceRecordReq, opts ...grpc.CallOption) (*SearchUIDeviceRecordResp, error)
	//SaveUIDevicePerfData 保存UI测试设备性能数据
	SaveUIDevicePerfData(ctx context.Context, in *SaveUIDevicePerfDataReq, opts ...grpc.CallOption) (*SaveUIDevicePerfDataResp, error)
	//GetUIDevicePerfData 获取UI测试设备性能数据
	GetUIDevicePerfData(ctx context.Context, in *GetUIDevicePerfDataReq, opts ...grpc.CallOption) (*GetUIDevicePerfDataResp, error)
}

type uIReporterClient struct {
	cc grpc.ClientConnInterface
}

func NewUIReporterClient(cc grpc.ClientConnInterface) UIReporterClient {
	return &uIReporterClient{cc}
}

func (c *uIReporterClient) CreateUICaseRecord(ctx context.Context, in *PutUICaseRecordRequest, opts ...grpc.CallOption) (*CreateUICaseRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUICaseRecordResponse)
	err := c.cc.Invoke(ctx, UIReporter_CreateUICaseRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) ModifyUICaseRecord(ctx context.Context, in *PutUICaseRecordRequest, opts ...grpc.CallOption) (*ModifyUICaseRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyUICaseRecordResponse)
	err := c.cc.Invoke(ctx, UIReporter_ModifyUICaseRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) CreateUISuiteRecord(ctx context.Context, in *PutUISuiteRecordRequest, opts ...grpc.CallOption) (*CreateUISuiteRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUISuiteRecordResponse)
	err := c.cc.Invoke(ctx, UIReporter_CreateUISuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) ModifyUISuiteRecord(ctx context.Context, in *PutUISuiteRecordRequest, opts ...grpc.CallOption) (*ModifyUISuiteRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyUISuiteRecordResponse)
	err := c.cc.Invoke(ctx, UIReporter_ModifyUISuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) CreateUIPlanRecord(ctx context.Context, in *PutUIPlanRecordRequest, opts ...grpc.CallOption) (*CreateUIPlanRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUIPlanRecordResponse)
	err := c.cc.Invoke(ctx, UIReporter_CreateUIPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) ModifyUIPlanRecord(ctx context.Context, in *PutUIPlanRecordRequest, opts ...grpc.CallOption) (*ModifyUIPlanRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyUIPlanRecordResponse)
	err := c.cc.Invoke(ctx, UIReporter_ModifyUIPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) ViewUIPlanRecord(ctx context.Context, in *ViewUIPlanRecordRequest, opts ...grpc.CallOption) (*ViewUIPlanRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewUIPlanRecordResponse)
	err := c.cc.Invoke(ctx, UIReporter_ViewUIPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) ListUIPlanRecord(ctx context.Context, in *ListUIPlanRecordRequest, opts ...grpc.CallOption) (*ListUIPlanRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUIPlanRecordResponse)
	err := c.cc.Invoke(ctx, UIReporter_ListUIPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) GetUIPlanCasesInfo(ctx context.Context, in *GetUIPlanCasesInfoRequest, opts ...grpc.CallOption) (*GetUIPlanCasesInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUIPlanCasesInfoResponse)
	err := c.cc.Invoke(ctx, UIReporter_GetUIPlanCasesInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) GetUIPlanRecord(ctx context.Context, in *GetUIPlanRecordReq, opts ...grpc.CallOption) (*GetUIPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUIPlanRecordResp)
	err := c.cc.Invoke(ctx, UIReporter_GetUIPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) SearchUISuiteRecord(ctx context.Context, in *SearchUISuiteRecordReq, opts ...grpc.CallOption) (*SearchUISuiteRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUISuiteRecordResp)
	err := c.cc.Invoke(ctx, UIReporter_SearchUISuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) SearchUICaseRecord(ctx context.Context, in *SearchUICaseRecordReq, opts ...grpc.CallOption) (*SearchUICaseRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUICaseRecordResp)
	err := c.cc.Invoke(ctx, UIReporter_SearchUICaseRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) GetUICaseRecord(ctx context.Context, in *GetUICaseRecordReq, opts ...grpc.CallOption) (*GetUICaseRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUICaseRecordResp)
	err := c.cc.Invoke(ctx, UIReporter_GetUICaseRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) ListUICaseStep(ctx context.Context, in *ListUICaseStepReq, opts ...grpc.CallOption) (*ListUICaseStepResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUICaseStepResp)
	err := c.cc.Invoke(ctx, UIReporter_ListUICaseStep_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) GetUICaseStep(ctx context.Context, in *GetUICaseStepReq, opts ...grpc.CallOption) (*GetUICaseStepResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUICaseStepResp)
	err := c.cc.Invoke(ctx, UIReporter_GetUICaseStep_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) SearchUIDeviceRecord(ctx context.Context, in *SearchUIDeviceRecordReq, opts ...grpc.CallOption) (*SearchUIDeviceRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUIDeviceRecordResp)
	err := c.cc.Invoke(ctx, UIReporter_SearchUIDeviceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) SaveUIDevicePerfData(ctx context.Context, in *SaveUIDevicePerfDataReq, opts ...grpc.CallOption) (*SaveUIDevicePerfDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SaveUIDevicePerfDataResp)
	err := c.cc.Invoke(ctx, UIReporter_SaveUIDevicePerfData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uIReporterClient) GetUIDevicePerfData(ctx context.Context, in *GetUIDevicePerfDataReq, opts ...grpc.CallOption) (*GetUIDevicePerfDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUIDevicePerfDataResp)
	err := c.cc.Invoke(ctx, UIReporter_GetUIDevicePerfData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UIReporterServer is the server API for UIReporter service.
// All implementations must embed UnimplementedUIReporterServer
// for forward compatibility.
type UIReporterServer interface {
	//CreateUICaseRecord 创建UI用例执行记录
	CreateUICaseRecord(context.Context, *PutUICaseRecordRequest) (*CreateUICaseRecordResponse, error)
	//ModifyUICaseRecord 修改UI用例执行记录
	ModifyUICaseRecord(context.Context, *PutUICaseRecordRequest) (*ModifyUICaseRecordResponse, error)
	//CreateUISuiteRecord 创建UI集合执行记录
	CreateUISuiteRecord(context.Context, *PutUISuiteRecordRequest) (*CreateUISuiteRecordResponse, error)
	//ModifyUISuiteRecord 修改UI集合执行记录
	ModifyUISuiteRecord(context.Context, *PutUISuiteRecordRequest) (*ModifyUISuiteRecordResponse, error)
	//CreateUIPlanRecord 创建UI计划执行记录
	CreateUIPlanRecord(context.Context, *PutUIPlanRecordRequest) (*CreateUIPlanRecordResponse, error)
	//ModifyUIPlanRecord 修改UI计划执行记录
	ModifyUIPlanRecord(context.Context, *PutUIPlanRecordRequest) (*ModifyUIPlanRecordResponse, error)
	//ViewUIPlanRecord 查看UI计划执行记录
	ViewUIPlanRecord(context.Context, *ViewUIPlanRecordRequest) (*ViewUIPlanRecordResponse, error)
	//ListUIPlanRecord UI计划执行记录列表
	ListUIPlanRecord(context.Context, *ListUIPlanRecordRequest) (*ListUIPlanRecordResponse, error)
	//GetUIPlanCasesInfo 获取UI计划关联用例信息
	GetUIPlanCasesInfo(context.Context, *GetUIPlanCasesInfoRequest) (*GetUIPlanCasesInfoResponse, error)
	//GetUIPlanRecord 获取UI计划执行记录
	GetUIPlanRecord(context.Context, *GetUIPlanRecordReq) (*GetUIPlanRecordResp, error)
	//SearchUISuiteRecord 搜索UI计划执行记录下的UI集合执行记录
	SearchUISuiteRecord(context.Context, *SearchUISuiteRecordReq) (*SearchUISuiteRecordResp, error)
	//SearchUICaseRecord 搜索UI集合执行记录下的UI用例执行记录
	SearchUICaseRecord(context.Context, *SearchUICaseRecordReq) (*SearchUICaseRecordResp, error)
	//GetUICaseRecord 获取UI用例执行记录
	GetUICaseRecord(context.Context, *GetUICaseRecordReq) (*GetUICaseRecordResp, error)
	//ListUICaseStep 获取UI用例执行步骤列表
	ListUICaseStep(context.Context, *ListUICaseStepReq) (*ListUICaseStepResp, error)
	//GetUICaseStep 获取UI用例执行步骤
	GetUICaseStep(context.Context, *GetUICaseStepReq) (*GetUICaseStepResp, error)
	//SearchUIDeviceRecord 搜索UI计划执行记录下的设备记录
	SearchUIDeviceRecord(context.Context, *SearchUIDeviceRecordReq) (*SearchUIDeviceRecordResp, error)
	//SaveUIDevicePerfData 保存UI测试设备性能数据
	SaveUIDevicePerfData(context.Context, *SaveUIDevicePerfDataReq) (*SaveUIDevicePerfDataResp, error)
	//GetUIDevicePerfData 获取UI测试设备性能数据
	GetUIDevicePerfData(context.Context, *GetUIDevicePerfDataReq) (*GetUIDevicePerfDataResp, error)
	mustEmbedUnimplementedUIReporterServer()
}

// UnimplementedUIReporterServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUIReporterServer struct{}

func (UnimplementedUIReporterServer) CreateUICaseRecord(context.Context, *PutUICaseRecordRequest) (*CreateUICaseRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUICaseRecord not implemented")
}
func (UnimplementedUIReporterServer) ModifyUICaseRecord(context.Context, *PutUICaseRecordRequest) (*ModifyUICaseRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyUICaseRecord not implemented")
}
func (UnimplementedUIReporterServer) CreateUISuiteRecord(context.Context, *PutUISuiteRecordRequest) (*CreateUISuiteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUISuiteRecord not implemented")
}
func (UnimplementedUIReporterServer) ModifyUISuiteRecord(context.Context, *PutUISuiteRecordRequest) (*ModifyUISuiteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyUISuiteRecord not implemented")
}
func (UnimplementedUIReporterServer) CreateUIPlanRecord(context.Context, *PutUIPlanRecordRequest) (*CreateUIPlanRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUIPlanRecord not implemented")
}
func (UnimplementedUIReporterServer) ModifyUIPlanRecord(context.Context, *PutUIPlanRecordRequest) (*ModifyUIPlanRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyUIPlanRecord not implemented")
}
func (UnimplementedUIReporterServer) ViewUIPlanRecord(context.Context, *ViewUIPlanRecordRequest) (*ViewUIPlanRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewUIPlanRecord not implemented")
}
func (UnimplementedUIReporterServer) ListUIPlanRecord(context.Context, *ListUIPlanRecordRequest) (*ListUIPlanRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUIPlanRecord not implemented")
}
func (UnimplementedUIReporterServer) GetUIPlanCasesInfo(context.Context, *GetUIPlanCasesInfoRequest) (*GetUIPlanCasesInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUIPlanCasesInfo not implemented")
}
func (UnimplementedUIReporterServer) GetUIPlanRecord(context.Context, *GetUIPlanRecordReq) (*GetUIPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUIPlanRecord not implemented")
}
func (UnimplementedUIReporterServer) SearchUISuiteRecord(context.Context, *SearchUISuiteRecordReq) (*SearchUISuiteRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUISuiteRecord not implemented")
}
func (UnimplementedUIReporterServer) SearchUICaseRecord(context.Context, *SearchUICaseRecordReq) (*SearchUICaseRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUICaseRecord not implemented")
}
func (UnimplementedUIReporterServer) GetUICaseRecord(context.Context, *GetUICaseRecordReq) (*GetUICaseRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUICaseRecord not implemented")
}
func (UnimplementedUIReporterServer) ListUICaseStep(context.Context, *ListUICaseStepReq) (*ListUICaseStepResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUICaseStep not implemented")
}
func (UnimplementedUIReporterServer) GetUICaseStep(context.Context, *GetUICaseStepReq) (*GetUICaseStepResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUICaseStep not implemented")
}
func (UnimplementedUIReporterServer) SearchUIDeviceRecord(context.Context, *SearchUIDeviceRecordReq) (*SearchUIDeviceRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUIDeviceRecord not implemented")
}
func (UnimplementedUIReporterServer) SaveUIDevicePerfData(context.Context, *SaveUIDevicePerfDataReq) (*SaveUIDevicePerfDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveUIDevicePerfData not implemented")
}
func (UnimplementedUIReporterServer) GetUIDevicePerfData(context.Context, *GetUIDevicePerfDataReq) (*GetUIDevicePerfDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUIDevicePerfData not implemented")
}
func (UnimplementedUIReporterServer) mustEmbedUnimplementedUIReporterServer() {}
func (UnimplementedUIReporterServer) testEmbeddedByValue()                    {}

// UnsafeUIReporterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UIReporterServer will
// result in compilation errors.
type UnsafeUIReporterServer interface {
	mustEmbedUnimplementedUIReporterServer()
}

func RegisterUIReporterServer(s grpc.ServiceRegistrar, srv UIReporterServer) {
	// If the following call pancis, it indicates UnimplementedUIReporterServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UIReporter_ServiceDesc, srv)
}

func _UIReporter_CreateUICaseRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutUICaseRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).CreateUICaseRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_CreateUICaseRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).CreateUICaseRecord(ctx, req.(*PutUICaseRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_ModifyUICaseRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutUICaseRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).ModifyUICaseRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_ModifyUICaseRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).ModifyUICaseRecord(ctx, req.(*PutUICaseRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_CreateUISuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutUISuiteRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).CreateUISuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_CreateUISuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).CreateUISuiteRecord(ctx, req.(*PutUISuiteRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_ModifyUISuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutUISuiteRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).ModifyUISuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_ModifyUISuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).ModifyUISuiteRecord(ctx, req.(*PutUISuiteRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_CreateUIPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutUIPlanRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).CreateUIPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_CreateUIPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).CreateUIPlanRecord(ctx, req.(*PutUIPlanRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_ModifyUIPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutUIPlanRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).ModifyUIPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_ModifyUIPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).ModifyUIPlanRecord(ctx, req.(*PutUIPlanRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_ViewUIPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewUIPlanRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).ViewUIPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_ViewUIPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).ViewUIPlanRecord(ctx, req.(*ViewUIPlanRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_ListUIPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUIPlanRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).ListUIPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_ListUIPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).ListUIPlanRecord(ctx, req.(*ListUIPlanRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_GetUIPlanCasesInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUIPlanCasesInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).GetUIPlanCasesInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_GetUIPlanCasesInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).GetUIPlanCasesInfo(ctx, req.(*GetUIPlanCasesInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_GetUIPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUIPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).GetUIPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_GetUIPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).GetUIPlanRecord(ctx, req.(*GetUIPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_SearchUISuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUISuiteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).SearchUISuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_SearchUISuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).SearchUISuiteRecord(ctx, req.(*SearchUISuiteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_SearchUICaseRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUICaseRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).SearchUICaseRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_SearchUICaseRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).SearchUICaseRecord(ctx, req.(*SearchUICaseRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_GetUICaseRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUICaseRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).GetUICaseRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_GetUICaseRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).GetUICaseRecord(ctx, req.(*GetUICaseRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_ListUICaseStep_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUICaseStepReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).ListUICaseStep(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_ListUICaseStep_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).ListUICaseStep(ctx, req.(*ListUICaseStepReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_GetUICaseStep_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUICaseStepReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).GetUICaseStep(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_GetUICaseStep_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).GetUICaseStep(ctx, req.(*GetUICaseStepReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_SearchUIDeviceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUIDeviceRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).SearchUIDeviceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_SearchUIDeviceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).SearchUIDeviceRecord(ctx, req.(*SearchUIDeviceRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_SaveUIDevicePerfData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveUIDevicePerfDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).SaveUIDevicePerfData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_SaveUIDevicePerfData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).SaveUIDevicePerfData(ctx, req.(*SaveUIDevicePerfDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UIReporter_GetUIDevicePerfData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUIDevicePerfDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UIReporterServer).GetUIDevicePerfData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UIReporter_GetUIDevicePerfData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UIReporterServer).GetUIDevicePerfData(ctx, req.(*GetUIDevicePerfDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UIReporter_ServiceDesc is the grpc.ServiceDesc for UIReporter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UIReporter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "reporter.UIReporter",
	HandlerType: (*UIReporterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateUICaseRecord",
			Handler:    _UIReporter_CreateUICaseRecord_Handler,
		},
		{
			MethodName: "ModifyUICaseRecord",
			Handler:    _UIReporter_ModifyUICaseRecord_Handler,
		},
		{
			MethodName: "CreateUISuiteRecord",
			Handler:    _UIReporter_CreateUISuiteRecord_Handler,
		},
		{
			MethodName: "ModifyUISuiteRecord",
			Handler:    _UIReporter_ModifyUISuiteRecord_Handler,
		},
		{
			MethodName: "CreateUIPlanRecord",
			Handler:    _UIReporter_CreateUIPlanRecord_Handler,
		},
		{
			MethodName: "ModifyUIPlanRecord",
			Handler:    _UIReporter_ModifyUIPlanRecord_Handler,
		},
		{
			MethodName: "ViewUIPlanRecord",
			Handler:    _UIReporter_ViewUIPlanRecord_Handler,
		},
		{
			MethodName: "ListUIPlanRecord",
			Handler:    _UIReporter_ListUIPlanRecord_Handler,
		},
		{
			MethodName: "GetUIPlanCasesInfo",
			Handler:    _UIReporter_GetUIPlanCasesInfo_Handler,
		},
		{
			MethodName: "GetUIPlanRecord",
			Handler:    _UIReporter_GetUIPlanRecord_Handler,
		},
		{
			MethodName: "SearchUISuiteRecord",
			Handler:    _UIReporter_SearchUISuiteRecord_Handler,
		},
		{
			MethodName: "SearchUICaseRecord",
			Handler:    _UIReporter_SearchUICaseRecord_Handler,
		},
		{
			MethodName: "GetUICaseRecord",
			Handler:    _UIReporter_GetUICaseRecord_Handler,
		},
		{
			MethodName: "ListUICaseStep",
			Handler:    _UIReporter_ListUICaseStep_Handler,
		},
		{
			MethodName: "GetUICaseStep",
			Handler:    _UIReporter_GetUICaseStep_Handler,
		},
		{
			MethodName: "SearchUIDeviceRecord",
			Handler:    _UIReporter_SearchUIDeviceRecord_Handler,
		},
		{
			MethodName: "SaveUIDevicePerfData",
			Handler:    _UIReporter_SaveUIDevicePerfData_Handler,
		},
		{
			MethodName: "GetUIDevicePerfData",
			Handler:    _UIReporter_GetUIDevicePerfData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "reporter/reporter.proto",
}

const (
	PerfReporter_CreatePerfCaseRecord_FullMethodName             = "/reporter.PerfReporter/CreatePerfCaseRecord"
	PerfReporter_ModifyPerfCaseRecord_FullMethodName             = "/reporter.PerfReporter/ModifyPerfCaseRecord"
	PerfReporter_GetPerfCaseRecord_FullMethodName                = "/reporter.PerfReporter/GetPerfCaseRecord"
	PerfReporter_CreatePerfSuiteRecord_FullMethodName            = "/reporter.PerfReporter/CreatePerfSuiteRecord"
	PerfReporter_ModifyPerfSuiteRecord_FullMethodName            = "/reporter.PerfReporter/ModifyPerfSuiteRecord"
	PerfReporter_GetPerfSuiteRecord_FullMethodName               = "/reporter.PerfReporter/GetPerfSuiteRecord"
	PerfReporter_CreatePerfPlanRecord_FullMethodName             = "/reporter.PerfReporter/CreatePerfPlanRecord"
	PerfReporter_ModifyPerfPlanRecord_FullMethodName             = "/reporter.PerfReporter/ModifyPerfPlanRecord"
	PerfReporter_SearchPerfPlanRecord_FullMethodName             = "/reporter.PerfReporter/SearchPerfPlanRecord"
	PerfReporter_GetPerfPlanRecord_FullMethodName                = "/reporter.PerfReporter/GetPerfPlanRecord"
	PerfReporter_SearchPerfCaseRecord_FullMethodName             = "/reporter.PerfReporter/SearchPerfCaseRecord"
	PerfReporter_UpdateMonitorURLOfPerfPlanRecord_FullMethodName = "/reporter.PerfReporter/UpdateMonitorURLOfPerfPlanRecord"
)

// PerfReporterClient is the client API for PerfReporter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PerfReporterClient interface {
	//CreatePerfCaseRecord 创建压测用例执行记录
	CreatePerfCaseRecord(ctx context.Context, in *CreatePerfCaseRecordReq, opts ...grpc.CallOption) (*CreatePerfCaseRecordResp, error)
	//ModifyPerfCaseRecord 修改压测用例执行记录
	ModifyPerfCaseRecord(ctx context.Context, in *ModifyPerfCaseRecordReq, opts ...grpc.CallOption) (*ModifyPerfCaseRecordResp, error)
	//GetPerfCaseRecord 获取压测用例执行记录
	GetPerfCaseRecord(ctx context.Context, in *GetPerfCaseRecordReq, opts ...grpc.CallOption) (*GetPerfCaseRecordResp, error)
	//CreatePerfSuiteRecord 创建压测集合执行记录
	CreatePerfSuiteRecord(ctx context.Context, in *CreatePerfSuiteRecordReq, opts ...grpc.CallOption) (*CreatePerfSuiteRecordResp, error)
	//ModifyPerfSuiteRecord 修改压测集合执行记录
	ModifyPerfSuiteRecord(ctx context.Context, in *ModifyPerfSuiteRecordReq, opts ...grpc.CallOption) (*ModifyPerfSuiteRecordResp, error)
	//GetPerfSuiteRecord 获取压测集合执行记录
	GetPerfSuiteRecord(ctx context.Context, in *GetPerfSuiteRecordReq, opts ...grpc.CallOption) (*GetPerfSuiteRecordResp, error)
	//CreatePerfPlanRecord 创建压测计划执行记录
	CreatePerfPlanRecord(ctx context.Context, in *CreatePerfPlanRecordReq, opts ...grpc.CallOption) (*CreatePerfPlanRecordResp, error)
	//ModifyPerfPlanRecord 修改压测计划执行记录
	ModifyPerfPlanRecord(ctx context.Context, in *ModifyPerfPlanRecordReq, opts ...grpc.CallOption) (*ModifyPerfPlanRecordResp, error)
	//SearchPerfPlanRecord 搜索压测计划执行记录
	SearchPerfPlanRecord(ctx context.Context, in *SearchPerfPlanRecordReq, opts ...grpc.CallOption) (*SearchPerfPlanRecordResp, error)
	//GetPerfPlanRecord 获取压测计划执行记录
	GetPerfPlanRecord(ctx context.Context, in *GetPerfPlanRecordReq, opts ...grpc.CallOption) (*GetPerfPlanRecordResp, error)
	//SearchPerfCaseRecord 搜索压测计划执行记录下的压测用例执行记录
	SearchPerfCaseRecord(ctx context.Context, in *SearchPerfCaseRecordReq, opts ...grpc.CallOption) (*SearchPerfCaseRecordResp, error)
	//UpdateMonitorURLOfPerfPlanRecord 更新压测计划执行记录的`metric_url`字段
	UpdateMonitorURLOfPerfPlanRecord(ctx context.Context, in *UpdateMonitorURLOfPerfPlanRecordReq, opts ...grpc.CallOption) (*UpdateMonitorURLOfPerfPlanRecordResp, error)
}

type perfReporterClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfReporterClient(cc grpc.ClientConnInterface) PerfReporterClient {
	return &perfReporterClient{cc}
}

func (c *perfReporterClient) CreatePerfCaseRecord(ctx context.Context, in *CreatePerfCaseRecordReq, opts ...grpc.CallOption) (*CreatePerfCaseRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePerfCaseRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_CreatePerfCaseRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) ModifyPerfCaseRecord(ctx context.Context, in *ModifyPerfCaseRecordReq, opts ...grpc.CallOption) (*ModifyPerfCaseRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPerfCaseRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_ModifyPerfCaseRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) GetPerfCaseRecord(ctx context.Context, in *GetPerfCaseRecordReq, opts ...grpc.CallOption) (*GetPerfCaseRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPerfCaseRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_GetPerfCaseRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) CreatePerfSuiteRecord(ctx context.Context, in *CreatePerfSuiteRecordReq, opts ...grpc.CallOption) (*CreatePerfSuiteRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePerfSuiteRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_CreatePerfSuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) ModifyPerfSuiteRecord(ctx context.Context, in *ModifyPerfSuiteRecordReq, opts ...grpc.CallOption) (*ModifyPerfSuiteRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPerfSuiteRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_ModifyPerfSuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) GetPerfSuiteRecord(ctx context.Context, in *GetPerfSuiteRecordReq, opts ...grpc.CallOption) (*GetPerfSuiteRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPerfSuiteRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_GetPerfSuiteRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) CreatePerfPlanRecord(ctx context.Context, in *CreatePerfPlanRecordReq, opts ...grpc.CallOption) (*CreatePerfPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePerfPlanRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_CreatePerfPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) ModifyPerfPlanRecord(ctx context.Context, in *ModifyPerfPlanRecordReq, opts ...grpc.CallOption) (*ModifyPerfPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPerfPlanRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_ModifyPerfPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) SearchPerfPlanRecord(ctx context.Context, in *SearchPerfPlanRecordReq, opts ...grpc.CallOption) (*SearchPerfPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfPlanRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_SearchPerfPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) GetPerfPlanRecord(ctx context.Context, in *GetPerfPlanRecordReq, opts ...grpc.CallOption) (*GetPerfPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPerfPlanRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_GetPerfPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) SearchPerfCaseRecord(ctx context.Context, in *SearchPerfCaseRecordReq, opts ...grpc.CallOption) (*SearchPerfCaseRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfCaseRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_SearchPerfCaseRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfReporterClient) UpdateMonitorURLOfPerfPlanRecord(ctx context.Context, in *UpdateMonitorURLOfPerfPlanRecordReq, opts ...grpc.CallOption) (*UpdateMonitorURLOfPerfPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateMonitorURLOfPerfPlanRecordResp)
	err := c.cc.Invoke(ctx, PerfReporter_UpdateMonitorURLOfPerfPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfReporterServer is the server API for PerfReporter service.
// All implementations must embed UnimplementedPerfReporterServer
// for forward compatibility.
type PerfReporterServer interface {
	//CreatePerfCaseRecord 创建压测用例执行记录
	CreatePerfCaseRecord(context.Context, *CreatePerfCaseRecordReq) (*CreatePerfCaseRecordResp, error)
	//ModifyPerfCaseRecord 修改压测用例执行记录
	ModifyPerfCaseRecord(context.Context, *ModifyPerfCaseRecordReq) (*ModifyPerfCaseRecordResp, error)
	//GetPerfCaseRecord 获取压测用例执行记录
	GetPerfCaseRecord(context.Context, *GetPerfCaseRecordReq) (*GetPerfCaseRecordResp, error)
	//CreatePerfSuiteRecord 创建压测集合执行记录
	CreatePerfSuiteRecord(context.Context, *CreatePerfSuiteRecordReq) (*CreatePerfSuiteRecordResp, error)
	//ModifyPerfSuiteRecord 修改压测集合执行记录
	ModifyPerfSuiteRecord(context.Context, *ModifyPerfSuiteRecordReq) (*ModifyPerfSuiteRecordResp, error)
	//GetPerfSuiteRecord 获取压测集合执行记录
	GetPerfSuiteRecord(context.Context, *GetPerfSuiteRecordReq) (*GetPerfSuiteRecordResp, error)
	//CreatePerfPlanRecord 创建压测计划执行记录
	CreatePerfPlanRecord(context.Context, *CreatePerfPlanRecordReq) (*CreatePerfPlanRecordResp, error)
	//ModifyPerfPlanRecord 修改压测计划执行记录
	ModifyPerfPlanRecord(context.Context, *ModifyPerfPlanRecordReq) (*ModifyPerfPlanRecordResp, error)
	//SearchPerfPlanRecord 搜索压测计划执行记录
	SearchPerfPlanRecord(context.Context, *SearchPerfPlanRecordReq) (*SearchPerfPlanRecordResp, error)
	//GetPerfPlanRecord 获取压测计划执行记录
	GetPerfPlanRecord(context.Context, *GetPerfPlanRecordReq) (*GetPerfPlanRecordResp, error)
	//SearchPerfCaseRecord 搜索压测计划执行记录下的压测用例执行记录
	SearchPerfCaseRecord(context.Context, *SearchPerfCaseRecordReq) (*SearchPerfCaseRecordResp, error)
	//UpdateMonitorURLOfPerfPlanRecord 更新压测计划执行记录的`metric_url`字段
	UpdateMonitorURLOfPerfPlanRecord(context.Context, *UpdateMonitorURLOfPerfPlanRecordReq) (*UpdateMonitorURLOfPerfPlanRecordResp, error)
	mustEmbedUnimplementedPerfReporterServer()
}

// UnimplementedPerfReporterServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfReporterServer struct{}

func (UnimplementedPerfReporterServer) CreatePerfCaseRecord(context.Context, *CreatePerfCaseRecordReq) (*CreatePerfCaseRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePerfCaseRecord not implemented")
}
func (UnimplementedPerfReporterServer) ModifyPerfCaseRecord(context.Context, *ModifyPerfCaseRecordReq) (*ModifyPerfCaseRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPerfCaseRecord not implemented")
}
func (UnimplementedPerfReporterServer) GetPerfCaseRecord(context.Context, *GetPerfCaseRecordReq) (*GetPerfCaseRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPerfCaseRecord not implemented")
}
func (UnimplementedPerfReporterServer) CreatePerfSuiteRecord(context.Context, *CreatePerfSuiteRecordReq) (*CreatePerfSuiteRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePerfSuiteRecord not implemented")
}
func (UnimplementedPerfReporterServer) ModifyPerfSuiteRecord(context.Context, *ModifyPerfSuiteRecordReq) (*ModifyPerfSuiteRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPerfSuiteRecord not implemented")
}
func (UnimplementedPerfReporterServer) GetPerfSuiteRecord(context.Context, *GetPerfSuiteRecordReq) (*GetPerfSuiteRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPerfSuiteRecord not implemented")
}
func (UnimplementedPerfReporterServer) CreatePerfPlanRecord(context.Context, *CreatePerfPlanRecordReq) (*CreatePerfPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePerfPlanRecord not implemented")
}
func (UnimplementedPerfReporterServer) ModifyPerfPlanRecord(context.Context, *ModifyPerfPlanRecordReq) (*ModifyPerfPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPerfPlanRecord not implemented")
}
func (UnimplementedPerfReporterServer) SearchPerfPlanRecord(context.Context, *SearchPerfPlanRecordReq) (*SearchPerfPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfPlanRecord not implemented")
}
func (UnimplementedPerfReporterServer) GetPerfPlanRecord(context.Context, *GetPerfPlanRecordReq) (*GetPerfPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPerfPlanRecord not implemented")
}
func (UnimplementedPerfReporterServer) SearchPerfCaseRecord(context.Context, *SearchPerfCaseRecordReq) (*SearchPerfCaseRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfCaseRecord not implemented")
}
func (UnimplementedPerfReporterServer) UpdateMonitorURLOfPerfPlanRecord(context.Context, *UpdateMonitorURLOfPerfPlanRecordReq) (*UpdateMonitorURLOfPerfPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMonitorURLOfPerfPlanRecord not implemented")
}
func (UnimplementedPerfReporterServer) mustEmbedUnimplementedPerfReporterServer() {}
func (UnimplementedPerfReporterServer) testEmbeddedByValue()                      {}

// UnsafePerfReporterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfReporterServer will
// result in compilation errors.
type UnsafePerfReporterServer interface {
	mustEmbedUnimplementedPerfReporterServer()
}

func RegisterPerfReporterServer(s grpc.ServiceRegistrar, srv PerfReporterServer) {
	// If the following call pancis, it indicates UnimplementedPerfReporterServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfReporter_ServiceDesc, srv)
}

func _PerfReporter_CreatePerfCaseRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePerfCaseRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).CreatePerfCaseRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_CreatePerfCaseRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).CreatePerfCaseRecord(ctx, req.(*CreatePerfCaseRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_ModifyPerfCaseRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPerfCaseRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).ModifyPerfCaseRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_ModifyPerfCaseRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).ModifyPerfCaseRecord(ctx, req.(*ModifyPerfCaseRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_GetPerfCaseRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerfCaseRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).GetPerfCaseRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_GetPerfCaseRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).GetPerfCaseRecord(ctx, req.(*GetPerfCaseRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_CreatePerfSuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePerfSuiteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).CreatePerfSuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_CreatePerfSuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).CreatePerfSuiteRecord(ctx, req.(*CreatePerfSuiteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_ModifyPerfSuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPerfSuiteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).ModifyPerfSuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_ModifyPerfSuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).ModifyPerfSuiteRecord(ctx, req.(*ModifyPerfSuiteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_GetPerfSuiteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerfSuiteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).GetPerfSuiteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_GetPerfSuiteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).GetPerfSuiteRecord(ctx, req.(*GetPerfSuiteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_CreatePerfPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePerfPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).CreatePerfPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_CreatePerfPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).CreatePerfPlanRecord(ctx, req.(*CreatePerfPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_ModifyPerfPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPerfPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).ModifyPerfPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_ModifyPerfPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).ModifyPerfPlanRecord(ctx, req.(*ModifyPerfPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_SearchPerfPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).SearchPerfPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_SearchPerfPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).SearchPerfPlanRecord(ctx, req.(*SearchPerfPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_GetPerfPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerfPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).GetPerfPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_GetPerfPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).GetPerfPlanRecord(ctx, req.(*GetPerfPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_SearchPerfCaseRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfCaseRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).SearchPerfCaseRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_SearchPerfCaseRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).SearchPerfCaseRecord(ctx, req.(*SearchPerfCaseRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfReporter_UpdateMonitorURLOfPerfPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMonitorURLOfPerfPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfReporterServer).UpdateMonitorURLOfPerfPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfReporter_UpdateMonitorURLOfPerfPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfReporterServer).UpdateMonitorURLOfPerfPlanRecord(ctx, req.(*UpdateMonitorURLOfPerfPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfReporter_ServiceDesc is the grpc.ServiceDesc for PerfReporter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfReporter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "reporter.PerfReporter",
	HandlerType: (*PerfReporterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePerfCaseRecord",
			Handler:    _PerfReporter_CreatePerfCaseRecord_Handler,
		},
		{
			MethodName: "ModifyPerfCaseRecord",
			Handler:    _PerfReporter_ModifyPerfCaseRecord_Handler,
		},
		{
			MethodName: "GetPerfCaseRecord",
			Handler:    _PerfReporter_GetPerfCaseRecord_Handler,
		},
		{
			MethodName: "CreatePerfSuiteRecord",
			Handler:    _PerfReporter_CreatePerfSuiteRecord_Handler,
		},
		{
			MethodName: "ModifyPerfSuiteRecord",
			Handler:    _PerfReporter_ModifyPerfSuiteRecord_Handler,
		},
		{
			MethodName: "GetPerfSuiteRecord",
			Handler:    _PerfReporter_GetPerfSuiteRecord_Handler,
		},
		{
			MethodName: "CreatePerfPlanRecord",
			Handler:    _PerfReporter_CreatePerfPlanRecord_Handler,
		},
		{
			MethodName: "ModifyPerfPlanRecord",
			Handler:    _PerfReporter_ModifyPerfPlanRecord_Handler,
		},
		{
			MethodName: "SearchPerfPlanRecord",
			Handler:    _PerfReporter_SearchPerfPlanRecord_Handler,
		},
		{
			MethodName: "GetPerfPlanRecord",
			Handler:    _PerfReporter_GetPerfPlanRecord_Handler,
		},
		{
			MethodName: "SearchPerfCaseRecord",
			Handler:    _PerfReporter_SearchPerfCaseRecord_Handler,
		},
		{
			MethodName: "UpdateMonitorURLOfPerfPlanRecord",
			Handler:    _PerfReporter_UpdateMonitorURLOfPerfPlanRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "reporter/reporter.proto",
}

const (
	StabilityReporter_ListStabilityPlanRecord_FullMethodName     = "/reporter.StabilityReporter/ListStabilityPlanRecord"
	StabilityReporter_SearchStabilityPlanRecord_FullMethodName   = "/reporter.StabilityReporter/SearchStabilityPlanRecord"
	StabilityReporter_GetStabilityPlanRecord_FullMethodName      = "/reporter.StabilityReporter/GetStabilityPlanRecord"
	StabilityReporter_SearchStabilityDeviceRecord_FullMethodName = "/reporter.StabilityReporter/SearchStabilityDeviceRecord"
	StabilityReporter_ListStabilityDeviceStep_FullMethodName     = "/reporter.StabilityReporter/ListStabilityDeviceStep"
	StabilityReporter_GetStabilityDevicePerfData_FullMethodName  = "/reporter.StabilityReporter/GetStabilityDevicePerfData"
	StabilityReporter_GetStabilityDeviceActivity_FullMethodName  = "/reporter.StabilityReporter/GetStabilityDeviceActivity"
)

// StabilityReporterClient is the client API for StabilityReporter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StabilityReporterClient interface {
	// ListStabilityPlanRecord 获取稳测计划的执行记录
	ListStabilityPlanRecord(ctx context.Context, in *ListStabilityPlanRecordReq, opts ...grpc.CallOption) (*ListStabilityPlanRecordResp, error)
	// SearchStabilityPlanRecord 搜索稳测的执行记录
	SearchStabilityPlanRecord(ctx context.Context, in *SearchStabilityPlanRecordReq, opts ...grpc.CallOption) (*SearchStabilityPlanRecordResp, error)
	// GetStabilityPlanRecord 获取稳测执行报告的计划信息
	GetStabilityPlanRecord(ctx context.Context, in *GetStabilityPlanRecordReq, opts ...grpc.CallOption) (*GetStabilityPlanRecordResp, error)
	// SearchStabilityDeviceRecord 搜索稳测执行报告的总览设备
	SearchStabilityDeviceRecord(ctx context.Context, in *SearchStabilityDeviceRecordReq, opts ...grpc.CallOption) (*SearchStabilityDeviceRecordResp, error)
	// ListStabilityDeviceStep 获取稳测执行报告的设备步骤日志
	ListStabilityDeviceStep(ctx context.Context, in *ListStabilityDeviceStepReq, opts ...grpc.CallOption) (*ListStabilityDeviceStepResp, error)
	// GetStabilityDevicePerfData 获取稳测执行报告的设备性能数据
	GetStabilityDevicePerfData(ctx context.Context, in *GetStabilityDevicePerfDataReq, opts ...grpc.CallOption) (*GetStabilityDevicePerfDataResp, error)
	// GetStabilityDeviceActivity 获取稳测执行报告的设备Activity统计
	GetStabilityDeviceActivity(ctx context.Context, in *GetStabilityDeviceActivityReq, opts ...grpc.CallOption) (*GetStabilityDeviceActivityResp, error)
}

type stabilityReporterClient struct {
	cc grpc.ClientConnInterface
}

func NewStabilityReporterClient(cc grpc.ClientConnInterface) StabilityReporterClient {
	return &stabilityReporterClient{cc}
}

func (c *stabilityReporterClient) ListStabilityPlanRecord(ctx context.Context, in *ListStabilityPlanRecordReq, opts ...grpc.CallOption) (*ListStabilityPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListStabilityPlanRecordResp)
	err := c.cc.Invoke(ctx, StabilityReporter_ListStabilityPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityReporterClient) SearchStabilityPlanRecord(ctx context.Context, in *SearchStabilityPlanRecordReq, opts ...grpc.CallOption) (*SearchStabilityPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchStabilityPlanRecordResp)
	err := c.cc.Invoke(ctx, StabilityReporter_SearchStabilityPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityReporterClient) GetStabilityPlanRecord(ctx context.Context, in *GetStabilityPlanRecordReq, opts ...grpc.CallOption) (*GetStabilityPlanRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetStabilityPlanRecordResp)
	err := c.cc.Invoke(ctx, StabilityReporter_GetStabilityPlanRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityReporterClient) SearchStabilityDeviceRecord(ctx context.Context, in *SearchStabilityDeviceRecordReq, opts ...grpc.CallOption) (*SearchStabilityDeviceRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchStabilityDeviceRecordResp)
	err := c.cc.Invoke(ctx, StabilityReporter_SearchStabilityDeviceRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityReporterClient) ListStabilityDeviceStep(ctx context.Context, in *ListStabilityDeviceStepReq, opts ...grpc.CallOption) (*ListStabilityDeviceStepResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListStabilityDeviceStepResp)
	err := c.cc.Invoke(ctx, StabilityReporter_ListStabilityDeviceStep_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityReporterClient) GetStabilityDevicePerfData(ctx context.Context, in *GetStabilityDevicePerfDataReq, opts ...grpc.CallOption) (*GetStabilityDevicePerfDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetStabilityDevicePerfDataResp)
	err := c.cc.Invoke(ctx, StabilityReporter_GetStabilityDevicePerfData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityReporterClient) GetStabilityDeviceActivity(ctx context.Context, in *GetStabilityDeviceActivityReq, opts ...grpc.CallOption) (*GetStabilityDeviceActivityResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetStabilityDeviceActivityResp)
	err := c.cc.Invoke(ctx, StabilityReporter_GetStabilityDeviceActivity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StabilityReporterServer is the server API for StabilityReporter service.
// All implementations must embed UnimplementedStabilityReporterServer
// for forward compatibility.
type StabilityReporterServer interface {
	// ListStabilityPlanRecord 获取稳测计划的执行记录
	ListStabilityPlanRecord(context.Context, *ListStabilityPlanRecordReq) (*ListStabilityPlanRecordResp, error)
	// SearchStabilityPlanRecord 搜索稳测的执行记录
	SearchStabilityPlanRecord(context.Context, *SearchStabilityPlanRecordReq) (*SearchStabilityPlanRecordResp, error)
	// GetStabilityPlanRecord 获取稳测执行报告的计划信息
	GetStabilityPlanRecord(context.Context, *GetStabilityPlanRecordReq) (*GetStabilityPlanRecordResp, error)
	// SearchStabilityDeviceRecord 搜索稳测执行报告的总览设备
	SearchStabilityDeviceRecord(context.Context, *SearchStabilityDeviceRecordReq) (*SearchStabilityDeviceRecordResp, error)
	// ListStabilityDeviceStep 获取稳测执行报告的设备步骤日志
	ListStabilityDeviceStep(context.Context, *ListStabilityDeviceStepReq) (*ListStabilityDeviceStepResp, error)
	// GetStabilityDevicePerfData 获取稳测执行报告的设备性能数据
	GetStabilityDevicePerfData(context.Context, *GetStabilityDevicePerfDataReq) (*GetStabilityDevicePerfDataResp, error)
	// GetStabilityDeviceActivity 获取稳测执行报告的设备Activity统计
	GetStabilityDeviceActivity(context.Context, *GetStabilityDeviceActivityReq) (*GetStabilityDeviceActivityResp, error)
	mustEmbedUnimplementedStabilityReporterServer()
}

// UnimplementedStabilityReporterServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedStabilityReporterServer struct{}

func (UnimplementedStabilityReporterServer) ListStabilityPlanRecord(context.Context, *ListStabilityPlanRecordReq) (*ListStabilityPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStabilityPlanRecord not implemented")
}
func (UnimplementedStabilityReporterServer) SearchStabilityPlanRecord(context.Context, *SearchStabilityPlanRecordReq) (*SearchStabilityPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchStabilityPlanRecord not implemented")
}
func (UnimplementedStabilityReporterServer) GetStabilityPlanRecord(context.Context, *GetStabilityPlanRecordReq) (*GetStabilityPlanRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStabilityPlanRecord not implemented")
}
func (UnimplementedStabilityReporterServer) SearchStabilityDeviceRecord(context.Context, *SearchStabilityDeviceRecordReq) (*SearchStabilityDeviceRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchStabilityDeviceRecord not implemented")
}
func (UnimplementedStabilityReporterServer) ListStabilityDeviceStep(context.Context, *ListStabilityDeviceStepReq) (*ListStabilityDeviceStepResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStabilityDeviceStep not implemented")
}
func (UnimplementedStabilityReporterServer) GetStabilityDevicePerfData(context.Context, *GetStabilityDevicePerfDataReq) (*GetStabilityDevicePerfDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStabilityDevicePerfData not implemented")
}
func (UnimplementedStabilityReporterServer) GetStabilityDeviceActivity(context.Context, *GetStabilityDeviceActivityReq) (*GetStabilityDeviceActivityResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStabilityDeviceActivity not implemented")
}
func (UnimplementedStabilityReporterServer) mustEmbedUnimplementedStabilityReporterServer() {}
func (UnimplementedStabilityReporterServer) testEmbeddedByValue()                           {}

// UnsafeStabilityReporterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StabilityReporterServer will
// result in compilation errors.
type UnsafeStabilityReporterServer interface {
	mustEmbedUnimplementedStabilityReporterServer()
}

func RegisterStabilityReporterServer(s grpc.ServiceRegistrar, srv StabilityReporterServer) {
	// If the following call pancis, it indicates UnimplementedStabilityReporterServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&StabilityReporter_ServiceDesc, srv)
}

func _StabilityReporter_ListStabilityPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStabilityPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityReporterServer).ListStabilityPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityReporter_ListStabilityPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityReporterServer).ListStabilityPlanRecord(ctx, req.(*ListStabilityPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityReporter_SearchStabilityPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchStabilityPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityReporterServer).SearchStabilityPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityReporter_SearchStabilityPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityReporterServer).SearchStabilityPlanRecord(ctx, req.(*SearchStabilityPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityReporter_GetStabilityPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStabilityPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityReporterServer).GetStabilityPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityReporter_GetStabilityPlanRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityReporterServer).GetStabilityPlanRecord(ctx, req.(*GetStabilityPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityReporter_SearchStabilityDeviceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchStabilityDeviceRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityReporterServer).SearchStabilityDeviceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityReporter_SearchStabilityDeviceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityReporterServer).SearchStabilityDeviceRecord(ctx, req.(*SearchStabilityDeviceRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityReporter_ListStabilityDeviceStep_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStabilityDeviceStepReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityReporterServer).ListStabilityDeviceStep(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityReporter_ListStabilityDeviceStep_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityReporterServer).ListStabilityDeviceStep(ctx, req.(*ListStabilityDeviceStepReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityReporter_GetStabilityDevicePerfData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStabilityDevicePerfDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityReporterServer).GetStabilityDevicePerfData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityReporter_GetStabilityDevicePerfData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityReporterServer).GetStabilityDevicePerfData(ctx, req.(*GetStabilityDevicePerfDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityReporter_GetStabilityDeviceActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStabilityDeviceActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityReporterServer).GetStabilityDeviceActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityReporter_GetStabilityDeviceActivity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityReporterServer).GetStabilityDeviceActivity(ctx, req.(*GetStabilityDeviceActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

// StabilityReporter_ServiceDesc is the grpc.ServiceDesc for StabilityReporter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StabilityReporter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "reporter.StabilityReporter",
	HandlerType: (*StabilityReporterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListStabilityPlanRecord",
			Handler:    _StabilityReporter_ListStabilityPlanRecord_Handler,
		},
		{
			MethodName: "SearchStabilityPlanRecord",
			Handler:    _StabilityReporter_SearchStabilityPlanRecord_Handler,
		},
		{
			MethodName: "GetStabilityPlanRecord",
			Handler:    _StabilityReporter_GetStabilityPlanRecord_Handler,
		},
		{
			MethodName: "SearchStabilityDeviceRecord",
			Handler:    _StabilityReporter_SearchStabilityDeviceRecord_Handler,
		},
		{
			MethodName: "ListStabilityDeviceStep",
			Handler:    _StabilityReporter_ListStabilityDeviceStep_Handler,
		},
		{
			MethodName: "GetStabilityDevicePerfData",
			Handler:    _StabilityReporter_GetStabilityDevicePerfData_Handler,
		},
		{
			MethodName: "GetStabilityDeviceActivity",
			Handler:    _StabilityReporter_GetStabilityDeviceActivity_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "reporter/reporter.proto",
}
