package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ StabilityPlanExecutionRecordModel = (*customStabilityPlanExecutionRecordModel)(nil)

	stabilityPlanExecutionRecordInsertFields = stringx.Remove(stabilityPlanExecutionRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// StabilityPlanExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customStabilityPlanExecutionRecordModel.
	StabilityPlanExecutionRecordModel interface {
		stabilityPlanExecutionRecordModel
		types.DBModel

		withSession(session sqlx.Session) StabilityPlanExecutionRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *StabilityPlanExecutionRecord) squirrel.InsertBuilder
		UpdateBuilder(data *StabilityPlanExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityPlanExecutionRecord, error)

		GenerateSearchStabilityPlanExecutionRecordSqlBuilder(req SearchStabilityPlanExecutionRecordReq) (
			searchStabilityPlanExecutionRecordSelectBuilder, searchStabilityPlanExecutionRecordCountBuilder,
		)
		FindCountStabilityPlanExecutionRecords(
			ctx context.Context, countBuilder searchStabilityPlanExecutionRecordCountBuilder,
		) (int64, error)
		FindStabilityPlanExecutionRecords(
			ctx context.Context, selectBuilder searchStabilityPlanExecutionRecordSelectBuilder,
		) ([]*StabilityPlanExecutionRecord, error)

		FindOneByTaskIDExecuteIDProjectID(
			ctx context.Context, taskID, executeID, projectID string,
		) (*StabilityPlanExecutionRecord, error)
	}

	customStabilityPlanExecutionRecordModel struct {
		*defaultStabilityPlanExecutionRecordModel

		conn sqlx.SqlConn
	}
)

// NewStabilityPlanExecutionRecordModel returns a model for the database table.
func NewStabilityPlanExecutionRecordModel(conn sqlx.SqlConn) StabilityPlanExecutionRecordModel {
	return &customStabilityPlanExecutionRecordModel{
		defaultStabilityPlanExecutionRecordModel: newStabilityPlanExecutionRecordModel(conn),
		conn:                                     conn,
	}
}

func (m *customStabilityPlanExecutionRecordModel) withSession(session sqlx.Session) StabilityPlanExecutionRecordModel {
	return NewStabilityPlanExecutionRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customStabilityPlanExecutionRecordModel) Table() string {
	return m.table
}

func (m *customStabilityPlanExecutionRecordModel) Fields() []string {
	return stabilityPlanExecutionRecordFieldNames
}

func (m *customStabilityPlanExecutionRecordModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customStabilityPlanExecutionRecordModel) InsertBuilder(data *StabilityPlanExecutionRecord) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(stabilityPlanExecutionRecordInsertFields...).Values()
}

func (m *customStabilityPlanExecutionRecordModel) UpdateBuilder(data *StabilityPlanExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customStabilityPlanExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(stabilityPlanExecutionRecordFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityPlanExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityPlanExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customStabilityPlanExecutionRecordModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityPlanExecutionRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*StabilityPlanExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

type searchStabilityPlanExecutionRecordSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchStabilityPlanExecutionRecordCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customStabilityPlanExecutionRecordModel) GenerateSearchStabilityPlanExecutionRecordSqlBuilder(req SearchStabilityPlanExecutionRecordReq) (
	searchStabilityPlanExecutionRecordSelectBuilder, searchStabilityPlanExecutionRecordCountBuilder,
) {
	var sb, scb squirrel.SelectBuilder

	sb = sqlbuilder.SearchOptions(
		m.SelectBuilder().Where("`project_id` = ?", req.ProjectID),
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptions(
		m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
		sqlbuilder.WithCondition(m, req.Condition),
	)

	if len(req.PlanID) > 0 {
		sb = sb.Where("`plan_id` = ?", req.PlanID)
		scb = scb.Where("`plan_id` = ?", req.PlanID)
	}

	return searchStabilityPlanExecutionRecordSelectBuilder{SelectBuilder: sb}, searchStabilityPlanExecutionRecordCountBuilder{SelectBuilder: scb}
}

func (m *customStabilityPlanExecutionRecordModel) FindCountStabilityPlanExecutionRecords(ctx context.Context, countBuilder searchStabilityPlanExecutionRecordCountBuilder) (
	int64, error,
) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customStabilityPlanExecutionRecordModel) FindStabilityPlanExecutionRecords(
	ctx context.Context, selectBuilder searchStabilityPlanExecutionRecordSelectBuilder,
) ([]*StabilityPlanExecutionRecord, error) {
	var resp []*StabilityPlanExecutionRecord

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customStabilityPlanExecutionRecordModel) FindOneByTaskIDExecuteIDProjectID(
	ctx context.Context, taskID, executeID, projectID string,
) (*StabilityPlanExecutionRecord, error) {
	sb := m.SelectBuilder().Where(
		"`task_id` = ? AND `execute_id` = ? AND `project_id` = ?", taskID, executeID, projectID,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}
