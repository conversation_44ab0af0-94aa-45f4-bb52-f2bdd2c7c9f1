# 文件拦截器使用指南

## 概述

文件拦截器（StreamInterceptor）是一个用于拦截和处理session的stdout和stderr输出的工具。它满足以下需求：

1. **stderr处理**：直接写入文件
2. **stdout处理**：写入文件 + 行级别的回调处理
3. **行缓冲**：确保只有完整的行才会触发回调
4. **数据顺序一致性**：保证从session获取的数据与写入文件的数据顺序一致
5. **并发安全**：支持多goroutine并发写入

## 核心组件

### 1. StreamInterceptor

主要的拦截器类，实现了`io.Writer`接口：

```go
type StreamInterceptor struct {
    file         io.Writer     // 目标文件
    lineCallback LineCallback  // 行回调函数
    lineChan     chan string   // 行数据管道
    buffer       bytes.Buffer  // stdout缓冲区
    mutex        sync.Mutex    // 保护buffer的并发访问
    wg           sync.WaitGroup // 等待goroutine完成
    closed       bool          // 是否已关闭
}
```

### 2. LineCallback

行处理回调函数类型：

```go
type LineCallback func(line string)
```

### 3. StderrWriter

专用于stderr的写入器：

```go
type StderrWriter struct {
    interceptor *StreamInterceptor
}
```

## 基本使用方法

### 1. 创建拦截器

```go
// 打开日志文件
logFile, err := os.OpenFile("output.log", os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
if err != nil {
    return err
}
defer logFile.Close()

// 定义行处理回调
lineCallback := func(line string) {
    // 处理每一行stdout数据
    fmt.Printf("处理行: %s\n", line)
}

// 创建拦截器
interceptor := NewStreamInterceptor(logFile, lineCallback)
defer interceptor.Close()
```

### 2. 设置session输出

```go
// 假设session有Stdout和Stderr字段
session.Stdout = interceptor                    // stdout使用拦截器
session.Stderr = NewStderrWriter(interceptor)   // stderr使用专用写入器
```

### 3. 在现有代码中集成

以您的fastbot代码为例：

```go
func (d *AndroidDevice) runFastbotWithInterceptor(ctx context.Context, key, packageName string, opts ...FastbotOption) error {
    // ... 现有的初始化代码 ...

    if o.reportPath != "" {
        localOutputPath := filepath.Join(o.reportPath, d.serial)
        logPath := filepath.Join(localOutputPath, logNameOfFastbot)
        
        logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0o755)
        if err != nil {
            d.Errorf("failed to open log file, serial: %s, file: %s, error: %+v", d.serial, logPath, err)
        } else {
            defer logFile.Close()
            
            // 定义行处理回调
            lineCallback := func(line string) {
                d.processFastbotLogLine(line)
            }
            
            // 创建拦截器
            interceptor := NewStreamInterceptor(logFile, lineCallback)
            defer interceptor.Close()
            
            // 设置session输出
            session.Stdout = interceptor
            session.Stderr = NewStderrWriter(interceptor)
        }
    }

    // ... 其余代码保持不变 ...
}

// 处理fastbot日志行
func (d *AndroidDevice) processFastbotLogLine(line string) {
    line = strings.TrimSpace(line)
    if line == "" {
        return
    }
    
    switch {
    case strings.Contains(line, "Monkey finished"):
        d.Infof("fastbot test completed: %s", line)
    case strings.Contains(line, "CRASH"):
        d.Errorf("application crash detected: %s", line)
    case strings.Contains(line, "ANR"):
        d.Errorf("ANR detected: %s", line)
    case strings.Contains(line, "Exception"):
        d.Warnf("exception detected: %s", line)
    case strings.Contains(line, "Events injected:"):
        d.Debugf("progress update: %s", line)
    default:
        d.Debugf("fastbot log: %s", line)
    }
}
```

## 高级用法

### 1. 自定义行处理逻辑

```go
lineCallback := func(line string) {
    // 解析JSON格式的日志
    if strings.HasPrefix(line, "{") && strings.HasSuffix(line, "}") {
        var logData map[string]interface{}
        if err := json.Unmarshal([]byte(line), &logData); err == nil {
            // 处理JSON数据
            if level, ok := logData["level"]; ok && level == "error" {
                // 处理错误日志
            }
        }
    }
    
    // 检测特定模式
    if matched, _ := regexp.MatchString(`progress:\s*(\d+)%`, line); matched {
        // 提取进度信息
    }
    
    // 实时通知
    if strings.Contains(line, "CRITICAL") {
        // 发送告警通知
    }
}
```

### 2. 多文件输出

```go
// 创建多个文件
mainLog, _ := os.OpenFile("main.log", os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
errorLog, _ := os.OpenFile("error.log", os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)

// 使用MultiWriter同时写入多个文件
multiWriter := NewMultiWriter(mainLog, errorLog)

lineCallback := func(line string) {
    // 错误行额外写入错误日志
    if strings.Contains(line, "ERROR") {
        errorLog.WriteString(fmt.Sprintf("[%s] %s\n", time.Now().Format(time.RFC3339), line))
    }
}

interceptor := NewStreamInterceptor(multiWriter, lineCallback)
```

## 特性说明

### 1. 数据顺序一致性

拦截器确保数据按以下顺序处理：
1. 数据首先写入文件
2. 然后进行行处理
3. 保证文件中的数据顺序与原始输出一致

### 2. 行缓冲机制

- **完整行**：以`\n`结尾的数据立即发送到回调
- **部分行**：缓存直到收到完整行
- **关闭时处理**：拦截器关闭时处理剩余的缓冲数据

### 3. 并发安全

- 使用mutex保护内部缓冲区
- 支持多个goroutine同时写入
- 线程安全的管道通信

### 4. 资源管理

- 自动管理内部goroutine
- 正确的资源清理
- 优雅的关闭机制

## 测试验证

拦截器包含完整的测试套件，验证以下功能：

1. **基本功能测试**：验证完整行的处理
2. **部分行测试**：验证行缓冲机制
3. **stderr处理测试**：验证stderr直接写入文件
4. **并发写入测试**：验证多goroutine并发安全
5. **数据顺序测试**：验证数据顺序一致性

运行测试：

```bash
go test ./staworker/mqc/internal/logic/statest/device/... -run TestStreamInterceptor -v
```

## 注意事项

1. **及时关闭**：确保在使用完毕后调用`interceptor.Close()`
2. **错误处理**：检查文件打开和写入的错误
3. **回调性能**：回调函数应该快速执行，避免阻塞
4. **内存管理**：对于大量数据，注意管道缓冲区大小
5. **文件权限**：确保有足够的权限创建和写入日志文件

## 总结

文件拦截器提供了一个强大而灵活的解决方案，用于处理session的输出流。它不仅满足了您的所有需求，还提供了良好的扩展性和可维护性。通过合理使用这个工具，您可以轻松实现日志记录、实时监控、错误检测等功能。
