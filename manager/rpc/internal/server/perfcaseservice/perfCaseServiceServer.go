// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perfcaseservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfcaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfCaseServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfCaseServiceServer
}

func NewPerfCaseServiceServer(svcCtx *svc.ServiceContext) *PerfCaseServiceServer {
	return &PerfCaseServiceServer{
		svcCtx: svcCtx,
	}
}

// RemovePerfCase 删除压测用例
func (s *PerfCaseServiceServer) RemovePerfCase(ctx context.Context, in *pb.RemovePerfCaseReq) (*pb.RemovePerfCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfcaseservicelogic.NewRemovePerfCaseLogic(ctx, s.svcCtx)

	return l.RemovePerfCase(in)
}

// ViewPerfCase 查看压测用例
func (s *PerfCaseServiceServer) ViewPerfCase(ctx context.Context, in *pb.ViewPerfCaseReq) (*pb.ViewPerfCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfcaseservicelogic.NewViewPerfCaseLogic(ctx, s.svcCtx)

	return l.ViewPerfCase(in)
}
