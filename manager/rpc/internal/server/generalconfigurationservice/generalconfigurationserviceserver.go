// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	generalconfigurationservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/generalconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GeneralConfigurationServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedGeneralConfigurationServiceServer
}

func NewGeneralConfigurationServiceServer(svcCtx *svc.ServiceContext) *GeneralConfigurationServiceServer {
	return &GeneralConfigurationServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateGeneralConfiguration 创建通用配置
func (s *GeneralConfigurationServiceServer) CreateGeneralConfiguration(ctx context.Context, in *pb.CreateGeneralConfigurationReq) (*pb.CreateGeneralConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := generalconfigurationservicelogic.NewCreateGeneralConfigurationLogic(ctx, s.svcCtx)

	return l.CreateGeneralConfiguration(in)
}

// RemoveGeneralConfiguration 删除通用配置
func (s *GeneralConfigurationServiceServer) RemoveGeneralConfiguration(ctx context.Context, in *pb.RemoveGeneralConfigurationReq) (*pb.RemoveGeneralConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := generalconfigurationservicelogic.NewRemoveGeneralConfigurationLogic(ctx, s.svcCtx)

	return l.RemoveGeneralConfiguration(in)
}

// ModifyGeneralConfiguration 编辑通用配置
func (s *GeneralConfigurationServiceServer) ModifyGeneralConfiguration(ctx context.Context, in *pb.ModifyGeneralConfigurationReq) (*pb.ModifyGeneralConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := generalconfigurationservicelogic.NewModifyGeneralConfigurationLogic(ctx, s.svcCtx)

	return l.ModifyGeneralConfiguration(in)
}

// SearchGeneralConfiguration 搜索通用配置
func (s *GeneralConfigurationServiceServer) SearchGeneralConfiguration(ctx context.Context, in *pb.SearchGeneralConfigurationReq) (*pb.SearchGeneralConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := generalconfigurationservicelogic.NewSearchGeneralConfigurationLogic(ctx, s.svcCtx)

	return l.SearchGeneralConfiguration(in)
}

// ViewGeneralConfiguration 查看通用配置
func (s *GeneralConfigurationServiceServer) ViewGeneralConfiguration(ctx context.Context, in *pb.ViewGeneralConfigurationReq) (*pb.ViewGeneralConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := generalconfigurationservicelogic.NewViewGeneralConfigurationLogic(ctx, s.svcCtx)

	return l.ViewGeneralConfiguration(in)
}
