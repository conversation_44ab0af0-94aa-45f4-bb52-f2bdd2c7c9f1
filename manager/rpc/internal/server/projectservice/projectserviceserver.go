// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	projectservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/projectservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ProjectServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedProjectServiceServer
}

func NewProjectServiceServer(svcCtx *svc.ServiceContext) *ProjectServiceServer {
	return &ProjectServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateProject 创建项目
func (s *ProjectServiceServer) CreateProject(ctx context.Context, in *pb.CreateProjectReq) (*pb.CreateProjectResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectservicelogic.NewCreateProjectLogic(ctx, s.svcCtx)

	return l.CreateProject(in)
}

// RemoveProject 删除项目
func (s *ProjectServiceServer) RemoveProject(ctx context.Context, in *pb.RemoveProjectReq) (*pb.RemoveProjectResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectservicelogic.NewRemoveProjectLogic(ctx, s.svcCtx)

	return l.RemoveProject(in)
}

// ModifyProject 编辑项目
func (s *ProjectServiceServer) ModifyProject(ctx context.Context, in *pb.ModifyProjectReq) (*pb.ModifyProjectResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectservicelogic.NewModifyProjectLogic(ctx, s.svcCtx)

	return l.ModifyProject(in)
}

// SearchProject 搜索项目
func (s *ProjectServiceServer) SearchProject(ctx context.Context, in *pb.SearchProjectReq) (*pb.SearchProjectResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectservicelogic.NewSearchProjectLogic(ctx, s.svcCtx)

	return l.SearchProject(in)
}

// ViewProject 查看项目
func (s *ProjectServiceServer) ViewProject(ctx context.Context, in *pb.ViewProjectReq) (*pb.ViewProjectResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectservicelogic.NewViewProjectLogic(ctx, s.svcCtx)

	return l.ViewProject(in)
}

// SearchProjectUser 搜索项目用户
func (s *ProjectServiceServer) SearchProjectUser(ctx context.Context, in *pb.SearchProjectUserReq) (*pb.SearchProjectUserResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectservicelogic.NewSearchProjectUserLogic(ctx, s.svcCtx)

	return l.SearchProjectUser(in)
}

// ModifyProjectReviewFunction 开启、关闭用例审核功能
func (s *ProjectServiceServer) ModifyProjectReviewFunction(ctx context.Context, in *pb.ModifyProjectReviewFunctionReq) (*pb.ModifyProjectReviewFunctionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectservicelogic.NewModifyProjectReviewFunctionLogic(ctx, s.svcCtx)

	return l.ModifyProjectReviewFunction(in)
}

// ModifyProjectCoverageFunction 开启、关闭接口用例覆盖率功能
func (s *ProjectServiceServer) ModifyProjectCoverageFunction(ctx context.Context, in *pb.ModifyProjectCoverageFunctionReq) (*pb.ModifyProjectCoverageFunctionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectservicelogic.NewModifyProjectCoverageFunctionLogic(ctx, s.svcCtx)

	return l.ModifyProjectCoverageFunction(in)
}
