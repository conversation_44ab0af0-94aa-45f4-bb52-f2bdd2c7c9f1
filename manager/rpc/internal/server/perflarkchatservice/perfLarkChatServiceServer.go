// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perflarkchatservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perflarkchatservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfLarkChatServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfLarkChatServiceServer
}

func NewPerfLarkChatServiceServer(svcCtx *svc.ServiceContext) *PerfLarkChatServiceServer {
	return &PerfLarkChatServiceServer{
		svcCtx: svcCtx,
	}
}

// ModifyPerfLarkChat 编辑压测通知飞书群组列表
func (s *PerfLarkChatServiceServer) ModifyPerfLarkChat(ctx context.Context, in *pb.ModifyPerfLarkChatReq) (*pb.ModifyPerfLarkChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perflarkchatservicelogic.NewModifyPerfLarkChatLogic(ctx, s.svcCtx)

	return l.ModifyPerfLarkChat(in)
}

// SearchPerfLarkChat 搜索压测通知飞书群组
func (s *PerfLarkChatServiceServer) SearchPerfLarkChat(ctx context.Context, in *pb.SearchPerfLarkChatReq) (*pb.SearchPerfLarkChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perflarkchatservicelogic.NewSearchPerfLarkChatLogic(ctx, s.svcCtx)

	return l.SearchPerfLarkChat(in)
}

// DeletePerfLarkChat 删除压测通知飞书群组（由飞书群解散事件触发）
func (s *PerfLarkChatServiceServer) DeletePerfLarkChat(ctx context.Context, in *pb.DeletePerfLarkChatReq) (*pb.DeletePerfLarkChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perflarkchatservicelogic.NewDeletePerfLarkChatLogic(ctx, s.svcCtx)

	return l.DeletePerfLarkChat(in)
}

// UpdatePerfLarkChat 更新压测通知飞书群组（由飞书群配置修改事件触发）
func (s *PerfLarkChatServiceServer) UpdatePerfLarkChat(ctx context.Context, in *pb.UpdatePerfLarkChatReq) (*pb.UpdatePerfLarkChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perflarkchatservicelogic.NewUpdatePerfLarkChatLogic(ctx, s.svcCtx)

	return l.UpdatePerfLarkChat(in)
}
