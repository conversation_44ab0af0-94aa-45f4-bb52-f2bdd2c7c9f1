// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	casecommonservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/casecommonservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CaseCommonServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedCaseCommonServiceServer
}

func NewCaseCommonServiceServer(svcCtx *svc.ServiceContext) *CaseCommonServiceServer {
	return &CaseCommonServiceServer{
		svcCtx: svcCtx,
	}
}

// SearchNotReleasedCase 搜索未上线用例
func (s *CaseCommonServiceServer) SearchNotReleasedCase(ctx context.Context, in *pb.SearchCaseReq) (*pb.SearchCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := casecommonservicelogic.NewSearchNotReleasedCaseLogic(ctx, s.svcCtx)

	return l.SearchNotReleasedCase(in)
}

// SearchFailLogCase 搜索用例失败记录
func (s *CaseCommonServiceServer) SearchFailLogCase(ctx context.Context, in *pb.SearchCaseFailLogReq) (*pb.SearchCaseFailLogResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := casecommonservicelogic.NewSearchFailLogCaseLogic(ctx, s.svcCtx)

	return l.SearchFailLogCase(in)
}

// DeleteFailLogCase 删除（忽略）用例失败记录
func (s *CaseCommonServiceServer) DeleteFailLogCase(ctx context.Context, in *pb.DeleteFailLogCaseReq) (*pb.DeleteFailLogCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := casecommonservicelogic.NewDeleteFailLogCaseLogic(ctx, s.svcCtx)

	return l.DeleteFailLogCase(in)
}

// BatchDeleteFailLogCase 批量删除（忽略）用例失败记录
func (s *CaseCommonServiceServer) BatchDeleteFailLogCase(ctx context.Context, in *pb.BatchDeleteCaseFailLogReq) (*pb.BatchDeleteCaseFailLogResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := casecommonservicelogic.NewBatchDeleteFailLogCaseLogic(ctx, s.svcCtx)

	return l.BatchDeleteFailLogCase(in)
}

// CreateOrModifyFailedCase 创建或修改失败用例记录
func (s *CaseCommonServiceServer) CreateOrModifyFailedCase(ctx context.Context, in *pb.CreateOrModifyFailedCaseReq) (*pb.CreateOrModifyFailedCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := casecommonservicelogic.NewCreateOrModifyFailedCaseLogic(ctx, s.svcCtx)

	return l.CreateOrModifyFailedCase(in)
}
