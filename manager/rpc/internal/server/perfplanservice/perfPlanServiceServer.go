// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perfplanservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfPlanServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfPlanServiceServer
}

func NewPerfPlanServiceServer(svcCtx *svc.ServiceContext) *PerfPlanServiceServer {
	return &PerfPlanServiceServer{
		svcCtx: svcCtx,
	}
}

// CreatePerfPlan 创建压测计划
func (s *PerfPlanServiceServer) CreatePerfPlan(ctx context.Context, in *pb.CreatePerfPlanReq) (*pb.CreatePerfPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanservicelogic.NewCreatePerfPlanLogic(ctx, s.svcCtx)

	return l.CreatePerfPlan(in)
}

// RemovePerfPlan 删除压测计划
func (s *PerfPlanServiceServer) RemovePerfPlan(ctx context.Context, in *pb.RemovePerfPlanReq) (*pb.RemovePerfPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanservicelogic.NewRemovePerfPlanLogic(ctx, s.svcCtx)

	return l.RemovePerfPlan(in)
}

// ModifyPerfPlan 编辑压测计划
func (s *PerfPlanServiceServer) ModifyPerfPlan(ctx context.Context, in *pb.ModifyPerfPlanReq) (*pb.ModifyPerfPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanservicelogic.NewModifyPerfPlanLogic(ctx, s.svcCtx)

	return l.ModifyPerfPlan(in)
}

// SearchPerfPlan 搜索压测计划
func (s *PerfPlanServiceServer) SearchPerfPlan(ctx context.Context, in *pb.SearchPerfPlanReq) (*pb.SearchPerfPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanservicelogic.NewSearchPerfPlanLogic(ctx, s.svcCtx)

	return l.SearchPerfPlan(in)
}

// ViewPerfPlan 查看压测计划
func (s *PerfPlanServiceServer) ViewPerfPlan(ctx context.Context, in *pb.ViewPerfPlanReq) (*pb.ViewPerfPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanservicelogic.NewViewPerfPlanLogic(ctx, s.svcCtx)

	return l.ViewPerfPlan(in)
}

// SearchCaseInPerfPlan 搜索压测计划中的压测用例
func (s *PerfPlanServiceServer) SearchCaseInPerfPlan(ctx context.Context, in *pb.SearchCaseInPerfPlanReq) (*pb.SearchCaseInPerfPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanservicelogic.NewSearchCaseInPerfPlanLogic(ctx, s.svcCtx)

	return l.SearchCaseInPerfPlan(in)
}
