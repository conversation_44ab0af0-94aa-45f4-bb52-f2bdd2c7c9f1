// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perfplanv2servicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfplanv2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfPlanV2ServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfPlanV2ServiceServer
}

func NewPerfPlanV2ServiceServer(svcCtx *svc.ServiceContext) *PerfPlanV2ServiceServer {
	return &PerfPlanV2ServiceServer{
		svcCtx: svcCtx,
	}
}

// CreatePerfPlanV2 创建压测计划
func (s *PerfPlanV2ServiceServer) CreatePerfPlanV2(ctx context.Context, in *pb.CreatePerfPlanV2Req) (*pb.CreatePerfPlanV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewCreatePerfPlanV2Logic(ctx, s.svcCtx)

	return l.CreatePerfPlanV2(in)
}

// RemovePerfPlanV2 删除压测计划
func (s *PerfPlanV2ServiceServer) RemovePerfPlanV2(ctx context.Context, in *pb.RemovePerfPlanV2Req) (*pb.RemovePerfPlanV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewRemovePerfPlanV2Logic(ctx, s.svcCtx)

	return l.RemovePerfPlanV2(in)
}

// ModifyPerfPlanV2 编辑压测计划
func (s *PerfPlanV2ServiceServer) ModifyPerfPlanV2(ctx context.Context, in *pb.ModifyPerfPlanV2Req) (*pb.ModifyPerfPlanV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewModifyPerfPlanV2Logic(ctx, s.svcCtx)

	return l.ModifyPerfPlanV2(in)
}

// SearchPerfPlanV2 搜索压测计划
func (s *PerfPlanV2ServiceServer) SearchPerfPlanV2(ctx context.Context, in *pb.SearchPerfPlanV2Req) (*pb.SearchPerfPlanV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewSearchPerfPlanV2Logic(ctx, s.svcCtx)

	return l.SearchPerfPlanV2(in)
}

// ViewPerfPlanV2 查看压测计划
func (s *PerfPlanV2ServiceServer) ViewPerfPlanV2(ctx context.Context, in *pb.ViewPerfPlanV2Req) (*pb.ViewPerfPlanV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewViewPerfPlanV2Logic(ctx, s.svcCtx)

	return l.ViewPerfPlanV2(in)
}

// SearchCaseInPerfPlanV2 搜索压测计划中的压测用例
func (s *PerfPlanV2ServiceServer) SearchCaseInPerfPlanV2(ctx context.Context, in *pb.SearchCaseInPerfPlanV2Req) (*pb.SearchCaseInPerfPlanV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewSearchCaseInPerfPlanV2Logic(ctx, s.svcCtx)

	return l.SearchCaseInPerfPlanV2(in)
}

// SearchProtobufInPerfPlanV2 搜索压测计划中的Protobuf配置
func (s *PerfPlanV2ServiceServer) SearchProtobufInPerfPlanV2(ctx context.Context, in *pb.SearchProtobufInPerfPlanV2Req) (*pb.SearchProtobufInPerfPlanV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewSearchProtobufInPerfPlanV2Logic(ctx, s.svcCtx)

	return l.SearchProtobufInPerfPlanV2(in)
}

// SearchRuleInPerfPlanV2 搜索压测计划中的停止规则
func (s *PerfPlanV2ServiceServer) SearchRuleInPerfPlanV2(ctx context.Context, in *pb.SearchRuleInPerfPlanV2Req) (*pb.SearchRuleInPerfPlanV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewSearchRuleInPerfPlanV2Logic(ctx, s.svcCtx)

	return l.SearchRuleInPerfPlanV2(in)
}

// UpdatePerfPlanByCase 通过编辑压测用例触发关联的压测计划进行相应的更新（包括：持续时长、压测数据、施压机资源）
func (s *PerfPlanV2ServiceServer) UpdatePerfPlanByCase(ctx context.Context, in *pb.UpdatePerfPlanByCaseReq) (*pb.UpdatePerfPlanByCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewUpdatePerfPlanByCaseLogic(ctx, s.svcCtx)

	return l.UpdatePerfPlanByCase(in)
}

// UpdatePerfPlanByChatID 更新压测计划中自动创建的飞书群ID
func (s *PerfPlanV2ServiceServer) UpdatePerfPlanByChatID(ctx context.Context, in *pb.UpdatePerfPlanByChatIDReq) (*pb.UpdatePerfPlanByChatIDResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfplanv2servicelogic.NewUpdatePerfPlanByChatIDLogic(ctx, s.svcCtx)

	return l.UpdatePerfPlanByChatID(in)
}
