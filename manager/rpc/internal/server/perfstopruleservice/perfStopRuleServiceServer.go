// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perfstopruleservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfstopruleservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfStopRuleServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfStopRuleServiceServer
}

func NewPerfStopRuleServiceServer(svcCtx *svc.ServiceContext) *PerfStopRuleServiceServer {
	return &PerfStopRuleServiceServer{
		svcCtx: svcCtx,
	}
}

// CreatePerfStopRule 创建压测停止规则
func (s *PerfStopRuleServiceServer) CreatePerfStopRule(ctx context.Context, in *pb.CreatePerfStopRuleReq) (*pb.CreatePerfStopRuleResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfstopruleservicelogic.NewCreatePerfStopRuleLogic(ctx, s.svcCtx)

	return l.CreatePerfStopRule(in)
}

// RemovePerfStopRule 删除压测停止规则
func (s *PerfStopRuleServiceServer) RemovePerfStopRule(ctx context.Context, in *pb.RemovePerfStopRuleReq) (*pb.RemovePerfStopRuleResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfstopruleservicelogic.NewRemovePerfStopRuleLogic(ctx, s.svcCtx)

	return l.RemovePerfStopRule(in)
}

// ModifyPerfStopRule 编辑压测停止规则
func (s *PerfStopRuleServiceServer) ModifyPerfStopRule(ctx context.Context, in *pb.ModifyPerfStopRuleReq) (*pb.ModifyPerfStopRuleResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfstopruleservicelogic.NewModifyPerfStopRuleLogic(ctx, s.svcCtx)

	return l.ModifyPerfStopRule(in)
}

// SearchPerfStopRule 搜索压测停止规则
func (s *PerfStopRuleServiceServer) SearchPerfStopRule(ctx context.Context, in *pb.SearchPerfStopRuleReq) (*pb.SearchPerfStopRuleResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfstopruleservicelogic.NewSearchPerfStopRuleLogic(ctx, s.svcCtx)

	return l.SearchPerfStopRule(in)
}

// ViewPerfStopRule 查看压测停止规则
func (s *PerfStopRuleServiceServer) ViewPerfStopRule(ctx context.Context, in *pb.ViewPerfStopRuleReq) (*pb.ViewPerfStopRuleResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfstopruleservicelogic.NewViewPerfStopRuleLogic(ctx, s.svcCtx)

	return l.ViewPerfStopRule(in)
}
