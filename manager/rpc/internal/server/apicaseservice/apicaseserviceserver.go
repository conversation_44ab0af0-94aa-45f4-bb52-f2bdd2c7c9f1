// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	apicaseservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apicaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ApiCaseServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedApiCaseServiceServer
}

func NewApiCaseServiceServer(svcCtx *svc.ServiceContext) *ApiCaseServiceServer {
	return &ApiCaseServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateApiCase 创建API用例
func (s *ApiCaseServiceServer) CreateApiCase(ctx context.Context, in *pb.CreateApiCaseReq) (*pb.CreateApiCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apicaseservicelogic.NewCreateApiCaseLogic(ctx, s.svcCtx)

	return l.CreateApiCase(in)
}

// RemoveApiCase 删除API用例
func (s *ApiCaseServiceServer) RemoveApiCase(ctx context.Context, in *pb.RemoveApiCaseReq) (*pb.RemoveApiCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apicaseservicelogic.NewRemoveApiCaseLogic(ctx, s.svcCtx)

	return l.RemoveApiCase(in)
}

// ModifyApiCase 编辑API用例
func (s *ApiCaseServiceServer) ModifyApiCase(ctx context.Context, in *pb.ModifyApiCaseReq) (*pb.ModifyApiCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apicaseservicelogic.NewModifyApiCaseLogic(ctx, s.svcCtx)

	return l.ModifyApiCase(in)
}

// SearchApiCase 搜索API用例
func (s *ApiCaseServiceServer) SearchApiCase(ctx context.Context, in *pb.SearchApiCaseReq) (*pb.SearchApiCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apicaseservicelogic.NewSearchApiCaseLogic(ctx, s.svcCtx)

	return l.SearchApiCase(in)
}

// ViewApiCase 查看API用例
func (s *ApiCaseServiceServer) ViewApiCase(ctx context.Context, in *pb.ViewApiCaseReq) (*pb.ViewApiCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apicaseservicelogic.NewViewApiCaseLogic(ctx, s.svcCtx)

	return l.ViewApiCase(in)
}

// SearchApiCaseReference 搜索API用例引用详情
func (s *ApiCaseServiceServer) SearchApiCaseReference(ctx context.Context, in *pb.SearchApiCaseReferenceReq) (*pb.SearchApiCaseReferenceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apicaseservicelogic.NewSearchApiCaseReferenceLogic(ctx, s.svcCtx)

	return l.SearchApiCaseReference(in)
}

// MaintainApiCase 维护API用例
func (s *ApiCaseServiceServer) MaintainApiCase(ctx context.Context, in *pb.MaintainApiCaseReq) (*pb.MaintainApiCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apicaseservicelogic.NewMaintainApiCaseLogic(ctx, s.svcCtx)

	return l.MaintainApiCase(in)
}

// PublishApiCase 发布API用例
func (s *ApiCaseServiceServer) PublishApiCase(ctx context.Context, in *pb.PublishApiCaseReq) (*pb.PublishApiCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apicaseservicelogic.NewPublishApiCaseLogic(ctx, s.svcCtx)

	return l.PublishApiCase(in)
}
