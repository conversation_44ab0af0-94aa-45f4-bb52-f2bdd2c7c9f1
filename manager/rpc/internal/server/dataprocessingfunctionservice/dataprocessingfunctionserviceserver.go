// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	dataprocessingfunctionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/dataprocessingfunctionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DataProcessingFunctionServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedDataProcessingFunctionServiceServer
}

func NewDataProcessingFunctionServiceServer(svcCtx *svc.ServiceContext) *DataProcessingFunctionServiceServer {
	return &DataProcessingFunctionServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateDataProcessingFunction 创建数据处理函数
func (s *DataProcessingFunctionServiceServer) CreateDataProcessingFunction(ctx context.Context, in *pb.CreateOrModifyDataProcessingFunctionReq) (*pb.CreateOrModifyDataProcessingFunctionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := dataprocessingfunctionservicelogic.NewCreateDataProcessingFunctionLogic(ctx, s.svcCtx)

	return l.CreateDataProcessingFunction(in)
}

// RemoveDataProcessingFunction 删除数据处理函数
func (s *DataProcessingFunctionServiceServer) RemoveDataProcessingFunction(ctx context.Context, in *pb.RemoveDataProcessingFunctionReq) (*pb.RemoveDataProcessingFunctionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := dataprocessingfunctionservicelogic.NewRemoveDataProcessingFunctionLogic(ctx, s.svcCtx)

	return l.RemoveDataProcessingFunction(in)
}

// ModifyDataProcessingFunction 编辑数据处理函数
func (s *DataProcessingFunctionServiceServer) ModifyDataProcessingFunction(ctx context.Context, in *pb.CreateOrModifyDataProcessingFunctionReq) (*pb.CreateOrModifyDataProcessingFunctionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := dataprocessingfunctionservicelogic.NewModifyDataProcessingFunctionLogic(ctx, s.svcCtx)

	return l.ModifyDataProcessingFunction(in)
}

// SearchDataProcessingFunction 搜索数据处理函数
func (s *DataProcessingFunctionServiceServer) SearchDataProcessingFunction(ctx context.Context, in *pb.SearchDataProcessingFunctionReq) (*pb.SearchDataProcessingFunctionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := dataprocessingfunctionservicelogic.NewSearchDataProcessingFunctionLogic(ctx, s.svcCtx)

	return l.SearchDataProcessingFunction(in)
}

// ViewDataProcessingFunction 查看数据处理函数
func (s *DataProcessingFunctionServiceServer) ViewDataProcessingFunction(ctx context.Context, in *pb.ViewDataProcessingFunctionReq) (*pb.ViewDataProcessingFunctionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := dataprocessingfunctionservicelogic.NewViewDataProcessingFunctionLogic(ctx, s.svcCtx)

	return l.ViewDataProcessingFunction(in)
}
