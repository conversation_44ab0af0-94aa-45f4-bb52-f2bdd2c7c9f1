// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	apiexecutionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apiexecutionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ApiExecutionServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedApiExecutionServiceServer
}

func NewApiExecutionServiceServer(svcCtx *svc.ServiceContext) *ApiExecutionServiceServer {
	return &ApiExecutionServiceServer{
		svcCtx: svcCtx,
	}
}

// GetApiExecutionData 获取API执行数据
func (s *ApiExecutionServiceServer) GetApiExecutionData(ctx context.Context, in *pb.GetApiExecutionDataReq) (*pb.ApiExecutionData, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiexecutionservicelogic.NewGetApiExecutionDataLogic(ctx, s.svcCtx)

	return l.GetApiExecutionData(in)
}

// GetApiExecutionDataStream 批量获取API执行数据（服务端流式）
func (s *ApiExecutionServiceServer) GetApiExecutionDataStream(in *pb.GetApiExecutionDataStreamReq, stream pb.ApiExecutionService_GetApiExecutionDataStreamServer) error {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return err
		}
	}

	l := apiexecutionservicelogic.NewGetApiExecutionDataStreamLogic(stream.Context(), s.svcCtx)

	return l.GetApiExecutionDataStream(in, stream)
}

// GetApiExecutionDataStructure 获取API执行数据结构
func (s *ApiExecutionServiceServer) GetApiExecutionDataStructure(ctx context.Context, in *pb.GetApiExecutionDataStructureReq) (*pb.ApiExecutionData, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiexecutionservicelogic.NewGetApiExecutionDataStructureLogic(ctx, s.svcCtx)

	return l.GetApiExecutionDataStructure(in)
}
