// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	uiplanservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/uiplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UiPlanServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedUiPlanServiceServer
}

func NewUiPlanServiceServer(svcCtx *svc.ServiceContext) *UiPlanServiceServer {
	return &UiPlanServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateUiPlan 创建UI计划
func (s *UiPlanServiceServer) CreateUiPlan(ctx context.Context, in *pb.CreateUiPlanReq) (*pb.CreateUiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewCreateUiPlanLogic(ctx, s.svcCtx)

	return l.CreateUiPlan(in)
}

// RemoveUiPlan 删除UI计划
func (s *UiPlanServiceServer) RemoveUiPlan(ctx context.Context, in *pb.RemoveUiPlanReq) (*pb.RemoveUiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewRemoveUiPlanLogic(ctx, s.svcCtx)

	return l.RemoveUiPlan(in)
}

// ModifyUiPlan 编辑UI计划
func (s *UiPlanServiceServer) ModifyUiPlan(ctx context.Context, in *pb.ModifyUiPlanReq) (*pb.ModifyUiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewModifyUiPlanLogic(ctx, s.svcCtx)

	return l.ModifyUiPlan(in)
}

// SearchUiPlan 搜索UI计划
func (s *UiPlanServiceServer) SearchUiPlan(ctx context.Context, in *pb.SearchUiPlanReq) (*pb.SearchUiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewSearchUiPlanLogic(ctx, s.svcCtx)

	return l.SearchUiPlan(in)
}

// ViewUiPlan 查看UI计划
func (s *UiPlanServiceServer) ViewUiPlan(ctx context.Context, in *pb.ViewUiPlanReq) (*pb.ViewUiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewViewUiPlanLogic(ctx, s.svcCtx)

	return l.ViewUiPlan(in)
}

// SearchUiPlanByProjectIdConfigId 搜索关联指定Git配置的UI计划
func (s *UiPlanServiceServer) SearchUiPlanByProjectIdConfigId(ctx context.Context, in *pb.SearchUiPlanByProjectIdConfigIdReq) (*pb.SearchUiPlanByProjectIdConfigIdResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewSearchUiPlanByProjectIdConfigIdLogic(ctx, s.svcCtx)

	return l.SearchUiPlanByProjectIdConfigId(in)
}

// GetCaseTreeOfUIPlan 获取UI计划的用例树
func (s *UiPlanServiceServer) GetCaseTreeOfUIPlan(ctx context.Context, in *pb.GetCaseTreeOfUIPlanReq) (*pb.GetCaseTreeOfUIPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewGetCaseTreeOfUIPlanLogic(ctx, s.svcCtx)

	return l.GetCaseTreeOfUIPlan(in)
}

// GetCaseTreeOfNotAddedToUIPlan 获取不在指定的UI计划中的用例树
func (s *UiPlanServiceServer) GetCaseTreeOfNotAddedToUIPlan(ctx context.Context, in *pb.GetCaseTreeOfNotAddedToUIPlanReq) (*pb.GetCaseTreeOfNotAddedToUIPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewGetCaseTreeOfNotAddedToUIPlanLogic(ctx, s.svcCtx)

	return l.GetCaseTreeOfNotAddedToUIPlan(in)
}

// SearchCaseInUIPlan 搜索UI计划中的用例
func (s *UiPlanServiceServer) SearchCaseInUIPlan(ctx context.Context, in *pb.SearchCaseInUIPlanReq) (*pb.SearchCaseInUIPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewSearchCaseInUIPlanLogic(ctx, s.svcCtx)

	return l.SearchCaseInUIPlan(in)
}

// ListDisableCaseInUIPlan 搜索UI计划中的失效用例
func (s *UiPlanServiceServer) ListDisableCaseInUIPlan(ctx context.Context, in *pb.ListDisableCaseInUIPlanReq) (*pb.ListDisableCaseInUIPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewListDisableCaseInUIPlanLogic(ctx, s.svcCtx)

	return l.ListDisableCaseInUIPlan(in)
}

// SearchCaseNotInUIPlan 搜索不在指定的UI计划中的用例
func (s *UiPlanServiceServer) SearchCaseNotInUIPlan(ctx context.Context, in *pb.SearchCaseNotInUIPlanReq) (*pb.SearchCaseNotInUIPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewSearchCaseNotInUIPlanLogic(ctx, s.svcCtx)

	return l.SearchCaseNotInUIPlan(in)
}

// AddCaseToUIPlan 添加用例到UI计划中
func (s *UiPlanServiceServer) AddCaseToUIPlan(ctx context.Context, in *pb.AddCaseToUIPlanReq) (*pb.AddCaseToUIPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewAddCaseToUIPlanLogic(ctx, s.svcCtx)

	return l.AddCaseToUIPlan(in)
}

// RemoveCaseFromUIPlan 移除UI计划中的用例
func (s *UiPlanServiceServer) RemoveCaseFromUIPlan(ctx context.Context, in *pb.RemoveCaseFromUIPlanReq) (*pb.RemoveCaseFromUIPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewRemoveCaseFromUIPlanLogic(ctx, s.svcCtx)

	return l.RemoveCaseFromUIPlan(in)
}

// SearchLikeUiPlan 搜索收藏UI计划
func (s *UiPlanServiceServer) SearchLikeUiPlan(ctx context.Context, in *pb.SearchUiPlanReq) (*pb.SearchUiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiplanservicelogic.NewSearchLikeUiPlanLogic(ctx, s.svcCtx)

	return l.SearchLikeUiPlan(in)
}
