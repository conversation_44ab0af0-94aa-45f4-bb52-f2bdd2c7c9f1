// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type TagServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedTagServiceServer
}

func NewTagServiceServer(svcCtx *svc.ServiceContext) *TagServiceServer {
	return &TagServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateTag 创建标签
func (s *TagServiceServer) CreateTag(ctx context.Context, in *pb.CreateTagReq) (*pb.CreateTagResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := tagservicelogic.NewCreateTagLogic(ctx, s.svcCtx)

	return l.CreateTag(in)
}

// RemoveTag 删除标签
func (s *TagServiceServer) RemoveTag(ctx context.Context, in *pb.RemoveTagReq) (*pb.RemoveTagResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := tagservicelogic.NewRemoveTagLogic(ctx, s.svcCtx)

	return l.RemoveTag(in)
}

// ModifyTag 编辑标签
func (s *TagServiceServer) ModifyTag(ctx context.Context, in *pb.ModifyTagReq) (*pb.ModifyTagResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := tagservicelogic.NewModifyTagLogic(ctx, s.svcCtx)

	return l.ModifyTag(in)
}

// SearchTag 查询标签
func (s *TagServiceServer) SearchTag(ctx context.Context, in *pb.SearchTagReq) (*pb.SearchTagResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := tagservicelogic.NewSearchTagLogic(ctx, s.svcCtx)

	return l.SearchTag(in)
}

// ViewTag   查看标签
func (s *TagServiceServer) ViewTag(ctx context.Context, in *pb.ViewTagReq) (*pb.ViewTagResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := tagservicelogic.NewViewTagLogic(ctx, s.svcCtx)

	return l.ViewTag(in)
}
