// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	interfacedefinitionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/interfacedefinitionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type InterfaceDefinitionServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedInterfaceDefinitionServiceServer
}

func NewInterfaceDefinitionServiceServer(svcCtx *svc.ServiceContext) *InterfaceDefinitionServiceServer {
	return &InterfaceDefinitionServiceServer{
		svcCtx: svcCtx,
	}
}

// LocalImportInterfaceDefinition 本地导入接口定义
func (s *InterfaceDefinitionServiceServer) LocalImportInterfaceDefinition(ctx context.Context, in *pb.LocalImportInterfaceDefinitionReq) (*pb.LocalImportInterfaceDefinitionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewLocalImportInterfaceDefinitionLogic(ctx, s.svcCtx)

	return l.LocalImportInterfaceDefinition(in)
}

// CreateInterfaceDocument 创建接口文档
func (s *InterfaceDefinitionServiceServer) CreateInterfaceDocument(ctx context.Context, in *pb.CreateInterfaceDocumentReq) (*pb.CreateInterfaceDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewCreateInterfaceDocumentLogic(ctx, s.svcCtx)

	return l.CreateInterfaceDocument(in)
}

// RemoveInterfaceDocument 删除接口文档
func (s *InterfaceDefinitionServiceServer) RemoveInterfaceDocument(ctx context.Context, in *pb.RemoveInterfaceDocumentReq) (*pb.RemoveInterfaceDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewRemoveInterfaceDocumentLogic(ctx, s.svcCtx)

	return l.RemoveInterfaceDocument(in)
}

// ModifyInterfaceDocument 编辑接口文档
func (s *InterfaceDefinitionServiceServer) ModifyInterfaceDocument(ctx context.Context, in *pb.ModifyInterfaceDocumentReq) (*pb.ModifyInterfaceDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewModifyInterfaceDocumentLogic(ctx, s.svcCtx)

	return l.ModifyInterfaceDocument(in)
}

// SearchInterfaceDocument 搜索接口文档
func (s *InterfaceDefinitionServiceServer) SearchInterfaceDocument(ctx context.Context, in *pb.SearchInterfaceDocumentReq) (*pb.SearchInterfaceDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewSearchInterfaceDocumentLogic(ctx, s.svcCtx)

	return l.SearchInterfaceDocument(in)
}

// ViewInterfaceDocument 查看接口文档
func (s *InterfaceDefinitionServiceServer) ViewInterfaceDocument(ctx context.Context, in *pb.ViewInterfaceDocumentReq) (*pb.ViewInterfaceDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewViewInterfaceDocumentLogic(ctx, s.svcCtx)

	return l.ViewInterfaceDocument(in)
}

// MockInterfaceDocument 根据接口文档生成接口用例数据
func (s *InterfaceDefinitionServiceServer) MockInterfaceDocument(ctx context.Context, in *pb.MockInterfaceDocumentReq) (*pb.MockInterfaceDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewMockInterfaceDocumentLogic(ctx, s.svcCtx)

	return l.MockInterfaceDocument(in)
}

// SearchInterfaceDocumentReference 搜索接口集合引用详情
func (s *InterfaceDefinitionServiceServer) SearchInterfaceDocumentReference(ctx context.Context, in *pb.SearchInterfaceDocumentReferenceReq) (*pb.SearchInterfaceDocumentReferenceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewSearchInterfaceDocumentReferenceLogic(ctx, s.svcCtx)

	return l.SearchInterfaceDocumentReference(in)
}

// ModifyInterfaceDocumentReferenceState 修改接口集合所在的API计划的引用状态
func (s *InterfaceDefinitionServiceServer) ModifyInterfaceDocumentReferenceState(ctx context.Context, in *pb.ModifyInterfaceDocumentReferenceStateReq) (*pb.ModifyInterfaceDocumentReferenceStateResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewModifyInterfaceDocumentReferenceStateLogic(ctx, s.svcCtx)

	return l.ModifyInterfaceDocumentReferenceState(in)
}

// CreateInterfaceSchema 创建接口数据模型
func (s *InterfaceDefinitionServiceServer) CreateInterfaceSchema(ctx context.Context, in *pb.CreateInterfaceSchemaReq) (*pb.CreateInterfaceSchemaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewCreateInterfaceSchemaLogic(ctx, s.svcCtx)

	return l.CreateInterfaceSchema(in)
}

// RemoveInterfaceSchema 删除接口数据模型
func (s *InterfaceDefinitionServiceServer) RemoveInterfaceSchema(ctx context.Context, in *pb.RemoveInterfaceSchemaReq) (*pb.RemoveInterfaceSchemaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewRemoveInterfaceSchemaLogic(ctx, s.svcCtx)

	return l.RemoveInterfaceSchema(in)
}

// ModifyInterfaceSchema 编辑接口数据模型
func (s *InterfaceDefinitionServiceServer) ModifyInterfaceSchema(ctx context.Context, in *pb.ModifyInterfaceSchemaReq) (*pb.ModifyInterfaceSchemaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewModifyInterfaceSchemaLogic(ctx, s.svcCtx)

	return l.ModifyInterfaceSchema(in)
}

// SearchInterfaceSchema 搜索接口数据模型
func (s *InterfaceDefinitionServiceServer) SearchInterfaceSchema(ctx context.Context, in *pb.SearchInterfaceSchemaReq) (*pb.SearchInterfaceSchemaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewSearchInterfaceSchemaLogic(ctx, s.svcCtx)

	return l.SearchInterfaceSchema(in)
}

// ViewInterfaceSchema 查看接口数据模型
func (s *InterfaceDefinitionServiceServer) ViewInterfaceSchema(ctx context.Context, in *pb.ViewInterfaceSchemaReq) (*pb.ViewInterfaceSchemaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewViewInterfaceSchemaLogic(ctx, s.svcCtx)

	return l.ViewInterfaceSchema(in)
}

// CreateInterfaceConfig 创建接口配置
func (s *InterfaceDefinitionServiceServer) CreateInterfaceConfig(ctx context.Context, in *pb.CreateInterfaceConfigReq) (*pb.CreateInterfaceConfigResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewCreateInterfaceConfigLogic(ctx, s.svcCtx)

	return l.CreateInterfaceConfig(in)
}

// RemoveInterfaceConfig 删除接口配置
func (s *InterfaceDefinitionServiceServer) RemoveInterfaceConfig(ctx context.Context, in *pb.RemoveInterfaceConfigReq) (*pb.RemoveInterfaceConfigResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewRemoveInterfaceConfigLogic(ctx, s.svcCtx)

	return l.RemoveInterfaceConfig(in)
}

// ModifyInterfaceConfig 编辑接口配置
func (s *InterfaceDefinitionServiceServer) ModifyInterfaceConfig(ctx context.Context, in *pb.ModifyInterfaceConfigReq) (*pb.ModifyInterfaceConfigResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewModifyInterfaceConfigLogic(ctx, s.svcCtx)

	return l.ModifyInterfaceConfig(in)
}

// SearchInterfaceConfig 搜索接口配置
func (s *InterfaceDefinitionServiceServer) SearchInterfaceConfig(ctx context.Context, in *pb.SearchInterfaceConfigReq) (*pb.SearchInterfaceConfigResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewSearchInterfaceConfigLogic(ctx, s.svcCtx)

	return l.SearchInterfaceConfig(in)
}

// ViewInterfaceConfig 查看接口配置
func (s *InterfaceDefinitionServiceServer) ViewInterfaceConfig(ctx context.Context, in *pb.ViewInterfaceConfigReq) (*pb.ViewInterfaceConfigResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewViewInterfaceConfigLogic(ctx, s.svcCtx)

	return l.ViewInterfaceConfig(in)
}

// CreateInterfaceCase 创建接口用例
func (s *InterfaceDefinitionServiceServer) CreateInterfaceCase(ctx context.Context, in *pb.CreateInterfaceCaseReq) (*pb.CreateInterfaceCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewCreateInterfaceCaseLogic(ctx, s.svcCtx)

	return l.CreateInterfaceCase(in)
}

// RemoveInterfaceCase 删除接口用例
func (s *InterfaceDefinitionServiceServer) RemoveInterfaceCase(ctx context.Context, in *pb.RemoveInterfaceCaseReq) (*pb.RemoveInterfaceCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewRemoveInterfaceCaseLogic(ctx, s.svcCtx)

	return l.RemoveInterfaceCase(in)
}

// ModifyInterfaceCase 编辑接口用例
func (s *InterfaceDefinitionServiceServer) ModifyInterfaceCase(ctx context.Context, in *pb.ModifyInterfaceCaseReq) (*pb.ModifyInterfaceCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewModifyInterfaceCaseLogic(ctx, s.svcCtx)

	return l.ModifyInterfaceCase(in)
}

// SearchInterfaceCase 搜索接口用例
func (s *InterfaceDefinitionServiceServer) SearchInterfaceCase(ctx context.Context, in *pb.SearchInterfaceCaseReq) (*pb.SearchInterfaceCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewSearchInterfaceCaseLogic(ctx, s.svcCtx)

	return l.SearchInterfaceCase(in)
}

// ViewInterfaceCase 查看接口用例
func (s *InterfaceDefinitionServiceServer) ViewInterfaceCase(ctx context.Context, in *pb.ViewInterfaceCaseReq) (*pb.ViewInterfaceCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewViewInterfaceCaseLogic(ctx, s.svcCtx)

	return l.ViewInterfaceCase(in)
}

// MaintainInterfaceCase 维护接口用例
func (s *InterfaceDefinitionServiceServer) MaintainInterfaceCase(ctx context.Context, in *pb.MaintainInterfaceCaseReq) (*pb.MaintainInterfaceCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewMaintainInterfaceCaseLogic(ctx, s.svcCtx)

	return l.MaintainInterfaceCase(in)
}

// PublishInterfaceCase 发布接口用例
func (s *InterfaceDefinitionServiceServer) PublishInterfaceCase(ctx context.Context, in *pb.PublishInterfaceCaseReq) (*pb.PublishInterfaceCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewPublishInterfaceCaseLogic(ctx, s.svcCtx)

	return l.PublishInterfaceCase(in)
}

// SearchInterfaceCaseReference 搜索接口用例引用详情
func (s *InterfaceDefinitionServiceServer) SearchInterfaceCaseReference(ctx context.Context, in *pb.SearchInterfaceCaseReferenceReq) (*pb.SearchInterfaceCaseReferenceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewSearchInterfaceCaseReferenceLogic(ctx, s.svcCtx)

	return l.SearchInterfaceCaseReference(in)
}

// UpdateInterfaceCoverageData 更新接口覆盖率数据
func (s *InterfaceDefinitionServiceServer) UpdateInterfaceCoverageData(ctx context.Context, in *pb.UpdateInterfaceCoverageDataReq) (*pb.UpdateInterfaceCoverageDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewUpdateInterfaceCoverageDataLogic(ctx, s.svcCtx)

	return l.UpdateInterfaceCoverageData(in)
}

// GetInterfaceCoverageTeams 获取接口覆盖率相关的团队
func (s *InterfaceDefinitionServiceServer) GetInterfaceCoverageTeams(ctx context.Context, in *pb.GetInterfaceCoverageTeamsReq) (*pb.GetInterfaceCoverageTeamsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewGetInterfaceCoverageTeamsLogic(ctx, s.svcCtx)

	return l.GetInterfaceCoverageTeams(in)
}

// GetInterfaceCoverageData 获取接口覆盖率数据
func (s *InterfaceDefinitionServiceServer) GetInterfaceCoverageData(ctx context.Context, in *pb.GetInterfaceCoverageDataReq) (*pb.GetInterfaceCoverageDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewGetInterfaceCoverageDataLogic(ctx, s.svcCtx)

	return l.GetInterfaceCoverageData(in)
}

// UpdateInterfaceDocumentTags 更新接口文档标签
func (s *InterfaceDefinitionServiceServer) UpdateInterfaceDocumentTags(ctx context.Context, in *pb.UpdateInterfaceDocumentTagsReq) (*pb.UpdateInterfaceDocumentTagsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := interfacedefinitionservicelogic.NewUpdateInterfaceDocumentTagsLogic(ctx, s.svcCtx)

	return l.UpdateInterfaceDocumentTags(in)
}
