// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	notifyservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/notifyservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type NotifyServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedNotifyServiceServer
}

func NewNotifyServiceServer(svcCtx *svc.ServiceContext) *NotifyServiceServer {
	return &NotifyServiceServer{
		svcCtx: svcCtx,
	}
}

func (s *NotifyServiceServer) CreatePlanNotify(ctx context.Context, in *pb.CreatePlanNotifyReq) (*pb.CreatePlanNotifyResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := notifyservicelogic.NewCreatePlanNotifyLogic(ctx, s.svcCtx)

	return l.CreatePlanNotify(in)
}

func (s *NotifyServiceServer) RemovePlanNotify(ctx context.Context, in *pb.RemovePlanNotifyReq) (*pb.RemovePlanNotifyResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := notifyservicelogic.NewRemovePlanNotifyLogic(ctx, s.svcCtx)

	return l.RemovePlanNotify(in)
}

func (s *NotifyServiceServer) ModifyPlanNotify(ctx context.Context, in *pb.ModifyPlanNotifyReq) (*pb.ModifyPlanNotifyResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := notifyservicelogic.NewModifyPlanNotifyLogic(ctx, s.svcCtx)

	return l.ModifyPlanNotify(in)
}

func (s *NotifyServiceServer) SearchPlanNotify(ctx context.Context, in *pb.SearchPlanNotifyReq) (*pb.SearchPlanNotifyResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := notifyservicelogic.NewSearchPlanNotifyLogic(ctx, s.svcCtx)

	return l.SearchPlanNotify(in)
}

func (s *NotifyServiceServer) GetPlanNotify(ctx context.Context, in *pb.GetPlanNotifyReq) (*pb.GetPlanNotifyResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := notifyservicelogic.NewGetPlanNotifyLogic(ctx, s.svcCtx)

	return l.GetPlanNotify(in)
}
