// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	apisuiteservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apisuiteservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ApiSuiteServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedApiSuiteServiceServer
}

func NewApiSuiteServiceServer(svcCtx *svc.ServiceContext) *ApiSuiteServiceServer {
	return &ApiSuiteServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateApiSuite 创建API集合
func (s *ApiSuiteServiceServer) CreateApiSuite(ctx context.Context, in *pb.CreateApiSuiteReq) (*pb.CreateApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewCreateApiSuiteLogic(ctx, s.svcCtx)

	return l.CreateApiSuite(in)
}

// RemoveApiSuite 删除API集合
func (s *ApiSuiteServiceServer) RemoveApiSuite(ctx context.Context, in *pb.RemoveApiSuiteReq) (*pb.RemoveApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewRemoveApiSuiteLogic(ctx, s.svcCtx)

	return l.RemoveApiSuite(in)
}

// ModifyApiSuite 编辑API集合
func (s *ApiSuiteServiceServer) ModifyApiSuite(ctx context.Context, in *pb.ModifyApiSuiteReq) (*pb.ModifyApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewModifyApiSuiteLogic(ctx, s.svcCtx)

	return l.ModifyApiSuite(in)
}

// SearchApiSuite 搜索API集合
func (s *ApiSuiteServiceServer) SearchApiSuite(ctx context.Context, in *pb.SearchApiSuiteReq) (*pb.SearchApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewSearchApiSuiteLogic(ctx, s.svcCtx)

	return l.SearchApiSuite(in)
}

// ViewApiSuite 查看API集合
func (s *ApiSuiteServiceServer) ViewApiSuite(ctx context.Context, in *pb.ViewApiSuiteReq) (*pb.ViewApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewViewApiSuiteLogic(ctx, s.svcCtx)

	return l.ViewApiSuite(in)
}

// SearchApiCaseInApiSuite 搜索API集合中的API用例
func (s *ApiSuiteServiceServer) SearchApiCaseInApiSuite(ctx context.Context, in *pb.SearchApiCaseInApiSuiteReq) (*pb.SearchApiCaseInApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewSearchApiCaseInApiSuiteLogic(ctx, s.svcCtx)

	return l.SearchApiCaseInApiSuite(in)
}

// SearchApiCaseNotInApiSuite 搜索不在指定的API集合中的API用例
func (s *ApiSuiteServiceServer) SearchApiCaseNotInApiSuite(ctx context.Context, in *pb.SearchApiCaseNotInApiSuiteReq) (*pb.SearchApiCaseNotInApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewSearchApiCaseNotInApiSuiteLogic(ctx, s.svcCtx)

	return l.SearchApiCaseNotInApiSuite(in)
}

// AddApiCaseToApiSuite 添加API用例到API集合中
func (s *ApiSuiteServiceServer) AddApiCaseToApiSuite(ctx context.Context, in *pb.AddApiCaseToApiSuiteReq) (*pb.AddApiCaseToApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewAddApiCaseToApiSuiteLogic(ctx, s.svcCtx)

	return l.AddApiCaseToApiSuite(in)
}

// RemoveApiCaseFromApiSuite 从API集合中移除API用例
func (s *ApiSuiteServiceServer) RemoveApiCaseFromApiSuite(ctx context.Context, in *pb.RemoveApiCaseFromApiSuiteReq) (*pb.RemoveApiCaseFromApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewRemoveApiCaseFromApiSuiteLogic(ctx, s.svcCtx)

	return l.RemoveApiCaseFromApiSuite(in)
}

// SearchApiSuiteReference 搜索API集合引用详情
func (s *ApiSuiteServiceServer) SearchApiSuiteReference(ctx context.Context, in *pb.SearchApiSuiteReferenceReq) (*pb.SearchApiSuiteReferenceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewSearchApiSuiteReferenceLogic(ctx, s.svcCtx)

	return l.SearchApiSuiteReference(in)
}

// ModifyApiSuiteReferenceState 修改API集合所在的API计划的引用状态
func (s *ApiSuiteServiceServer) ModifyApiSuiteReferenceState(ctx context.Context, in *pb.ModifyApiSuiteReferenceStateReq) (*pb.ModifyApiSuiteReferenceStateResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewModifyApiSuiteReferenceStateLogic(ctx, s.svcCtx)

	return l.ModifyApiSuiteReferenceState(in)
}

// SearchCaseInApiSuite 搜索API集合中的用例
func (s *ApiSuiteServiceServer) SearchCaseInApiSuite(ctx context.Context, in *pb.SearchCaseInApiSuiteReq) (*pb.SearchCaseInApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewSearchCaseInApiSuiteLogic(ctx, s.svcCtx)

	return l.SearchCaseInApiSuite(in)
}

// AddCaseToApiSuite 添加用例到API集合中
func (s *ApiSuiteServiceServer) AddCaseToApiSuite(ctx context.Context, in *pb.AddCaseToApiSuiteReq) (*pb.AddCaseToApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewAddCaseToApiSuiteLogic(ctx, s.svcCtx)

	return l.AddCaseToApiSuite(in)
}

// RemoveCaseFromApiSuite 从API集合中移除用例
func (s *ApiSuiteServiceServer) RemoveCaseFromApiSuite(ctx context.Context, in *pb.RemoveCaseFromApiSuiteReq) (*pb.RemoveCaseFromApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewRemoveCaseFromApiSuiteLogic(ctx, s.svcCtx)

	return l.RemoveCaseFromApiSuite(in)
}

// SearchServiceCaseNotInApiSuite 搜索不在指定的API集合中的精准测试用例
func (s *ApiSuiteServiceServer) SearchServiceCaseNotInApiSuite(ctx context.Context, in *pb.SearchServiceCaseNotInApiSuiteReq) (*pb.SearchServiceCaseNotInApiSuiteResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apisuiteservicelogic.NewSearchServiceCaseNotInApiSuiteLogic(ctx, s.svcCtx)

	return l.SearchServiceCaseNotInApiSuite(in)
}
