// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	plancommonservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/plancommonservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PlanCommonServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPlanCommonServiceServer
}

func NewPlanCommonServiceServer(svcCtx *svc.ServiceContext) *PlanCommonServiceServer {
	return &PlanCommonServiceServer{
		svcCtx: svcCtx,
	}
}

// GetApiExecutionData 处理计划收藏
func (s *PlanCommonServiceServer) HandleLikePlan(ctx context.Context, in *pb.HandleLikePlanReq) (*pb.HandleLikePlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := plancommonservicelogic.NewHandleLikePlanLogic(ctx, s.svcCtx)

	return l.HandleLikePlan(in)
}

// CheckLikePlan 检查计划收藏情况
func (s *PlanCommonServiceServer) CheckLikePlan(ctx context.Context, in *pb.CheckLikePlanReq) (*pb.CheckLikePlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := plancommonservicelogic.NewCheckLikePlanLogic(ctx, s.svcCtx)

	return l.CheckLikePlan(in)
}
