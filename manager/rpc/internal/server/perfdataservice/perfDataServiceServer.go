// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perfdataservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfdataservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfDataServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfDataServiceServer
}

func NewPerfDataServiceServer(svcCtx *svc.ServiceContext) *PerfDataServiceServer {
	return &PerfDataServiceServer{
		svcCtx: svcCtx,
	}
}

// RemovePerfData 删除压测数据
func (s *PerfDataServiceServer) RemovePerfData(ctx context.Context, in *pb.RemovePerfDataReq) (*pb.RemovePerfDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfdataservicelogic.NewRemovePerfDataLogic(ctx, s.svcCtx)

	return l.RemovePerfData(in)
}

// SearchPerfData 搜索压测数据
func (s *PerfDataServiceServer) SearchPerfData(ctx context.Context, in *pb.SearchPerfDataReq) (*pb.SearchPerfDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfdataservicelogic.NewSearchPerfDataLogic(ctx, s.svcCtx)

	return l.SearchPerfData(in)
}
