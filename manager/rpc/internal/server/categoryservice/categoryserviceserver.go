// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CategoryServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedCategoryServiceServer
}

func NewCategoryServiceServer(svcCtx *svc.ServiceContext) *CategoryServiceServer {
	return &CategoryServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateCategory 创建分类
func (s *CategoryServiceServer) CreateCategory(ctx context.Context, in *pb.CreateCategoryReq) (*pb.CreateCategoryResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := categoryservicelogic.NewCreateCategoryLogic(ctx, s.svcCtx)

	return l.CreateCategory(in)
}

// RemoveCategory 删除分类
func (s *CategoryServiceServer) RemoveCategory(ctx context.Context, in *pb.RemoveCategoryReq) (*pb.RemoveCategoryResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := categoryservicelogic.NewRemoveCategoryLogic(ctx, s.svcCtx)

	return l.RemoveCategory(in)
}

// ModifyCategory 编辑分类
func (s *CategoryServiceServer) ModifyCategory(ctx context.Context, in *pb.ModifyCategoryReq) (*pb.ModifyCategoryResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := categoryservicelogic.NewModifyCategoryLogic(ctx, s.svcCtx)

	return l.ModifyCategory(in)
}

// SearchCategory 搜索分类
func (s *CategoryServiceServer) SearchCategory(ctx context.Context, in *pb.SearchCategoryReq) (*pb.SearchCategoryResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := categoryservicelogic.NewSearchCategoryLogic(ctx, s.svcCtx)

	return l.SearchCategory(in)
}

// MoveCategoryTree 移动分类树
func (s *CategoryServiceServer) MoveCategoryTree(ctx context.Context, in *pb.MoveCategoryTreeReq) (*pb.MoveCategoryTreeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := categoryservicelogic.NewMoveCategoryTreeLogic(ctx, s.svcCtx)

	return l.MoveCategoryTree(in)
}

// GetCategoryTree 获取分类树
func (s *CategoryServiceServer) GetCategoryTree(ctx context.Context, in *pb.GetCategoryTreeReq) (*pb.GetCategoryTreeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := categoryservicelogic.NewGetCategoryTreeLogic(ctx, s.svcCtx)

	return l.GetCategoryTree(in)
}
