// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	accountconfigurationservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/accountconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AccountConfigurationServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedAccountConfigurationServiceServer
}

func NewAccountConfigurationServiceServer(svcCtx *svc.ServiceContext) *AccountConfigurationServiceServer {
	return &AccountConfigurationServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateAccountConfiguration 创建池账号配置
func (s *AccountConfigurationServiceServer) CreateAccountConfiguration(ctx context.Context, in *pb.CreateAccountConfigurationReq) (*pb.CreateAccountConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := accountconfigurationservicelogic.NewCreateAccountConfigurationLogic(ctx, s.svcCtx)

	return l.CreateAccountConfiguration(in)
}

// RemoveAccountConfiguration 删除池账号配置
func (s *AccountConfigurationServiceServer) RemoveAccountConfiguration(ctx context.Context, in *pb.RemoveAccountConfigurationReq) (*pb.RemoveAccountConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := accountconfigurationservicelogic.NewRemoveAccountConfigurationLogic(ctx, s.svcCtx)

	return l.RemoveAccountConfiguration(in)
}

// ModifyAccountConfiguration 编辑池账号配置
func (s *AccountConfigurationServiceServer) ModifyAccountConfiguration(ctx context.Context, in *pb.ModifyAccountConfigurationReq) (*pb.ModifyAccountConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := accountconfigurationservicelogic.NewModifyAccountConfigurationLogic(ctx, s.svcCtx)

	return l.ModifyAccountConfiguration(in)
}

// SearchAccountConfiguration 搜索池账号配置
func (s *AccountConfigurationServiceServer) SearchAccountConfiguration(ctx context.Context, in *pb.SearchAccountConfigurationReq) (*pb.SearchAccountConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := accountconfigurationservicelogic.NewSearchAccountConfigurationLogic(ctx, s.svcCtx)

	return l.SearchAccountConfiguration(in)
}

// ViewAccountConfiguration 查看池账号配置
func (s *AccountConfigurationServiceServer) ViewAccountConfiguration(ctx context.Context, in *pb.ViewAccountConfigurationReq) (*pb.ViewAccountConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := accountconfigurationservicelogic.NewViewAccountConfigurationLogic(ctx, s.svcCtx)

	return l.ViewAccountConfiguration(in)
}
