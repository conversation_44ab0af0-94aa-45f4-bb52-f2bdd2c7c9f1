// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perfcasev2servicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfcasev2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfCaseV2ServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfCaseV2ServiceServer
}

func NewPerfCaseV2ServiceServer(svcCtx *svc.ServiceContext) *PerfCaseV2ServiceServer {
	return &PerfCaseV2ServiceServer{
		svcCtx: svcCtx,
	}
}

// CreatePerfCaseV2 创建压测用例
func (s *PerfCaseV2ServiceServer) CreatePerfCaseV2(ctx context.Context, in *pb.CreatePerfCaseV2Req) (*pb.CreatePerfCaseV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfcasev2servicelogic.NewCreatePerfCaseV2Logic(ctx, s.svcCtx)

	return l.CreatePerfCaseV2(in)
}

// RemovePerfCaseV2 删除压测用例
func (s *PerfCaseV2ServiceServer) RemovePerfCaseV2(ctx context.Context, in *pb.RemovePerfCaseV2Req) (*pb.RemovePerfCaseV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfcasev2servicelogic.NewRemovePerfCaseV2Logic(ctx, s.svcCtx)

	return l.RemovePerfCaseV2(in)
}

// ModifyPerfCaseV2 编辑压测用例
func (s *PerfCaseV2ServiceServer) ModifyPerfCaseV2(ctx context.Context, in *pb.ModifyPerfCaseV2Req) (*pb.ModifyPerfCaseV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfcasev2servicelogic.NewModifyPerfCaseV2Logic(ctx, s.svcCtx)

	return l.ModifyPerfCaseV2(in)
}

// SearchPerfCaseV2 搜索压测用例
func (s *PerfCaseV2ServiceServer) SearchPerfCaseV2(ctx context.Context, in *pb.SearchPerfCaseV2Req) (*pb.SearchPerfCaseV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfcasev2servicelogic.NewSearchPerfCaseV2Logic(ctx, s.svcCtx)

	return l.SearchPerfCaseV2(in)
}

// ViewPerfCaseV2 查看压测用例
func (s *PerfCaseV2ServiceServer) ViewPerfCaseV2(ctx context.Context, in *pb.ViewPerfCaseV2Req) (*pb.ViewPerfCaseV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfcasev2servicelogic.NewViewPerfCaseV2Logic(ctx, s.svcCtx)

	return l.ViewPerfCaseV2(in)
}
