// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	componentgroupservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/componentgroupservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ComponentGroupServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedComponentGroupServiceServer
}

func NewComponentGroupServiceServer(svcCtx *svc.ServiceContext) *ComponentGroupServiceServer {
	return &ComponentGroupServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateComponentGroup 创建组件组
func (s *ComponentGroupServiceServer) CreateComponentGroup(ctx context.Context, in *pb.CreateComponentGroupReq) (*pb.CreateComponentGroupResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := componentgroupservicelogic.NewCreateComponentGroupLogic(ctx, s.svcCtx)

	return l.CreateComponentGroup(in)
}

// RemoveComponentGroup 删除组件组
func (s *ComponentGroupServiceServer) RemoveComponentGroup(ctx context.Context, in *pb.RemoveComponentGroupReq) (*pb.RemoveComponentGroupResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := componentgroupservicelogic.NewRemoveComponentGroupLogic(ctx, s.svcCtx)

	return l.RemoveComponentGroup(in)
}

// ModifyComponentGroup 编辑组件组
func (s *ComponentGroupServiceServer) ModifyComponentGroup(ctx context.Context, in *pb.ModifyComponentGroupReq) (*pb.ModifyComponentGroupResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := componentgroupservicelogic.NewModifyComponentGroupLogic(ctx, s.svcCtx)

	return l.ModifyComponentGroup(in)
}

// SearchComponentGroup 搜索组件组
func (s *ComponentGroupServiceServer) SearchComponentGroup(ctx context.Context, in *pb.SearchComponentGroupReq) (*pb.SearchComponentGroupResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := componentgroupservicelogic.NewSearchComponentGroupLogic(ctx, s.svcCtx)

	return l.SearchComponentGroup(in)
}

// ViewComponentGroup 查看组件组
func (s *ComponentGroupServiceServer) ViewComponentGroup(ctx context.Context, in *pb.ViewComponentGroupReq) (*pb.ViewComponentGroupResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := componentgroupservicelogic.NewViewComponentGroupLogic(ctx, s.svcCtx)

	return l.ViewComponentGroup(in)
}

// SearchComponentGroupReference 搜索组件组引用详情
func (s *ComponentGroupServiceServer) SearchComponentGroupReference(ctx context.Context, in *pb.SearchComponentGroupReferenceReq) (*pb.SearchComponentGroupReferenceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := componentgroupservicelogic.NewSearchComponentGroupReferenceLogic(ctx, s.svcCtx)

	return l.SearchComponentGroupReference(in)
}
