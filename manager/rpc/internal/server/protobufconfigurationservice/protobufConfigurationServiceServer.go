// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	protobufconfigurationservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/protobufconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ProtobufConfigurationServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedProtobufConfigurationServiceServer
}

func NewProtobufConfigurationServiceServer(svcCtx *svc.ServiceContext) *ProtobufConfigurationServiceServer {
	return &ProtobufConfigurationServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateProtobufConfiguration 创建Protobuf配置
func (s *ProtobufConfigurationServiceServer) CreateProtobufConfiguration(ctx context.Context, in *pb.CreateProtobufConfigurationReq) (*pb.CreateProtobufConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := protobufconfigurationservicelogic.NewCreateProtobufConfigurationLogic(ctx, s.svcCtx)

	return l.CreateProtobufConfiguration(in)
}

// RemoveProtobufConfiguration 删除Protobuf配置
func (s *ProtobufConfigurationServiceServer) RemoveProtobufConfiguration(ctx context.Context, in *pb.RemoveProtobufConfigurationReq) (*pb.RemoveProtobufConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := protobufconfigurationservicelogic.NewRemoveProtobufConfigurationLogic(ctx, s.svcCtx)

	return l.RemoveProtobufConfiguration(in)
}

// ModifyProtobufConfiguration 编辑Protobuf配置
func (s *ProtobufConfigurationServiceServer) ModifyProtobufConfiguration(ctx context.Context, in *pb.ModifyProtobufConfigurationReq) (*pb.ModifyProtobufConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := protobufconfigurationservicelogic.NewModifyProtobufConfigurationLogic(ctx, s.svcCtx)

	return l.ModifyProtobufConfiguration(in)
}

// SearchProtobufConfiguration 搜索Protobuf配置
func (s *ProtobufConfigurationServiceServer) SearchProtobufConfiguration(ctx context.Context, in *pb.SearchProtobufConfigurationReq) (*pb.SearchProtobufConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := protobufconfigurationservicelogic.NewSearchProtobufConfigurationLogic(ctx, s.svcCtx)

	return l.SearchProtobufConfiguration(in)
}

// ViewProtobufConfiguration 查看Protobuf配置
func (s *ProtobufConfigurationServiceServer) ViewProtobufConfiguration(ctx context.Context, in *pb.ViewProtobufConfigurationReq) (*pb.ViewProtobufConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := protobufconfigurationservicelogic.NewViewProtobufConfigurationLogic(ctx, s.svcCtx)

	return l.ViewProtobufConfiguration(in)
}
