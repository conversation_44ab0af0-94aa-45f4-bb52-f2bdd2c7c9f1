// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	stabilityplanservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/stabilityplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type StabilityPlanServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedStabilityPlanServiceServer
}

func NewStabilityPlanServiceServer(svcCtx *svc.ServiceContext) *StabilityPlanServiceServer {
	return &StabilityPlanServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateStabilityPlan 创建稳定性测试计划
func (s *StabilityPlanServiceServer) CreateStabilityPlan(ctx context.Context, in *pb.CreateStabilityPlanReq) (*pb.CreateStabilityPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityplanservicelogic.NewCreateStabilityPlanLogic(ctx, s.svcCtx)

	return l.CreateStabilityPlan(in)
}

// RemoveStabilityPlan 删除稳定性测试计划
func (s *StabilityPlanServiceServer) RemoveStabilityPlan(ctx context.Context, in *pb.RemoveStabilityPlanReq) (*pb.RemoveStabilityPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityplanservicelogic.NewRemoveStabilityPlanLogic(ctx, s.svcCtx)

	return l.RemoveStabilityPlan(in)
}

// ModifyStabilityPlan 编辑稳定性测试计划
func (s *StabilityPlanServiceServer) ModifyStabilityPlan(ctx context.Context, in *pb.ModifyStabilityPlanReq) (*pb.ModifyStabilityPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityplanservicelogic.NewModifyStabilityPlanLogic(ctx, s.svcCtx)

	return l.ModifyStabilityPlan(in)
}

// SearchStabilityPlan 搜索稳定性测试计划
func (s *StabilityPlanServiceServer) SearchStabilityPlan(ctx context.Context, in *pb.SearchStabilityPlanReq) (*pb.SearchStabilityPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityplanservicelogic.NewSearchStabilityPlanLogic(ctx, s.svcCtx)

	return l.SearchStabilityPlan(in)
}

// ViewStabilityPlan   查看稳定性测试计划
func (s *StabilityPlanServiceServer) ViewStabilityPlan(ctx context.Context, in *pb.ViewStabilityPlanReq) (*pb.ViewStabilityPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityplanservicelogic.NewViewStabilityPlanLogic(ctx, s.svcCtx)

	return l.ViewStabilityPlan(in)
}
