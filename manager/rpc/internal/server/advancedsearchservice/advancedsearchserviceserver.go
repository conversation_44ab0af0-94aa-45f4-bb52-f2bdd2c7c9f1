// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	advancedsearchservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/advancedsearchservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AdvancedSearchServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedAdvancedSearchServiceServer
}

func NewAdvancedSearchServiceServer(svcCtx *svc.ServiceContext) *AdvancedSearchServiceServer {
	return &AdvancedSearchServiceServer{
		svcCtx: svcCtx,
	}
}

func (s *AdvancedSearchServiceServer) SearchAdvancedSearchField(ctx context.Context, in *pb.SearchAdvancedSearchFieldReq) (*pb.SearchAdvancedSearchFieldResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := advancedsearchservicelogic.NewSearchAdvancedSearchFieldLogic(ctx, s.svcCtx)

	return l.SearchAdvancedSearchField(in)
}

func (s *AdvancedSearchServiceServer) SearchAdvancedSearchCondition(ctx context.Context, in *pb.SearchAdvancedSearchConditionReq) (*pb.SearchAdvancedSearchConditionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := advancedsearchservicelogic.NewSearchAdvancedSearchConditionLogic(ctx, s.svcCtx)

	return l.SearchAdvancedSearchCondition(in)
}
