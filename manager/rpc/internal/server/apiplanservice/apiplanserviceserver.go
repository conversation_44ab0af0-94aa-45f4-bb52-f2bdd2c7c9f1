// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	apiplanservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apiplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ApiPlanServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedApiPlanServiceServer
}

func NewApiPlanServiceServer(svcCtx *svc.ServiceContext) *ApiPlanServiceServer {
	return &ApiPlanServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateApiPlan 创建API计划
func (s *ApiPlanServiceServer) CreateApiPlan(ctx context.Context, in *pb.CreateApiPlanReq) (*pb.CreateApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewCreateApiPlanLogic(ctx, s.svcCtx)

	return l.CreateApiPlan(in)
}

// RemoveApiPlan 删除API计划
func (s *ApiPlanServiceServer) RemoveApiPlan(ctx context.Context, in *pb.RemoveApiPlanReq) (*pb.RemoveApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewRemoveApiPlanLogic(ctx, s.svcCtx)

	return l.RemoveApiPlan(in)
}

// ModifyApiPlan 编辑API计划
func (s *ApiPlanServiceServer) ModifyApiPlan(ctx context.Context, in *pb.ModifyApiPlanReq) (*pb.ModifyApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewModifyApiPlanLogic(ctx, s.svcCtx)

	return l.ModifyApiPlan(in)
}

// SearchApiPlan 搜索API计划
func (s *ApiPlanServiceServer) SearchApiPlan(ctx context.Context, in *pb.SearchApiPlanReq) (*pb.SearchApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewSearchApiPlanLogic(ctx, s.svcCtx)

	return l.SearchApiPlan(in)
}

// ViewApiPlan 查看API计划
func (s *ApiPlanServiceServer) ViewApiPlan(ctx context.Context, in *pb.ViewApiPlanReq) (*pb.ViewApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewViewApiPlanLogic(ctx, s.svcCtx)

	return l.ViewApiPlan(in)
}

// SearchSuiteInApiPlan 搜索API计划中的集合（包括：场景集合和接口集合）
func (s *ApiPlanServiceServer) SearchSuiteInApiPlan(ctx context.Context, in *pb.SearchSuiteInApiPlanReq) (*pb.SearchSuiteInApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewSearchSuiteInApiPlanLogic(ctx, s.svcCtx)

	return l.SearchSuiteInApiPlan(in)
}

// SearchSuiteNotInApiPlan 搜索不在指定的API计划中的集合（包括：场景集合和接口集合）
func (s *ApiPlanServiceServer) SearchSuiteNotInApiPlan(ctx context.Context, in *pb.SearchSuiteNotInApiPlanReq) (*pb.SearchSuiteNotInApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewSearchSuiteNotInApiPlanLogic(ctx, s.svcCtx)

	return l.SearchSuiteNotInApiPlan(in)
}

// AddSuiteToApiPlan 添加集合到API计划中
func (s *ApiPlanServiceServer) AddSuiteToApiPlan(ctx context.Context, in *pb.AddSuiteToApiPlanReq) (*pb.AddSuiteToApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewAddSuiteToApiPlanLogic(ctx, s.svcCtx)

	return l.AddSuiteToApiPlan(in)
}

// RemoveSuiteFromApiPlan 移除API计划中的集合
func (s *ApiPlanServiceServer) RemoveSuiteFromApiPlan(ctx context.Context, in *pb.RemoveSuiteFromApiPlanReq) (*pb.RemoveSuiteFromApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewRemoveSuiteFromApiPlanLogic(ctx, s.svcCtx)

	return l.RemoveSuiteFromApiPlan(in)
}

// SearchCaseInApiPlan 搜索API计划中指定集合中的用例
func (s *ApiPlanServiceServer) SearchCaseInApiPlan(ctx context.Context, in *pb.SearchCaseInApiPlanReq) (*pb.SearchCaseInApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewSearchCaseInApiPlanLogic(ctx, s.svcCtx)

	return l.SearchCaseInApiPlan(in)
}

// ModifyApiPlanReferenceState 修改API计划执行数据的引用状态（包括：集合以及集合中的用例）
func (s *ApiPlanServiceServer) ModifyApiPlanReferenceState(ctx context.Context, in *pb.ModifyApiPlanReferenceStateReq) (*pb.ModifyApiPlanReferenceStateResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewModifyApiPlanReferenceStateLogic(ctx, s.svcCtx)

	return l.ModifyApiPlanReferenceState(in)
}

// AdvancedSearchSuiteNotInApiPlan 高级搜索接口文档
func (s *ApiPlanServiceServer) AdvancedSearchSuiteNotInApiPlan(ctx context.Context, in *pb.AdvancedSearchSuiteNotInApiPlanReq) (*pb.AdvancedSearchSuiteNotInApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewAdvancedSearchSuiteNotInApiPlanLogic(ctx, s.svcCtx)

	return l.AdvancedSearchSuiteNotInApiPlan(in)
}

// SearchLikeApiPlan 搜索API计划
func (s *ApiPlanServiceServer) SearchLikeApiPlan(ctx context.Context, in *pb.SearchApiPlanReq) (*pb.SearchApiPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := apiplanservicelogic.NewSearchLikeApiPlanLogic(ctx, s.svcCtx)

	return l.SearchLikeApiPlan(in)
}
