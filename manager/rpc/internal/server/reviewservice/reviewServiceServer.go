// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	reviewservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/reviewservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ReviewServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedReviewServiceServer
}

func NewReviewServiceServer(svcCtx *svc.ServiceContext) *ReviewServiceServer {
	return &ReviewServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateReviewRecord 申请审核
func (s *ReviewServiceServer) CreateReviewRecord(ctx context.Context, in *pb.CreateReviewRecordReq) (*pb.CreateReviewRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reviewservicelogic.NewCreateReviewRecordLogic(ctx, s.svcCtx)

	return l.CreateReviewRecord(in)
}

// ModifyReviewRecord 编辑审核
func (s *ReviewServiceServer) ModifyReviewRecord(ctx context.Context, in *pb.ModifyReviewRecordReq) (*pb.ModifyReviewRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reviewservicelogic.NewModifyReviewRecordLogic(ctx, s.svcCtx)

	return l.ModifyReviewRecord(in)
}

// RevokeReviewRecord 撤回审核
func (s *ReviewServiceServer) RevokeReviewRecord(ctx context.Context, in *pb.RevokeReviewRecordReq) (*pb.RevokeReviewRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reviewservicelogic.NewRevokeReviewRecordLogic(ctx, s.svcCtx)

	return l.RevokeReviewRecord(in)
}

// ApproveReviewRecord 审批审核
func (s *ReviewServiceServer) ApproveReviewRecord(ctx context.Context, in *pb.ApproveReviewRecordReq) (*pb.ApproveReviewRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reviewservicelogic.NewApproveReviewRecordLogic(ctx, s.svcCtx)

	return l.ApproveReviewRecord(in)
}

// SearchReviewRecord 搜索审核记录（包括申请记录）
func (s *ReviewServiceServer) SearchReviewRecord(ctx context.Context, in *pb.SearchReviewRecordReq) (*pb.SearchReviewRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reviewservicelogic.NewSearchReviewRecordLogic(ctx, s.svcCtx)

	return l.SearchReviewRecord(in)
}
