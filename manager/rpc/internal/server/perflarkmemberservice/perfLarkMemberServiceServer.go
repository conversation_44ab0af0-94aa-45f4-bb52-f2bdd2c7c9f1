// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perflarkmemberservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perflarkmemberservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfLarkMemberServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfLarkMemberServiceServer
}

func NewPerfLarkMemberServiceServer(svcCtx *svc.ServiceContext) *PerfLarkMemberServiceServer {
	return &PerfLarkMemberServiceServer{
		svcCtx: svcCtx,
	}
}

// CreatePerfLarkMember 添加飞书自动拉群成员
func (s *PerfLarkMemberServiceServer) CreatePerfLarkMember(ctx context.Context, in *pb.CreatePerfLarkMemberReq) (*pb.CreatePerfLarkMemberResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perflarkmemberservicelogic.NewCreatePerfLarkMemberLogic(ctx, s.svcCtx)

	return l.CreatePerfLarkMember(in)
}

// RemovePerfLarkMember 删除飞书自动拉群成员
func (s *PerfLarkMemberServiceServer) RemovePerfLarkMember(ctx context.Context, in *pb.RemovePerfLarkMemberReq) (*pb.RemovePerfLarkMemberResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perflarkmemberservicelogic.NewRemovePerfLarkMemberLogic(ctx, s.svcCtx)

	return l.RemovePerfLarkMember(in)
}

// SearchPerfLarkMember 搜索飞书自动拉群成员
func (s *PerfLarkMemberServiceServer) SearchPerfLarkMember(ctx context.Context, in *pb.SearchPerfLarkMemberReq) (*pb.SearchPerfLarkMemberResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perflarkmemberservicelogic.NewSearchPerfLarkMemberLogic(ctx, s.svcCtx)

	return l.SearchPerfLarkMember(in)
}
