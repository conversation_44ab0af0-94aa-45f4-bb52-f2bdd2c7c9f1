// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	gitconfigurationservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/gitconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GitConfigurationServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedGitConfigurationServiceServer
}

func NewGitConfigurationServiceServer(svcCtx *svc.ServiceContext) *GitConfigurationServiceServer {
	return &GitConfigurationServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateGitConfiguration 创建Git配置
func (s *GitConfigurationServiceServer) CreateGitConfiguration(ctx context.Context, in *pb.CreateGitConfigurationReq) (*pb.CreateGitConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := gitconfigurationservicelogic.NewCreateGitConfigurationLogic(ctx, s.svcCtx)

	return l.CreateGitConfiguration(in)
}

// RemoveGitConfiguration 删除Git配置
func (s *GitConfigurationServiceServer) RemoveGitConfiguration(ctx context.Context, in *pb.RemoveGitConfigurationReq) (*pb.RemoveGitConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := gitconfigurationservicelogic.NewRemoveGitConfigurationLogic(ctx, s.svcCtx)

	return l.RemoveGitConfiguration(in)
}

// ModifyGitConfiguration 编辑Git配置
func (s *GitConfigurationServiceServer) ModifyGitConfiguration(ctx context.Context, in *pb.ModifyGitConfigurationReq) (*pb.ModifyGitConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := gitconfigurationservicelogic.NewModifyGitConfigurationLogic(ctx, s.svcCtx)

	return l.ModifyGitConfiguration(in)
}

// SearchGitConfiguration 搜索Git配置
func (s *GitConfigurationServiceServer) SearchGitConfiguration(ctx context.Context, in *pb.SearchGitConfigurationReq) (*pb.SearchGitConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := gitconfigurationservicelogic.NewSearchGitConfigurationLogic(ctx, s.svcCtx)

	return l.SearchGitConfiguration(in)
}

// ViewGitConfiguration 查看Git配置
func (s *GitConfigurationServiceServer) ViewGitConfiguration(ctx context.Context, in *pb.ViewGitConfigurationReq) (*pb.ViewGitConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := gitconfigurationservicelogic.NewViewGitConfigurationLogic(ctx, s.svcCtx)

	return l.ViewGitConfiguration(in)
}

// TestGitConfiguration 测试Git配置
func (s *GitConfigurationServiceServer) TestGitConfiguration(ctx context.Context, in *pb.TestGitConfigurationReq) (*pb.TestGitConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := gitconfigurationservicelogic.NewTestGitConfigurationLogic(ctx, s.svcCtx)

	return l.TestGitConfiguration(in)
}

// SyncGitConfiguration 通过手动同步Git配置对应的Git项目测试数据
func (s *GitConfigurationServiceServer) SyncGitConfiguration(ctx context.Context, in *pb.SyncGitConfigurationReq) (*pb.SyncGitConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := gitconfigurationservicelogic.NewSyncGitConfigurationLogic(ctx, s.svcCtx)

	return l.SyncGitConfiguration(in)
}

// SyncGitConfigurationByWebhook 通过Webhook同步Git配置对应的Git项目测试数据
func (s *GitConfigurationServiceServer) SyncGitConfigurationByWebhook(ctx context.Context, in *pb.SyncGitConfigurationByWebhookReq) (*pb.SyncGitConfigurationByWebhookResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := gitconfigurationservicelogic.NewSyncGitConfigurationByWebhookLogic(ctx, s.svcCtx)

	return l.SyncGitConfigurationByWebhook(in)
}
