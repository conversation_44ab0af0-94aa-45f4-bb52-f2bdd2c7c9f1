package uiplanservicelogic

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AddCaseToUIPlanLogic struct {
	*BaseLogic
}

func NewAddCaseToUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddCaseToUIPlanLogic {
	return &AddCaseToUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// AddCaseToUIPlan 添加用例到UI计划中
func (l *AddCaseToUIPlanLogic) AddCaseToUIPlan(in *pb.AddCaseToUIPlanReq) (out *pb.AddCaseToUIPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	plan, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	if len(in.GetCasePaths()) == 0 {
		l.Warn("the number of UI cases to be added is 0")
		return &pb.AddCaseToUIPlanResp{}, nil
	}

	adds := set.NewHashset(uint64(len(in.GetCasePaths())), generic.Equals[string], generic.HashString)
	for _, path := range in.GetCasePaths() {
		adds.Put(path)
	}

	// all cases in git project
	cases, err := l.getAllCases(plan.ProjectId, plan.GitConfigId)
	if err != nil {
		return nil, err
	} else if cases.Size() == 0 {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"not found any cases can be add to the ui plan, project_id: %s, git_config_id: %s",
			plan.ProjectId, plan.GitConfigId,
		)
	}

	// all cases in ui plan
	existing, err := l.getAllCasesInUIPlan(plan.ProjectId, plan.PlanId)
	if err != nil {
		return nil, err
	}

	// the cases that are in `adds` but not in `existing`
	adds = adds.Difference(existing)
	// the cases that are both in `adds` and `cases`
	adds = adds.Intersection(cases)
	if adds.Size() == 0 {
		l.Warnf(
			"the actual number of UI cases to be added is 0, project_id: %s, plan_id: %s, git_config_id: %s",
			plan.ProjectId, plan.PlanId, plan.GitConfigId,
		)
		return &pb.AddCaseToUIPlanResp{}, nil
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockUiPlanProjectIdPlanIdPrefix, in.GetProjectId(), in.GetPlanId())
	fn := func() error {
		stmt, _, err := l.svcCtx.UiPlanReferenceModel.InsertBuilder(&model.UiPlanReferenceRelationship{}).ToSql()
		if err != nil {
			return err
		}

		inserter, err := sqlx.NewBulkInserter(l.svcCtx.DB, stmt)
		if err != nil {
			return err
		}

		for _, add := range adds.Keys() {
			if add == "" {
				continue
			}

			data := &model.UiPlanReferenceRelationship{
				ProjectId:   plan.ProjectId,
				GitConfigId: plan.GitConfigId,
				Path:        add,
				PlanId:      plan.PlanId,
				CreatedBy:   l.currentUser.Account,
				UpdatedBy:   l.currentUser.Account,
			}
			_, args, err := l.svcCtx.UiPlanReferenceModel.InsertBuilder(data).ToSql()
			if err != nil {
				l.Warnf(
					"failed to build the query into a SQL string and bound args, data: %s, error: %+v",
					jsonx.MarshalToStringIgnoreError(data), err,
				)
				continue
			}

			if err = inserter.Insert(args...); err != nil {
				continue
			}
		}

		inserter.Flush()

		return nil
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &pb.AddCaseToUIPlanResp{}, nil
}

func (l *AddCaseToUIPlanLogic) getAllCases(projectID, gitConfigID string) (*set.Set[string], error) {
	nodes, err := l.BaseLogic.getAllGitProjectTreeNodes(projectID, gitConfigID)
	if err != nil {
		return nil, err
	}

	s := set.NewHashset(uint64(len(nodes)), generic.Equals[string], generic.HashString)
	for _, node := range nodes {
		if node.Type != string(common.ConstNodeTypeFunction) {
			continue
		}

		s.Put(node.Path)
	}

	return &s, nil
}

func (l *AddCaseToUIPlanLogic) getAllCasesInUIPlan(projectID, planID string) (*set.Set[string], error) {
	rrs, err := l.BaseLogic.getAllCasesInUIPlan(projectID, planID)
	if err != nil {
		return nil, err
	}

	s := set.NewHashset(uint64(len(rrs)), generic.Equals[string], generic.HashString)
	for _, rr := range rrs {
		s.Put(rr.Path)
	}

	return &s, nil
}
