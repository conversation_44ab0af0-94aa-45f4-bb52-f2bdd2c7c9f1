package fsm

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

var (
	_ ResourceMetadata = (*model.ComponentGroup)(nil)
	_ ResourceMetadata = (*model.ApiCase)(nil)
	_ ResourceMetadata = (*model.InterfaceCase)(nil)
)

type Resource interface {
	ResourceMetadata
	ResourceUpdater
	ResourceNotifier
}

type ResourceMetadata interface {
	ProjectID() string                             // 项目ID
	ResourceBranch() string                        // 资源所属分支
	ResourceParentType() common.ReviewResourceType // 资源父类型
	ResourceType() common.ReviewResourceType       // 资源类型
	ResourceID() string                            // 资源ID
	ResourceName() string                          // 资源名称
	ResourceState() common.ResourceState           // 资源状态
	Maintainer() string                            // 资源维护者
}

type ResourceUpdater interface {
	GenLockKey() string // 生成分布式锁的Key

	SetResourceState(string)     // 设置`state`
	SetResourceUpdatedBy(string) // 设置`updated_by`

	Refresh(context.Context, *svc.ServiceContext) error              // 刷新资源
	Update(context.Context, *svc.ServiceContext, sqlx.Session) error // 更新资源
}

type ResourceNotifier interface {
	ReviewEvent() common.ReviewResourceEvent // 审核资源事件
	Receivers() []string                     // 通知接收者
	ReviewResult() common.ReviewStatus       // 审核结果
	ReviewRemark() string                    // 审核备注
}

type NotificationItem struct {
	ProjectID            string                      `json:"project_id"`              // 项目ID
	ResourceBranch       string                      `json:"resource_branch"`         // 资源所属分支
	ResourceParentType   common.ReviewResourceType   `json:"resource_parent_type"`    // 资源父类型
	ResourceParentTypeZH common.ReviewResourceTypeZH `json:"resource_parent_type_zh"` // 资源父类型的中文名称
	ResourceType         common.ReviewResourceType   `json:"resource_type"`           // 资源类型
	ResourceTypeZH       common.ReviewResourceTypeZH `json:"resource_type_zh"`        // 资源类型的中文名称
	ResourceID           string                      `json:"resource_id"`             // 资源ID
	ResourceName         string                      `json:"resource_name"`           // 资源名称
	ReviewResult         common.ReviewStatus         `json:"review_result"`           // 审核结果
	ReviewResultZH       common.ReviewStatusZH       `json:"review_result_zh"`        // 审核结果的中文名称
	ReviewRemark         string                      `json:"review_remark"`           // 审核备注
	NotifiedBy           string                      `json:"notified_by"`             // 通知者
	NotifiedAt           time.Time                   `json:"notified_at"`             // 通知时间
	RedirectURL          string                      `json:"redirect_url"`            // 跳转地址
}
