package projectservicelogic

import (
	"context"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	interfacedefinitionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/interfacedefinitionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createCategoryLogic        *categoryservicelogic.CreateCategoryLogic
	createInterfaceSchemaLogic *interfacedefinitionservicelogic.CreateInterfaceSchemaLogic

	converters []commonutils.TypeConverter

	categoryIdRecorder map[string]string
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createCategoryLogic:        categoryservicelogic.NewCreateCategoryLogic(ctx, svcCtx),
		createInterfaceSchemaLogic: interfacedefinitionservicelogic.NewCreateInterfaceSchemaLogic(ctx, svcCtx),

		converters: []commonutils.TypeConverter{},

		categoryIdRecorder: make(map[string]string),
	}
}

func (l *BaseLogic) generateProjectId() (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenProjectId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.ProjectModel.FindOneByProjectId(l.ctx, id)
				if err == model.ErrNotFound || r == nil {
					return true
				}
				return false
			},
		),
	)
	projectId := g.Next()
	if projectId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate project id, please try it later",
			),
		)
	}

	return projectId, nil
}

type relation struct {
	Type         string
	CategoryType string
	RootType     string
	NodeType     string
	NodeId       string
	Name         string
	Children     []*relation
}

type createCategoryFn func(req categoryservicelogic.CreateCategoryInternalReq) (*model.Category, error)

func (l *BaseLogic) createProjectCategoryTree(context context.Context, session sqlx.Session, projectId string) error {
	fn := func(req categoryservicelogic.CreateCategoryInternalReq) (*model.Category, error) {
		return l.createCategoryLogic.CreateCategoryForInternal(context, session, req)
	}
	rs := []*relation{
		{
			// 接口文档
			Type:         common.ConstCategoryTreeTypeInterfaceDocument,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllDocument,
		},
		{
			// 接口数据模型
			Type:         common.ConstCategoryTreeTypeInterfaceSchema,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllSchema,
			Children: []*relation{
				{
					Type:         common.ConstCategoryTreeTypeInterfaceSchema,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootTDSService,
					Name:         common.ConstCategorySubRootTDSService,
					Children: []*relation{
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSCommonCallReqSchemaId,
							Name:         common.ConstTDSCommonCallReqSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSSearchClientSchemaId,
							Name:         common.ConstTDSSearchClientSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSFixDictSchemaId,
							Name:         common.ConstTDSFixDictSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSCommonRespSchemaId,
							Name:         common.ConstTDSCommonRespSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSErrorRespSchemaId,
							Name:         common.ConstTDSErrorRespSchemaName,
						},
					},
				},
				{
					Type:         common.ConstCategoryTreeTypeInterfaceSchema,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootTTMetaClient,
					Name:         common.ConstCategorySubRootTTMetaClient,
					Children: []*relation{
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTTMetaClient,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTTMetaCommonCallReqSchemaId,
							Name:         common.ConstTTMetaClientCommonCallReqSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTTMetaClientSearchClientSchemaId,
							Name:         common.ConstTTMetaClientSearchClientSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTTMetaCommonRespSchemaId,
							Name:         common.ConstTTMetaClientCommonRespSchemaName,
						},
					},
				},
				{
					Type:         common.ConstCategoryTreeTypeInterfaceSchema,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootApiProxy,
					Name:         common.ConstCategorySubRootApiProxy,
					Children: []*relation{
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootApiProxy,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstApiProxyCommonApiCallReqSchemaId,
							Name:         common.ConstApiProxyCommonApiCallReqSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootApiProxy,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstApiProxyCommonRespSchemaId,
							Name:         common.ConstApiProxyCommonRespSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootApiProxy,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstApiProxyCommonRespDataSchemaId,
							Name:         common.ConstApiProxyCommonRespDataSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootApiProxy,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstApiProxyCommonRespDataCallRespSchemaId,
							Name:         common.ConstApiProxyCommonRespDataCallRespSchemaName,
						},
					},
				},
			},
		},
		{
			// 组件组
			Type:         common.ConstCategoryTreeTypeComponentGroup,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllComponentGroup,
			Children: []*relation{
				{
					Type:         common.ConstCategoryTreeTypeComponentGroup,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootBusinessComponent,
					Name:         common.ConstCategorySubRootBusinessComponent,
				},
				{
					Type:         common.ConstCategoryTreeTypeComponentGroup,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootSetupTeardownComponent,
					Name:         common.ConstCategorySubRootSetupTeardownComponent,
				},
			},
		},
		{
			// API测试用例
			Type:         common.ConstCategoryTreeTypeApiCase,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllApiCase,
		},
		{
			// API测试集合
			Type:         common.ConstCategoryTreeTypeApiSuite,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllApiSuite,
		},
		{
			// API测试计划
			Type:         common.ConstCategoryTreeTypeApiPlan,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllApiPlan,
		},
		{
			// UI测试计划
			Type:         common.ConstCategoryTreeTypeUiPlan,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllUiPlan,
		},
		{
			// 压力测试用例
			Type:         common.ConstCategoryTreeTypePerfCase,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllPerfScenario,
		},
		{
			// 压力测试计划
			Type:         common.ConstCategoryTreeTypePerfPlan,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllPerfPlan,
		},
		{
			// 稳定性测试计划
			Type:         common.ConstCategoryTreeTypeStabilityPlan,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllStabilityPlan,
		},
	}

	return l.createProjectCategoryTreeByRelation(fn, projectId, "", rs)
}

func (l *BaseLogic) createProjectCategoryTreeByRelation(
	fn createCategoryFn, projectId, parentId string, relations []*relation,
) error {
	for i, r := range relations {
		req := categoryservicelogic.CreateCategoryInternalReq{
			CreateCategoryReq: &pb.CreateCategoryReq{
				ProjectId:   projectId,
				Type:        r.Type,
				Name:        r.Name,
				Description: r.Name,
				ParentId:    parentId,
			},
			CategoryType:   r.CategoryType,
			RootType:       r.RootType,
			NodeType:       r.NodeType,
			NodeId:         r.NodeId,
			Builtin:        true,
			IsInternalCall: true,
		}

		if parentId != "" {
			req.Index = int64(i + 1)
		} else {
			req.Index = 1
		}

		category, err := fn(req)
		if err != nil {
			return err
		}

		// 缓存本次创建项目涉及的`category_id`
		l.categoryIdRecorder[r.Name] = category.CategoryId

		if len(r.Children) > 0 {
			if err = l.createProjectCategoryTreeByRelation(fn, projectId, category.CategoryId, r.Children); err != nil {
				return err
			}
		}
	}

	return nil
}

type schema struct {
	Id   string
	Name string
}

func (l *BaseLogic) createProjectBuiltinSchema(context context.Context, session sqlx.Session, projectId string) error {
	ss := []schema{
		{
			Id:   common.ConstTDSCommonCallReqSchemaId,
			Name: common.ConstTDSCommonCallReqSchemaName,
		},
		{
			Id:   common.ConstTDSSearchClientSchemaId,
			Name: common.ConstTDSSearchClientSchemaName,
		},
		{
			Id:   common.ConstTDSFixDictSchemaId,
			Name: common.ConstTDSFixDictSchemaName,
		},
		{
			Id:   common.ConstTDSCommonRespSchemaId,
			Name: common.ConstTDSCommonRespSchemaName,
		},
		{
			Id:   common.ConstTDSErrorRespSchemaId,
			Name: common.ConstTDSErrorRespSchemaName,
		},
	}

	for _, s := range ss {
		categoryId, ok := l.categoryIdRecorder[s.Name]
		if !ok {
			return errors.WithStack(
				errorx.Err(
					codes.BuiltinOperationFailure,
					fmt.Sprintf("cannot find the category id of the builtin interface schema[%s]", s.Name),
				),
			)
		}

		data, err := interfacedefinitionservicelogic.NewSchemaByName(s.Name)
		if err != nil {
			return err
		}

		if _, err = l.createInterfaceSchemaLogic.CreateInterfaceSchemaForInternal(
			context, session, interfacedefinitionservicelogic.CreateInterfaceSchemaInternalReq{
				CreateInterfaceSchemaReq: &pb.CreateInterfaceSchemaReq{
					ProjectId:  projectId,
					CategoryId: categoryId,
					FullName:   common.ConstTDSServiceName + "." + s.Name,
					Name:       s.Name,
					Data:       data,
				},
				SchemaId: s.Id,
				Mode:     common.ConstInterfaceDefinitionCreateModeBuiltin,
			},
		); err != nil {
			interfacedefinitionservicelogic.ReleaseSchema(data)

			return err
		}

		interfacedefinitionservicelogic.ReleaseSchema(data)
	}

	// ttMeta
	tmss := []schema{
		{
			Id:   common.ConstTTMetaCommonCallReqSchemaId,
			Name: common.ConstTTMetaClientCommonCallReqSchemaName,
		},
		{
			Id:   common.ConstTTMetaClientSearchClientSchemaId,
			Name: common.ConstTTMetaClientSearchClientSchemaName,
		},
		{
			Id:   common.ConstTTMetaCommonRespSchemaId,
			Name: common.ConstTTMetaClientCommonRespSchemaName,
		},
	}

	for _, s := range tmss {
		categoryId, ok := l.categoryIdRecorder[s.Name]
		if !ok {
			return errors.WithStack(
				errorx.Err(
					codes.BuiltinOperationFailure,
					fmt.Sprintf("cannot find the category id of the builtin interface schema[%s]", s.Name),
				),
			)
		}

		data, err := interfacedefinitionservicelogic.NewSchemaByName(s.Name)
		if err != nil {
			return err
		}

		if _, err = l.createInterfaceSchemaLogic.CreateInterfaceSchemaForInternal(
			context, session, interfacedefinitionservicelogic.CreateInterfaceSchemaInternalReq{
				CreateInterfaceSchemaReq: &pb.CreateInterfaceSchemaReq{
					ProjectId:  projectId,
					CategoryId: categoryId,
					FullName:   common.ConstTTMetaServiceName + "." + s.Name,
					Name:       s.Name,
					Data:       data,
				},
				SchemaId: s.Id,
				Mode:     common.ConstInterfaceDefinitionCreateModeBuiltin,
			},
		); err != nil {
			interfacedefinitionservicelogic.ReleaseSchema(data)

			return err
		}

		interfacedefinitionservicelogic.ReleaseSchema(data)
	}

	// api-proxy
	apss := []schema{
		{
			Id:   common.ConstApiProxyCommonApiCallReqSchemaId,
			Name: common.ConstApiProxyCommonApiCallReqSchemaName,
		},
		{
			Id:   common.ConstApiProxyCommonRespSchemaId,
			Name: common.ConstApiProxyCommonRespSchemaName,
		},
		{
			Id:   common.ConstApiProxyCommonRespDataSchemaId,
			Name: common.ConstApiProxyCommonRespDataSchemaName,
		},
		{
			Id:   common.ConstApiProxyCommonRespDataCallRespSchemaId,
			Name: common.ConstApiProxyCommonRespDataCallRespSchemaName,
		},
	}

	for _, s := range apss {
		categoryId, ok := l.categoryIdRecorder[s.Name]
		if !ok {
			return errors.WithStack(
				errorx.Err(
					codes.BuiltinOperationFailure,
					fmt.Sprintf("cannot find the category id of the builtin interface schema[%s]", s.Name),
				),
			)
		}

		data, err := interfacedefinitionservicelogic.NewSchemaByName(s.Name)
		if err != nil {
			return err
		}

		if _, err = l.createInterfaceSchemaLogic.CreateInterfaceSchemaForInternal(
			context, session, interfacedefinitionservicelogic.CreateInterfaceSchemaInternalReq{
				CreateInterfaceSchemaReq: &pb.CreateInterfaceSchemaReq{
					ProjectId:  projectId,
					CategoryId: categoryId,
					FullName:   common.ConstApiProxyServiceName + "." + s.Name,
					Name:       s.Name,
					Data:       data,
				},
				SchemaId: s.Id,
				Mode:     common.ConstInterfaceDefinitionCreateModeBuiltin,
			},
		); err != nil {
			interfacedefinitionservicelogic.ReleaseSchema(data)

			return err
		}

		interfacedefinitionservicelogic.ReleaseSchema(data)
	}

	return nil
}

func (l *BaseLogic) generateSearchSqlBuilder(
	req *pb.SearchProjectReq, showAll bool, projectIDs ...string,
) (sb, scb squirrel.SelectBuilder) {
	m := l.svcCtx.ProjectModel

	condition := req.GetCondition()
	if !showAll {
		if len(projectIDs) == 0 {
			// insert a project id which does not exist, indicates that the current user does not belong to any project
			projectIDs = append(projectIDs, "")
		}

		// condition: request condition + the projects belong to current user
		condition = &rpc.Condition{
			Single: nil,
			Group: &rpc.GroupCondition{
				Relationship: constants.AND,
				Conditions: []*rpc.Condition{
					{
						Single: &rpc.SingleCondition{
							Field:   "project_id",
							Compare: constants.DBIn,
							In:      projectIDs,
						},
					},
					condition,
				},
			},
		}
	}

	sb = sqlbuilder.SearchOptions(
		m.SelectBuilder(), sqlbuilder.WithCondition(m, condition), sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptions(m.SelectCountBuilder(), sqlbuilder.WithCondition(m, condition))

	return sb, scb
}
