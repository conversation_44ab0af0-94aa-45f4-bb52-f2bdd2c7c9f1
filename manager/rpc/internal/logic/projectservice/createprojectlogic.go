package projectservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	permissionpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateProjectLogic struct {
	*BaseLogic
}

func NewCreateProjectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateProjectLogic {
	return &CreateProjectLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateProject 创建项目
func (l *CreateProjectLogic) CreateProject(in *pb.CreateProjectReq) (out *pb.CreateProjectResp, err error) {
	projectId, err := l.generateProjectId()
	if err != nil {
		return nil, err
	}

	now := time.Now()
	project := &model.Project{
		ProjectId: projectId,
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		ReviewEnabled: cast.ToInt64(in.GetReviewEnabled()),
		CreatedBy:     l.currentUser.Account,
		UpdatedBy:     l.currentUser.Account,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	if err = l.svcCtx.ProjectModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err = l.svcCtx.ProjectModel.Insert(context, session, project); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.ProjectModel.Table(), jsonx.MarshalIgnoreError(project), err,
				)
			}

			// create builtin categories
			if err = l.createProjectCategoryTree(context, session, projectId); err != nil {
				return err
			}

			// create builtin schemas
			if err = l.createProjectBuiltinSchema(context, session, projectId); err != nil {
				return err
			}

			// create a domain of current project
			_, err = l.svcCtx.PermissionDomainRpc.CreateDomain(
				context, &permissionpb.CreateDomainReq{
					DomainSource: common.ConstDomainSourceProbe,
					DomainType:   constants.DomainType_Project,
					DomainId:     projectId,
				},
			)
			if err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	out = &pb.CreateProjectResp{Project: &pb.Project{}}
	if err = utils.Copy(out.Project, project, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy project to response, project: %s, error: %+v",
			jsonx.MarshalIgnoreError(project), err,
		)
	}

	return out, nil
}
