package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/emirpasic/gods/containers"
	dll "github.com/emirpasic/gods/lists/doublylinkedlist"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var (
	funcParameters         = ([]*pb.Parameter)(nil)
	funcReturns            = ([]*pb.Return)(nil)
	imports                = ([]*pb.Import)(nil)
	exports                = ([]*pb.Export)(nil)
	variables              = ([]*commonpb.GeneralConfigVar)(nil)
	accountConfig          = (*pb.AccountConfig)(nil)
	document               = (*pb.Document)(nil)
	schema                 = (*pb.Schema)(nil)
	inputParams            = ([]*pb.InputParameter)(nil)
	outputParams           = ([]*pb.OutputParameter)(nil)
	sqlNullString          = sql.NullString{}
	tags                   = ([]string)(nil)
	testArgs               = ([]string)(nil)
	reviewers              = ([]string)(nil)
	excludePaths           = ([]string)(nil)
	excludeFiles           = ([]string)(nil)
	keepalive              = (*commonpb.PerfKeepalive)(nil)
	statsOfAPIs            = ([]*pb.StatsOfApi)(nil)
	rateLimits             = ([]*commonpb.RateLimitV2)(nil)
	perfCaseSteps          = ([]*commonpb.PerfCaseStepV2)(nil)
	statsOfSteps           = ([]*pb.StatsOfStep)(nil)
	deviceIDs              = ([]string)(nil)
	stabilityCustomDevices = (*commonpb.StabilityCustomDevices)(nil)
	stabilityCustomScript  = (*commonpb.StabilityCustomScript)(nil)
)

func StringToFunctionType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.FunctionType_BUILTIN)
}

func StringToCodeLanguage() utils.TypeConverter {
	return utils.StringToPBEnum(pb.CodeLanguage_GOLANG)
}

func StringToParameters() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(funcParameters, nil)
}

func StringToReturns() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(funcReturns, nil)
}

func StringToParameterOrReturnType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.ParameterOrReturnType_STRING)
}

func StringToImports() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(imports, nil)
}

func StringToExports() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(exports, nil)
}

func StringToVariables() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(variables, nil)
}

func StringToAccountConfig() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(accountConfig, nil)
}

func StringToDocument() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(document, nil)
}

func StringToSchema() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(schema, nil)
}

func StringToInputParameters() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(inputParams, nil)
}

func StringToOutputParameters() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(outputParams, nil)
}

func SqlNullStringToTags() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: tags,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return []string{}, nil
			} else {
				var o []string

				err := utils.StringToAny(s.String, &o)

				return o, err
			}
		},
	}
}

func Int64ToCommonState() utils.TypeConverter {
	return utils.Int64ToPBEnum(pb.CommonState_CS_NULL)
}

func StringToResourceState() utils.TypeConverter {
	return utils.StringToPBEnum(pb.ResourceState_RS_NULL)
}

// Deprecated: Use `commonpb.StringToTriggerMode` instead.
func StringToTriggerMode() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.TriggerMode_NULL)
}

// Deprecated: Use `commonpb.StringToPurposeType` instead.
func StringToPurposeType() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.PurposeType_UNDEFINED)
}

// Deprecated: Use `commonpb.StringToPlatformType` instead.
func StringToOperateSystem() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.PlatformType_PT_NULL)
}

func SqlNullStringToTestArgs() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: testArgs,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return []string{}, nil
			} else {
				var o []string

				err := utils.StringToAny(s.String, &o)

				return o, err
			}
		},
	}
}

// Deprecated: Use `commonpb.StringToFailRetry` instead.
func StringToFailRetry() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.FailRetry_ZERO)
}

// Deprecated: Use `commonpb.StringToTestLanguage` instead.
func StringToTestLanguage() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.TestLanguage_TestLanguage_NULL)
}

// Deprecated: Use `commonpb.StringToTestFramework` instead.
func StringToTestFramework() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.TestFramework_TestFramework_NULL)
}

func StringToNotifyMode() utils.TypeConverter {
	return utils.StringToPBEnum(pb.NotifyMode_ALWAYS_NOTIFY)
}

func NotifyModeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.NotifyMode_ALWAYS_NOTIFY)
}

func StringToNotifyType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.NotifyType_LARK_GROUP)
}

func NotifyTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.NotifyType_LARK_GROUP)
}

func StringToSceneType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.SceneType_ST_NULL)
}

func SceneTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.SceneType_ST_NULL)
}

func StringToReviewResourceType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.ReviewResourceType_RRT_NULL)
}

func ReviewResourceTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.ReviewResourceType_RRT_NULL)
}

func StringToReviewers() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: utils.String,
		DstType: reviewers,
		Fn: func(src any) (any, error) {
			if s, ok := src.(string); !ok {
				return []string{}, nil
			} else {
				var o []string

				err := utils.StringToAny(s, &o)

				return o, err
			}
		},
	}
}

func SqlNullStringToExcludePaths() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: excludePaths,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return []string{}, nil
			} else {
				var o []string

				err := utils.StringToAny(s.String, &o)

				return o, err
			}
		},
	}
}

func SqlNullStringToExcludeFiles() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: excludeFiles,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return []string{}, nil
			} else {
				var o []string

				err := utils.StringToAny(s.String, &o)

				return o, err
			}
		},
	}
}

func StringToReviewStatus() utils.TypeConverter {
	return utils.StringToPBEnum(pb.ReviewStatus_REVIEW_STATUS_NULL)
}

func ReviewStatusToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.ReviewStatus_REVIEW_STATUS_NULL)
}

func StringToPerfKeepalive() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(keepalive, nil)
}

func StringToStatsOfAPIs() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(statsOfAPIs, nil)
}

func StringToRateLimits() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(rateLimits, nil)
}

func StringToPerfCaseSteps() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(perfCaseSteps, nil)
}

func StringToStatsOfSteps() utils.TypeConverter {
	return commonutils.StringToApiStructOrRpcMessage(statsOfSteps, nil)
}

func SqlNullStringToPerfCaseSteps() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: perfCaseSteps,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return []*commonpb.PerfCaseStepV2{}, nil
			} else {
				var o []*commonpb.PerfCaseStepV2

				err := utils.StringToAny(s.String, &o)

				return o, err
			}
		},
	}
}

func SqlNullStringToStabilityCustomDevices() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: stabilityCustomDevices,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return nil, nil
			} else {
				var o commonpb.StabilityCustomDevices

				err := utils.StringToAny(s.String, &o)

				return &o, err
			}
		},
	}
}

func SqlNullStringToStabilityCustomScript() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: stabilityCustomScript,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return nil, nil
			} else {
				var o commonpb.StabilityCustomScript

				err := utils.StringToAny(s.String, &o)

				return &o, err
			}
		},
	}
}

func SqlNullStringToDeviceIDs() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: deviceIDs,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return []string{}, nil
			} else {
				var o []string

				err := utils.StringToAny(s.String, &o)

				return o, err
			}
		},
	}
}

func GetCountFunctionByCategoryTreeType(
	ctx context.Context, svcCtx *svc.ServiceContext, tp string,
) (model.CountByCategoryIdFunc, error) {
	var countModel model.CountByCategoryIdModel

	switch tp {
	case common.ConstCategoryTreeTypeInterfaceDocument:
		countModel = svcCtx.InterfaceDocumentModel
	case common.ConstCategoryTreeTypeInterfaceSchema:
		countModel = svcCtx.InterfaceSchemaModel
	case common.ConstCategoryTreeTypeComponentGroup:
		countModel = svcCtx.ComponentGroupModel
	case common.ConstCategoryTreeTypeApiCase:
		countModel = svcCtx.ApiCaseModel
	case common.ConstCategoryTreeTypeApiSuite:
		countModel = svcCtx.ApiSuiteModel
	case common.ConstCategoryTreeTypeApiPlan:
		countModel = svcCtx.ApiPlanModel
	case common.ConstCategoryTreeTypeUiPlan:
		countModel = svcCtx.UiPlanModel
	case common.ConstCategoryTreeTypePerfCase:
		countModel = svcCtx.PerfCaseV2Model
	case common.ConstCategoryTreeTypePerfPlan:
		countModel = svcCtx.PerfPlanV2Model
	case common.ConstCategoryTreeTypeStabilityPlan:
		countModel = svcCtx.StabilityPlanModel
	default:
		return nil, errors.WithStack(
			errorx.Err(
				errorx.DoesNotSupport, fmt.Sprintf("the type of category tree[%s] doesn't support", tp),
			),
		)
	}

	if countModel == nil {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.DoesNotSupport, fmt.Sprintf("the type of category tree[%s] doesn't support", tp),
			),
		)
	}
	return model.GetCountByCategoryIdFunction(ctx, countModel, svcCtx.CategoryModel)
}

func ContainerToString(container containers.Container) string {
	values := make([]string, 0, container.Size())
	for _, value := range container.Values() {
		values = append(values, fmt.Sprintf("%v", value))
	}

	return "[" + strings.Join(values, ", ") + "]"
}

func CheckCircularReference(
	ctx context.Context, m model.ComponentGroupModel, projectId string, relations []*pb.Relation,
) error {
	stack := dll.New()
	return checkCircularReference(ctx, m, projectId, relations, stack)
}

func checkCircularReference(
	ctx context.Context, m model.ComponentGroupModel, projectId string, relations []*pb.Relation, stack *dll.List,
) error {
	for _, relation := range relations {
		referenceId := relation.GetReferenceId()
		children := relation.GetChildrenRelations()

		if referenceId != "" && stack.Contains(referenceId) {
			return errors.WithStack(
				errorx.Err(
					errorx.ProhibitedBehavior, fmt.Sprintf(
						"circular references are not allowed, stack: %s, reference_id: %s", ContainerToString(stack),
						referenceId,
					),
				),
			)
		} else if referenceId != "" {
			stack.Prepend(referenceId)

			group, err := m.FindLatestOneNoCache(ctx, projectId, referenceId)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find component group with project_id[%s] and component_group_id[%s], error: %+v",
					projectId, referenceId, err,
				)
			}

			var r pb.Relation
			if err = jsonx.UnmarshalFromString(group.ReferenceStructure, &r); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.SerializationError, err.Error()),
					"failed to unmarshal the component group[%s], error: %+v", group.Name, err,
				)
			}

			rs := []*pb.Relation{&r}

			if err = checkCircularReference(ctx, m, projectId, rs, stack); err != nil {
				return err
			}

			// this will not occur under normal circumstances
			if len(children) > 0 {
				for _, child := range children {
					if err = checkCircularReference(ctx, m, projectId, child, stack); err != nil {
						return err
					}
				}
			}

			stack.Remove(0)
		} else if len(children) > 0 {
			for _, child := range children {
				if err := checkCircularReference(ctx, m, projectId, child, stack); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

func UpdateReferenceImports(imports []*pb.Import, elementData map[string]any) {
	v, ok := elementData[common.ConstFieldJSONNameImports]
	if !ok {
		return
	}

	list, ok := v.([]any)
	if !ok {
		return
	}

	for _, i := range imports {
		found := false
		for _, l := range list {
			j, ok := l.(map[string]any)
			if !ok {
				continue
			}
			if i.GetName() == j[common.ConstFieldJSONNameName] {
				found = true

				if i.GetDescription() != j[common.ConstFieldJSONNameDescription] {
					j[common.ConstFieldJSONNameDescription] = i.Description
				}

				break
			}
		}
		if !found {
			//ipt := &pb.Import{
			//	Name:        i.GetName(),
			//	Source:      i.GetSource(),
			//	Manual:      i.GetManual(),
			//	Export:      &pb.VariableExport{},
			//	Environment: &pb.VariableEnvironment{},
			//	Function:    &pb.VariableFunction{},
			//	Description: i.GetDescription(),
			//}
			// [20240118] `protobuf`序列化时会把枚举字段转为数字（int64）
			// google.golang.org/protobuf@v1.31.1-0.20231027082548-f4a6c1f6e5c1/encoding/protojson/encode.go:331
			bs := protobuf.MarshalJSONIgnoreError(i)

			var m map[string]any
			// [20240118] `jsonx。Unmarshal`默认设置了`UseNumber`，会把数字转为`json.Number`，再序列化时会转为字符串
			// 不设置`UseNumber`的话，会把数字转为`float64`，再序列化时还会转为数字
			// go1.18.10/src/encoding/json/decode.go:843
			_ = json.Unmarshal(bs, &m)
			list = append(list, m)
		}
	}

	elementData[common.ConstFieldJSONNameImports] = list
}

func UpdateReferenceExports(exports []*pb.Export, elementData map[string]any) {
	v, ok := elementData[common.ConstFieldJSONNameExports]
	if !ok {
		return
	}

	list, ok := v.([]any)
	if !ok {
		return
	}

	for _, e := range exports {
		found := false
		for _, l := range list {
			j, ok := l.(map[string]any)
			if !ok {
				continue
			}
			if e.GetName() == j[common.ConstFieldJSONNameName] {
				found = true

				if e.GetDescription() != j[common.ConstFieldJSONNameDescription] {
					j[common.ConstFieldJSONNameDescription] = e.Description
				}

				break
			}
		}
		if !found {
			//ept := &pb.Export{
			//	Name:        e.Name,
			//	Export:      &pb.VariableExport{},
			//	Description: e.Description,
			//}
			// [20240118] `protobuf`序列化时会把枚举字段转为数字（int64）
			// google.golang.org/protobuf@v1.31.1-0.20231027082548-f4a6c1f6e5c1/encoding/protojson/encode.go:331
			bs := protobuf.MarshalJSONIgnoreError(e)

			var m map[string]any
			// [20240118] `jsonx。Unmarshal`默认设置了`UseNumber`，会把数字转为`json.Number`，再序列化时会转为字符串
			// 不设置`UseNumber`的话，会把数字转为`float64`，再序列化时还会转为数字
			// go1.18.10/src/encoding/json/decode.go:843
			_ = json.Unmarshal(bs, &m)
			list = append(list, m)
		}
	}

	elementData[common.ConstFieldJSONNameExports] = list
}

func ConvertAnyToValue(v any) *structpb.Value {
	val, _ := structpb.NewValue(v)
	return val
}

func ConvertValueToAny(v *structpb.Value) any {
	return v.AsInterface()
}

func ConvertAnyToValues(vs ...any) []*structpb.Value {
	o := make([]*structpb.Value, 0, len(vs))
	for _, v := range vs {
		o = append(o, ConvertAnyToValue(v))
	}

	return o
}

func ConvertValuesToAny(vs ...*structpb.Value) []any {
	o := make([]any, 0, len(vs))
	for _, v := range vs {
		o = append(o, ConvertValueToAny(v))
	}

	return o
}

func ConvertStringToResourceState(v string) pb.ResourceState {
	switch common.ResourceState(v) {
	case common.ConstResourceStateEnabled:
		return pb.ResourceState_RS_ENABLED
	case common.ConstResourceStateDisabled:
		return pb.ResourceState_RS_DISABLED
	case common.ConstResourceStateNew:
		return pb.ResourceState_RS_NEW
	case common.ConstResourceStateToBeImplemented:
		return pb.ResourceState_RS_TO_BE_IMPLEMENTED
	case common.ConstResourceStateToBeMaintained:
		return pb.ResourceState_RS_TO_BE_MAINTAINED
	case common.ConstResourceStatePendingReview:
		return pb.ResourceState_RS_PENDING_REVIEW
	case common.ConstResourceStatePublished:
		return pb.ResourceState_RS_PUBLISHED
	default:
		return pb.ResourceState_RS_NULL
	}
}

func ConvertStringToReviewResourceType(v string) pb.ReviewResourceType {
	switch common.ReviewResourceType(v) {
	case common.ConstReviewResourceTypeComponentGroup:
		return pb.ReviewResourceType_RRT_COMPONENT_GROUP
	case common.ConstReviewResourceTypeCase:
		return pb.ReviewResourceType_RRT_CASE
	case common.ConstReviewResourceTypeSetupComponent:
		return pb.ReviewResourceType_RRT_SETUP_COMPONENT
	case common.ConstReviewResourceTypeTeardownComponent:
		return pb.ReviewResourceType_RRT_TEARDOWN_COMPONENT
	case common.ConstReviewResourceTypeBusinessComponent:
		return pb.ReviewResourceType_RRT_BUSINESS_COMPONENT
	case common.ConstReviewResourceTypeAPICase:
		return pb.ReviewResourceType_RRT_API_CASE
	case common.ConstReviewResourceTypeInterfaceCase:
		return pb.ReviewResourceType_RRT_INTERFACE_CASE
	default:
		return pb.ReviewResourceType_RRT_NULL
	}
}

func ConvertStringToCommonState(v string) pb.CommonState {
	switch common.ResourceState(v) {
	case common.ConstResourceStateEnabled:
		return pb.CommonState_CS_ENABLE
	case common.ConstResourceStateDisabled:
		return pb.CommonState_CS_DISABLE
	case common.ConstResourceStatePublished:
		return pb.CommonState_CS_ENABLE
	default:
		return pb.CommonState_CS_DISABLE
	}
}
