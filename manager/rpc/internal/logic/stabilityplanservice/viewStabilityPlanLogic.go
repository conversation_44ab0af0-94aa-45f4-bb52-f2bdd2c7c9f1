package stabilityplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewStabilityPlanLogic struct {
	*BaseLogic
}

func NewViewStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewStabilityPlanLogic {
	return &ViewStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewStabilityPlan   查看稳定性测试计划
func (l *ViewStabilityPlanLogic) ViewStabilityPlan(in *pb.ViewStabilityPlanReq) (out *pb.ViewStabilityPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	stabilityPlan, err := model.CheckStabilityPlanByPlanId(l.ctx, l.svcCtx.StabilityPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	out = &pb.ViewStabilityPlanResp{Plan: &pb.StabilityPlan{}}
	if err = utils.Copy(out.Plan, stabilityPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy stability plan[%+v] to response, error: %+v",
			stabilityPlan, err,
		)
	}

	return out, nil
}
