package stabilityplanservicelogic_test

import (
	"database/sql"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	structpb "google.golang.org/protobuf/types/known/structpb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

func TestCreateStabilityPlan(t *testing.T) {
	req := &pb.CreateStabilityPlanReq{
		ProjectId:       "project_id:Kqllt5-9fA-I5UOdhjA5d",
		CategoryId:      "category_id:-Y0S0cIqp22qfgoblZE1C",
		Name:            "稳定性测试计划名称",
		Description:     "稳定性测试计划描述",
		State:           pb.CommonState_CS_ENABLE,
		Type:            commonpb.TriggerMode_INTERFACE,
		PriorityType:    commonpb.PriorityType_High,
		CronExpression:  "18 0 * * ?",
		Tags:            []string{"T1", "T2", "T3"},
		AccountConfigId: "account_config_id:5-c994WbKNQPldoylUjO2",
		DeviceType:      commonpb.DeviceType_REAL_PHONE,
		PlatformType:    commonpb.PlatformType_IOS,
		Devices: &commonpb.StabilityCustomDevices{
			Devices: &commonpb.StabilityCustomDevices_Udids{
				Udids: &structpb.ListValue{
					Values: []*structpb.Value{
						{
							Kind: &structpb.Value_StringValue{
								StringValue: "LZYTYLZT9HFI6DLN",
							},
						},
						{
							Kind: &structpb.Value_StringValue{
								StringValue: "PXUYD22628002359",
							},
						},
					},
				},
			},
		},
		PackageName:     "com.yiyou.ga",
		AppDownloadLink: "https://d-internal.ttyuyin.com/d/app/GAClient/download?code=Ni40NS4wQDQ4OTQ",
		Duration:        300,
		Activities: []string{
			"com.yiyou.ga.client.BlankActivity",
			"com.yiyou.ga.lab.internal.kol.KolInfoActivity",
			"com.yiyou.ga.lab.internal.LabHandleProgressActivity",
		},
		CustomScript: &commonpb.StabilityCustomScript{
			Script: &commonpb.StabilityCustomScript_Image{
				Image: "docker.io/yiyou/stability-test-script:v1.0.0",
			},
		},
		MaintainedBy: "T5210",
		// CreatedBy:    "T5210",
		// UpdatedBy:    "T5210",
		// CreateAt:     time.Now().Unix(),
		// UpdateAt:     time.Now().Unix(),
	}

	now := time.Now()
	in := &model.StabilityPlan{
		ProjectId:  req.GetProjectId(),
		CategoryId: req.GetCategoryId(),
		PlanId:     "stability_plan_id:-Y0S0cIqp22qfgoblZE15",
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		State:        int64(req.GetState()),
		Type:         protobuf.GetEnumStringOf(req.GetType()),
		PriorityType: int64(req.GetPriorityType()),
		CronExpression: sql.NullString{
			String: req.GetCronExpression(),
			Valid:  req.GetCronExpression() != "",
		},
		Tags: sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(req.GetTags()),
			Valid:  true,
		},
		AccountConfigId: sql.NullString{
			String: req.GetAccountConfigId(),
			Valid:  req.GetAccountConfigId() != "",
		},
		DeviceType:   int64(req.GetDeviceType()),
		PlatformType: int64(req.GetPlatformType()),
		Devices: sql.NullString{
			String: protobuf.MarshalJSONToStringIgnoreError(req.GetDevices()),
			Valid:  true,
		},
		PackageName:     req.GetPackageName(),
		AppDownloadLink: req.GetAppDownloadLink(),
		Duration:        int64(req.GetDuration()),
		Activities: sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(req.GetActivities()),
			Valid:  true,
		},
		CustomScript: sql.NullString{
			String: protobuf.MarshalJSONToStringIgnoreError(req.GetCustomScript()),
			Valid:  true,
		},
		MaintainedBy: sql.NullString{
			String: req.GetMaintainedBy(),
			Valid:  req.GetMaintainedBy() != "",
		},
		CreatedBy: "T1704",
		UpdatedBy: "T1704",
		DeletedBy: sql.NullString{
			String: req.GetMaintainedBy(),
			Valid:  req.GetMaintainedBy() != "",
		},
		CreatedAt: now,
		UpdatedAt: now,
		DeletedAt: sql.NullTime{
			Time:  now,
			Valid: true,
		},
	}
	t.Logf("%+v", in)

	converters := []utils.TypeConverter{
		commonpb.StringToTriggerMode(), // type
		// logic.SqlNullStringToTags(),                   // tags, activities
		logic.SqlNullStringToStabilityCustomDevices(), // devices
		logic.SqlNullStringToStabilityCustomScript(),  // custom_script
	}

	out := &pb.StabilityPlan{Tags: []string{"tag1", "tag2"}}
	if err := utils.Copy(out, in, converters...); err != nil {
		t.Fatalf(
			"failed to copy data to response, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}
	t.Logf("%+v", out)
}
