package stabilityplanservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchStabilityPlanLogic struct {
	*BaseLogic
}

func NewSearchStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchStabilityPlanLogic {
	return &SearchStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchStabilityPlan 搜索稳定性测试计划
func (l *SearchStabilityPlanLogic) SearchStabilityPlan(in *pb.SearchStabilityPlanReq) (out *pb.SearchStabilityPlanResp, err error) {
	out = &pb.SearchStabilityPlanResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeStabilityPlan, in.GetCategoryId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.StabilityPlanModel.GenerateSearchStabilityPlanSqlBuilder(
		model.SearchStabilityPlanReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID:  in.GetProjectId(),
				Condition:  in.GetCondition(),
				Pagination: in.GetPagination(),
				Sort:       rpc.ConvertSortFields(in.GetSort()),
			},
			CategoryID:    in.GetCategoryId(),
			DrillDown:     true,
			CategoryModel: l.svcCtx.CategoryModel,
		},
	)

	count, err := l.svcCtx.StabilityPlanModel.FindCountStabilityPlans(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to count stability plan with project_id[%s] and category_id[%s], error: %+v",
			in.GetProjectId(), in.GetCategoryId(), err,
		)
	}

	out.TotalCount = uint64(count)
	out.TotalPage = 1

	stabilityPlans, err := l.svcCtx.StabilityPlanModel.FindStabilityPlans(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find stability plan with project_id[%s] and category_id[%s], error: %+v",
			in.GetProjectId(), in.GetCategoryId(), err,
		)
	}

	out.Items = make([]*pb.StabilityPlan, 0, len(stabilityPlans))
	for _, stabilityPlan := range stabilityPlans {
		item := &pb.StabilityPlan{}
		if err = utils.Copy(item, stabilityPlan, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy stability plan[%+v] to outonse, error: %+v", stabilityPlan, err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = uint64(math.Ceil(float64(out.TotalCount) / float64(out.PageSize)))
	}

	return out, nil
}
