package stabilityplanservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveStabilityPlanLogic struct {
	*BaseLogic
}

func NewRemoveStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveStabilityPlanLogic {
	return &RemoveStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveStabilityPlan 删除稳定性测试计划
func (l *RemoveStabilityPlanLogic) RemoveStabilityPlan(in *pb.RemoveStabilityPlanReq) (out *pb.RemoveStabilityPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	planIDs := in.GetPlanIds()
	workers := len(planIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, planID := range planIDs {
				source <- planID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveStabilityPlanResp{}, nil
}

func (l *RemoveStabilityPlanLogic) remove(projectID, planID string) (err error) {
	// validate the plan_id in req
	if _, err = model.CheckStabilityPlanByPlanId(l.ctx, l.svcCtx.StabilityPlanModel, projectID, planID); err != nil {
		return err
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockStabilityPlanProjectIDPlanIDPrefix, projectID, planID)
	fn := func() error {
		return l.svcCtx.PerfPlanV2Model.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: tag_reference_relationship
				if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
					context, session, projectID, common.ConstReferenceTypeStabilityPlan, planID, "",
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove tag reference relationship, project_id: %s, reference_type: %s, reference_id: %s, error: %+v",
						projectID, common.ConstReferenceTypePerfPlan, planID, err,
					)
				}

				// Table: notify
				if _, err := l.svcCtx.NotifyModel.RemoveByPlanId(context, session, projectID, planID); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove plan notify, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				// Table: stability_plan
				if _, err := l.svcCtx.StabilityPlanModel.RemoveByPlanID(context, session, projectID, planID); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove stability plan, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
