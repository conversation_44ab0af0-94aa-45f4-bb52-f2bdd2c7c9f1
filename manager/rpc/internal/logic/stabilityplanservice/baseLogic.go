package stabilityplanservicelogic

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	beatcommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/common"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/beat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	notifyservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/notifyservice"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic    *tagservicelogic.CreateTagLogic
	createNotifyLogic *notifyservicelogic.CreatePlanNotifyLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic:    tagservicelogic.NewCreateTagLogic(ctx, svcCtx),
		createNotifyLogic: notifyservicelogic.NewCreatePlanNotifyLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToTriggerMode(),                // type
			logic.SqlNullStringToTags(),                   // tags, activities
			logic.SqlNullStringToStabilityCustomDevices(), // devices
			logic.SqlNullStringToStabilityCustomScript(),  // custom_script
		},
	}
}

func (l *BaseLogic) generatePlanID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenStabilityPlanID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.StabilityPlanModel.FindOneByProjectIdPlanId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	planID := g.Next()
	if planID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate perf plan id, please try it later",
		)
	}

	return planID, nil
}

func (l *BaseLogic) getUserInfoByAccount(account string) (*userpb.UserInfo, error) {
	resp, err := l.svcCtx.UserRpc.ViewUser(
		l.ctx, &userpb.GetUserReq{
			Account: account,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp.GetUserInfo(), nil
}

func (l *BaseLogic) updateNotifyItems(
	ctx context.Context, session sqlx.Session, stabilityPlan *model.StabilityPlan, chats []*commonpb.LarkChat,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	notifyItems := make([]*pb.CreateNotifyItem, 0, len(chats))
	for _, chat := range chats {
		notifyItems = append(
			notifyItems, &pb.CreateNotifyItem{
				ReceiverName: chat.GetName(),
				Receiver:     chat.GetChatId(),
			},
		)
	}

	return l.createNotifyLogic.CreatePlanNotifyForInternal(
		ctx, session, types.CreateOrUpdateNotifyReference{
			ProjectID:   stabilityPlan.ProjectId,
			PlanID:      stabilityPlan.PlanId,
			NotifyMode:  pb.NotifyMode_ALWAYS_NOTIFY,
			NotifyType:  pb.NotifyType_LARK_CHAT,
			NotifyItems: notifyItems,
		},
	)
}

func (l *BaseLogic) generatePeriodicTaskName(projectID, planID string) string {
	return fmt.Sprintf("%s:projectId:planId:%s:%s", constants.MQTaskTypeDispatcherPeriodicPlanTask, projectID, planID)
}

func (l *BaseLogic) createScheduleTask(req *pb.CreateStabilityPlanReq, stabilityPlan *model.StabilityPlan) error {
	if req.GetType() == commonpb.TriggerMode_SCHEDULE {
		if _, err := l.svcCtx.BeatRPC.CreateOrModifyPeriodicTask(
			l.ctx, &beatpb.CreateOrModifyPeriodicTaskReq{
				Name:  l.generatePeriodicTaskName(stabilityPlan.ProjectId, stabilityPlan.PlanId),
				Type:  constants.MQTaskTypeDispatcherPeriodicPlanTask,
				Queue: constants.MQNamePeriodicPlanTask,
				Spec:  stabilityPlan.CronExpression.String,
				Payload: protobuf.MarshalJSONToStringIgnoreError(
					&commonpb.PeriodicPlanTaskInfo{
						ProjectId:      stabilityPlan.ProjectId,
						PlanId:         stabilityPlan.PlanId,
						CronExpression: stabilityPlan.CronExpression.String,
						PlanType:       commonpb.PlanType_PERF,
					},
				),
				Version: beatcommon.V2,
			},
		); err != nil {
			l.Errorf(
				"failed to create schedule task of stability plan, project_id: %s, plan_id: %s, name: %s, error: %+v",
				stabilityPlan.ProjectId, stabilityPlan.PlanId, stabilityPlan.Name, err,
			)
			return err
		}
	}

	return nil
}

func (l *BaseLogic) updateScheduleTask(req *pb.ModifyStabilityPlanReq, origin *model.StabilityPlan) error {
	var (
		beatOperator beat.ScheduleTaskOperator

		projectID = req.GetProjectId()
		planID    = req.GetPlanId()
		planName  = req.GetName()
		toState   = req.GetState()
		toType    = req.GetType()
		toCron    = req.GetCronExpression()

		fromState = origin.State
		fromType  = origin.Type
		fromCron  = origin.CronExpression.String
	)

	// enable to disable
	if fromState == int64(qetconstants.EnableStatus) && toState == pb.CommonState_CS_DISABLE {
		// stop schedule task
		if fromType == commonpb.TriggerMode_SCHEDULE.String() {
			beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
		}
	}

	// disable to enable
	if fromState == int64(qetconstants.DisableStatus) && toState == pb.CommonState_CS_ENABLE {
		// start schedule task
		if toType == commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}
	}

	// enable to enable
	if fromState == int64(qetconstants.EnableStatus) && toState == pb.CommonState_CS_ENABLE {
		// schedule to other type
		if fromType == commonpb.TriggerMode_SCHEDULE.String() && toType != commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
		}

		// other type to schedule
		if fromType != commonpb.TriggerMode_SCHEDULE.String() && toType == commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}

		// schedule to schedule, and cron expression has been changed
		if fromType == commonpb.TriggerMode_SCHEDULE.String() && toType == commonpb.TriggerMode_SCHEDULE && fromCron != toCron {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}
	}

	switch beatOperator {
	case beat.ConstBeatScheduleTaskOperatorCreate:
		if _, err := l.svcCtx.BeatRPC.CreateOrModifyPeriodicTask(
			l.ctx, &beatpb.CreateOrModifyPeriodicTaskReq{
				Name:  l.generatePeriodicTaskName(projectID, planID),
				Type:  constants.MQTaskTypeDispatcherPeriodicPlanTask,
				Queue: constants.MQNamePeriodicPlanTask,
				Spec:  toCron,
				Payload: protobuf.MarshalJSONToStringIgnoreError(
					&commonpb.PeriodicPlanTaskInfo{
						ProjectId:      projectID,
						PlanId:         planID,
						CronExpression: toCron,
						PlanType:       commonpb.PlanType_STABILITY,
					},
				),
				Version: beatcommon.V2,
			},
		); err != nil {
			l.Errorf(
				"failed to %s schedule task of stability plan, project_id: %s, plan_id: %s, name: %s, error: %+v",
				strings.ToLower(beatOperator), projectID, planID, planName, err,
			)
			return err
		}
	case beat.ConstBeatScheduleTaskOperatorRemove:
		if _, err := l.svcCtx.BeatRPC.RemovePeriodicTask(
			l.ctx, &beatpb.RemovePeriodicTaskReq{
				Name: l.generatePeriodicTaskName(projectID, planID),
			},
		); err != nil {
			l.Errorf(
				"failed to %s schedule task of stability plan, project_id: %s, plan_id: %s, name: %s, error: %+v",
				strings.ToLower(beatOperator), projectID, planID, planName, err,
			)
			return err
		}
	}

	return nil
}
