package apisuiteservicelogic

import (
	"context"
	"fmt"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveApiCaseFromApiSuiteLogic struct {
	*BaseLogic
}

func NewRemoveApiCaseFromApiSuiteLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoveApiCaseFromApiSuiteLogic {
	return &RemoveApiCaseFromApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveApiCaseFromApiSuite 从API集合中移除API用例
func (l *RemoveApiCaseFromApiSuiteLogic) RemoveApiCaseFromApiSuite(in *pb.RemoveApiCaseFromApiSuiteReq) (
	resp *pb.RemoveApiCaseFromApiSuiteResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	apiSuite, err := model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId())
	if err != nil {
		return nil, err
	}

	rrs, err := l.svcCtx.ApiCaseReferenceModel.FindReferenceByReference(
		l.ctx, in.GetProjectId(), common.ConstReferenceTypeApiSuite, in.GetSuiteId(),
	)
	if err != nil {
		if err != model.ErrNotFound {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api case in api suite with project_id[%s] and suite_id[%s], error: %+v",
				in.GetProjectId(), in.GetSuiteId(), err,
			)
		}
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior,
				fmt.Sprintf("cannot remove api case from an empty api suite[%s]", apiSuite.Name),
			),
		)
	}

	recorder := make(map[string]*model.ApiCaseReferenceRelationship)
	allSet := hashset.New()
	for _, rr := range rrs {
		rr := rr
		allSet.Add(rr.CaseId)
		recorder[rr.CaseId] = rr
	}

	removeSet := hashset.New()
	for _, caseId := range in.GetCaseIds() {
		removeSet.Add(caseId)
	}

	diffSet := removeSet.Difference(allSet)
	if !diffSet.Empty() {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot remove the api cases %s which are not in the api suite[%s]",
					logic.ContainerToString(diffSet), apiSuite.Name,
				),
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiSuiteProjectIdSuiteIdPrefix, in.GetProjectId(), in.GetSuiteId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	workers := removeSet.Size()
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	_ = mr.MapReduceVoid[string, string](
		func(source chan<- string) {
			for _, v := range removeSet.Values() {
				source <- v.(string)
			}
		}, func(item string, writer mr.Writer[string], cancel func(error)) {
			if reference, ok := recorder[item]; !ok {
				err = multierror.Append(
					err, errors.Wrapf(errorx.ErrGrpcInternal, "cannot find the reference of api case[%s]", item),
				)
			} else {
				// remove api case reference of api suite
				if e := l.removeApiCaseReference(l.ctx, nil, reference); e != nil {
					err = multierror.Append(err, e)
				} else {
					writer.Write(item)
				}
			}
		}, func(pipe <-chan string, cancel func(error)) {
			req := createApiCaseReferenceInternalReq{
				ProjectId: in.GetProjectId(),
				SuiteId:   in.GetSuiteId(),
				CaseIds: make(
					[]string, 0, constants.ConstDefaultMakeSliceSize,
				), // 预置切片容量为`constants.ConstDefaultMakeSliceSize`，减少切片扩容的情况
			}

			for item := range pipe {
				req.CaseIds = append(req.CaseIds, item)
			}

			// remove api plan reference of api case which is removed from api suite
			if e := l.removeApiPlanReference(
				l.ctx, nil, createOrRemoveApiPlanReferenceInternalReq{
					createApiCaseReferenceInternalReq: req,
				},
			); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveApiCaseFromApiSuiteResp{}, err
}
