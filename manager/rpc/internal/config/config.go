package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/zrpc"

	consumerv1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/consumer"
	producerv1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/producer"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qettypes "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cmdb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type Config struct {
	zrpc.RpcServerConf

	DB    qettypes.DBConfig
	Cache cache.CacheConf

	Beat       zrpc.RpcClientConf
	DeviceHub  zrpc.RpcClientConf
	LarkProxy  zrpc.RpcClientConf
	Notifier   zrpc.RpcClientConf
	Permission zrpc.RpcClientConf
	Relation   zrpc.RpcClientConf
	Reporter   zrpc.RpcClientConf
	User       zrpc.RpcClientConf

	WorkerConsumerV2         consumerv2.Config
	WorkerConsumerV1         consumerv1.ConsumerConfig
	WorkerConsumerV1Producer producerv2.Config

	WorkerProducerV1   producerv1.ProducerConfig
	UIWorkerProducer   producerv2.Config
	ManagerConsumer    consumerv2.Config
	ManagerProducer    producerv2.Config
	LarkProxyConsumer  consumerv2.Config
	DispatcherProducer producerv2.Config

	FailedCaseCleaner               commontypes.ClearStrategy
	UpdateInterfaceDefinition       commontypes.ClearStrategy
	UpdateInterfaceCoverage         commontypes.ClearStrategy
	UpdateInterfaceMetricsReference commontypes.ClearStrategy
	UpdateAdvancedNotification      commontypes.ClearStrategy
	DeleteDisabledDevice            commontypes.ClearStrategy
	GitLab                          qettypes.GitLabConfig
	CMDB                            cmdb.Config
	PVCPath                         string
	StarProbeURL                    string
	AppInsight                      appInsight.Config
}

func (c Config) ListenOn() string {
	return c.RpcServerConf.ListenOn
}

func (c Config) LogConfig() logx.LogConf {
	return c.RpcServerConf.ServiceConf.Log
}
