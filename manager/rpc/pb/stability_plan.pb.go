// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/stability_plan.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StabilityPlan struct {
	state           protoimpl.MessageState     `protogen:"open.v1"`
	ProjectId       string                     `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                     // 项目ID
	CategoryId      string                     `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                                  // 分类ID
	PlanId          string                     `protobuf:"bytes,3,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                              // 计划ID
	Name            string                     `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                                               // 计划名称
	Description     string                     `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                                                 // 描述
	State           CommonState                `protobuf:"varint,13,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                   // 状态
	Type            pb.TriggerMode             `protobuf:"varint,14,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                      // 计划类型（手动、定时）
	PriorityType    pb.PriorityType            `protobuf:"varint,15,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"` // 优先级
	CronExpression  string                     `protobuf:"bytes,16,opt,name=cron_expression,json=cronExpression,proto3" json:"cron_expression,omitempty"`                     // 定时触发计划的Cron表达式
	Tags            []string                   `protobuf:"bytes,17,rep,name=tags,proto3" json:"tags,omitempty"`                                                               // 标签
	AccountConfigId string                     `protobuf:"bytes,31,opt,name=account_config_id,json=accountConfigId,proto3" json:"account_config_id,omitempty"`                // 池账号配置ID
	DeviceType      pb.DeviceType              `protobuf:"varint,32,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`         // 设备类型（真机、云手机）
	PlatformType    pb.PlatformType            `protobuf:"varint,33,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、iOS）
	Devices         *pb.StabilityCustomDevices `protobuf:"bytes,34,opt,name=devices,proto3" json:"devices,omitempty"`                                                         // 自定义设备
	PackageName     string                     `protobuf:"bytes,35,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                              // 包名，用于启动APP
	AppDownloadLink string                     `protobuf:"bytes,36,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                // APP下载地址
	Duration        uint32                     `protobuf:"varint,37,opt,name=duration,proto3" json:"duration,omitempty"`                                                      // 运行时长（单位：分钟）
	Activities      []string                   `protobuf:"bytes,38,rep,name=activities,proto3" json:"activities,omitempty"`                                                   // 指定的Activity列表
	CustomScript    *pb.StabilityCustomScript  `protobuf:"bytes,39,opt,name=custom_script,json=customScript,proto3" json:"custom_script,omitempty"`                           // 自定义脚本
	MaintainedBy    string                     `protobuf:"bytes,95,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                           // 维护者
	CreatedBy       string                     `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                    // 创建者
	UpdatedBy       string                     `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                    // 更新者
	CreatedAt       int64                      `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                   // 创建时间
	UpdatedAt       int64                      `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                   // 更新时间
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StabilityPlan) Reset() {
	*x = StabilityPlan{}
	mi := &file_manager_stability_plan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityPlan) ProtoMessage() {}

func (x *StabilityPlan) ProtoReflect() protoreflect.Message {
	mi := &file_manager_stability_plan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityPlan.ProtoReflect.Descriptor instead.
func (*StabilityPlan) Descriptor() ([]byte, []int) {
	return file_manager_stability_plan_proto_rawDescGZIP(), []int{0}
}

func (x *StabilityPlan) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StabilityPlan) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *StabilityPlan) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *StabilityPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StabilityPlan) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *StabilityPlan) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *StabilityPlan) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *StabilityPlan) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *StabilityPlan) GetCronExpression() string {
	if x != nil {
		return x.CronExpression
	}
	return ""
}

func (x *StabilityPlan) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *StabilityPlan) GetAccountConfigId() string {
	if x != nil {
		return x.AccountConfigId
	}
	return ""
}

func (x *StabilityPlan) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *StabilityPlan) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *StabilityPlan) GetDevices() *pb.StabilityCustomDevices {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *StabilityPlan) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *StabilityPlan) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *StabilityPlan) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *StabilityPlan) GetActivities() []string {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *StabilityPlan) GetCustomScript() *pb.StabilityCustomScript {
	if x != nil {
		return x.CustomScript
	}
	return nil
}

func (x *StabilityPlan) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *StabilityPlan) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *StabilityPlan) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *StabilityPlan) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *StabilityPlan) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_stability_plan_proto protoreflect.FileDescriptor

var file_manager_stability_plan_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x07, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72, 0x6f, 0x6e, 0x5f,
	0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x63, 0x72, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64,
	0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x38, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x52, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x26, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x5f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_stability_plan_proto_rawDescOnce sync.Once
	file_manager_stability_plan_proto_rawDescData = file_manager_stability_plan_proto_rawDesc
)

func file_manager_stability_plan_proto_rawDescGZIP() []byte {
	file_manager_stability_plan_proto_rawDescOnce.Do(func() {
		file_manager_stability_plan_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_stability_plan_proto_rawDescData)
	})
	return file_manager_stability_plan_proto_rawDescData
}

var file_manager_stability_plan_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_manager_stability_plan_proto_goTypes = []any{
	(*StabilityPlan)(nil),             // 0: manager.StabilityPlan
	(CommonState)(0),                  // 1: manager.CommonState
	(pb.TriggerMode)(0),               // 2: common.TriggerMode
	(pb.PriorityType)(0),              // 3: common.PriorityType
	(pb.DeviceType)(0),                // 4: common.DeviceType
	(pb.PlatformType)(0),              // 5: common.PlatformType
	(*pb.StabilityCustomDevices)(nil), // 6: common.StabilityCustomDevices
	(*pb.StabilityCustomScript)(nil),  // 7: common.StabilityCustomScript
}
var file_manager_stability_plan_proto_depIdxs = []int32{
	1, // 0: manager.StabilityPlan.state:type_name -> manager.CommonState
	2, // 1: manager.StabilityPlan.type:type_name -> common.TriggerMode
	3, // 2: manager.StabilityPlan.priority_type:type_name -> common.PriorityType
	4, // 3: manager.StabilityPlan.device_type:type_name -> common.DeviceType
	5, // 4: manager.StabilityPlan.platform_type:type_name -> common.PlatformType
	6, // 5: manager.StabilityPlan.devices:type_name -> common.StabilityCustomDevices
	7, // 6: manager.StabilityPlan.custom_script:type_name -> common.StabilityCustomScript
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_manager_stability_plan_proto_init() }
func file_manager_stability_plan_proto_init() {
	if File_manager_stability_plan_proto != nil {
		return
	}
	file_manager_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_stability_plan_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_stability_plan_proto_goTypes,
		DependencyIndexes: file_manager_stability_plan_proto_depIdxs,
		MessageInfos:      file_manager_stability_plan_proto_msgTypes,
	}.Build()
	File_manager_stability_plan_proto = out.File
	file_manager_stability_plan_proto_rawDesc = nil
	file_manager_stability_plan_proto_goTypes = nil
	file_manager_stability_plan_proto_depIdxs = nil
}
