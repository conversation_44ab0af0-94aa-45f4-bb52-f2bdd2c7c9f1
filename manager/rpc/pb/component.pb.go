// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/component.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// VariableSource 变量来源
type VariableSource int32

const (
	VariableSource_MANUAL      VariableSource = 0 // 手写
	VariableSource_EXPORT      VariableSource = 1 // 出参
	VariableSource_ENVIRONMENT VariableSource = 2 // 通用配置
	VariableSource_FUNCTION    VariableSource = 3 // 数据处理函数
)

// Enum value maps for VariableSource.
var (
	VariableSource_name = map[int32]string{
		0: "MANUAL",
		1: "EXPORT",
		2: "ENVIRONMENT",
		3: "FUNCTION",
	}
	VariableSource_value = map[string]int32{
		"MANUAL":      0,
		"EXPORT":      1,
		"ENVIRONMENT": 2,
		"FUNCTION":    3,
	}
)

func (x VariableSource) Enum() *VariableSource {
	p := new(VariableSource)
	*p = x
	return p
}

func (x VariableSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VariableSource) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[0].Descriptor()
}

func (VariableSource) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[0]
}

func (x VariableSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VariableSource.Descriptor instead.
func (VariableSource) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{0}
}

// ConditionType 条件类型
type ConditionType int32

const (
	ConditionType_SINGLE ConditionType = 0 // 单条件
	ConditionType_GROUP  ConditionType = 1 // 条件组
)

// Enum value maps for ConditionType.
var (
	ConditionType_name = map[int32]string{
		0: "SINGLE",
		1: "GROUP",
	}
	ConditionType_value = map[string]int32{
		"SINGLE": 0,
		"GROUP":  1,
	}
)

func (x ConditionType) Enum() *ConditionType {
	p := new(ConditionType)
	*p = x
	return p
}

func (x ConditionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConditionType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[1].Descriptor()
}

func (ConditionType) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[1]
}

func (x ConditionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConditionType.Descriptor instead.
func (ConditionType) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{1}
}

// Relationship 逻辑关系
type Relationship int32

const (
	Relationship_AND Relationship = 0 // 且
	Relationship_OR  Relationship = 1 // 或
)

// Enum value maps for Relationship.
var (
	Relationship_name = map[int32]string{
		0: "AND",
		1: "OR",
	}
	Relationship_value = map[string]int32{
		"AND": 0,
		"OR":  1,
	}
)

func (x Relationship) Enum() *Relationship {
	p := new(Relationship)
	*p = x
	return p
}

func (x Relationship) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Relationship) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[2].Descriptor()
}

func (Relationship) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[2]
}

func (x Relationship) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Relationship.Descriptor instead.
func (Relationship) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{2}
}

// ExtractType 提取类型
type ExtractType int32

const (
	ExtractType_JMESPATH ExtractType = 0 // JMESPath
	ExtractType_REGEX    ExtractType = 1 // 正则
)

// Enum value maps for ExtractType.
var (
	ExtractType_name = map[int32]string{
		0: "JMESPATH",
		1: "REGEX",
	}
	ExtractType_value = map[string]int32{
		"JMESPATH": 0,
		"REGEX":    1,
	}
)

func (x ExtractType) Enum() *ExtractType {
	p := new(ExtractType)
	*p = x
	return p
}

func (x ExtractType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExtractType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[3].Descriptor()
}

func (ExtractType) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[3]
}

func (x ExtractType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExtractType.Descriptor instead.
func (ExtractType) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{3}
}

// ResponseSource 响应信息提取来源
type ResponseSource int32

const (
	ResponseSource_HEADERS     ResponseSource = 0
	ResponseSource_BODY        ResponseSource = 1
	ResponseSource_STATUS_CODE ResponseSource = 2
)

// Enum value maps for ResponseSource.
var (
	ResponseSource_name = map[int32]string{
		0: "HEADERS",
		1: "BODY",
		2: "STATUS_CODE",
	}
	ResponseSource_value = map[string]int32{
		"HEADERS":     0,
		"BODY":        1,
		"STATUS_CODE": 2,
	}
)

func (x ResponseSource) Enum() *ResponseSource {
	p := new(ResponseSource)
	*p = x
	return p
}

func (x ResponseSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResponseSource) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[4].Descriptor()
}

func (ResponseSource) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[4]
}

func (x ResponseSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResponseSource.Descriptor instead.
func (ResponseSource) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{4}
}

// DataSourceType 数据源类型
type DataSourceType int32

const (
	DataSourceType_MYSQL      DataSourceType = 0 // MySQL
	DataSourceType_POSTGRESQL DataSourceType = 1 // PostgreSQL
)

// Enum value maps for DataSourceType.
var (
	DataSourceType_name = map[int32]string{
		0: "MYSQL",
		1: "POSTGRESQL",
	}
	DataSourceType_value = map[string]int32{
		"MYSQL":      0,
		"POSTGRESQL": 1,
	}
)

func (x DataSourceType) Enum() *DataSourceType {
	p := new(DataSourceType)
	*p = x
	return p
}

func (x DataSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[5].Descriptor()
}

func (DataSourceType) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[5]
}

func (x DataSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataSourceType.Descriptor instead.
func (DataSourceType) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{5}
}

type LoopComponent_Type int32

const (
	LoopComponent_FOR      LoopComponent_Type = 0 // 次数循环
	LoopComponent_FOR_EACH LoopComponent_Type = 1 // ForEach循环
	LoopComponent_WHILE    LoopComponent_Type = 2 // While循环
)

// Enum value maps for LoopComponent_Type.
var (
	LoopComponent_Type_name = map[int32]string{
		0: "FOR",
		1: "FOR_EACH",
		2: "WHILE",
	}
	LoopComponent_Type_value = map[string]int32{
		"FOR":      0,
		"FOR_EACH": 1,
		"WHILE":    2,
	}
)

func (x LoopComponent_Type) Enum() *LoopComponent_Type {
	p := new(LoopComponent_Type)
	*p = x
	return p
}

func (x LoopComponent_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoopComponent_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[6].Descriptor()
}

func (LoopComponent_Type) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[6]
}

func (x LoopComponent_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoopComponent_Type.Descriptor instead.
func (LoopComponent_Type) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{32, 0}
}

type HttpRequestComponent_Authorization_Type int32

const (
	HttpRequestComponent_Authorization_NO_AUTH      HttpRequestComponent_Authorization_Type = 0
	HttpRequestComponent_Authorization_API_KEY      HttpRequestComponent_Authorization_Type = 1
	HttpRequestComponent_Authorization_BEARER_TOKEN HttpRequestComponent_Authorization_Type = 2
	HttpRequestComponent_Authorization_BASIC_AUTH   HttpRequestComponent_Authorization_Type = 3
)

// Enum value maps for HttpRequestComponent_Authorization_Type.
var (
	HttpRequestComponent_Authorization_Type_name = map[int32]string{
		0: "NO_AUTH",
		1: "API_KEY",
		2: "BEARER_TOKEN",
		3: "BASIC_AUTH",
	}
	HttpRequestComponent_Authorization_Type_value = map[string]int32{
		"NO_AUTH":      0,
		"API_KEY":      1,
		"BEARER_TOKEN": 2,
		"BASIC_AUTH":   3,
	}
)

func (x HttpRequestComponent_Authorization_Type) Enum() *HttpRequestComponent_Authorization_Type {
	p := new(HttpRequestComponent_Authorization_Type)
	*p = x
	return p
}

func (x HttpRequestComponent_Authorization_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HttpRequestComponent_Authorization_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[7].Descriptor()
}

func (HttpRequestComponent_Authorization_Type) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[7]
}

func (x HttpRequestComponent_Authorization_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HttpRequestComponent_Authorization_Type.Descriptor instead.
func (HttpRequestComponent_Authorization_Type) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 0, 0}
}

type HttpRequestComponent_Authorization_ApiKey_AddTo int32

const (
	HttpRequestComponent_Authorization_ApiKey_HEADERS      HttpRequestComponent_Authorization_ApiKey_AddTo = 0
	HttpRequestComponent_Authorization_ApiKey_QUERY_PARAMS HttpRequestComponent_Authorization_ApiKey_AddTo = 1
)

// Enum value maps for HttpRequestComponent_Authorization_ApiKey_AddTo.
var (
	HttpRequestComponent_Authorization_ApiKey_AddTo_name = map[int32]string{
		0: "HEADERS",
		1: "QUERY_PARAMS",
	}
	HttpRequestComponent_Authorization_ApiKey_AddTo_value = map[string]int32{
		"HEADERS":      0,
		"QUERY_PARAMS": 1,
	}
)

func (x HttpRequestComponent_Authorization_ApiKey_AddTo) Enum() *HttpRequestComponent_Authorization_ApiKey_AddTo {
	p := new(HttpRequestComponent_Authorization_ApiKey_AddTo)
	*p = x
	return p
}

func (x HttpRequestComponent_Authorization_ApiKey_AddTo) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HttpRequestComponent_Authorization_ApiKey_AddTo) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[8].Descriptor()
}

func (HttpRequestComponent_Authorization_ApiKey_AddTo) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[8]
}

func (x HttpRequestComponent_Authorization_ApiKey_AddTo) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HttpRequestComponent_Authorization_ApiKey_AddTo.Descriptor instead.
func (HttpRequestComponent_Authorization_ApiKey_AddTo) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 0, 0, 0}
}

type HttpRequestComponent_Body_ContentType int32

const (
	HttpRequestComponent_Body_NONE                        HttpRequestComponent_Body_ContentType = 0 // none
	HttpRequestComponent_Body_MULTIPART_FORM_DATA         HttpRequestComponent_Body_ContentType = 1 // multipart/form-data
	HttpRequestComponent_Body_APPLICATION_FORM_URLENCODED HttpRequestComponent_Body_ContentType = 2 // application/x-www-form-urlencoded
	HttpRequestComponent_Body_TEXT_PLAIN                  HttpRequestComponent_Body_ContentType = 3 // raw: text/plain
	HttpRequestComponent_Body_APPLICATION_JSON            HttpRequestComponent_Body_ContentType = 4 // raw: application/json
)

// Enum value maps for HttpRequestComponent_Body_ContentType.
var (
	HttpRequestComponent_Body_ContentType_name = map[int32]string{
		0: "NONE",
		1: "MULTIPART_FORM_DATA",
		2: "APPLICATION_FORM_URLENCODED",
		3: "TEXT_PLAIN",
		4: "APPLICATION_JSON",
	}
	HttpRequestComponent_Body_ContentType_value = map[string]int32{
		"NONE":                        0,
		"MULTIPART_FORM_DATA":         1,
		"APPLICATION_FORM_URLENCODED": 2,
		"TEXT_PLAIN":                  3,
		"APPLICATION_JSON":            4,
	}
)

func (x HttpRequestComponent_Body_ContentType) Enum() *HttpRequestComponent_Body_ContentType {
	p := new(HttpRequestComponent_Body_ContentType)
	*p = x
	return p
}

func (x HttpRequestComponent_Body_ContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HttpRequestComponent_Body_ContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[9].Descriptor()
}

func (HttpRequestComponent_Body_ContentType) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[9]
}

func (x HttpRequestComponent_Body_ContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HttpRequestComponent_Body_ContentType.Descriptor instead.
func (HttpRequestComponent_Body_ContentType) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 2, 0}
}

type HttpRequestComponent_Assertion_Body_Type int32

const (
	HttpRequestComponent_Assertion_Body_JMESPATH HttpRequestComponent_Assertion_Body_Type = 0 // JMESPath
	HttpRequestComponent_Assertion_Body_REGEX    HttpRequestComponent_Assertion_Body_Type = 1 // 正则
)

// Enum value maps for HttpRequestComponent_Assertion_Body_Type.
var (
	HttpRequestComponent_Assertion_Body_Type_name = map[int32]string{
		0: "JMESPATH",
		1: "REGEX",
	}
	HttpRequestComponent_Assertion_Body_Type_value = map[string]int32{
		"JMESPATH": 0,
		"REGEX":    1,
	}
)

func (x HttpRequestComponent_Assertion_Body_Type) Enum() *HttpRequestComponent_Assertion_Body_Type {
	p := new(HttpRequestComponent_Assertion_Body_Type)
	*p = x
	return p
}

func (x HttpRequestComponent_Assertion_Body_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HttpRequestComponent_Assertion_Body_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[10].Descriptor()
}

func (HttpRequestComponent_Assertion_Body_Type) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[10]
}

func (x HttpRequestComponent_Assertion_Body_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HttpRequestComponent_Assertion_Body_Type.Descriptor instead.
func (HttpRequestComponent_Assertion_Body_Type) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 4, 1, 0}
}

type HttpRequestComponent_Export_Body_Type int32

const (
	HttpRequestComponent_Export_Body_JMESPATH HttpRequestComponent_Export_Body_Type = 0 // JMESPath
	HttpRequestComponent_Export_Body_REGEX    HttpRequestComponent_Export_Body_Type = 1 // 正则
)

// Enum value maps for HttpRequestComponent_Export_Body_Type.
var (
	HttpRequestComponent_Export_Body_Type_name = map[int32]string{
		0: "JMESPATH",
		1: "REGEX",
	}
	HttpRequestComponent_Export_Body_Type_value = map[string]int32{
		"JMESPATH": 0,
		"REGEX":    1,
	}
)

func (x HttpRequestComponent_Export_Body_Type) Enum() *HttpRequestComponent_Export_Body_Type {
	p := new(HttpRequestComponent_Export_Body_Type)
	*p = x
	return p
}

func (x HttpRequestComponent_Export_Body_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HttpRequestComponent_Export_Body_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[11].Descriptor()
}

func (HttpRequestComponent_Export_Body_Type) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[11]
}

func (x HttpRequestComponent_Export_Body_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HttpRequestComponent_Export_Body_Type.Descriptor instead.
func (HttpRequestComponent_Export_Body_Type) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 5, 1, 0}
}

type WaitComponent_Type int32

const (
	WaitComponent_SLEEP  WaitComponent_Type = 0 // 休眠
	WaitComponent_MANUAL WaitComponent_Type = 1 // 人工
)

// Enum value maps for WaitComponent_Type.
var (
	WaitComponent_Type_name = map[int32]string{
		0: "SLEEP",
		1: "MANUAL",
	}
	WaitComponent_Type_value = map[string]int32{
		"SLEEP":  0,
		"MANUAL": 1,
	}
)

func (x WaitComponent_Type) Enum() *WaitComponent_Type {
	p := new(WaitComponent_Type)
	*p = x
	return p
}

func (x WaitComponent_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WaitComponent_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[12].Descriptor()
}

func (WaitComponent_Type) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[12]
}

func (x WaitComponent_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WaitComponent_Type.Descriptor instead.
func (WaitComponent_Type) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{36, 0}
}

type WaitComponent_Manual_Action int32

const (
	WaitComponent_Manual_CONTINUE  WaitComponent_Manual_Action = 0 // 继续
	WaitComponent_Manual_TERMINATE WaitComponent_Manual_Action = 1 // 终止
)

// Enum value maps for WaitComponent_Manual_Action.
var (
	WaitComponent_Manual_Action_name = map[int32]string{
		0: "CONTINUE",
		1: "TERMINATE",
	}
	WaitComponent_Manual_Action_value = map[string]int32{
		"CONTINUE":  0,
		"TERMINATE": 1,
	}
)

func (x WaitComponent_Manual_Action) Enum() *WaitComponent_Manual_Action {
	p := new(WaitComponent_Manual_Action)
	*p = x
	return p
}

func (x WaitComponent_Manual_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WaitComponent_Manual_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_component_proto_enumTypes[13].Descriptor()
}

func (WaitComponent_Manual_Action) Type() protoreflect.EnumType {
	return &file_manager_component_proto_enumTypes[13]
}

func (x WaitComponent_Manual_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WaitComponent_Manual_Action.Descriptor instead.
func (WaitComponent_Manual_Action) EnumDescriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{36, 1, 0}
}

type VariableManual struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"` // 手写的值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VariableManual) Reset() {
	*x = VariableManual{}
	mi := &file_manager_component_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VariableManual) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VariableManual) ProtoMessage() {}

func (x *VariableManual) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VariableManual.ProtoReflect.Descriptor instead.
func (*VariableManual) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{0}
}

func (x *VariableManual) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type VariableExport struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"` // 来源节点ID
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`                 // 来源节点的出参变量名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VariableExport) Reset() {
	*x = VariableExport{}
	mi := &file_manager_component_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VariableExport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VariableExport) ProtoMessage() {}

func (x *VariableExport) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VariableExport.ProtoReflect.Descriptor instead.
func (*VariableExport) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{1}
}

func (x *VariableExport) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *VariableExport) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type VariableEnvironment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"` // 通用配置中的变量名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VariableEnvironment) Reset() {
	*x = VariableEnvironment{}
	mi := &file_manager_component_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VariableEnvironment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VariableEnvironment) ProtoMessage() {}

func (x *VariableEnvironment) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VariableEnvironment.ProtoReflect.Descriptor instead.
func (*VariableEnvironment) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{2}
}

func (x *VariableEnvironment) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type VariableFunction struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Name          string                        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                            // 函数名称
	Type          FunctionType                  `protobuf:"varint,2,opt,name=type,proto3,enum=manager.FunctionType" json:"type,omitempty"` // 函数类型
	Parameters    []*VariableFunction_Parameter `protobuf:"bytes,3,rep,name=parameters,proto3" json:"parameters,omitempty"`                // 参数列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VariableFunction) Reset() {
	*x = VariableFunction{}
	mi := &file_manager_component_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VariableFunction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VariableFunction) ProtoMessage() {}

func (x *VariableFunction) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VariableFunction.ProtoReflect.Descriptor instead.
func (*VariableFunction) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{3}
}

func (x *VariableFunction) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VariableFunction) GetType() FunctionType {
	if x != nil {
		return x.Type
	}
	return FunctionType_BUILTIN
}

func (x *VariableFunction) GetParameters() []*VariableFunction_Parameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

type VariableHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"` // 响应头的键
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VariableHeader) Reset() {
	*x = VariableHeader{}
	mi := &file_manager_component_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VariableHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VariableHeader) ProtoMessage() {}

func (x *VariableHeader) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VariableHeader.ProtoReflect.Descriptor instead.
func (*VariableHeader) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{4}
}

func (x *VariableHeader) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type VariableBody struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          ExtractType            `protobuf:"varint,1,opt,name=type,proto3,enum=manager.ExtractType" json:"type,omitempty"` // 提取类型
	Expression    string                 `protobuf:"bytes,2,opt,name=expression,proto3" json:"expression,omitempty"`               // 提取表达式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VariableBody) Reset() {
	*x = VariableBody{}
	mi := &file_manager_component_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VariableBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VariableBody) ProtoMessage() {}

func (x *VariableBody) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VariableBody.ProtoReflect.Descriptor instead.
func (*VariableBody) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{5}
}

func (x *VariableBody) GetType() ExtractType {
	if x != nil {
		return x.Type
	}
	return ExtractType_JMESPATH
}

func (x *VariableBody) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

// Import 入参
type Import struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 入参变量名称
	Source        VariableSource         `protobuf:"varint,2,opt,name=source,proto3,enum=manager.VariableSource" json:"source,omitempty"`
	Manual        *VariableManual        `protobuf:"bytes,3,opt,name=manual,proto3" json:"manual,omitempty"`
	Export        *VariableExport        `protobuf:"bytes,4,opt,name=export,proto3" json:"export,omitempty"`
	Environment   *VariableEnvironment   `protobuf:"bytes,5,opt,name=environment,proto3" json:"environment,omitempty"`
	Function      *VariableFunction      `protobuf:"bytes,6,opt,name=function,proto3" json:"function,omitempty"` // 为了兼容旧数据，这里不能做`validate`
	Description   string                 `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Import) Reset() {
	*x = Import{}
	mi := &file_manager_component_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Import) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Import) ProtoMessage() {}

func (x *Import) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Import.ProtoReflect.Descriptor instead.
func (*Import) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{6}
}

func (x *Import) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Import) GetSource() VariableSource {
	if x != nil {
		return x.Source
	}
	return VariableSource_MANUAL
}

func (x *Import) GetManual() *VariableManual {
	if x != nil {
		return x.Manual
	}
	return nil
}

func (x *Import) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *Import) GetEnvironment() *VariableEnvironment {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *Import) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

func (x *Import) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// Export 出参
type Export struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 出参变量名称
	Export        *VariableExport        `protobuf:"bytes,2,opt,name=export,proto3" json:"export,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Export) Reset() {
	*x = Export{}
	mi := &file_manager_component_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Export) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Export) ProtoMessage() {}

func (x *Export) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Export.ProtoReflect.Descriptor instead.
func (*Export) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{7}
}

func (x *Export) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Export) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *Export) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// ComponentGroupComponent 组件组组件（注：把组件组抽象为一个组件）
type ComponentGroupComponent struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ProjectId        string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                        // 项目ID
	ComponentGroupId string                 `protobuf:"bytes,2,opt,name=component_group_id,json=componentGroupId,proto3" json:"component_group_id,omitempty"` // 组件组ID
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                   // 组件组名称
	Description      string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                     // 组件组描述
	Version          string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`                                             // 版本
	Imports          []*Import              `protobuf:"bytes,6,rep,name=imports,proto3" json:"imports,omitempty"`                                             // 入参列表
	Exports          []*Export              `protobuf:"bytes,7,rep,name=exports,proto3" json:"exports,omitempty"`                                             // 出参列表
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ComponentGroupComponent) Reset() {
	*x = ComponentGroupComponent{}
	mi := &file_manager_component_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComponentGroupComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentGroupComponent) ProtoMessage() {}

func (x *ComponentGroupComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentGroupComponent.ProtoReflect.Descriptor instead.
func (*ComponentGroupComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{8}
}

func (x *ComponentGroupComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ComponentGroupComponent) GetComponentGroupId() string {
	if x != nil {
		return x.ComponentGroupId
	}
	return ""
}

func (x *ComponentGroupComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ComponentGroupComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ComponentGroupComponent) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ComponentGroupComponent) GetImports() []*Import {
	if x != nil {
		return x.Imports
	}
	return nil
}

func (x *ComponentGroupComponent) GetExports() []*Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

// CaseComponent 用例组件（注：把用例抽象为一个组件）
type CaseComponent struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                          // 项目ID
	CaseId         string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                                                   // 用例ID
	Name           string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                     // 用例名称
	Description    string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                       // 用例描述
	State          ResourceState          `protobuf:"varint,5,opt,name=state,proto3,enum=manager.ResourceState" json:"state,omitempty"`                                       // 用例状态
	ReferenceState CommonState            `protobuf:"varint,6,opt,name=reference_state,json=referenceState,proto3,enum=manager.CommonState" json:"reference_state,omitempty"` // 用例引用状态（获取API计划执行结构时，此字段才有效）
	Version        string                 `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`                                                               // 版本
	MaintainedBy   string                 `protobuf:"bytes,8,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                 // 维护者的用户ID
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CaseComponent) Reset() {
	*x = CaseComponent{}
	mi := &file_manager_component_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseComponent) ProtoMessage() {}

func (x *CaseComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseComponent.ProtoReflect.Descriptor instead.
func (*CaseComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{9}
}

func (x *CaseComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CaseComponent) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CaseComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CaseComponent) GetState() ResourceState {
	if x != nil {
		return x.State
	}
	return ResourceState_RS_NULL
}

func (x *CaseComponent) GetReferenceState() CommonState {
	if x != nil {
		return x.ReferenceState
	}
	return CommonState_CS_NULL
}

func (x *CaseComponent) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CaseComponent) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

// SuiteComponent 集合组件（注：把集合抽象为一个组件）
type SuiteComponent struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                       // 项目ID
	SuiteId           string                 `protobuf:"bytes,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                                                             // 集合ID
	Name              string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                  // 集合名称
	Description       string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                                    // 集合描述
	State             CommonState            `protobuf:"varint,5,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                                      // 集合状态
	ReferenceState    CommonState            `protobuf:"varint,6,opt,name=reference_state,json=referenceState,proto3,enum=manager.CommonState" json:"reference_state,omitempty"`              // 集合引用状态（获取API计划执行结构时，此字段才有效）
	CaseExecutionMode ExecutionMode          `protobuf:"varint,7,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SuiteComponent) Reset() {
	*x = SuiteComponent{}
	mi := &file_manager_component_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuiteComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuiteComponent) ProtoMessage() {}

func (x *SuiteComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuiteComponent.ProtoReflect.Descriptor instead.
func (*SuiteComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{10}
}

func (x *SuiteComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SuiteComponent) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *SuiteComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SuiteComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SuiteComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SuiteComponent) GetReferenceState() CommonState {
	if x != nil {
		return x.ReferenceState
	}
	return CommonState_CS_NULL
}

func (x *SuiteComponent) GetCaseExecutionMode() ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return ExecutionMode_EM_NULL
}

// PlanComponent 计划组件（注：把计划抽象为一个组件）
type PlanComponent struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ProjectId          string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                           // 项目ID
	PlanId             string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                    // 计划ID
	Name               string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                      // 计划名称
	Description        string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                                        // 计划描述
	State              CommonState            `protobuf:"varint,5,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                                          // 计划状态
	Type               pb.TriggerMode         `protobuf:"varint,6,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                                             // 计划类型（触发模式）
	Purpose            pb.PurposeType         `protobuf:"varint,7,opt,name=purpose,proto3,enum=common.PurposeType" json:"purpose,omitempty"`                                                       // 计划用途
	GeneralConfig      *pb.GeneralConfig      `protobuf:"bytes,8,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                                               // 通用配置
	AccountConfigs     []*pb.AccountConfig    `protobuf:"bytes,9,rep,name=account_configs,json=accountConfigs,proto3" json:"account_configs,omitempty"`                                            // 池账号配置列表
	SuiteExecutionMode ExecutionMode          `protobuf:"varint,10,opt,name=suite_execution_mode,json=suiteExecutionMode,proto3,enum=manager.ExecutionMode" json:"suite_execution_mode,omitempty"` // 集合执行方式
	CaseExecutionMode  ExecutionMode          `protobuf:"varint,11,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"`    // 用例执行方式
	MaintainedBy       string                 `protobuf:"bytes,12,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                                 // 维护者的用户ID
	CreatedBy          string                 `protobuf:"bytes,13,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                          // 创建者的用户ID
	UpdatedBy          string                 `protobuf:"bytes,14,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                          // 更新者的用户ID
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PlanComponent) Reset() {
	*x = PlanComponent{}
	mi := &file_manager_component_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlanComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanComponent) ProtoMessage() {}

func (x *PlanComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanComponent.ProtoReflect.Descriptor instead.
func (*PlanComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{11}
}

func (x *PlanComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PlanComponent) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PlanComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlanComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PlanComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *PlanComponent) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *PlanComponent) GetPurpose() pb.PurposeType {
	if x != nil {
		return x.Purpose
	}
	return pb.PurposeType(0)
}

func (x *PlanComponent) GetGeneralConfig() *pb.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *PlanComponent) GetAccountConfigs() []*pb.AccountConfig {
	if x != nil {
		return x.AccountConfigs
	}
	return nil
}

func (x *PlanComponent) GetSuiteExecutionMode() ExecutionMode {
	if x != nil {
		return x.SuiteExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *PlanComponent) GetCaseExecutionMode() ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *PlanComponent) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *PlanComponent) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PlanComponent) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

// InterfaceCaseComponent 接口用例组件（注：把接口用例抽象为一个组件）
type InterfaceCaseComponent struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                          // 项目ID
	DocumentId     string                 `protobuf:"bytes,2,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`                                       // 接口ID
	CaseId         string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                                                   // 接口用例ID
	Name           string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                                                     // 接口用例名称
	Description    string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                                                       // 接口用例描述
	State          ResourceState          `protobuf:"varint,6,opt,name=state,proto3,enum=manager.ResourceState" json:"state,omitempty"`                                       // 接口用例状态
	ReferenceState CommonState            `protobuf:"varint,7,opt,name=reference_state,json=referenceState,proto3,enum=manager.CommonState" json:"reference_state,omitempty"` // 接口用例引用状态（获取API计划执行结构时，此字段才有效）
	Version        string                 `protobuf:"bytes,8,opt,name=version,proto3" json:"version,omitempty"`                                                               // 版本
	MaintainedBy   string                 `protobuf:"bytes,9,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                 // 维护者的用户ID
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *InterfaceCaseComponent) Reset() {
	*x = InterfaceCaseComponent{}
	mi := &file_manager_component_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceCaseComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceCaseComponent) ProtoMessage() {}

func (x *InterfaceCaseComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceCaseComponent.ProtoReflect.Descriptor instead.
func (*InterfaceCaseComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{12}
}

func (x *InterfaceCaseComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *InterfaceCaseComponent) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *InterfaceCaseComponent) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *InterfaceCaseComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InterfaceCaseComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InterfaceCaseComponent) GetState() ResourceState {
	if x != nil {
		return x.State
	}
	return ResourceState_RS_NULL
}

func (x *InterfaceCaseComponent) GetReferenceState() CommonState {
	if x != nil {
		return x.ReferenceState
	}
	return CommonState_CS_NULL
}

func (x *InterfaceCaseComponent) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *InterfaceCaseComponent) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

// InterfaceDocumentComponent 接口文档（集合）组件（注：把接口集合抽象为一个组件）
type InterfaceDocumentComponent struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                       // 项目ID
	DocumentId        string                 `protobuf:"bytes,2,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`                                                    // 接口ID
	Name              string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                  // 接口名称
	Description       string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                                    // 接口描述
	State             CommonState            `protobuf:"varint,5,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                                      // 接口集合状态
	ReferenceState    CommonState            `protobuf:"varint,6,opt,name=reference_state,json=referenceState,proto3,enum=manager.CommonState" json:"reference_state,omitempty"`              // 接口集合引用状态（获取API计划执行结构时，此字段才有效）
	CaseExecutionMode ExecutionMode          `protobuf:"varint,7,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InterfaceDocumentComponent) Reset() {
	*x = InterfaceDocumentComponent{}
	mi := &file_manager_component_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceDocumentComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceDocumentComponent) ProtoMessage() {}

func (x *InterfaceDocumentComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceDocumentComponent.ProtoReflect.Descriptor instead.
func (*InterfaceDocumentComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{13}
}

func (x *InterfaceDocumentComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *InterfaceDocumentComponent) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *InterfaceDocumentComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InterfaceDocumentComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InterfaceDocumentComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *InterfaceDocumentComponent) GetReferenceState() CommonState {
	if x != nil {
		return x.ReferenceState
	}
	return CommonState_CS_NULL
}

func (x *InterfaceDocumentComponent) GetCaseExecutionMode() ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return ExecutionMode_EM_NULL
}

// UICaseComponent UI用例组件（注：把UI用例抽象为一个组件）
type UICaseComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`  // 项目ID
	CaseId        string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`           // 用例ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                             // 用例名称
	Alias         string                 `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`                           // 用例别名
	Path          string                 `protobuf:"bytes,5,opt,name=path,proto3" json:"path,omitempty"`                             // 用例路径
	State         CommonState            `protobuf:"varint,6,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"` // 用例状态
	Udid          string                 `protobuf:"bytes,11,opt,name=udid,proto3" json:"udid,omitempty"`                            // 指定的设备编号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UICaseComponent) Reset() {
	*x = UICaseComponent{}
	mi := &file_manager_component_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UICaseComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UICaseComponent) ProtoMessage() {}

func (x *UICaseComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UICaseComponent.ProtoReflect.Descriptor instead.
func (*UICaseComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{14}
}

func (x *UICaseComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UICaseComponent) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *UICaseComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UICaseComponent) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *UICaseComponent) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *UICaseComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *UICaseComponent) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

// UISuiteComponent UI集合组件（注：把UI组合抽象为一个组件）
type UISuiteComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`  // 项目ID
	SuiteId       string                 `protobuf:"bytes,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`        // 集合ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                             // 集合名称
	Alias         string                 `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`                           // 集合别名
	Path          string                 `protobuf:"bytes,5,opt,name=path,proto3" json:"path,omitempty"`                             // 集合路径
	State         CommonState            `protobuf:"varint,6,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"` // 集合状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UISuiteComponent) Reset() {
	*x = UISuiteComponent{}
	mi := &file_manager_component_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UISuiteComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UISuiteComponent) ProtoMessage() {}

func (x *UISuiteComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UISuiteComponent.ProtoReflect.Descriptor instead.
func (*UISuiteComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{15}
}

func (x *UISuiteComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UISuiteComponent) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *UISuiteComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UISuiteComponent) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *UISuiteComponent) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *UISuiteComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

type UIPlanMetaData struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	PriorityType         pb.PriorityType        `protobuf:"varint,1,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"`                       // 优先级
	GitConfig            *pb.GitConfig          `protobuf:"bytes,2,opt,name=git_config,json=gitConfig,proto3" json:"git_config,omitempty"`                                                          // Git配置
	SuiteExecutionMode   ExecutionMode          `protobuf:"varint,3,opt,name=suite_execution_mode,json=suiteExecutionMode,proto3,enum=manager.ExecutionMode" json:"suite_execution_mode,omitempty"` // 集合执行方式
	CaseExecutionMode    ExecutionMode          `protobuf:"varint,4,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"`    // 用例执行方式
	DeviceType           pb.DeviceType          `protobuf:"varint,5,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`                               // 设备类型（真机、云手机）
	PlatformType         pb.PlatformType        `protobuf:"varint,6,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"`                       // 平台类型（Android、iOS）
	PackageName          string                 `protobuf:"bytes,7,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                                                    // 包名，用于启动APP
	AppName              string                 `protobuf:"bytes,8,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                                                                // 应用名称
	CallbackUrl          string                 `protobuf:"bytes,9,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`                                                    // 回调地址
	AppDownloadLink      string                 `protobuf:"bytes,10,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                                     // App下载地址
	AppVersion           string                 `protobuf:"bytes,11,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`                                                      // App版本
	TestLanguage         pb.TestLanguage        `protobuf:"varint,12,opt,name=test_language,json=testLanguage,proto3,enum=common.TestLanguage" json:"test_language,omitempty"`                      // 测试语言 0：Python 1：Golang
	TestLanguageVersion  string                 `protobuf:"bytes,13,opt,name=test_language_version,json=testLanguageVersion,proto3" json:"test_language_version,omitempty"`                         // 测试语言版本
	TestFramework        pb.TestFramework       `protobuf:"varint,14,opt,name=test_framework,json=testFramework,proto3,enum=common.TestFramework" json:"test_framework,omitempty"`                  // 测试框架 1：pytest
	TestArgs             []string               `protobuf:"bytes,15,rep,name=test_args,json=testArgs,proto3" json:"test_args,omitempty"`                                                            // 附加参数
	ExecutionEnvironment string                 `protobuf:"bytes,16,opt,name=execution_environment,json=executionEnvironment,proto3" json:"execution_environment,omitempty"`                        // 执行环境
	FailRetry            pb.FailRetry           `protobuf:"varint,17,opt,name=fail_retry,json=failRetry,proto3,enum=common.FailRetry" json:"fail_retry,omitempty"`                                  // 失败重试
	Devices              []string               `protobuf:"bytes,21,rep,name=devices,proto3" json:"devices,omitempty"`                                                                              // 设备列表
	Together             bool                   `protobuf:"varint,22,opt,name=together,proto3" json:"together,omitempty"`                                                                           // 选择的设备是否一起执行
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UIPlanMetaData) Reset() {
	*x = UIPlanMetaData{}
	mi := &file_manager_component_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIPlanMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIPlanMetaData) ProtoMessage() {}

func (x *UIPlanMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIPlanMetaData.ProtoReflect.Descriptor instead.
func (*UIPlanMetaData) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{16}
}

func (x *UIPlanMetaData) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *UIPlanMetaData) GetGitConfig() *pb.GitConfig {
	if x != nil {
		return x.GitConfig
	}
	return nil
}

func (x *UIPlanMetaData) GetSuiteExecutionMode() ExecutionMode {
	if x != nil {
		return x.SuiteExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *UIPlanMetaData) GetCaseExecutionMode() ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *UIPlanMetaData) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *UIPlanMetaData) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *UIPlanMetaData) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *UIPlanMetaData) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UIPlanMetaData) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *UIPlanMetaData) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *UIPlanMetaData) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *UIPlanMetaData) GetTestLanguage() pb.TestLanguage {
	if x != nil {
		return x.TestLanguage
	}
	return pb.TestLanguage(0)
}

func (x *UIPlanMetaData) GetTestLanguageVersion() string {
	if x != nil {
		return x.TestLanguageVersion
	}
	return ""
}

func (x *UIPlanMetaData) GetTestFramework() pb.TestFramework {
	if x != nil {
		return x.TestFramework
	}
	return pb.TestFramework(0)
}

func (x *UIPlanMetaData) GetTestArgs() []string {
	if x != nil {
		return x.TestArgs
	}
	return nil
}

func (x *UIPlanMetaData) GetExecutionEnvironment() string {
	if x != nil {
		return x.ExecutionEnvironment
	}
	return ""
}

func (x *UIPlanMetaData) GetFailRetry() pb.FailRetry {
	if x != nil {
		return x.FailRetry
	}
	return pb.FailRetry(0)
}

func (x *UIPlanMetaData) GetDevices() []string {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *UIPlanMetaData) GetTogether() bool {
	if x != nil {
		return x.Together
	}
	return false
}

// UIPlanComponent UI计划组件（注：把UI计划抽象为一个组件）
type UIPlanComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`          // 项目ID
	PlanId        string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                   // 计划ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                     // 计划名称
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                       // 计划描述
	State         CommonState            `protobuf:"varint,5,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`         // 计划状态
	Type          pb.TriggerMode         `protobuf:"varint,6,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`            // 计划类型（触发模式）
	MetaData      *UIPlanMetaData        `protobuf:"bytes,7,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`             // 元数据
	MaintainedBy  string                 `protobuf:"bytes,8,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"` // 维护者的用户ID
	CreatedBy     string                 `protobuf:"bytes,9,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`          // 创建者的用户ID
	UpdatedBy     string                 `protobuf:"bytes,10,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`         // 更新者的用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIPlanComponent) Reset() {
	*x = UIPlanComponent{}
	mi := &file_manager_component_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIPlanComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIPlanComponent) ProtoMessage() {}

func (x *UIPlanComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIPlanComponent.ProtoReflect.Descriptor instead.
func (*UIPlanComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{17}
}

func (x *UIPlanComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UIPlanComponent) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *UIPlanComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UIPlanComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UIPlanComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *UIPlanComponent) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *UIPlanComponent) GetMetaData() *UIPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *UIPlanComponent) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *UIPlanComponent) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *UIPlanComponent) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type ServiceComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`       // 项目id
	ServiceName   string                 `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"` // 服务名
	ServiceId     string                 `protobuf:"bytes,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`       // 服务id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceComponent) Reset() {
	*x = ServiceComponent{}
	mi := &file_manager_component_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceComponent) ProtoMessage() {}

func (x *ServiceComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceComponent.ProtoReflect.Descriptor instead.
func (*ServiceComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{18}
}

func (x *ServiceComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ServiceComponent) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceComponent) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

// PerfDataComponent 压测数据组件（注：把压测数据抽象为一个组件）
type PerfDataComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`       // 项目ID
	DataId        string                 `protobuf:"bytes,2,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`                // 压测数据ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                  // 压测数据名称
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                    // 压测数据描述
	Extension     string                 `protobuf:"bytes,5,opt,name=extension,proto3" json:"extension,omitempty"`                        // 压测数据文件的扩展名
	Hash          string                 `protobuf:"bytes,6,opt,name=hash,proto3" json:"hash,omitempty"`                                  // 压测数据文件的一致性哈希值
	Size          uint32                 `protobuf:"varint,7,opt,name=size,proto3" json:"size,omitempty"`                                 // 压测数据文件的大小
	Path          string                 `protobuf:"bytes,8,opt,name=path,proto3" json:"path,omitempty"`                                  // 压测数据文件的路径
	NumberOfVu    uint32                 `protobuf:"varint,9,opt,name=number_of_vu,json=numberOfVu,proto3" json:"number_of_vu,omitempty"` // 虚拟用户数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfDataComponent) Reset() {
	*x = PerfDataComponent{}
	mi := &file_manager_component_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfDataComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfDataComponent) ProtoMessage() {}

func (x *PerfDataComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfDataComponent.ProtoReflect.Descriptor instead.
func (*PerfDataComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{19}
}

func (x *PerfDataComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfDataComponent) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *PerfDataComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfDataComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfDataComponent) GetExtension() string {
	if x != nil {
		return x.Extension
	}
	return ""
}

func (x *PerfDataComponent) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *PerfDataComponent) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PerfDataComponent) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *PerfDataComponent) GetNumberOfVu() uint32 {
	if x != nil {
		return x.NumberOfVu
	}
	return 0
}

// PerfCaseComponent 压测用例组件（注：把压测用例抽象为一个组件）
type PerfCaseComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`              // 项目ID
	CaseId        string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                       // 用例ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                         // 用例名称
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                           // 用例描述
	Extension     string                 `protobuf:"bytes,5,opt,name=extension,proto3" json:"extension,omitempty"`                               // Deprecated: 压测用例文件的扩展名
	Hash          string                 `protobuf:"bytes,6,opt,name=hash,proto3" json:"hash,omitempty"`                                         // Deprecated: 压测用例文件的一致性哈希值
	Size          uint32                 `protobuf:"varint,7,opt,name=size,proto3" json:"size,omitempty"`                                        // Deprecated: 压测用例文件的大小
	Path          string                 `protobuf:"bytes,8,opt,name=path,proto3" json:"path,omitempty"`                                         // Deprecated: 压测用例文件的路径
	TargetRps     int64                  `protobuf:"varint,9,opt,name=target_rps,json=targetRps,proto3" json:"target_rps,omitempty"`             // 目标的RPS
	RateLimits    []*pb.RateLimitV2      `protobuf:"bytes,10,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"`          // 限流配置
	SetupSteps    []*pb.PerfCaseStepV2   `protobuf:"bytes,21,rep,name=setup_steps,json=setupSteps,proto3" json:"setup_steps,omitempty"`          // 前置步骤列表
	SerialSteps   []*pb.PerfCaseStepV2   `protobuf:"bytes,22,rep,name=serial_steps,json=serialSteps,proto3" json:"serial_steps,omitempty"`       // 串行步骤列表
	ParallelSteps []*pb.PerfCaseStepV2   `protobuf:"bytes,23,rep,name=parallel_steps,json=parallelSteps,proto3" json:"parallel_steps,omitempty"` // 并行步骤列表
	TeardownSteps []*pb.PerfCaseStepV2   `protobuf:"bytes,24,rep,name=teardown_steps,json=teardownSteps,proto3" json:"teardown_steps,omitempty"` // 后置步骤列表
	PerfData      *PerfDataComponent     `protobuf:"bytes,31,opt,name=perf_data,json=perfData,proto3" json:"perf_data,omitempty"`                // 压测数据
	NumberOfVu    uint32                 `protobuf:"varint,32,opt,name=number_of_vu,json=numberOfVu,proto3" json:"number_of_vu,omitempty"`       // 虚拟用户数
	LoadGenerator *pb.LoadGenerator      `protobuf:"bytes,41,opt,name=load_generator,json=loadGenerator,proto3" json:"load_generator,omitempty"` // 施压机资源
	State         CommonState            `protobuf:"varint,51,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`            // 用例状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseComponent) Reset() {
	*x = PerfCaseComponent{}
	mi := &file_manager_component_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseComponent) ProtoMessage() {}

func (x *PerfCaseComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseComponent.ProtoReflect.Descriptor instead.
func (*PerfCaseComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{20}
}

func (x *PerfCaseComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfCaseComponent) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *PerfCaseComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfCaseComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfCaseComponent) GetExtension() string {
	if x != nil {
		return x.Extension
	}
	return ""
}

func (x *PerfCaseComponent) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *PerfCaseComponent) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PerfCaseComponent) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *PerfCaseComponent) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *PerfCaseComponent) GetRateLimits() []*pb.RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *PerfCaseComponent) GetSetupSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.SetupSteps
	}
	return nil
}

func (x *PerfCaseComponent) GetSerialSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.SerialSteps
	}
	return nil
}

func (x *PerfCaseComponent) GetParallelSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.ParallelSteps
	}
	return nil
}

func (x *PerfCaseComponent) GetTeardownSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.TeardownSteps
	}
	return nil
}

func (x *PerfCaseComponent) GetPerfData() *PerfDataComponent {
	if x != nil {
		return x.PerfData
	}
	return nil
}

func (x *PerfCaseComponent) GetNumberOfVu() uint32 {
	if x != nil {
		return x.NumberOfVu
	}
	return 0
}

func (x *PerfCaseComponent) GetLoadGenerator() *pb.LoadGenerator {
	if x != nil {
		return x.LoadGenerator
	}
	return nil
}

func (x *PerfCaseComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

// PerfSuiteComponent 压测集合组件（注：把压测集合抽象为一个组件）
type PerfSuiteComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`   // 项目ID
	SuiteId       string                 `protobuf:"bytes,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`         // 集合ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                             // 集合名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`               // 集合描述
	State         CommonState            `protobuf:"varint,21,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"` // 集合状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfSuiteComponent) Reset() {
	*x = PerfSuiteComponent{}
	mi := &file_manager_component_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfSuiteComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfSuiteComponent) ProtoMessage() {}

func (x *PerfSuiteComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfSuiteComponent.ProtoReflect.Descriptor instead.
func (*PerfSuiteComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{21}
}

func (x *PerfSuiteComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfSuiteComponent) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *PerfSuiteComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfSuiteComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfSuiteComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

// PerfPlanMetaData 压测计划元数据
type PerfPlanMetaData struct {
	state           protoimpl.MessageState    `protogen:"open.v1"`
	Protocol        pb.Protocol               `protobuf:"varint,1,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                             // 协议
	TargetEnv       pb.TargetEnvironment      `protobuf:"varint,2,opt,name=target_env,json=targetEnv,proto3,enum=common.TargetEnvironment" json:"target_env,omitempty"` // 目标环境
	ProtobufConfigs []*pb.ProtobufConfig      `protobuf:"bytes,3,rep,name=protobuf_configs,json=protobufConfigs,proto3" json:"protobuf_configs,omitempty"`              // Protobuf配置列表
	GeneralConfig   *pb.GeneralConfig         `protobuf:"bytes,4,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                    // 通用配置
	AccountConfig   *pb.AccountConfig         `protobuf:"bytes,5,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                    // 池账号配置
	TargetMaxRps    int64                     `protobuf:"varint,6,opt,name=target_max_rps,json=targetMaxRps,proto3" json:"target_max_rps,omitempty"`                    // 目标最大的RPS
	Duration        uint32                    `protobuf:"varint,7,opt,name=duration,proto3" json:"duration,omitempty"`                                                  // 压测持续时长
	Keepalive       *pb.PerfKeepalive         `protobuf:"bytes,8,opt,name=keepalive,proto3" json:"keepalive,omitempty"`                                                 // Deprecated: 保活参数
	Delay           uint32                    `protobuf:"varint,9,opt,name=delay,proto3" json:"delay,omitempty"`                                                        // Deprecated: 延迟执行时间
	RateLimits      *pb.PerfRateLimits        `protobuf:"bytes,21,opt,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"`                            // 压测相关的限流配置（包括：认证接口、心跳接口）
	Services        []*pb.PerfServiceMetaData `protobuf:"bytes,22,rep,name=services,proto3" json:"services,omitempty"`                                                  // 压测相关的服务的元数据
	Rules           []*pb.PerfStopRule        `protobuf:"bytes,23,rep,name=rules,proto3" json:"rules,omitempty"`                                                        // 压测停止规则列表
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PerfPlanMetaData) Reset() {
	*x = PerfPlanMetaData{}
	mi := &file_manager_component_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlanMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlanMetaData) ProtoMessage() {}

func (x *PerfPlanMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlanMetaData.ProtoReflect.Descriptor instead.
func (*PerfPlanMetaData) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{22}
}

func (x *PerfPlanMetaData) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *PerfPlanMetaData) GetTargetEnv() pb.TargetEnvironment {
	if x != nil {
		return x.TargetEnv
	}
	return pb.TargetEnvironment(0)
}

func (x *PerfPlanMetaData) GetProtobufConfigs() []*pb.ProtobufConfig {
	if x != nil {
		return x.ProtobufConfigs
	}
	return nil
}

func (x *PerfPlanMetaData) GetGeneralConfig() *pb.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *PerfPlanMetaData) GetAccountConfig() *pb.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *PerfPlanMetaData) GetTargetMaxRps() int64 {
	if x != nil {
		return x.TargetMaxRps
	}
	return 0
}

func (x *PerfPlanMetaData) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *PerfPlanMetaData) GetKeepalive() *pb.PerfKeepalive {
	if x != nil {
		return x.Keepalive
	}
	return nil
}

func (x *PerfPlanMetaData) GetDelay() uint32 {
	if x != nil {
		return x.Delay
	}
	return 0
}

func (x *PerfPlanMetaData) GetRateLimits() *pb.PerfRateLimits {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *PerfPlanMetaData) GetServices() []*pb.PerfServiceMetaData {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *PerfPlanMetaData) GetRules() []*pb.PerfStopRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

// PerfPlanComponent 压测计划组件（注：把压测计划抽象为一个组件）
type PerfPlanComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`           // 项目ID
	PlanId        string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                    // 计划ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                     // 计划名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                       // 计划描述
	Type          pb.TriggerMode         `protobuf:"varint,13,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`            // 计划类型（触发模式）
	MetaData      *PerfPlanMetaData      `protobuf:"bytes,14,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`             // 元数据
	State         CommonState            `protobuf:"varint,21,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`         // 计划状态
	MaintainedBy  string                 `protobuf:"bytes,22,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"` // 维护者的用户ID
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`          // 创建者的用户ID
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`          // 更新者的用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfPlanComponent) Reset() {
	*x = PerfPlanComponent{}
	mi := &file_manager_component_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlanComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlanComponent) ProtoMessage() {}

func (x *PerfPlanComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlanComponent.ProtoReflect.Descriptor instead.
func (*PerfPlanComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{23}
}

func (x *PerfPlanComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfPlanComponent) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PerfPlanComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfPlanComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfPlanComponent) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *PerfPlanComponent) GetMetaData() *PerfPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *PerfPlanComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *PerfPlanComponent) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *PerfPlanComponent) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfPlanComponent) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

// StabilityPlanMetaData 稳定性测试计划元数据
type StabilityPlanMetaData struct {
	state           protoimpl.MessageState     `protogen:"open.v1"`
	AccountConfig   *pb.AccountConfig          `protobuf:"bytes,1,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                         // 池账号配置
	LarkChats       []*pb.LarkChat             `protobuf:"bytes,2,rep,name=lark_chats,json=larkChats,proto3" json:"lark_chats,omitempty"`                                     // 飞书群组列表
	DeviceType      pb.DeviceType              `protobuf:"varint,11,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`         // 设备类型（真机、云手机）
	PlatformType    pb.PlatformType            `protobuf:"varint,12,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、iOS）
	Devices         *pb.StabilityCustomDevices `protobuf:"bytes,13,opt,name=devices,proto3" json:"devices,omitempty"`                                                         // 自定义设备
	PackageName     string                     `protobuf:"bytes,21,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                              // App包名
	AppDownloadLink string                     `protobuf:"bytes,22,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                // App下载地址
	Activities      []string                   `protobuf:"bytes,23,rep,name=activities,proto3" json:"activities,omitempty"`                                                   // 指定的Activity列表
	CustomScript    *pb.StabilityCustomScript  `protobuf:"bytes,24,opt,name=custom_script,json=customScript,proto3" json:"custom_script,omitempty"`                           // 自定义脚本
	Duration        uint32                     `protobuf:"varint,31,opt,name=duration,proto3" json:"duration,omitempty"`                                                      // 执行时长
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StabilityPlanMetaData) Reset() {
	*x = StabilityPlanMetaData{}
	mi := &file_manager_component_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityPlanMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityPlanMetaData) ProtoMessage() {}

func (x *StabilityPlanMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityPlanMetaData.ProtoReflect.Descriptor instead.
func (*StabilityPlanMetaData) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{24}
}

func (x *StabilityPlanMetaData) GetAccountConfig() *pb.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *StabilityPlanMetaData) GetLarkChats() []*pb.LarkChat {
	if x != nil {
		return x.LarkChats
	}
	return nil
}

func (x *StabilityPlanMetaData) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *StabilityPlanMetaData) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *StabilityPlanMetaData) GetDevices() *pb.StabilityCustomDevices {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *StabilityPlanMetaData) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *StabilityPlanMetaData) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *StabilityPlanMetaData) GetActivities() []string {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *StabilityPlanMetaData) GetCustomScript() *pb.StabilityCustomScript {
	if x != nil {
		return x.CustomScript
	}
	return nil
}

func (x *StabilityPlanMetaData) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

// StabilityPlanComponent 稳定性测试计划组件（注：把稳定测试性计划抽象为一个组件）
type StabilityPlanComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`           // 项目ID
	PlanId        string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                    // 计划ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                     // 计划名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                       // 计划描述
	Type          pb.TriggerMode         `protobuf:"varint,13,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`            // 计划类型（触发模式）
	MetaData      *StabilityPlanMetaData `protobuf:"bytes,14,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`             // 元数据
	State         CommonState            `protobuf:"varint,21,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`         // 计划状态
	MaintainedBy  string                 `protobuf:"bytes,22,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"` // 维护者的用户ID
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`          // 创建者的用户ID
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`          // 更新者的用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityPlanComponent) Reset() {
	*x = StabilityPlanComponent{}
	mi := &file_manager_component_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityPlanComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityPlanComponent) ProtoMessage() {}

func (x *StabilityPlanComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityPlanComponent.ProtoReflect.Descriptor instead.
func (*StabilityPlanComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{25}
}

func (x *StabilityPlanComponent) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StabilityPlanComponent) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *StabilityPlanComponent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StabilityPlanComponent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *StabilityPlanComponent) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *StabilityPlanComponent) GetMetaData() *StabilityPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *StabilityPlanComponent) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *StabilityPlanComponent) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *StabilityPlanComponent) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *StabilityPlanComponent) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

// StartComponent START组件
type StartComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartComponent) Reset() {
	*x = StartComponent{}
	mi := &file_manager_component_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartComponent) ProtoMessage() {}

func (x *StartComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartComponent.ProtoReflect.Descriptor instead.
func (*StartComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{26}
}

// EndComponent END组件
type EndComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndComponent) Reset() {
	*x = EndComponent{}
	mi := &file_manager_component_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndComponent) ProtoMessage() {}

func (x *EndComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndComponent.ProtoReflect.Descriptor instead.
func (*EndComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{27}
}

// SetupComponent 前置组件 - 框
type SetupComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Imports       []*Import              `protobuf:"bytes,1,rep,name=imports,proto3" json:"imports,omitempty"`
	Exports       []*Export              `protobuf:"bytes,2,rep,name=exports,proto3" json:"exports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetupComponent) Reset() {
	*x = SetupComponent{}
	mi := &file_manager_component_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetupComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetupComponent) ProtoMessage() {}

func (x *SetupComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetupComponent.ProtoReflect.Descriptor instead.
func (*SetupComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{28}
}

func (x *SetupComponent) GetImports() []*Import {
	if x != nil {
		return x.Imports
	}
	return nil
}

func (x *SetupComponent) GetExports() []*Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

// TeardownComponent 后置组件 - 框
type TeardownComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Imports       []*Import              `protobuf:"bytes,1,rep,name=imports,proto3" json:"imports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TeardownComponent) Reset() {
	*x = TeardownComponent{}
	mi := &file_manager_component_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TeardownComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeardownComponent) ProtoMessage() {}

func (x *TeardownComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeardownComponent.ProtoReflect.Descriptor instead.
func (*TeardownComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{29}
}

func (x *TeardownComponent) GetImports() []*Import {
	if x != nil {
		return x.Imports
	}
	return nil
}

// BusinessSingleComponent 业务单请求 - 框
type BusinessSingleComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Imports       []*Import              `protobuf:"bytes,1,rep,name=imports,proto3" json:"imports,omitempty"`
	Exports       []*Export              `protobuf:"bytes,2,rep,name=exports,proto3" json:"exports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BusinessSingleComponent) Reset() {
	*x = BusinessSingleComponent{}
	mi := &file_manager_component_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusinessSingleComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSingleComponent) ProtoMessage() {}

func (x *BusinessSingleComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSingleComponent.ProtoReflect.Descriptor instead.
func (*BusinessSingleComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{30}
}

func (x *BusinessSingleComponent) GetImports() []*Import {
	if x != nil {
		return x.Imports
	}
	return nil
}

func (x *BusinessSingleComponent) GetExports() []*Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

// BusinessGroupComponent 业务行为组 - 框
type BusinessGroupComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Imports       []*Import              `protobuf:"bytes,1,rep,name=imports,proto3" json:"imports,omitempty"`
	Exports       []*Export              `protobuf:"bytes,2,rep,name=exports,proto3" json:"exports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BusinessGroupComponent) Reset() {
	*x = BusinessGroupComponent{}
	mi := &file_manager_component_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusinessGroupComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessGroupComponent) ProtoMessage() {}

func (x *BusinessGroupComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessGroupComponent.ProtoReflect.Descriptor instead.
func (*BusinessGroupComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{31}
}

func (x *BusinessGroupComponent) GetImports() []*Import {
	if x != nil {
		return x.Imports
	}
	return nil
}

func (x *BusinessGroupComponent) GetExports() []*Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

// LoopComponent 循环控制组件 - 框
type LoopComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          LoopComponent_Type     `protobuf:"varint,1,opt,name=type,proto3,enum=manager.LoopComponent_Type" json:"type,omitempty"` // 循环类型
	Timeout       int64                  `protobuf:"varint,2,opt,name=timeout,proto3" json:"timeout,omitempty"`                           // 循环超时时间
	For           *LoopComponent_For     `protobuf:"bytes,3,opt,name=for,proto3" json:"for,omitempty"`
	ForEach       *LoopComponent_ForEach `protobuf:"bytes,4,opt,name=for_each,json=forEach,proto3" json:"for_each,omitempty"`
	While         *LoopComponent_While   `protobuf:"bytes,5,opt,name=while,proto3" json:"while,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoopComponent) Reset() {
	*x = LoopComponent{}
	mi := &file_manager_component_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoopComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoopComponent) ProtoMessage() {}

func (x *LoopComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoopComponent.ProtoReflect.Descriptor instead.
func (*LoopComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{32}
}

func (x *LoopComponent) GetType() LoopComponent_Type {
	if x != nil {
		return x.Type
	}
	return LoopComponent_FOR
}

func (x *LoopComponent) GetTimeout() int64 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *LoopComponent) GetFor() *LoopComponent_For {
	if x != nil {
		return x.For
	}
	return nil
}

func (x *LoopComponent) GetForEach() *LoopComponent_ForEach {
	if x != nil {
		return x.ForEach
	}
	return nil
}

func (x *LoopComponent) GetWhile() *LoopComponent_While {
	if x != nil {
		return x.While
	}
	return nil
}

// HttpRequestComponent HTTP请求组件
type HttpRequestComponent struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	Url           string                               `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`       // HTTP请求Url（注：由于这里可能使用到变量，因此不能使用`uri_ref`进行验证）
	Method        string                               `protobuf:"bytes,2,opt,name=method,proto3" json:"method,omitempty"` // HTTP请求Method
	Authorization *HttpRequestComponent_Authorization  `protobuf:"bytes,3,opt,name=authorization,proto3" json:"authorization,omitempty"`
	Headers       []*HttpRequestComponent_KeyValueDesc `protobuf:"bytes,4,rep,name=headers,proto3" json:"headers,omitempty"`
	QueryParams   []*HttpRequestComponent_KeyValueDesc `protobuf:"bytes,5,rep,name=query_params,json=queryParams,proto3" json:"query_params,omitempty"`
	Body          *HttpRequestComponent_Body           `protobuf:"bytes,6,opt,name=body,proto3" json:"body,omitempty"`
	Timeout       *HttpRequestComponent_Timeout        `protobuf:"bytes,7,opt,name=timeout,proto3" json:"timeout,omitempty"`
	Assertions    []*HttpRequestComponent_Assertion    `protobuf:"bytes,8,rep,name=assertions,proto3" json:"assertions,omitempty"`
	Imports       []*Import                            `protobuf:"bytes,9,rep,name=imports,proto3" json:"imports,omitempty"`
	Exports       []*HttpRequestComponent_Export       `protobuf:"bytes,10,rep,name=exports,proto3" json:"exports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent) Reset() {
	*x = HttpRequestComponent{}
	mi := &file_manager_component_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent) ProtoMessage() {}

func (x *HttpRequestComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33}
}

func (x *HttpRequestComponent) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *HttpRequestComponent) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *HttpRequestComponent) GetAuthorization() *HttpRequestComponent_Authorization {
	if x != nil {
		return x.Authorization
	}
	return nil
}

func (x *HttpRequestComponent) GetHeaders() []*HttpRequestComponent_KeyValueDesc {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HttpRequestComponent) GetQueryParams() []*HttpRequestComponent_KeyValueDesc {
	if x != nil {
		return x.QueryParams
	}
	return nil
}

func (x *HttpRequestComponent) GetBody() *HttpRequestComponent_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *HttpRequestComponent) GetTimeout() *HttpRequestComponent_Timeout {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *HttpRequestComponent) GetAssertions() []*HttpRequestComponent_Assertion {
	if x != nil {
		return x.Assertions
	}
	return nil
}

func (x *HttpRequestComponent) GetImports() []*Import {
	if x != nil {
		return x.Imports
	}
	return nil
}

func (x *HttpRequestComponent) GetExports() []*HttpRequestComponent_Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

// ReferenceComponent 引用组件组组件（业务单请求组件组、业务行为组组件组）
type ReferenceComponent struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ReferenceId          string                 `protobuf:"bytes,1,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`                              // 引用ID，即被引用的组件组的ID
	Version              string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`                                                         // 引用版本，即被引用的组件组的版本
	Imports              []*Import              `protobuf:"bytes,3,rep,name=imports,proto3" json:"imports,omitempty"`                                                         // 入参列表
	Exports              []*Export              `protobuf:"bytes,4,rep,name=exports,proto3" json:"exports,omitempty"`                                                         // 出参列表
	ReferenceComponentId string                 `protobuf:"bytes,5,opt,name=reference_component_id,json=referenceComponentId,proto3" json:"reference_component_id,omitempty"` // 引用组件组中的组件ID，即被引用的组件组中实际被执行的组件ID
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ReferenceComponent) Reset() {
	*x = ReferenceComponent{}
	mi := &file_manager_component_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReferenceComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReferenceComponent) ProtoMessage() {}

func (x *ReferenceComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReferenceComponent.ProtoReflect.Descriptor instead.
func (*ReferenceComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{34}
}

func (x *ReferenceComponent) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *ReferenceComponent) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ReferenceComponent) GetImports() []*Import {
	if x != nil {
		return x.Imports
	}
	return nil
}

func (x *ReferenceComponent) GetExports() []*Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

func (x *ReferenceComponent) GetReferenceComponentId() string {
	if x != nil {
		return x.ReferenceComponentId
	}
	return ""
}

// ConditionComponent 条件控制组件
type ConditionComponent struct {
	state         protoimpl.MessageState              `protogen:"open.v1"`
	Type          ConditionType                       `protobuf:"varint,1,opt,name=type,proto3,enum=manager.ConditionType" json:"type,omitempty"`
	Single        *ConditionComponent_SingleCondition `protobuf:"bytes,2,opt,name=single,proto3" json:"single,omitempty"` // 单条件
	Group         *ConditionComponent_GroupCondition  `protobuf:"bytes,3,opt,name=group,proto3" json:"group,omitempty"`   // 条件组
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConditionComponent) Reset() {
	*x = ConditionComponent{}
	mi := &file_manager_component_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConditionComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionComponent) ProtoMessage() {}

func (x *ConditionComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionComponent.ProtoReflect.Descriptor instead.
func (*ConditionComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{35}
}

func (x *ConditionComponent) GetType() ConditionType {
	if x != nil {
		return x.Type
	}
	return ConditionType_SINGLE
}

func (x *ConditionComponent) GetSingle() *ConditionComponent_SingleCondition {
	if x != nil {
		return x.Single
	}
	return nil
}

func (x *ConditionComponent) GetGroup() *ConditionComponent_GroupCondition {
	if x != nil {
		return x.Group
	}
	return nil
}

// WaitComponent 等待控制组件
type WaitComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          WaitComponent_Type     `protobuf:"varint,1,opt,name=type,proto3,enum=manager.WaitComponent_Type" json:"type,omitempty"` // 等待类型
	Sleep         *WaitComponent_Sleep   `protobuf:"bytes,2,opt,name=sleep,proto3" json:"sleep,omitempty"`
	Manual        *WaitComponent_Manual  `protobuf:"bytes,3,opt,name=manual,proto3" json:"manual,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WaitComponent) Reset() {
	*x = WaitComponent{}
	mi := &file_manager_component_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WaitComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitComponent) ProtoMessage() {}

func (x *WaitComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitComponent.ProtoReflect.Descriptor instead.
func (*WaitComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{36}
}

func (x *WaitComponent) GetType() WaitComponent_Type {
	if x != nil {
		return x.Type
	}
	return WaitComponent_SLEEP
}

func (x *WaitComponent) GetSleep() *WaitComponent_Sleep {
	if x != nil {
		return x.Sleep
	}
	return nil
}

func (x *WaitComponent) GetManual() *WaitComponent_Manual {
	if x != nil {
		return x.Manual
	}
	return nil
}

// AssertComponent 断言判断组件
type AssertComponent struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Assertions    []*AssertComponent_Assertion `protobuf:"bytes,1,rep,name=assertions,proto3" json:"assertions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssertComponent) Reset() {
	*x = AssertComponent{}
	mi := &file_manager_component_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssertComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssertComponent) ProtoMessage() {}

func (x *AssertComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssertComponent.ProtoReflect.Descriptor instead.
func (*AssertComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{37}
}

func (x *AssertComponent) GetAssertions() []*AssertComponent_Assertion {
	if x != nil {
		return x.Assertions
	}
	return nil
}

// PoolAccountComponent 池账号组件
type PoolAccountComponent struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	ProductType   int64                              `protobuf:"varint,1,opt,name=product_type,json=productType,proto3" json:"product_type,omitempty"`
	Condition     *PoolAccountComponent_Condition    `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`
	Exports       []*PoolAccountComponent_KeyValPair `protobuf:"bytes,3,rep,name=exports,proto3" json:"exports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PoolAccountComponent) Reset() {
	*x = PoolAccountComponent{}
	mi := &file_manager_component_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PoolAccountComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolAccountComponent) ProtoMessage() {}

func (x *PoolAccountComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolAccountComponent.ProtoReflect.Descriptor instead.
func (*PoolAccountComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{38}
}

func (x *PoolAccountComponent) GetProductType() int64 {
	if x != nil {
		return x.ProductType
	}
	return 0
}

func (x *PoolAccountComponent) GetCondition() *PoolAccountComponent_Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *PoolAccountComponent) GetExports() []*PoolAccountComponent_KeyValPair {
	if x != nil {
		return x.Exports
	}
	return nil
}

// DataProcessingComponent 数据处理组件
type DataProcessingComponent struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Processes     []*DataProcessingComponent_Process `protobuf:"bytes,1,rep,name=processes,proto3" json:"processes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataProcessingComponent) Reset() {
	*x = DataProcessingComponent{}
	mi := &file_manager_component_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataProcessingComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataProcessingComponent) ProtoMessage() {}

func (x *DataProcessingComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataProcessingComponent.ProtoReflect.Descriptor instead.
func (*DataProcessingComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{39}
}

func (x *DataProcessingComponent) GetProcesses() []*DataProcessingComponent_Process {
	if x != nil {
		return x.Processes
	}
	return nil
}

// DataDrivenComponent 数据驱动组件
type DataDrivenComponent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataDrivenComponent) Reset() {
	*x = DataDrivenComponent{}
	mi := &file_manager_component_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataDrivenComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataDrivenComponent) ProtoMessage() {}

func (x *DataDrivenComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataDrivenComponent.ProtoReflect.Descriptor instead.
func (*DataDrivenComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{40}
}

// SqlExecutionComponent SQL执行组件
type SqlExecutionComponent struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Sqls          []*SqlExecutionComponent_Sql `protobuf:"bytes,1,rep,name=sqls,proto3" json:"sqls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SqlExecutionComponent) Reset() {
	*x = SqlExecutionComponent{}
	mi := &file_manager_component_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SqlExecutionComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SqlExecutionComponent) ProtoMessage() {}

func (x *SqlExecutionComponent) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SqlExecutionComponent.ProtoReflect.Descriptor instead.
func (*SqlExecutionComponent) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{41}
}

func (x *SqlExecutionComponent) GetSqls() []*SqlExecutionComponent_Sql {
	if x != nil {
		return x.Sqls
	}
	return nil
}

type VariableFunction_Parameter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 参数名称
	Source        VariableSource         `protobuf:"varint,2,opt,name=source,proto3,enum=manager.VariableSource" json:"source,omitempty"`
	Manual        *VariableManual        `protobuf:"bytes,3,opt,name=manual,proto3" json:"manual,omitempty"`
	Export        *VariableExport        `protobuf:"bytes,4,opt,name=export,proto3" json:"export,omitempty"`
	Environment   *VariableEnvironment   `protobuf:"bytes,5,opt,name=environment,proto3" json:"environment,omitempty"`
	Function      *VariableFunction      `protobuf:"bytes,6,opt,name=function,proto3" json:"function,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VariableFunction_Parameter) Reset() {
	*x = VariableFunction_Parameter{}
	mi := &file_manager_component_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VariableFunction_Parameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VariableFunction_Parameter) ProtoMessage() {}

func (x *VariableFunction_Parameter) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VariableFunction_Parameter.ProtoReflect.Descriptor instead.
func (*VariableFunction_Parameter) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{3, 0}
}

func (x *VariableFunction_Parameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VariableFunction_Parameter) GetSource() VariableSource {
	if x != nil {
		return x.Source
	}
	return VariableSource_MANUAL
}

func (x *VariableFunction_Parameter) GetManual() *VariableManual {
	if x != nil {
		return x.Manual
	}
	return nil
}

func (x *VariableFunction_Parameter) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *VariableFunction_Parameter) GetEnvironment() *VariableEnvironment {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *VariableFunction_Parameter) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

type LoopComponent_For struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Times         int64                  `protobuf:"varint,1,opt,name=times,proto3" json:"times,omitempty"` // 循环次数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoopComponent_For) Reset() {
	*x = LoopComponent_For{}
	mi := &file_manager_component_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoopComponent_For) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoopComponent_For) ProtoMessage() {}

func (x *LoopComponent_For) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoopComponent_For.ProtoReflect.Descriptor instead.
func (*LoopComponent_For) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{32, 0}
}

func (x *LoopComponent_For) GetTimes() int64 {
	if x != nil {
		return x.Times
	}
	return 0
}

type LoopComponent_ForEach struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 循环对象变量名称
	Source        VariableSource         `protobuf:"varint,2,opt,name=source,proto3,enum=manager.VariableSource" json:"source,omitempty"`
	Manual        *VariableManual        `protobuf:"bytes,3,opt,name=manual,proto3" json:"manual,omitempty"`
	Export        *VariableExport        `protobuf:"bytes,4,opt,name=export,proto3" json:"export,omitempty"`
	Environment   *VariableEnvironment   `protobuf:"bytes,5,opt,name=environment,proto3" json:"environment,omitempty"`
	Function      *VariableFunction      `protobuf:"bytes,6,opt,name=function,proto3" json:"function,omitempty"` // 为了兼容旧数据，这里不能做`validate`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoopComponent_ForEach) Reset() {
	*x = LoopComponent_ForEach{}
	mi := &file_manager_component_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoopComponent_ForEach) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoopComponent_ForEach) ProtoMessage() {}

func (x *LoopComponent_ForEach) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoopComponent_ForEach.ProtoReflect.Descriptor instead.
func (*LoopComponent_ForEach) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{32, 1}
}

func (x *LoopComponent_ForEach) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LoopComponent_ForEach) GetSource() VariableSource {
	if x != nil {
		return x.Source
	}
	return VariableSource_MANUAL
}

func (x *LoopComponent_ForEach) GetManual() *VariableManual {
	if x != nil {
		return x.Manual
	}
	return nil
}

func (x *LoopComponent_ForEach) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *LoopComponent_ForEach) GetEnvironment() *VariableEnvironment {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *LoopComponent_ForEach) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

type LoopComponent_While struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	Type          ConditionType                        `protobuf:"varint,1,opt,name=type,proto3,enum=manager.ConditionType" json:"type,omitempty"`
	Single        *LoopComponent_While_SingleCondition `protobuf:"bytes,2,opt,name=single,proto3" json:"single,omitempty"` // 单条件
	Group         *LoopComponent_While_GroupCondition  `protobuf:"bytes,3,opt,name=group,proto3" json:"group,omitempty"`   // 条件组
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoopComponent_While) Reset() {
	*x = LoopComponent_While{}
	mi := &file_manager_component_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoopComponent_While) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoopComponent_While) ProtoMessage() {}

func (x *LoopComponent_While) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoopComponent_While.ProtoReflect.Descriptor instead.
func (*LoopComponent_While) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{32, 2}
}

func (x *LoopComponent_While) GetType() ConditionType {
	if x != nil {
		return x.Type
	}
	return ConditionType_SINGLE
}

func (x *LoopComponent_While) GetSingle() *LoopComponent_While_SingleCondition {
	if x != nil {
		return x.Single
	}
	return nil
}

func (x *LoopComponent_While) GetGroup() *LoopComponent_While_GroupCondition {
	if x != nil {
		return x.Group
	}
	return nil
}

type LoopComponent_While_SingleCondition struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	Left          *LoopComponent_While_SingleCondition_Value `protobuf:"bytes,1,opt,name=left,proto3" json:"left,omitempty"`       // 左值
	Compare       string                                     `protobuf:"bytes,2,opt,name=compare,proto3" json:"compare,omitempty"` // 比较方式
	Right         *LoopComponent_While_SingleCondition_Value `protobuf:"bytes,3,opt,name=right,proto3" json:"right,omitempty"`     // 右值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoopComponent_While_SingleCondition) Reset() {
	*x = LoopComponent_While_SingleCondition{}
	mi := &file_manager_component_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoopComponent_While_SingleCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoopComponent_While_SingleCondition) ProtoMessage() {}

func (x *LoopComponent_While_SingleCondition) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoopComponent_While_SingleCondition.ProtoReflect.Descriptor instead.
func (*LoopComponent_While_SingleCondition) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{32, 2, 0}
}

func (x *LoopComponent_While_SingleCondition) GetLeft() *LoopComponent_While_SingleCondition_Value {
	if x != nil {
		return x.Left
	}
	return nil
}

func (x *LoopComponent_While_SingleCondition) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *LoopComponent_While_SingleCondition) GetRight() *LoopComponent_While_SingleCondition_Value {
	if x != nil {
		return x.Right
	}
	return nil
}

type LoopComponent_While_GroupCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relationship  Relationship           `protobuf:"varint,1,opt,name=relationship,proto3,enum=manager.Relationship" json:"relationship,omitempty"`
	Conditions    []*LoopComponent_While `protobuf:"bytes,2,rep,name=conditions,proto3" json:"conditions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoopComponent_While_GroupCondition) Reset() {
	*x = LoopComponent_While_GroupCondition{}
	mi := &file_manager_component_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoopComponent_While_GroupCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoopComponent_While_GroupCondition) ProtoMessage() {}

func (x *LoopComponent_While_GroupCondition) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoopComponent_While_GroupCondition.ProtoReflect.Descriptor instead.
func (*LoopComponent_While_GroupCondition) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{32, 2, 1}
}

func (x *LoopComponent_While_GroupCondition) GetRelationship() Relationship {
	if x != nil {
		return x.Relationship
	}
	return Relationship_AND
}

func (x *LoopComponent_While_GroupCondition) GetConditions() []*LoopComponent_While {
	if x != nil {
		return x.Conditions
	}
	return nil
}

type LoopComponent_While_SingleCondition_Value struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 条件判断[左|右]值变量名称
	Source        VariableSource         `protobuf:"varint,2,opt,name=source,proto3,enum=manager.VariableSource" json:"source,omitempty"`
	Manual        *VariableManual        `protobuf:"bytes,3,opt,name=manual,proto3" json:"manual,omitempty"`
	Export        *VariableExport        `protobuf:"bytes,4,opt,name=export,proto3" json:"export,omitempty"`
	Environment   *VariableEnvironment   `protobuf:"bytes,5,opt,name=environment,proto3" json:"environment,omitempty"`
	Function      *VariableFunction      `protobuf:"bytes,6,opt,name=function,proto3" json:"function,omitempty"` // 为了兼容旧数据，这里不能做`validate`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoopComponent_While_SingleCondition_Value) Reset() {
	*x = LoopComponent_While_SingleCondition_Value{}
	mi := &file_manager_component_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoopComponent_While_SingleCondition_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoopComponent_While_SingleCondition_Value) ProtoMessage() {}

func (x *LoopComponent_While_SingleCondition_Value) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoopComponent_While_SingleCondition_Value.ProtoReflect.Descriptor instead.
func (*LoopComponent_While_SingleCondition_Value) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{32, 2, 0, 0}
}

func (x *LoopComponent_While_SingleCondition_Value) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LoopComponent_While_SingleCondition_Value) GetSource() VariableSource {
	if x != nil {
		return x.Source
	}
	return VariableSource_MANUAL
}

func (x *LoopComponent_While_SingleCondition_Value) GetManual() *VariableManual {
	if x != nil {
		return x.Manual
	}
	return nil
}

func (x *LoopComponent_While_SingleCondition_Value) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *LoopComponent_While_SingleCondition_Value) GetEnvironment() *VariableEnvironment {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *LoopComponent_While_SingleCondition_Value) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

type HttpRequestComponent_Authorization struct {
	state         protoimpl.MessageState                          `protogen:"open.v1"`
	Type          HttpRequestComponent_Authorization_Type         `protobuf:"varint,1,opt,name=type,proto3,enum=manager.HttpRequestComponent_Authorization_Type" json:"type,omitempty"` // 授权类型
	ApiKey        *HttpRequestComponent_Authorization_ApiKey      `protobuf:"bytes,2,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	BearerToken   *HttpRequestComponent_Authorization_BearerToken `protobuf:"bytes,3,opt,name=bearer_token,json=bearerToken,proto3" json:"bearer_token,omitempty"`
	BasicAuth     *HttpRequestComponent_Authorization_BasicAuth   `protobuf:"bytes,4,opt,name=basic_auth,json=basicAuth,proto3" json:"basic_auth,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Authorization) Reset() {
	*x = HttpRequestComponent_Authorization{}
	mi := &file_manager_component_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Authorization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Authorization) ProtoMessage() {}

func (x *HttpRequestComponent_Authorization) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Authorization.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Authorization) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 0}
}

func (x *HttpRequestComponent_Authorization) GetType() HttpRequestComponent_Authorization_Type {
	if x != nil {
		return x.Type
	}
	return HttpRequestComponent_Authorization_NO_AUTH
}

func (x *HttpRequestComponent_Authorization) GetApiKey() *HttpRequestComponent_Authorization_ApiKey {
	if x != nil {
		return x.ApiKey
	}
	return nil
}

func (x *HttpRequestComponent_Authorization) GetBearerToken() *HttpRequestComponent_Authorization_BearerToken {
	if x != nil {
		return x.BearerToken
	}
	return nil
}

func (x *HttpRequestComponent_Authorization) GetBasicAuth() *HttpRequestComponent_Authorization_BasicAuth {
	if x != nil {
		return x.BasicAuth
	}
	return nil
}

type HttpRequestComponent_KeyValueDesc struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`                 // 键
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`             // 值
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_KeyValueDesc) Reset() {
	*x = HttpRequestComponent_KeyValueDesc{}
	mi := &file_manager_component_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_KeyValueDesc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_KeyValueDesc) ProtoMessage() {}

func (x *HttpRequestComponent_KeyValueDesc) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_KeyValueDesc.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_KeyValueDesc) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 1}
}

func (x *HttpRequestComponent_KeyValueDesc) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *HttpRequestComponent_KeyValueDesc) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *HttpRequestComponent_KeyValueDesc) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type HttpRequestComponent_Body struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	Type          HttpRequestComponent_Body_ContentType `protobuf:"varint,1,opt,name=type,proto3,enum=manager.HttpRequestComponent_Body_ContentType" json:"type,omitempty"`
	FormData      []*HttpRequestComponent_KeyValueDesc  `protobuf:"bytes,2,rep,name=form_data,json=formData,proto3" json:"form_data,omitempty"`
	Raw           string                                `protobuf:"bytes,3,opt,name=raw,proto3" json:"raw,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Body) Reset() {
	*x = HttpRequestComponent_Body{}
	mi := &file_manager_component_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Body) ProtoMessage() {}

func (x *HttpRequestComponent_Body) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Body.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Body) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 2}
}

func (x *HttpRequestComponent_Body) GetType() HttpRequestComponent_Body_ContentType {
	if x != nil {
		return x.Type
	}
	return HttpRequestComponent_Body_NONE
}

func (x *HttpRequestComponent_Body) GetFormData() []*HttpRequestComponent_KeyValueDesc {
	if x != nil {
		return x.FormData
	}
	return nil
}

func (x *HttpRequestComponent_Body) GetRaw() string {
	if x != nil {
		return x.Raw
	}
	return ""
}

type HttpRequestComponent_Timeout struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ConnectTimeout  int64                  `protobuf:"varint,1,opt,name=connect_timeout,json=connectTimeout,proto3" json:"connect_timeout,omitempty"`    // 连接超时时间
	RequestTimeout  int64                  `protobuf:"varint,2,opt,name=request_timeout,json=requestTimeout,proto3" json:"request_timeout,omitempty"`    // 请求超时时间
	ResponseTimeout int64                  `protobuf:"varint,3,opt,name=response_timeout,json=responseTimeout,proto3" json:"response_timeout,omitempty"` // 响应超时时间
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *HttpRequestComponent_Timeout) Reset() {
	*x = HttpRequestComponent_Timeout{}
	mi := &file_manager_component_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Timeout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Timeout) ProtoMessage() {}

func (x *HttpRequestComponent_Timeout) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Timeout.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Timeout) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 3}
}

func (x *HttpRequestComponent_Timeout) GetConnectTimeout() int64 {
	if x != nil {
		return x.ConnectTimeout
	}
	return 0
}

func (x *HttpRequestComponent_Timeout) GetRequestTimeout() int64 {
	if x != nil {
		return x.RequestTimeout
	}
	return 0
}

func (x *HttpRequestComponent_Timeout) GetResponseTimeout() int64 {
	if x != nil {
		return x.ResponseTimeout
	}
	return 0
}

type HttpRequestComponent_Assertion struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	Source        ResponseSource                             `protobuf:"varint,1,opt,name=source,proto3,enum=manager.ResponseSource" json:"source,omitempty"` // 来源
	Headers       *HttpRequestComponent_Assertion_Headers    `protobuf:"bytes,2,opt,name=headers,proto3" json:"headers,omitempty"`                            // 响应头
	Body          *HttpRequestComponent_Assertion_Body       `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`                                  // 响应体
	StatusCode    *HttpRequestComponent_Assertion_StatusCode `protobuf:"bytes,4,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`    // 响应状态码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Assertion) Reset() {
	*x = HttpRequestComponent_Assertion{}
	mi := &file_manager_component_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Assertion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Assertion) ProtoMessage() {}

func (x *HttpRequestComponent_Assertion) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Assertion.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Assertion) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 4}
}

func (x *HttpRequestComponent_Assertion) GetSource() ResponseSource {
	if x != nil {
		return x.Source
	}
	return ResponseSource_HEADERS
}

func (x *HttpRequestComponent_Assertion) GetHeaders() *HttpRequestComponent_Assertion_Headers {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HttpRequestComponent_Assertion) GetBody() *HttpRequestComponent_Assertion_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *HttpRequestComponent_Assertion) GetStatusCode() *HttpRequestComponent_Assertion_StatusCode {
	if x != nil {
		return x.StatusCode
	}
	return nil
}

type HttpRequestComponent_Export struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	Name          string                               `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                  // 出参变量名称
	Source        ResponseSource                       `protobuf:"varint,2,opt,name=source,proto3,enum=manager.ResponseSource" json:"source,omitempty"` // 来源
	Headers       *HttpRequestComponent_Export_Headers `protobuf:"bytes,3,opt,name=headers,proto3" json:"headers,omitempty"`                            // 响应头
	Body          *HttpRequestComponent_Export_Body    `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`                                  // 响应体
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Export) Reset() {
	*x = HttpRequestComponent_Export{}
	mi := &file_manager_component_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Export) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Export) ProtoMessage() {}

func (x *HttpRequestComponent_Export) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Export.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Export) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 5}
}

func (x *HttpRequestComponent_Export) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HttpRequestComponent_Export) GetSource() ResponseSource {
	if x != nil {
		return x.Source
	}
	return ResponseSource_HEADERS
}

func (x *HttpRequestComponent_Export) GetHeaders() *HttpRequestComponent_Export_Headers {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HttpRequestComponent_Export) GetBody() *HttpRequestComponent_Export_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

type HttpRequestComponent_Authorization_ApiKey struct {
	state         protoimpl.MessageState                          `protogen:"open.v1"`
	Key           string                                          `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value         string                                          `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	AddTo         HttpRequestComponent_Authorization_ApiKey_AddTo `protobuf:"varint,3,opt,name=add_to,json=addTo,proto3,enum=manager.HttpRequestComponent_Authorization_ApiKey_AddTo" json:"add_to,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Authorization_ApiKey) Reset() {
	*x = HttpRequestComponent_Authorization_ApiKey{}
	mi := &file_manager_component_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Authorization_ApiKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Authorization_ApiKey) ProtoMessage() {}

func (x *HttpRequestComponent_Authorization_ApiKey) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Authorization_ApiKey.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Authorization_ApiKey) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 0, 0}
}

func (x *HttpRequestComponent_Authorization_ApiKey) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *HttpRequestComponent_Authorization_ApiKey) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *HttpRequestComponent_Authorization_ApiKey) GetAddTo() HttpRequestComponent_Authorization_ApiKey_AddTo {
	if x != nil {
		return x.AddTo
	}
	return HttpRequestComponent_Authorization_ApiKey_HEADERS
}

type HttpRequestComponent_Authorization_BearerToken struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Authorization_BearerToken) Reset() {
	*x = HttpRequestComponent_Authorization_BearerToken{}
	mi := &file_manager_component_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Authorization_BearerToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Authorization_BearerToken) ProtoMessage() {}

func (x *HttpRequestComponent_Authorization_BearerToken) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Authorization_BearerToken.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Authorization_BearerToken) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 0, 1}
}

func (x *HttpRequestComponent_Authorization_BearerToken) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type HttpRequestComponent_Authorization_BasicAuth struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Authorization_BasicAuth) Reset() {
	*x = HttpRequestComponent_Authorization_BasicAuth{}
	mi := &file_manager_component_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Authorization_BasicAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Authorization_BasicAuth) ProtoMessage() {}

func (x *HttpRequestComponent_Authorization_BasicAuth) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Authorization_BasicAuth.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Authorization_BasicAuth) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 0, 2}
}

func (x *HttpRequestComponent_Authorization_BasicAuth) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *HttpRequestComponent_Authorization_BasicAuth) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type HttpRequestComponent_Assertion_Headers struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`               // 响应头的键
	Compare       string                 `protobuf:"bytes,2,opt,name=compare,proto3" json:"compare,omitempty"`       // 比较方式
	Expression    string                 `protobuf:"bytes,3,opt,name=expression,proto3" json:"expression,omitempty"` // 期望值或正则表达式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Assertion_Headers) Reset() {
	*x = HttpRequestComponent_Assertion_Headers{}
	mi := &file_manager_component_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Assertion_Headers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Assertion_Headers) ProtoMessage() {}

func (x *HttpRequestComponent_Assertion_Headers) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Assertion_Headers.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Assertion_Headers) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 4, 0}
}

func (x *HttpRequestComponent_Assertion_Headers) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *HttpRequestComponent_Assertion_Headers) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *HttpRequestComponent_Assertion_Headers) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

type HttpRequestComponent_Assertion_Body struct {
	state         protoimpl.MessageState                        `protogen:"open.v1"`
	Type          HttpRequestComponent_Assertion_Body_Type      `protobuf:"varint,1,opt,name=type,proto3,enum=manager.HttpRequestComponent_Assertion_Body_Type" json:"type,omitempty"` // 提取类型
	Jmespath      *HttpRequestComponent_Assertion_Body_JMESPath `protobuf:"bytes,2,opt,name=jmespath,proto3" json:"jmespath,omitempty"`
	Regex         *HttpRequestComponent_Assertion_Body_Regex    `protobuf:"bytes,3,opt,name=regex,proto3" json:"regex,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Assertion_Body) Reset() {
	*x = HttpRequestComponent_Assertion_Body{}
	mi := &file_manager_component_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Assertion_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Assertion_Body) ProtoMessage() {}

func (x *HttpRequestComponent_Assertion_Body) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Assertion_Body.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Assertion_Body) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 4, 1}
}

func (x *HttpRequestComponent_Assertion_Body) GetType() HttpRequestComponent_Assertion_Body_Type {
	if x != nil {
		return x.Type
	}
	return HttpRequestComponent_Assertion_Body_JMESPATH
}

func (x *HttpRequestComponent_Assertion_Body) GetJmespath() *HttpRequestComponent_Assertion_Body_JMESPath {
	if x != nil {
		return x.Jmespath
	}
	return nil
}

func (x *HttpRequestComponent_Assertion_Body) GetRegex() *HttpRequestComponent_Assertion_Body_Regex {
	if x != nil {
		return x.Regex
	}
	return nil
}

type HttpRequestComponent_Assertion_StatusCode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Compare       string                 `protobuf:"bytes,1,opt,name=compare,proto3" json:"compare,omitempty"`         // 比较方式
	Expectation   string                 `protobuf:"bytes,2,opt,name=expectation,proto3" json:"expectation,omitempty"` // 期望值或正则表达式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Assertion_StatusCode) Reset() {
	*x = HttpRequestComponent_Assertion_StatusCode{}
	mi := &file_manager_component_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Assertion_StatusCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Assertion_StatusCode) ProtoMessage() {}

func (x *HttpRequestComponent_Assertion_StatusCode) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Assertion_StatusCode.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Assertion_StatusCode) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 4, 2}
}

func (x *HttpRequestComponent_Assertion_StatusCode) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *HttpRequestComponent_Assertion_StatusCode) GetExpectation() string {
	if x != nil {
		return x.Expectation
	}
	return ""
}

type HttpRequestComponent_Assertion_Body_JMESPath struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Expression    string                 `protobuf:"bytes,1,opt,name=expression,proto3" json:"expression,omitempty"`   // JMESPath表达式
	Compare       string                 `protobuf:"bytes,2,opt,name=compare,proto3" json:"compare,omitempty"`         // 比较方式
	Expectation   string                 `protobuf:"bytes,3,opt,name=expectation,proto3" json:"expectation,omitempty"` // 期望值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Assertion_Body_JMESPath) Reset() {
	*x = HttpRequestComponent_Assertion_Body_JMESPath{}
	mi := &file_manager_component_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Assertion_Body_JMESPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Assertion_Body_JMESPath) ProtoMessage() {}

func (x *HttpRequestComponent_Assertion_Body_JMESPath) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Assertion_Body_JMESPath.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Assertion_Body_JMESPath) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 4, 1, 0}
}

func (x *HttpRequestComponent_Assertion_Body_JMESPath) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

func (x *HttpRequestComponent_Assertion_Body_JMESPath) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *HttpRequestComponent_Assertion_Body_JMESPath) GetExpectation() string {
	if x != nil {
		return x.Expectation
	}
	return ""
}

type HttpRequestComponent_Assertion_Body_Regex struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Expression    string                 `protobuf:"bytes,1,opt,name=expression,proto3" json:"expression,omitempty"` // Regex表达式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Assertion_Body_Regex) Reset() {
	*x = HttpRequestComponent_Assertion_Body_Regex{}
	mi := &file_manager_component_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Assertion_Body_Regex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Assertion_Body_Regex) ProtoMessage() {}

func (x *HttpRequestComponent_Assertion_Body_Regex) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Assertion_Body_Regex.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Assertion_Body_Regex) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 4, 1, 1}
}

func (x *HttpRequestComponent_Assertion_Body_Regex) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

type HttpRequestComponent_Export_Headers struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"` // 响应头的键
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Export_Headers) Reset() {
	*x = HttpRequestComponent_Export_Headers{}
	mi := &file_manager_component_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Export_Headers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Export_Headers) ProtoMessage() {}

func (x *HttpRequestComponent_Export_Headers) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Export_Headers.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Export_Headers) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 5, 0}
}

func (x *HttpRequestComponent_Export_Headers) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type HttpRequestComponent_Export_Body struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	Type          HttpRequestComponent_Export_Body_Type `protobuf:"varint,1,opt,name=type,proto3,enum=manager.HttpRequestComponent_Export_Body_Type" json:"type,omitempty"` // 提取类型
	Expression    string                                `protobuf:"bytes,2,opt,name=expression,proto3" json:"expression,omitempty"`                                         // 提取表达式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpRequestComponent_Export_Body) Reset() {
	*x = HttpRequestComponent_Export_Body{}
	mi := &file_manager_component_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpRequestComponent_Export_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestComponent_Export_Body) ProtoMessage() {}

func (x *HttpRequestComponent_Export_Body) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestComponent_Export_Body.ProtoReflect.Descriptor instead.
func (*HttpRequestComponent_Export_Body) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{33, 5, 1}
}

func (x *HttpRequestComponent_Export_Body) GetType() HttpRequestComponent_Export_Body_Type {
	if x != nil {
		return x.Type
	}
	return HttpRequestComponent_Export_Body_JMESPATH
}

func (x *HttpRequestComponent_Export_Body) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

type ConditionComponent_SingleCondition struct {
	state         protoimpl.MessageState                    `protogen:"open.v1"`
	Left          *ConditionComponent_SingleCondition_Value `protobuf:"bytes,1,opt,name=left,proto3" json:"left,omitempty"`       // 左值
	Compare       string                                    `protobuf:"bytes,2,opt,name=compare,proto3" json:"compare,omitempty"` // 比较方式
	Right         *ConditionComponent_SingleCondition_Value `protobuf:"bytes,3,opt,name=right,proto3" json:"right,omitempty"`     // 右值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConditionComponent_SingleCondition) Reset() {
	*x = ConditionComponent_SingleCondition{}
	mi := &file_manager_component_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConditionComponent_SingleCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionComponent_SingleCondition) ProtoMessage() {}

func (x *ConditionComponent_SingleCondition) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionComponent_SingleCondition.ProtoReflect.Descriptor instead.
func (*ConditionComponent_SingleCondition) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{35, 0}
}

func (x *ConditionComponent_SingleCondition) GetLeft() *ConditionComponent_SingleCondition_Value {
	if x != nil {
		return x.Left
	}
	return nil
}

func (x *ConditionComponent_SingleCondition) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *ConditionComponent_SingleCondition) GetRight() *ConditionComponent_SingleCondition_Value {
	if x != nil {
		return x.Right
	}
	return nil
}

type ConditionComponent_GroupCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relationship  Relationship           `protobuf:"varint,1,opt,name=relationship,proto3,enum=manager.Relationship" json:"relationship,omitempty"`
	Conditions    []*ConditionComponent  `protobuf:"bytes,2,rep,name=conditions,proto3" json:"conditions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConditionComponent_GroupCondition) Reset() {
	*x = ConditionComponent_GroupCondition{}
	mi := &file_manager_component_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConditionComponent_GroupCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionComponent_GroupCondition) ProtoMessage() {}

func (x *ConditionComponent_GroupCondition) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionComponent_GroupCondition.ProtoReflect.Descriptor instead.
func (*ConditionComponent_GroupCondition) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{35, 1}
}

func (x *ConditionComponent_GroupCondition) GetRelationship() Relationship {
	if x != nil {
		return x.Relationship
	}
	return Relationship_AND
}

func (x *ConditionComponent_GroupCondition) GetConditions() []*ConditionComponent {
	if x != nil {
		return x.Conditions
	}
	return nil
}

type ConditionComponent_SingleCondition_Value struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        VariableSource         `protobuf:"varint,1,opt,name=source,proto3,enum=manager.VariableSource" json:"source,omitempty"`
	Manual        *VariableManual        `protobuf:"bytes,2,opt,name=manual,proto3" json:"manual,omitempty"`
	Export        *VariableExport        `protobuf:"bytes,3,opt,name=export,proto3" json:"export,omitempty"`
	Environment   *VariableEnvironment   `protobuf:"bytes,4,opt,name=environment,proto3" json:"environment,omitempty"`
	Function      *VariableFunction      `protobuf:"bytes,5,opt,name=function,proto3" json:"function,omitempty"` // 为了兼容旧数据，这里不能做`validate`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConditionComponent_SingleCondition_Value) Reset() {
	*x = ConditionComponent_SingleCondition_Value{}
	mi := &file_manager_component_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConditionComponent_SingleCondition_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionComponent_SingleCondition_Value) ProtoMessage() {}

func (x *ConditionComponent_SingleCondition_Value) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionComponent_SingleCondition_Value.ProtoReflect.Descriptor instead.
func (*ConditionComponent_SingleCondition_Value) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{35, 0, 0}
}

func (x *ConditionComponent_SingleCondition_Value) GetSource() VariableSource {
	if x != nil {
		return x.Source
	}
	return VariableSource_MANUAL
}

func (x *ConditionComponent_SingleCondition_Value) GetManual() *VariableManual {
	if x != nil {
		return x.Manual
	}
	return nil
}

func (x *ConditionComponent_SingleCondition_Value) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *ConditionComponent_SingleCondition_Value) GetEnvironment() *VariableEnvironment {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *ConditionComponent_SingleCondition_Value) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

type WaitComponent_Sleep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Time          int64                  `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"` // 休眠时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WaitComponent_Sleep) Reset() {
	*x = WaitComponent_Sleep{}
	mi := &file_manager_component_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WaitComponent_Sleep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitComponent_Sleep) ProtoMessage() {}

func (x *WaitComponent_Sleep) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitComponent_Sleep.ProtoReflect.Descriptor instead.
func (*WaitComponent_Sleep) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{36, 0}
}

func (x *WaitComponent_Sleep) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type WaitComponent_Manual struct {
	state          protoimpl.MessageState      `protogen:"open.v1"`
	MaxWaitingTime int64                       `protobuf:"varint,1,opt,name=max_waiting_time,json=maxWaitingTime,proto3" json:"max_waiting_time,omitempty"`                                     // 最大等待人工处理时间
	DefaultAction  WaitComponent_Manual_Action `protobuf:"varint,2,opt,name=default_action,json=defaultAction,proto3,enum=manager.WaitComponent_Manual_Action" json:"default_action,omitempty"` // 默认处理方式（当等待时间达到最大值时都没有人工介入则按默认处理方式执行）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *WaitComponent_Manual) Reset() {
	*x = WaitComponent_Manual{}
	mi := &file_manager_component_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WaitComponent_Manual) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitComponent_Manual) ProtoMessage() {}

func (x *WaitComponent_Manual) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitComponent_Manual.ProtoReflect.Descriptor instead.
func (*WaitComponent_Manual) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{36, 1}
}

func (x *WaitComponent_Manual) GetMaxWaitingTime() int64 {
	if x != nil {
		return x.MaxWaitingTime
	}
	return 0
}

func (x *WaitComponent_Manual) GetDefaultAction() WaitComponent_Manual_Action {
	if x != nil {
		return x.DefaultAction
	}
	return WaitComponent_Manual_CONTINUE
}

type AssertComponent_Actual struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"` // 出参节点ID
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`                 // 节点的出参变量名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssertComponent_Actual) Reset() {
	*x = AssertComponent_Actual{}
	mi := &file_manager_component_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssertComponent_Actual) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssertComponent_Actual) ProtoMessage() {}

func (x *AssertComponent_Actual) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssertComponent_Actual.ProtoReflect.Descriptor instead.
func (*AssertComponent_Actual) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{37, 0}
}

func (x *AssertComponent_Actual) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *AssertComponent_Actual) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type AssertComponent_Expected struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        VariableSource         `protobuf:"varint,1,opt,name=source,proto3,enum=manager.VariableSource" json:"source,omitempty"` // 期望值来源
	Manual        *VariableManual        `protobuf:"bytes,2,opt,name=manual,proto3" json:"manual,omitempty"`
	Export        *VariableExport        `protobuf:"bytes,3,opt,name=export,proto3" json:"export,omitempty"`
	Environment   *VariableEnvironment   `protobuf:"bytes,4,opt,name=environment,proto3" json:"environment,omitempty"`
	Function      *VariableFunction      `protobuf:"bytes,5,opt,name=function,proto3" json:"function,omitempty"` // 为了兼容旧数据，这里不能做`validate`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssertComponent_Expected) Reset() {
	*x = AssertComponent_Expected{}
	mi := &file_manager_component_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssertComponent_Expected) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssertComponent_Expected) ProtoMessage() {}

func (x *AssertComponent_Expected) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssertComponent_Expected.ProtoReflect.Descriptor instead.
func (*AssertComponent_Expected) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{37, 1}
}

func (x *AssertComponent_Expected) GetSource() VariableSource {
	if x != nil {
		return x.Source
	}
	return VariableSource_MANUAL
}

func (x *AssertComponent_Expected) GetManual() *VariableManual {
	if x != nil {
		return x.Manual
	}
	return nil
}

func (x *AssertComponent_Expected) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *AssertComponent_Expected) GetEnvironment() *VariableEnvironment {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *AssertComponent_Expected) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

type AssertComponent_Assertion struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Actual        *AssertComponent_Actual   `protobuf:"bytes,1,opt,name=actual,proto3" json:"actual,omitempty"`     // 实际值
	Compare       string                    `protobuf:"bytes,2,opt,name=compare,proto3" json:"compare,omitempty"`   // 比较方式
	Expected      *AssertComponent_Expected `protobuf:"bytes,3,opt,name=expected,proto3" json:"expected,omitempty"` // 期望值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssertComponent_Assertion) Reset() {
	*x = AssertComponent_Assertion{}
	mi := &file_manager_component_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssertComponent_Assertion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssertComponent_Assertion) ProtoMessage() {}

func (x *AssertComponent_Assertion) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssertComponent_Assertion.ProtoReflect.Descriptor instead.
func (*AssertComponent_Assertion) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{37, 2}
}

func (x *AssertComponent_Assertion) GetActual() *AssertComponent_Actual {
	if x != nil {
		return x.Actual
	}
	return nil
}

func (x *AssertComponent_Assertion) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *AssertComponent_Assertion) GetExpected() *AssertComponent_Expected {
	if x != nil {
		return x.Expected
	}
	return nil
}

type PoolAccountComponent_SingleCondition struct {
	state         protoimpl.MessageState                        `protogen:"open.v1"`
	Field         string                                        `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Compare       string                                        `protobuf:"bytes,2,opt,name=compare,proto3" json:"compare,omitempty"`
	In            []string                                      `protobuf:"bytes,3,rep,name=in,proto3" json:"in,omitempty"`
	Between       *PoolAccountComponent_SingleCondition_Between `protobuf:"bytes,4,opt,name=between,proto3" json:"between,omitempty"`
	Other         *PoolAccountComponent_SingleCondition_Other   `protobuf:"bytes,5,opt,name=other,proto3" json:"other,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PoolAccountComponent_SingleCondition) Reset() {
	*x = PoolAccountComponent_SingleCondition{}
	mi := &file_manager_component_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PoolAccountComponent_SingleCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolAccountComponent_SingleCondition) ProtoMessage() {}

func (x *PoolAccountComponent_SingleCondition) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolAccountComponent_SingleCondition.ProtoReflect.Descriptor instead.
func (*PoolAccountComponent_SingleCondition) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{38, 0}
}

func (x *PoolAccountComponent_SingleCondition) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *PoolAccountComponent_SingleCondition) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *PoolAccountComponent_SingleCondition) GetIn() []string {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *PoolAccountComponent_SingleCondition) GetBetween() *PoolAccountComponent_SingleCondition_Between {
	if x != nil {
		return x.Between
	}
	return nil
}

func (x *PoolAccountComponent_SingleCondition) GetOther() *PoolAccountComponent_SingleCondition_Other {
	if x != nil {
		return x.Other
	}
	return nil
}

type PoolAccountComponent_GroupCondition struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Relationship  Relationship                      `protobuf:"varint,1,opt,name=relationship,proto3,enum=manager.Relationship" json:"relationship,omitempty"`
	Conditions    []*PoolAccountComponent_Condition `protobuf:"bytes,2,rep,name=conditions,proto3" json:"conditions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PoolAccountComponent_GroupCondition) Reset() {
	*x = PoolAccountComponent_GroupCondition{}
	mi := &file_manager_component_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PoolAccountComponent_GroupCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolAccountComponent_GroupCondition) ProtoMessage() {}

func (x *PoolAccountComponent_GroupCondition) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolAccountComponent_GroupCondition.ProtoReflect.Descriptor instead.
func (*PoolAccountComponent_GroupCondition) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{38, 1}
}

func (x *PoolAccountComponent_GroupCondition) GetRelationship() Relationship {
	if x != nil {
		return x.Relationship
	}
	return Relationship_AND
}

func (x *PoolAccountComponent_GroupCondition) GetConditions() []*PoolAccountComponent_Condition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

type PoolAccountComponent_Condition struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	Type          ConditionType                         `protobuf:"varint,1,opt,name=type,proto3,enum=manager.ConditionType" json:"type,omitempty"`
	Single        *PoolAccountComponent_SingleCondition `protobuf:"bytes,2,opt,name=single,proto3" json:"single,omitempty"`
	Group         *PoolAccountComponent_GroupCondition  `protobuf:"bytes,3,opt,name=group,proto3" json:"group,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PoolAccountComponent_Condition) Reset() {
	*x = PoolAccountComponent_Condition{}
	mi := &file_manager_component_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PoolAccountComponent_Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolAccountComponent_Condition) ProtoMessage() {}

func (x *PoolAccountComponent_Condition) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolAccountComponent_Condition.ProtoReflect.Descriptor instead.
func (*PoolAccountComponent_Condition) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{38, 2}
}

func (x *PoolAccountComponent_Condition) GetType() ConditionType {
	if x != nil {
		return x.Type
	}
	return ConditionType_SINGLE
}

func (x *PoolAccountComponent_Condition) GetSingle() *PoolAccountComponent_SingleCondition {
	if x != nil {
		return x.Single
	}
	return nil
}

func (x *PoolAccountComponent_Condition) GetGroup() *PoolAccountComponent_GroupCondition {
	if x != nil {
		return x.Group
	}
	return nil
}

type PoolAccountComponent_KeyValPair struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PoolAccountComponent_KeyValPair) Reset() {
	*x = PoolAccountComponent_KeyValPair{}
	mi := &file_manager_component_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PoolAccountComponent_KeyValPair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolAccountComponent_KeyValPair) ProtoMessage() {}

func (x *PoolAccountComponent_KeyValPair) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolAccountComponent_KeyValPair.ProtoReflect.Descriptor instead.
func (*PoolAccountComponent_KeyValPair) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{38, 3}
}

func (x *PoolAccountComponent_KeyValPair) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *PoolAccountComponent_KeyValPair) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type PoolAccountComponent_SingleCondition_Between struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Start         string                 `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PoolAccountComponent_SingleCondition_Between) Reset() {
	*x = PoolAccountComponent_SingleCondition_Between{}
	mi := &file_manager_component_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PoolAccountComponent_SingleCondition_Between) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolAccountComponent_SingleCondition_Between) ProtoMessage() {}

func (x *PoolAccountComponent_SingleCondition_Between) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolAccountComponent_SingleCondition_Between.ProtoReflect.Descriptor instead.
func (*PoolAccountComponent_SingleCondition_Between) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{38, 0, 0}
}

func (x *PoolAccountComponent_SingleCondition_Between) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *PoolAccountComponent_SingleCondition_Between) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

type PoolAccountComponent_SingleCondition_Other struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PoolAccountComponent_SingleCondition_Other) Reset() {
	*x = PoolAccountComponent_SingleCondition_Other{}
	mi := &file_manager_component_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PoolAccountComponent_SingleCondition_Other) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolAccountComponent_SingleCondition_Other) ProtoMessage() {}

func (x *PoolAccountComponent_SingleCondition_Other) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolAccountComponent_SingleCondition_Other.ProtoReflect.Descriptor instead.
func (*PoolAccountComponent_SingleCondition_Other) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{38, 0, 1}
}

func (x *PoolAccountComponent_SingleCondition_Other) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type DataProcessingComponent_Process struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`         // 出参变量名称
	Function      *VariableFunction      `protobuf:"bytes,2,opt,name=function,proto3" json:"function,omitempty"` // 数据处理函数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataProcessingComponent_Process) Reset() {
	*x = DataProcessingComponent_Process{}
	mi := &file_manager_component_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataProcessingComponent_Process) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataProcessingComponent_Process) ProtoMessage() {}

func (x *DataProcessingComponent_Process) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataProcessingComponent_Process.ProtoReflect.Descriptor instead.
func (*DataProcessingComponent_Process) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{39, 0}
}

func (x *DataProcessingComponent_Process) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DataProcessingComponent_Process) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

type SqlExecutionComponent_Value struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        VariableSource         `protobuf:"varint,1,opt,name=source,proto3,enum=manager.VariableSource" json:"source,omitempty"` // 期望值来源
	Manual        *VariableManual        `protobuf:"bytes,2,opt,name=manual,proto3" json:"manual,omitempty"`
	Export        *VariableExport        `protobuf:"bytes,3,opt,name=export,proto3" json:"export,omitempty"`
	Environment   *VariableEnvironment   `protobuf:"bytes,4,opt,name=environment,proto3" json:"environment,omitempty"`
	Function      *VariableFunction      `protobuf:"bytes,5,opt,name=function,proto3" json:"function,omitempty"` // 为了兼容旧数据，这里不能做`validate`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SqlExecutionComponent_Value) Reset() {
	*x = SqlExecutionComponent_Value{}
	mi := &file_manager_component_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SqlExecutionComponent_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SqlExecutionComponent_Value) ProtoMessage() {}

func (x *SqlExecutionComponent_Value) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SqlExecutionComponent_Value.ProtoReflect.Descriptor instead.
func (*SqlExecutionComponent_Value) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{41, 0}
}

func (x *SqlExecutionComponent_Value) GetSource() VariableSource {
	if x != nil {
		return x.Source
	}
	return VariableSource_MANUAL
}

func (x *SqlExecutionComponent_Value) GetManual() *VariableManual {
	if x != nil {
		return x.Manual
	}
	return nil
}

func (x *SqlExecutionComponent_Value) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *SqlExecutionComponent_Value) GetEnvironment() *VariableEnvironment {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *SqlExecutionComponent_Value) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

type SqlExecutionComponent_Sql struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Type          DataSourceType               `protobuf:"varint,1,opt,name=type,proto3,enum=manager.DataSourceType" json:"type,omitempty"` // 数据源类型
	Host          *SqlExecutionComponent_Value `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`                              // 主机地址
	Port          *SqlExecutionComponent_Value `protobuf:"bytes,3,opt,name=port,proto3" json:"port,omitempty"`                              // 端口
	User          *SqlExecutionComponent_Value `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`                              // 用户名
	Password      *SqlExecutionComponent_Value `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`                      // 密码
	Database      *SqlExecutionComponent_Value `protobuf:"bytes,6,opt,name=database,proto3" json:"database,omitempty"`                      // 数据库名称
	Sql           string                       `protobuf:"bytes,7,opt,name=sql,proto3" json:"sql,omitempty"`                                // 待执行的SQL（min_len: len("delete from t")）
	Name          string                       `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`                              // 出参变量名称
	Timeout       int64                        `protobuf:"varint,9,opt,name=timeout,proto3" json:"timeout,omitempty"`                       // SQL执行超时时间（单位为毫秒）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SqlExecutionComponent_Sql) Reset() {
	*x = SqlExecutionComponent_Sql{}
	mi := &file_manager_component_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SqlExecutionComponent_Sql) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SqlExecutionComponent_Sql) ProtoMessage() {}

func (x *SqlExecutionComponent_Sql) ProtoReflect() protoreflect.Message {
	mi := &file_manager_component_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SqlExecutionComponent_Sql.ProtoReflect.Descriptor instead.
func (*SqlExecutionComponent_Sql) Descriptor() ([]byte, []int) {
	return file_manager_component_proto_rawDescGZIP(), []int{41, 1}
}

func (x *SqlExecutionComponent_Sql) GetType() DataSourceType {
	if x != nil {
		return x.Type
	}
	return DataSourceType_MYSQL
}

func (x *SqlExecutionComponent_Sql) GetHost() *SqlExecutionComponent_Value {
	if x != nil {
		return x.Host
	}
	return nil
}

func (x *SqlExecutionComponent_Sql) GetPort() *SqlExecutionComponent_Value {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *SqlExecutionComponent_Sql) GetUser() *SqlExecutionComponent_Value {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *SqlExecutionComponent_Sql) GetPassword() *SqlExecutionComponent_Value {
	if x != nil {
		return x.Password
	}
	return nil
}

func (x *SqlExecutionComponent_Sql) GetDatabase() *SqlExecutionComponent_Value {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *SqlExecutionComponent_Sql) GetSql() string {
	if x != nil {
		return x.Sql
	}
	return ""
}

func (x *SqlExecutionComponent_Sql) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SqlExecutionComponent_Sql) GetTimeout() int64 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

var File_manager_component_proto protoreflect.FileDescriptor

var file_manager_component_proto_rawDesc = []byte{
	0x0a, 0x17, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x61, 0x72, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x16, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x32, 0x0a, 0x0e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01,
	0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x57, 0x0a, 0x0e, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x23, 0x0a, 0x07, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x37, 0x0a, 0x13, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x76,
	0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0xd0, 0x01, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa0, 0x04, 0x0a, 0x10, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x1a, 0xe7, 0x02, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x03, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x06,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f,
	0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x08,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x2e, 0x0a,
	0x0e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x1c, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x6e, 0x0a,
	0x0c, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x32, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x2a, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01,
	0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xfc, 0x02,
	0x0a, 0x06, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0,
	0x01, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x39,
	0x0a, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x6e, 0x76,
	0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x85, 0x01, 0x0a,
	0x06, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01,
	0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8c, 0x02, 0x0a, 0x17, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a,
	0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x22, 0xa9, 0x02, 0x0a, 0x0d, 0x43, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x22,
	0xb3, 0x02, 0x0a, 0x0e, 0x53, 0x75, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3d,
	0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a,
	0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0xf4, 0x04, 0x0a, 0x0d, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x70, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3e, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x48, 0x0a, 0x14, 0x73, 0x75, 0x69, 0x74, 0x65,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x12, 0x73,
	0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0xd3, 0x02, 0x0a,
	0x16, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a,
	0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64,
	0x42, 0x79, 0x22, 0xc5, 0x02, 0x0a, 0x1a, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0xc7, 0x01, 0x0a, 0x0f, 0x55,
	0x49, 0x43, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c,
	0x69, 0x61, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x64, 0x69, 0x64, 0x22, 0xb6, 0x01, 0x0a, 0x10, 0x55, 0x49, 0x53, 0x75, 0x69, 0x74, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x94, 0x07,
	0x0a, 0x0e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x39, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x67,
	0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x09, 0x67, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x48, 0x0a,
	0x14, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x12, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61,
	0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x33, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c,
	0x12, 0x2a, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a,
	0x0d, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x73, 0x74,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0e,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x0d, 0x74, 0x65, 0x73,
	0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x61, 0x72, 0x67, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x65, 0x73, 0x74, 0x41, 0x72, 0x67, 0x73, 0x12, 0x33, 0x0a, 0x15, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x0a,
	0x66, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x52, 0x09, 0x66, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x67, 0x65,
	0x74, 0x68, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x6f, 0x67, 0x65,
	0x74, 0x68, 0x65, 0x72, 0x22, 0xed, 0x02, 0x0a, 0x0f, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x6d,
	0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x22, 0x73, 0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0xfd, 0x01, 0x0a, 0x11, 0x50, 0x65,
	0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x76, 0x75, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x56, 0x75, 0x22, 0xe7, 0x05, 0x0a, 0x11, 0x50, 0x65,
	0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x52, 0x70, 0x73, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x56, 0x32,
	0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x0b,
	0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43,
	0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x75, 0x70,
	0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65,
	0x70, 0x56, 0x32, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73,
	0x12, 0x3d, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x73, 0x74, 0x65,
	0x70, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32,
	0x52, 0x0d, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12,
	0x3d, 0x0a, 0x0e, 0x74, 0x65, 0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x73, 0x74, 0x65, 0x70,
	0x73, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x52,
	0x0d, 0x74, 0x65, 0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x37,
	0x0a, 0x09, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x70,
	0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x76, 0x75, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x56, 0x75, 0x12, 0x3c, 0x0a, 0x0e, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x29, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x0d, 0x6c, 0x6f, 0x61, 0x64, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x33, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x12, 0x50, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0xe4, 0x04, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x66, 0x50,
	0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76,
	0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x45, 0x6e, 0x76, 0x12, 0x41, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x61, 0x78,
	0x5f, 0x72, 0x70, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x4d, 0x61, 0x78, 0x52, 0x70, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x4b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x52, 0x09,
	0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x6c,
	0x61, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x12,
	0x37, 0x0a, 0x0b, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x0a, 0x72, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x2a, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74,
	0x6f, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x22, 0xf1, 0x02,
	0x0a, 0x11, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x09, 0x6d, 0x65,
	0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x22, 0xff, 0x03, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50,
	0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2f, 0x0a, 0x0a, 0x6c, 0x61, 0x72,
	0x6b, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x61, 0x72, 0x6b, 0x43, 0x68, 0x61, 0x74, 0x52,
	0x09, 0x6c, 0x61, 0x72, 0x6b, 0x43, 0x68, 0x61, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x07, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x07, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xfb, 0x02, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x22, 0x10, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x22, 0x7a, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x75, 0x70, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28,
	0x01, 0x52, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x07, 0x65, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22,
	0x48, 0x0a, 0x11, 0x54, 0x65, 0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01,
	0x52, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x17, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28,
	0x01, 0x52, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x07, 0x65, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22,
	0x82, 0x01, 0x0a, 0x16, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x69, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12,
	0x33, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x22, 0xbe, 0x0d, 0x0a, 0x0d, 0x4c, 0x6f, 0x6f, 0x70, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c,
	0x6f, 0x6f, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x12, 0x36, 0x0a, 0x03, 0x66, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x6f, 0x70,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x66, 0x6f, 0x72, 0x12, 0x43, 0x0a, 0x08,
	0x66, 0x6f, 0x72, 0x5f, 0x65, 0x61, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x6f, 0x70, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x45, 0x61, 0x63, 0x68, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x66, 0x6f, 0x72, 0x45, 0x61, 0x63,
	0x68, 0x12, 0x3c, 0x0a, 0x05, 0x77, 0x68, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x6f, 0x70, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x68, 0x69, 0x6c, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x77, 0x68, 0x69, 0x6c, 0x65, 0x1a,
	0x24, 0x0a, 0x03, 0x46, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x1a, 0xdb, 0x02, 0x0a, 0x07, 0x46, 0x6f, 0x72, 0x45, 0x61, 0x63,
	0x68, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x06,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f,
	0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x08,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0xe5, 0x07, 0x0a, 0x05, 0x57, 0x68, 0x69, 0x6c, 0x65, 0x12, 0x34, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x6f,
	0x6f, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x68, 0x69, 0x6c,
	0x65, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x6f,
	0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x68, 0x69, 0x6c, 0x65,
	0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x1a, 0xe8, 0x04, 0x0a, 0x0f, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x6f,
	0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x68, 0x69, 0x6c, 0x65,
	0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x12, 0x53, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0xfa, 0x42, 0x36, 0x72, 0x34, 0x52, 0x02,
	0x45, 0x51, 0x52, 0x02, 0x4e, 0x45, 0x52, 0x02, 0x4c, 0x54, 0x52, 0x02, 0x4c, 0x45, 0x52, 0x02,
	0x47, 0x54, 0x52, 0x02, 0x47, 0x45, 0x52, 0x08, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53,
	0x52, 0x0c, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x02,
	0x52, 0x45, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x12, 0x52, 0x0a, 0x05, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x6f, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x57, 0x68, 0x69, 0x6c, 0x65, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x1a,
	0xd9, 0x02, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12,
	0x39, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x6e,
	0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x9d, 0x01, 0x0a, 0x0e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43,
	0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x68, 0x69, 0x70, 0x12, 0x46, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x4c, 0x6f, 0x6f, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x57, 0x68, 0x69, 0x6c, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x28, 0x0a, 0x04, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x4f, 0x52, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x46, 0x4f, 0x52, 0x5f, 0x45, 0x41, 0x43, 0x48, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x57, 0x48,
	0x49, 0x4c, 0x45, 0x10, 0x02, 0x22, 0x82, 0x1e, 0x0a, 0x14, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x19,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x02, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x35, 0x0a, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xfa, 0x42, 0x1a, 0x72, 0x18,
	0x52, 0x03, 0x47, 0x45, 0x54, 0x52, 0x04, 0x50, 0x4f, 0x53, 0x54, 0x52, 0x03, 0x50, 0x55, 0x54,
	0x52, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x12, 0x5b, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x4b, 0x65,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x73, 0x63, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x28, 0x01, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x57, 0x0a,
	0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74,
	0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x73, 0x63, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x40, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48,
	0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x49, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x12, 0x51, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x72, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x72, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x72, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x0a, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x28, 0x01, 0x52, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x65,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x07, 0x65, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x1a, 0x9c, 0x06, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x55, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x64,
	0x0a, 0x0c, 0x62, 0x65, 0x61, 0x72, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48,
	0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x42, 0x65, 0x61, 0x72, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x62, 0x65, 0x61, 0x72, 0x65, 0x72, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x5e, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x41, 0x75, 0x74, 0x68, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x62, 0x61, 0x73, 0x69, 0x63,
	0x41, 0x75, 0x74, 0x68, 0x1a, 0xcb, 0x01, 0x0a, 0x06, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12,
	0x1c, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x20, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x59, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x5f, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x38, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x64, 0x64, 0x54, 0x6f, 0x22, 0x26, 0x0a, 0x05, 0x41, 0x64,
	0x64, 0x54, 0x6f, 0x12, 0x0b, 0x0a, 0x07, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x53, 0x10, 0x00,
	0x12, 0x10, 0x0a, 0x0c, 0x51, 0x55, 0x45, 0x52, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53,
	0x10, 0x01, 0x1a, 0x2f, 0x0a, 0x0b, 0x42, 0x65, 0x61, 0x72, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x20, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x1a, 0x5b, 0x0a, 0x09, 0x42, 0x61, 0x73, 0x69, 0x63, 0x41, 0x75, 0x74, 0x68,
	0x12, 0x26, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x22, 0x42, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x4f, 0x5f, 0x41,
	0x55, 0x54, 0x48, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x50, 0x49, 0x5f, 0x4b, 0x45, 0x59,
	0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x45, 0x41, 0x52, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x4b,
	0x45, 0x4e, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x10, 0x03, 0x1a, 0x70, 0x0a, 0x0c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x44, 0x65, 0x73, 0x63, 0x12, 0x1c, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xb2, 0x02, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12,
	0x4c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x6f, 0x64,
	0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a,
	0x09, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x73, 0x63, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x08, 0x66, 0x6f, 0x72, 0x6d, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x10, 0x0a, 0x03, 0x72, 0x61, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72,
	0x61, 0x77, 0x22, 0x77, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x4d,
	0x55, 0x4c, 0x54, 0x49, 0x50, 0x41, 0x52, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x55, 0x52, 0x4c, 0x45, 0x4e, 0x43, 0x4f,
	0x44, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x50, 0x4c,
	0x41, 0x49, 0x4e, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x04, 0x1a, 0xa1, 0x01, 0x0a, 0x07,
	0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x30, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x30, 0x0a, 0x0f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x32, 0x0a, 0x10, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a,
	0xfa, 0x09, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x53, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x72, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x4a, 0x0a,
	0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x72,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x5d, 0x0a, 0x0b, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0xe4, 0x01, 0x0a, 0x07, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x8e, 0x01, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x74, 0xfa, 0x42, 0x71, 0x72, 0x6f, 0x52, 0x02, 0x45, 0x51, 0x52,
	0x02, 0x4e, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x45, 0x51, 0x52, 0x06, 0x4c, 0x45, 0x4e,
	0x5f, 0x4e, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x4c, 0x54, 0x52, 0x06, 0x4c, 0x45, 0x4e,
	0x5f, 0x4c, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x47, 0x54, 0x52, 0x06, 0x4c, 0x45, 0x4e,
	0x5f, 0x47, 0x45, 0x52, 0x08, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x0c, 0x4e,
	0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x0b, 0x53, 0x54, 0x41,
	0x52, 0x54, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x52, 0x09, 0x45, 0x4e, 0x44, 0x53, 0x5f, 0x57,
	0x49, 0x54, 0x48, 0x52, 0x02, 0x52, 0x45, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0xd0, 0x01, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x1a,
	0xce, 0x04, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x4f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x64, 0x79, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x5b, 0x0a, 0x08, 0x6a, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x72,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x4a, 0x4d, 0x45, 0x53, 0x50, 0x61,
	0x74, 0x68, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6a, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x74, 0x68, 0x12, 0x52, 0x0a, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42,
	0x6f, 0x64, 0x79, 0x2e, 0x52, 0x65, 0x67, 0x65, 0x78, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x1a, 0xed, 0x01, 0x0a, 0x08, 0x4a,
	0x4d, 0x45, 0x53, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x86, 0x01, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x6c, 0xfa, 0x42, 0x69, 0x72, 0x67, 0x52, 0x02, 0x45, 0x51,
	0x52, 0x02, 0x4e, 0x45, 0x52, 0x02, 0x4c, 0x54, 0x52, 0x02, 0x4c, 0x45, 0x52, 0x02, 0x47, 0x54,
	0x52, 0x02, 0x47, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x45, 0x51, 0x52, 0x06, 0x4c, 0x45,
	0x4e, 0x5f, 0x4e, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x4c, 0x54, 0x52, 0x06, 0x4c, 0x45,
	0x4e, 0x5f, 0x4c, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x47, 0x54, 0x52, 0x06, 0x4c, 0x45,
	0x4e, 0x5f, 0x47, 0x45, 0x52, 0x08, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x0c,
	0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x02, 0x52, 0x45,
	0xd0, 0x01, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x12, 0x2c, 0x0a, 0x0b,
	0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x65,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x33, 0x0a, 0x05, 0x52, 0x65,
	0x67, 0x65, 0x78, 0x12, 0x2a, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0xd0, 0x01, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x1f, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4a, 0x4d, 0x45, 0x53, 0x50,
	0x41, 0x54, 0x48, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x47, 0x45, 0x58, 0x10, 0x01,
	0x1a, 0x7a, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3e,
	0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x24, 0xfa, 0x42, 0x21, 0x72, 0x1f, 0x52, 0x02, 0x45, 0x51, 0x52, 0x02, 0x4e, 0x45, 0x52, 0x02,
	0x4c, 0x54, 0x52, 0x02, 0x4c, 0x45, 0x52, 0x02, 0x47, 0x54, 0x52, 0x02, 0x47, 0x45, 0x52, 0x02,
	0x52, 0x45, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x12, 0x2c,
	0x0a, 0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52,
	0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xcb, 0x03, 0x0a,
	0x06, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01,
	0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x50, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74,
	0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x47, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x27, 0x0a,
	0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01,
	0x01, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x1a, 0xa1, 0x01, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12,
	0x4c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a,
	0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0a, 0x65,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x1f, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4a, 0x4d, 0x45, 0x53, 0x50, 0x41, 0x54, 0x48, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x52, 0x45, 0x47, 0x45, 0x58, 0x10, 0x01, 0x22, 0xfa, 0x01, 0x0a, 0x12, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x2a, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x07,
	0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xed, 0x07, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x34,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0xfa, 0x04, 0x0a, 0x0f,
	0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x4f, 0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6c, 0x65, 0x66, 0x74,
	0x12, 0x86, 0x01, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x6c, 0xfa, 0x42, 0x69, 0x72, 0x67, 0x52, 0x02, 0x45, 0x51, 0x52, 0x02, 0x4e,
	0x45, 0x52, 0x02, 0x4c, 0x54, 0x52, 0x02, 0x4c, 0x45, 0x52, 0x02, 0x47, 0x54, 0x52, 0x02, 0x47,
	0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x45, 0x51, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x4e,
	0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x4c, 0x54, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x4c,
	0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x47, 0x54, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x47,
	0x45, 0x52, 0x08, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x0c, 0x4e, 0x4f, 0x54,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x52, 0x02, 0x52, 0x45, 0xd0, 0x01, 0x01,
	0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x12, 0x51, 0x0a, 0x05, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x1a, 0xb9, 0x02, 0x0a,
	0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x39, 0x0a, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72,
	0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45,
	0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x35, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x9c, 0x01, 0x0a, 0x0e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0c, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70,
	0x12, 0x45, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xca, 0x03, 0x0a, 0x0d, 0x57, 0x61, 0x69, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x57, 0x61,
	0x69, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x6c, 0x65, 0x65,
	0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x73, 0x6c, 0x65,
	0x65, 0x70, 0x12, 0x3f, 0x0a, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x57, 0x61, 0x69,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x08, 0x01, 0x52, 0x06, 0x6d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x1a, 0x24, 0x0a, 0x05, 0x53, 0x6c, 0x65, 0x65, 0x70, 0x12, 0x1b, 0x0a, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x28, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x1a, 0xb9, 0x01, 0x0a, 0x06, 0x4d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x77, 0x61, 0x69, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x57, 0x61, 0x69, 0x74,
	0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x55, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x25,
	0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x4f, 0x4e, 0x54,
	0x49, 0x4e, 0x55, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e,
	0x41, 0x54, 0x45, 0x10, 0x01, 0x22, 0x1d, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a,
	0x05, 0x53, 0x4c, 0x45, 0x45, 0x50, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x10, 0x01, 0x22, 0x92, 0x06, 0x0a, 0x0f, 0x41, 0x73, 0x73, 0x65, 0x72, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x72, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x72, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x72, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x4f, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c,
	0x12, 0x23, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x06, 0x6e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0xbc, 0x02, 0x0a, 0x08, 0x45, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x39, 0x0a, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x06, 0x65, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x65,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x76,
	0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x35, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x66, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xa0, 0x02, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65, 0x72,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x63, 0x74, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x12, 0x86, 0x01, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x6c, 0xfa, 0x42, 0x69, 0x72, 0x67,
	0x52, 0x02, 0x45, 0x51, 0x52, 0x02, 0x4e, 0x45, 0x52, 0x02, 0x4c, 0x54, 0x52, 0x02, 0x4c, 0x45,
	0x52, 0x02, 0x47, 0x54, 0x52, 0x02, 0x47, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x45, 0x51,
	0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x4e, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x4c, 0x54,
	0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x4c, 0x45, 0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x47, 0x54,
	0x52, 0x06, 0x4c, 0x45, 0x4e, 0x5f, 0x47, 0x45, 0x52, 0x08, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49,
	0x4e, 0x53, 0x52, 0x0c, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53,
	0x52, 0x02, 0x52, 0x45, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65,
	0x12, 0x47, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x70,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xee, 0x08, 0x0a, 0x14, 0x50, 0x6f,
	0x6f, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4f,
	0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6f, 0x6c,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x4c, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x50, 0x61, 0x69, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x08, 0x02, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x1a, 0xc2, 0x03,
	0x0a, 0x0f, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x4d, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xfa, 0x42, 0x30, 0x72, 0x2e, 0x52, 0x02, 0x45, 0x51, 0x52,
	0x02, 0x4e, 0x45, 0x52, 0x02, 0x4c, 0x54, 0x52, 0x02, 0x4c, 0x45, 0x52, 0x02, 0x47, 0x54, 0x52,
	0x02, 0x47, 0x45, 0x52, 0x04, 0x4c, 0x49, 0x4b, 0x45, 0x52, 0x02, 0x49, 0x4e, 0x52, 0x07, 0x42,
	0x45, 0x54, 0x57, 0x45, 0x45, 0x4e, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x72, 0x65, 0x12, 0x18, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x59, 0x0a, 0x07,
	0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x65, 0x74,
	0x77, 0x65, 0x65, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x12, 0x53, 0x0a, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x1a, 0x49, 0x0a, 0x07,
	0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0,
	0x01, 0x01, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x65, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0,
	0x01, 0x01, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x1a, 0x29, 0x0a, 0x05, 0x4f, 0x74, 0x68, 0x65, 0x72,
	0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x1a, 0xa8, 0x01, 0x0a, 0x0e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68,
	0x69, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x12, 0x51, 0x0a, 0x0a, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28,
	0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xcc, 0x01,
	0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6f, 0x6c,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x06, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0x4c, 0x0a, 0x0a,
	0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x50, 0x61, 0x69, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0xd0, 0x01, 0x01, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0xd0, 0x01, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xd7, 0x01, 0x0a, 0x17, 0x44,
	0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x1a, 0x6a, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x61, 0x74, 0x61, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0x96, 0x07, 0x0a, 0x15,
	0x53, 0x71, 0x6c, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x04, 0x73, 0x71, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x71,
	0x6c, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x53, 0x71, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28,
	0x01, 0x52, 0x04, 0x73, 0x71, 0x6c, 0x73, 0x1a, 0xb9, 0x02, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x06,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f,
	0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x08,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0xfe, 0x03, 0x0a, 0x03, 0x53, 0x71, 0x6c, 0x12, 0x35, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x42, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x71, 0x6c, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53,
	0x71, 0x6c, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x42, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x53, 0x71, 0x6c, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x4a,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x71, 0x6c, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x4a, 0x0a, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x71, 0x6c, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x73, 0x71, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x0d, 0x52, 0x03, 0x73, 0x71,
	0x6c, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x2a, 0x47, 0x0a, 0x0e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x58, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x01, 0x12, 0x0f,
	0x0a, 0x0b, 0x45, 0x4e, 0x56, 0x49, 0x52, 0x4f, 0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12,
	0x0c, 0x0a, 0x08, 0x46, 0x55, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x2a, 0x26, 0x0a,
	0x0d, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a,
	0x0a, 0x06, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x10, 0x01, 0x2a, 0x1f, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x68, 0x69, 0x70, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x00, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x52, 0x10, 0x01, 0x2a, 0x26, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4a, 0x4d, 0x45, 0x53, 0x50, 0x41, 0x54,
	0x48, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x47, 0x45, 0x58, 0x10, 0x01, 0x2a, 0x38,
	0x0a, 0x0e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x0b, 0x0a, 0x07, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x53, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x42, 0x4f, 0x44, 0x59, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x02, 0x2a, 0x2b, 0x0a, 0x0e, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x59,
	0x53, 0x51, 0x4c, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x4f, 0x53, 0x54, 0x47, 0x52, 0x45,
	0x53, 0x51, 0x4c, 0x10, 0x01, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74,
	0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62,
	0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_component_proto_rawDescOnce sync.Once
	file_manager_component_proto_rawDescData = file_manager_component_proto_rawDesc
)

func file_manager_component_proto_rawDescGZIP() []byte {
	file_manager_component_proto_rawDescOnce.Do(func() {
		file_manager_component_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_component_proto_rawDescData)
	})
	return file_manager_component_proto_rawDescData
}

var file_manager_component_proto_enumTypes = make([]protoimpl.EnumInfo, 14)
var file_manager_component_proto_msgTypes = make([]protoimpl.MessageInfo, 82)
var file_manager_component_proto_goTypes = []any{
	(VariableSource)(0),                          // 0: manager.VariableSource
	(ConditionType)(0),                           // 1: manager.ConditionType
	(Relationship)(0),                            // 2: manager.Relationship
	(ExtractType)(0),                             // 3: manager.ExtractType
	(ResponseSource)(0),                          // 4: manager.ResponseSource
	(DataSourceType)(0),                          // 5: manager.DataSourceType
	(LoopComponent_Type)(0),                      // 6: manager.LoopComponent.Type
	(HttpRequestComponent_Authorization_Type)(0), // 7: manager.HttpRequestComponent.Authorization.Type
	(HttpRequestComponent_Authorization_ApiKey_AddTo)(0),   // 8: manager.HttpRequestComponent.Authorization.ApiKey.AddTo
	(HttpRequestComponent_Body_ContentType)(0),             // 9: manager.HttpRequestComponent.Body.ContentType
	(HttpRequestComponent_Assertion_Body_Type)(0),          // 10: manager.HttpRequestComponent.Assertion.Body.Type
	(HttpRequestComponent_Export_Body_Type)(0),             // 11: manager.HttpRequestComponent.Export.Body.Type
	(WaitComponent_Type)(0),                                // 12: manager.WaitComponent.Type
	(WaitComponent_Manual_Action)(0),                       // 13: manager.WaitComponent.Manual.Action
	(*VariableManual)(nil),                                 // 14: manager.VariableManual
	(*VariableExport)(nil),                                 // 15: manager.VariableExport
	(*VariableEnvironment)(nil),                            // 16: manager.VariableEnvironment
	(*VariableFunction)(nil),                               // 17: manager.VariableFunction
	(*VariableHeader)(nil),                                 // 18: manager.VariableHeader
	(*VariableBody)(nil),                                   // 19: manager.VariableBody
	(*Import)(nil),                                         // 20: manager.Import
	(*Export)(nil),                                         // 21: manager.Export
	(*ComponentGroupComponent)(nil),                        // 22: manager.ComponentGroupComponent
	(*CaseComponent)(nil),                                  // 23: manager.CaseComponent
	(*SuiteComponent)(nil),                                 // 24: manager.SuiteComponent
	(*PlanComponent)(nil),                                  // 25: manager.PlanComponent
	(*InterfaceCaseComponent)(nil),                         // 26: manager.InterfaceCaseComponent
	(*InterfaceDocumentComponent)(nil),                     // 27: manager.InterfaceDocumentComponent
	(*UICaseComponent)(nil),                                // 28: manager.UICaseComponent
	(*UISuiteComponent)(nil),                               // 29: manager.UISuiteComponent
	(*UIPlanMetaData)(nil),                                 // 30: manager.UIPlanMetaData
	(*UIPlanComponent)(nil),                                // 31: manager.UIPlanComponent
	(*ServiceComponent)(nil),                               // 32: manager.ServiceComponent
	(*PerfDataComponent)(nil),                              // 33: manager.PerfDataComponent
	(*PerfCaseComponent)(nil),                              // 34: manager.PerfCaseComponent
	(*PerfSuiteComponent)(nil),                             // 35: manager.PerfSuiteComponent
	(*PerfPlanMetaData)(nil),                               // 36: manager.PerfPlanMetaData
	(*PerfPlanComponent)(nil),                              // 37: manager.PerfPlanComponent
	(*StabilityPlanMetaData)(nil),                          // 38: manager.StabilityPlanMetaData
	(*StabilityPlanComponent)(nil),                         // 39: manager.StabilityPlanComponent
	(*StartComponent)(nil),                                 // 40: manager.StartComponent
	(*EndComponent)(nil),                                   // 41: manager.EndComponent
	(*SetupComponent)(nil),                                 // 42: manager.SetupComponent
	(*TeardownComponent)(nil),                              // 43: manager.TeardownComponent
	(*BusinessSingleComponent)(nil),                        // 44: manager.BusinessSingleComponent
	(*BusinessGroupComponent)(nil),                         // 45: manager.BusinessGroupComponent
	(*LoopComponent)(nil),                                  // 46: manager.LoopComponent
	(*HttpRequestComponent)(nil),                           // 47: manager.HttpRequestComponent
	(*ReferenceComponent)(nil),                             // 48: manager.ReferenceComponent
	(*ConditionComponent)(nil),                             // 49: manager.ConditionComponent
	(*WaitComponent)(nil),                                  // 50: manager.WaitComponent
	(*AssertComponent)(nil),                                // 51: manager.AssertComponent
	(*PoolAccountComponent)(nil),                           // 52: manager.PoolAccountComponent
	(*DataProcessingComponent)(nil),                        // 53: manager.DataProcessingComponent
	(*DataDrivenComponent)(nil),                            // 54: manager.DataDrivenComponent
	(*SqlExecutionComponent)(nil),                          // 55: manager.SqlExecutionComponent
	(*VariableFunction_Parameter)(nil),                     // 56: manager.VariableFunction.Parameter
	(*LoopComponent_For)(nil),                              // 57: manager.LoopComponent.For
	(*LoopComponent_ForEach)(nil),                          // 58: manager.LoopComponent.ForEach
	(*LoopComponent_While)(nil),                            // 59: manager.LoopComponent.While
	(*LoopComponent_While_SingleCondition)(nil),            // 60: manager.LoopComponent.While.SingleCondition
	(*LoopComponent_While_GroupCondition)(nil),             // 61: manager.LoopComponent.While.GroupCondition
	(*LoopComponent_While_SingleCondition_Value)(nil),      // 62: manager.LoopComponent.While.SingleCondition.Value
	(*HttpRequestComponent_Authorization)(nil),             // 63: manager.HttpRequestComponent.Authorization
	(*HttpRequestComponent_KeyValueDesc)(nil),              // 64: manager.HttpRequestComponent.KeyValueDesc
	(*HttpRequestComponent_Body)(nil),                      // 65: manager.HttpRequestComponent.Body
	(*HttpRequestComponent_Timeout)(nil),                   // 66: manager.HttpRequestComponent.Timeout
	(*HttpRequestComponent_Assertion)(nil),                 // 67: manager.HttpRequestComponent.Assertion
	(*HttpRequestComponent_Export)(nil),                    // 68: manager.HttpRequestComponent.Export
	(*HttpRequestComponent_Authorization_ApiKey)(nil),      // 69: manager.HttpRequestComponent.Authorization.ApiKey
	(*HttpRequestComponent_Authorization_BearerToken)(nil), // 70: manager.HttpRequestComponent.Authorization.BearerToken
	(*HttpRequestComponent_Authorization_BasicAuth)(nil),   // 71: manager.HttpRequestComponent.Authorization.BasicAuth
	(*HttpRequestComponent_Assertion_Headers)(nil),         // 72: manager.HttpRequestComponent.Assertion.Headers
	(*HttpRequestComponent_Assertion_Body)(nil),            // 73: manager.HttpRequestComponent.Assertion.Body
	(*HttpRequestComponent_Assertion_StatusCode)(nil),      // 74: manager.HttpRequestComponent.Assertion.StatusCode
	(*HttpRequestComponent_Assertion_Body_JMESPath)(nil),   // 75: manager.HttpRequestComponent.Assertion.Body.JMESPath
	(*HttpRequestComponent_Assertion_Body_Regex)(nil),      // 76: manager.HttpRequestComponent.Assertion.Body.Regex
	(*HttpRequestComponent_Export_Headers)(nil),            // 77: manager.HttpRequestComponent.Export.Headers
	(*HttpRequestComponent_Export_Body)(nil),               // 78: manager.HttpRequestComponent.Export.Body
	(*ConditionComponent_SingleCondition)(nil),             // 79: manager.ConditionComponent.SingleCondition
	(*ConditionComponent_GroupCondition)(nil),              // 80: manager.ConditionComponent.GroupCondition
	(*ConditionComponent_SingleCondition_Value)(nil),       // 81: manager.ConditionComponent.SingleCondition.Value
	(*WaitComponent_Sleep)(nil),                            // 82: manager.WaitComponent.Sleep
	(*WaitComponent_Manual)(nil),                           // 83: manager.WaitComponent.Manual
	(*AssertComponent_Actual)(nil),                         // 84: manager.AssertComponent.Actual
	(*AssertComponent_Expected)(nil),                       // 85: manager.AssertComponent.Expected
	(*AssertComponent_Assertion)(nil),                      // 86: manager.AssertComponent.Assertion
	(*PoolAccountComponent_SingleCondition)(nil),           // 87: manager.PoolAccountComponent.SingleCondition
	(*PoolAccountComponent_GroupCondition)(nil),            // 88: manager.PoolAccountComponent.GroupCondition
	(*PoolAccountComponent_Condition)(nil),                 // 89: manager.PoolAccountComponent.Condition
	(*PoolAccountComponent_KeyValPair)(nil),                // 90: manager.PoolAccountComponent.KeyValPair
	(*PoolAccountComponent_SingleCondition_Between)(nil),   // 91: manager.PoolAccountComponent.SingleCondition.Between
	(*PoolAccountComponent_SingleCondition_Other)(nil),     // 92: manager.PoolAccountComponent.SingleCondition.Other
	(*DataProcessingComponent_Process)(nil),                // 93: manager.DataProcessingComponent.Process
	(*SqlExecutionComponent_Value)(nil),                    // 94: manager.SqlExecutionComponent.Value
	(*SqlExecutionComponent_Sql)(nil),                      // 95: manager.SqlExecutionComponent.Sql
	(FunctionType)(0),                                      // 96: manager.FunctionType
	(ResourceState)(0),                                     // 97: manager.ResourceState
	(CommonState)(0),                                       // 98: manager.CommonState
	(ExecutionMode)(0),                                     // 99: manager.ExecutionMode
	(pb.TriggerMode)(0),                                    // 100: common.TriggerMode
	(pb.PurposeType)(0),                                    // 101: common.PurposeType
	(*pb.GeneralConfig)(nil),                               // 102: common.GeneralConfig
	(*pb.AccountConfig)(nil),                               // 103: common.AccountConfig
	(pb.PriorityType)(0),                                   // 104: common.PriorityType
	(*pb.GitConfig)(nil),                                   // 105: common.GitConfig
	(pb.DeviceType)(0),                                     // 106: common.DeviceType
	(pb.PlatformType)(0),                                   // 107: common.PlatformType
	(pb.TestLanguage)(0),                                   // 108: common.TestLanguage
	(pb.TestFramework)(0),                                  // 109: common.TestFramework
	(pb.FailRetry)(0),                                      // 110: common.FailRetry
	(*pb.RateLimitV2)(nil),                                 // 111: common.RateLimitV2
	(*pb.PerfCaseStepV2)(nil),                              // 112: common.PerfCaseStepV2
	(*pb.LoadGenerator)(nil),                               // 113: common.LoadGenerator
	(pb.Protocol)(0),                                       // 114: common.Protocol
	(pb.TargetEnvironment)(0),                              // 115: common.TargetEnvironment
	(*pb.ProtobufConfig)(nil),                              // 116: common.ProtobufConfig
	(*pb.PerfKeepalive)(nil),                               // 117: common.PerfKeepalive
	(*pb.PerfRateLimits)(nil),                              // 118: common.PerfRateLimits
	(*pb.PerfServiceMetaData)(nil),                         // 119: common.PerfServiceMetaData
	(*pb.PerfStopRule)(nil),                                // 120: common.PerfStopRule
	(*pb.LarkChat)(nil),                                    // 121: common.LarkChat
	(*pb.StabilityCustomDevices)(nil),                      // 122: common.StabilityCustomDevices
	(*pb.StabilityCustomScript)(nil),                       // 123: common.StabilityCustomScript
}
var file_manager_component_proto_depIdxs = []int32{
	96,  // 0: manager.VariableFunction.type:type_name -> manager.FunctionType
	56,  // 1: manager.VariableFunction.parameters:type_name -> manager.VariableFunction.Parameter
	3,   // 2: manager.VariableBody.type:type_name -> manager.ExtractType
	0,   // 3: manager.Import.source:type_name -> manager.VariableSource
	14,  // 4: manager.Import.manual:type_name -> manager.VariableManual
	15,  // 5: manager.Import.export:type_name -> manager.VariableExport
	16,  // 6: manager.Import.environment:type_name -> manager.VariableEnvironment
	17,  // 7: manager.Import.function:type_name -> manager.VariableFunction
	15,  // 8: manager.Export.export:type_name -> manager.VariableExport
	20,  // 9: manager.ComponentGroupComponent.imports:type_name -> manager.Import
	21,  // 10: manager.ComponentGroupComponent.exports:type_name -> manager.Export
	97,  // 11: manager.CaseComponent.state:type_name -> manager.ResourceState
	98,  // 12: manager.CaseComponent.reference_state:type_name -> manager.CommonState
	98,  // 13: manager.SuiteComponent.state:type_name -> manager.CommonState
	98,  // 14: manager.SuiteComponent.reference_state:type_name -> manager.CommonState
	99,  // 15: manager.SuiteComponent.case_execution_mode:type_name -> manager.ExecutionMode
	98,  // 16: manager.PlanComponent.state:type_name -> manager.CommonState
	100, // 17: manager.PlanComponent.type:type_name -> common.TriggerMode
	101, // 18: manager.PlanComponent.purpose:type_name -> common.PurposeType
	102, // 19: manager.PlanComponent.general_config:type_name -> common.GeneralConfig
	103, // 20: manager.PlanComponent.account_configs:type_name -> common.AccountConfig
	99,  // 21: manager.PlanComponent.suite_execution_mode:type_name -> manager.ExecutionMode
	99,  // 22: manager.PlanComponent.case_execution_mode:type_name -> manager.ExecutionMode
	97,  // 23: manager.InterfaceCaseComponent.state:type_name -> manager.ResourceState
	98,  // 24: manager.InterfaceCaseComponent.reference_state:type_name -> manager.CommonState
	98,  // 25: manager.InterfaceDocumentComponent.state:type_name -> manager.CommonState
	98,  // 26: manager.InterfaceDocumentComponent.reference_state:type_name -> manager.CommonState
	99,  // 27: manager.InterfaceDocumentComponent.case_execution_mode:type_name -> manager.ExecutionMode
	98,  // 28: manager.UICaseComponent.state:type_name -> manager.CommonState
	98,  // 29: manager.UISuiteComponent.state:type_name -> manager.CommonState
	104, // 30: manager.UIPlanMetaData.priority_type:type_name -> common.PriorityType
	105, // 31: manager.UIPlanMetaData.git_config:type_name -> common.GitConfig
	99,  // 32: manager.UIPlanMetaData.suite_execution_mode:type_name -> manager.ExecutionMode
	99,  // 33: manager.UIPlanMetaData.case_execution_mode:type_name -> manager.ExecutionMode
	106, // 34: manager.UIPlanMetaData.device_type:type_name -> common.DeviceType
	107, // 35: manager.UIPlanMetaData.platform_type:type_name -> common.PlatformType
	108, // 36: manager.UIPlanMetaData.test_language:type_name -> common.TestLanguage
	109, // 37: manager.UIPlanMetaData.test_framework:type_name -> common.TestFramework
	110, // 38: manager.UIPlanMetaData.fail_retry:type_name -> common.FailRetry
	98,  // 39: manager.UIPlanComponent.state:type_name -> manager.CommonState
	100, // 40: manager.UIPlanComponent.type:type_name -> common.TriggerMode
	30,  // 41: manager.UIPlanComponent.meta_data:type_name -> manager.UIPlanMetaData
	111, // 42: manager.PerfCaseComponent.rate_limits:type_name -> common.RateLimitV2
	112, // 43: manager.PerfCaseComponent.setup_steps:type_name -> common.PerfCaseStepV2
	112, // 44: manager.PerfCaseComponent.serial_steps:type_name -> common.PerfCaseStepV2
	112, // 45: manager.PerfCaseComponent.parallel_steps:type_name -> common.PerfCaseStepV2
	112, // 46: manager.PerfCaseComponent.teardown_steps:type_name -> common.PerfCaseStepV2
	33,  // 47: manager.PerfCaseComponent.perf_data:type_name -> manager.PerfDataComponent
	113, // 48: manager.PerfCaseComponent.load_generator:type_name -> common.LoadGenerator
	98,  // 49: manager.PerfCaseComponent.state:type_name -> manager.CommonState
	98,  // 50: manager.PerfSuiteComponent.state:type_name -> manager.CommonState
	114, // 51: manager.PerfPlanMetaData.protocol:type_name -> common.Protocol
	115, // 52: manager.PerfPlanMetaData.target_env:type_name -> common.TargetEnvironment
	116, // 53: manager.PerfPlanMetaData.protobuf_configs:type_name -> common.ProtobufConfig
	102, // 54: manager.PerfPlanMetaData.general_config:type_name -> common.GeneralConfig
	103, // 55: manager.PerfPlanMetaData.account_config:type_name -> common.AccountConfig
	117, // 56: manager.PerfPlanMetaData.keepalive:type_name -> common.PerfKeepalive
	118, // 57: manager.PerfPlanMetaData.rate_limits:type_name -> common.PerfRateLimits
	119, // 58: manager.PerfPlanMetaData.services:type_name -> common.PerfServiceMetaData
	120, // 59: manager.PerfPlanMetaData.rules:type_name -> common.PerfStopRule
	100, // 60: manager.PerfPlanComponent.type:type_name -> common.TriggerMode
	36,  // 61: manager.PerfPlanComponent.meta_data:type_name -> manager.PerfPlanMetaData
	98,  // 62: manager.PerfPlanComponent.state:type_name -> manager.CommonState
	103, // 63: manager.StabilityPlanMetaData.account_config:type_name -> common.AccountConfig
	121, // 64: manager.StabilityPlanMetaData.lark_chats:type_name -> common.LarkChat
	106, // 65: manager.StabilityPlanMetaData.device_type:type_name -> common.DeviceType
	107, // 66: manager.StabilityPlanMetaData.platform_type:type_name -> common.PlatformType
	122, // 67: manager.StabilityPlanMetaData.devices:type_name -> common.StabilityCustomDevices
	123, // 68: manager.StabilityPlanMetaData.custom_script:type_name -> common.StabilityCustomScript
	100, // 69: manager.StabilityPlanComponent.type:type_name -> common.TriggerMode
	38,  // 70: manager.StabilityPlanComponent.meta_data:type_name -> manager.StabilityPlanMetaData
	98,  // 71: manager.StabilityPlanComponent.state:type_name -> manager.CommonState
	20,  // 72: manager.SetupComponent.imports:type_name -> manager.Import
	21,  // 73: manager.SetupComponent.exports:type_name -> manager.Export
	20,  // 74: manager.TeardownComponent.imports:type_name -> manager.Import
	20,  // 75: manager.BusinessSingleComponent.imports:type_name -> manager.Import
	21,  // 76: manager.BusinessSingleComponent.exports:type_name -> manager.Export
	20,  // 77: manager.BusinessGroupComponent.imports:type_name -> manager.Import
	21,  // 78: manager.BusinessGroupComponent.exports:type_name -> manager.Export
	6,   // 79: manager.LoopComponent.type:type_name -> manager.LoopComponent.Type
	57,  // 80: manager.LoopComponent.for:type_name -> manager.LoopComponent.For
	58,  // 81: manager.LoopComponent.for_each:type_name -> manager.LoopComponent.ForEach
	59,  // 82: manager.LoopComponent.while:type_name -> manager.LoopComponent.While
	63,  // 83: manager.HttpRequestComponent.authorization:type_name -> manager.HttpRequestComponent.Authorization
	64,  // 84: manager.HttpRequestComponent.headers:type_name -> manager.HttpRequestComponent.KeyValueDesc
	64,  // 85: manager.HttpRequestComponent.query_params:type_name -> manager.HttpRequestComponent.KeyValueDesc
	65,  // 86: manager.HttpRequestComponent.body:type_name -> manager.HttpRequestComponent.Body
	66,  // 87: manager.HttpRequestComponent.timeout:type_name -> manager.HttpRequestComponent.Timeout
	67,  // 88: manager.HttpRequestComponent.assertions:type_name -> manager.HttpRequestComponent.Assertion
	20,  // 89: manager.HttpRequestComponent.imports:type_name -> manager.Import
	68,  // 90: manager.HttpRequestComponent.exports:type_name -> manager.HttpRequestComponent.Export
	20,  // 91: manager.ReferenceComponent.imports:type_name -> manager.Import
	21,  // 92: manager.ReferenceComponent.exports:type_name -> manager.Export
	1,   // 93: manager.ConditionComponent.type:type_name -> manager.ConditionType
	79,  // 94: manager.ConditionComponent.single:type_name -> manager.ConditionComponent.SingleCondition
	80,  // 95: manager.ConditionComponent.group:type_name -> manager.ConditionComponent.GroupCondition
	12,  // 96: manager.WaitComponent.type:type_name -> manager.WaitComponent.Type
	82,  // 97: manager.WaitComponent.sleep:type_name -> manager.WaitComponent.Sleep
	83,  // 98: manager.WaitComponent.manual:type_name -> manager.WaitComponent.Manual
	86,  // 99: manager.AssertComponent.assertions:type_name -> manager.AssertComponent.Assertion
	89,  // 100: manager.PoolAccountComponent.condition:type_name -> manager.PoolAccountComponent.Condition
	90,  // 101: manager.PoolAccountComponent.exports:type_name -> manager.PoolAccountComponent.KeyValPair
	93,  // 102: manager.DataProcessingComponent.processes:type_name -> manager.DataProcessingComponent.Process
	95,  // 103: manager.SqlExecutionComponent.sqls:type_name -> manager.SqlExecutionComponent.Sql
	0,   // 104: manager.VariableFunction.Parameter.source:type_name -> manager.VariableSource
	14,  // 105: manager.VariableFunction.Parameter.manual:type_name -> manager.VariableManual
	15,  // 106: manager.VariableFunction.Parameter.export:type_name -> manager.VariableExport
	16,  // 107: manager.VariableFunction.Parameter.environment:type_name -> manager.VariableEnvironment
	17,  // 108: manager.VariableFunction.Parameter.function:type_name -> manager.VariableFunction
	0,   // 109: manager.LoopComponent.ForEach.source:type_name -> manager.VariableSource
	14,  // 110: manager.LoopComponent.ForEach.manual:type_name -> manager.VariableManual
	15,  // 111: manager.LoopComponent.ForEach.export:type_name -> manager.VariableExport
	16,  // 112: manager.LoopComponent.ForEach.environment:type_name -> manager.VariableEnvironment
	17,  // 113: manager.LoopComponent.ForEach.function:type_name -> manager.VariableFunction
	1,   // 114: manager.LoopComponent.While.type:type_name -> manager.ConditionType
	60,  // 115: manager.LoopComponent.While.single:type_name -> manager.LoopComponent.While.SingleCondition
	61,  // 116: manager.LoopComponent.While.group:type_name -> manager.LoopComponent.While.GroupCondition
	62,  // 117: manager.LoopComponent.While.SingleCondition.left:type_name -> manager.LoopComponent.While.SingleCondition.Value
	62,  // 118: manager.LoopComponent.While.SingleCondition.right:type_name -> manager.LoopComponent.While.SingleCondition.Value
	2,   // 119: manager.LoopComponent.While.GroupCondition.relationship:type_name -> manager.Relationship
	59,  // 120: manager.LoopComponent.While.GroupCondition.conditions:type_name -> manager.LoopComponent.While
	0,   // 121: manager.LoopComponent.While.SingleCondition.Value.source:type_name -> manager.VariableSource
	14,  // 122: manager.LoopComponent.While.SingleCondition.Value.manual:type_name -> manager.VariableManual
	15,  // 123: manager.LoopComponent.While.SingleCondition.Value.export:type_name -> manager.VariableExport
	16,  // 124: manager.LoopComponent.While.SingleCondition.Value.environment:type_name -> manager.VariableEnvironment
	17,  // 125: manager.LoopComponent.While.SingleCondition.Value.function:type_name -> manager.VariableFunction
	7,   // 126: manager.HttpRequestComponent.Authorization.type:type_name -> manager.HttpRequestComponent.Authorization.Type
	69,  // 127: manager.HttpRequestComponent.Authorization.api_key:type_name -> manager.HttpRequestComponent.Authorization.ApiKey
	70,  // 128: manager.HttpRequestComponent.Authorization.bearer_token:type_name -> manager.HttpRequestComponent.Authorization.BearerToken
	71,  // 129: manager.HttpRequestComponent.Authorization.basic_auth:type_name -> manager.HttpRequestComponent.Authorization.BasicAuth
	9,   // 130: manager.HttpRequestComponent.Body.type:type_name -> manager.HttpRequestComponent.Body.ContentType
	64,  // 131: manager.HttpRequestComponent.Body.form_data:type_name -> manager.HttpRequestComponent.KeyValueDesc
	4,   // 132: manager.HttpRequestComponent.Assertion.source:type_name -> manager.ResponseSource
	72,  // 133: manager.HttpRequestComponent.Assertion.headers:type_name -> manager.HttpRequestComponent.Assertion.Headers
	73,  // 134: manager.HttpRequestComponent.Assertion.body:type_name -> manager.HttpRequestComponent.Assertion.Body
	74,  // 135: manager.HttpRequestComponent.Assertion.status_code:type_name -> manager.HttpRequestComponent.Assertion.StatusCode
	4,   // 136: manager.HttpRequestComponent.Export.source:type_name -> manager.ResponseSource
	77,  // 137: manager.HttpRequestComponent.Export.headers:type_name -> manager.HttpRequestComponent.Export.Headers
	78,  // 138: manager.HttpRequestComponent.Export.body:type_name -> manager.HttpRequestComponent.Export.Body
	8,   // 139: manager.HttpRequestComponent.Authorization.ApiKey.add_to:type_name -> manager.HttpRequestComponent.Authorization.ApiKey.AddTo
	10,  // 140: manager.HttpRequestComponent.Assertion.Body.type:type_name -> manager.HttpRequestComponent.Assertion.Body.Type
	75,  // 141: manager.HttpRequestComponent.Assertion.Body.jmespath:type_name -> manager.HttpRequestComponent.Assertion.Body.JMESPath
	76,  // 142: manager.HttpRequestComponent.Assertion.Body.regex:type_name -> manager.HttpRequestComponent.Assertion.Body.Regex
	11,  // 143: manager.HttpRequestComponent.Export.Body.type:type_name -> manager.HttpRequestComponent.Export.Body.Type
	81,  // 144: manager.ConditionComponent.SingleCondition.left:type_name -> manager.ConditionComponent.SingleCondition.Value
	81,  // 145: manager.ConditionComponent.SingleCondition.right:type_name -> manager.ConditionComponent.SingleCondition.Value
	2,   // 146: manager.ConditionComponent.GroupCondition.relationship:type_name -> manager.Relationship
	49,  // 147: manager.ConditionComponent.GroupCondition.conditions:type_name -> manager.ConditionComponent
	0,   // 148: manager.ConditionComponent.SingleCondition.Value.source:type_name -> manager.VariableSource
	14,  // 149: manager.ConditionComponent.SingleCondition.Value.manual:type_name -> manager.VariableManual
	15,  // 150: manager.ConditionComponent.SingleCondition.Value.export:type_name -> manager.VariableExport
	16,  // 151: manager.ConditionComponent.SingleCondition.Value.environment:type_name -> manager.VariableEnvironment
	17,  // 152: manager.ConditionComponent.SingleCondition.Value.function:type_name -> manager.VariableFunction
	13,  // 153: manager.WaitComponent.Manual.default_action:type_name -> manager.WaitComponent.Manual.Action
	0,   // 154: manager.AssertComponent.Expected.source:type_name -> manager.VariableSource
	14,  // 155: manager.AssertComponent.Expected.manual:type_name -> manager.VariableManual
	15,  // 156: manager.AssertComponent.Expected.export:type_name -> manager.VariableExport
	16,  // 157: manager.AssertComponent.Expected.environment:type_name -> manager.VariableEnvironment
	17,  // 158: manager.AssertComponent.Expected.function:type_name -> manager.VariableFunction
	84,  // 159: manager.AssertComponent.Assertion.actual:type_name -> manager.AssertComponent.Actual
	85,  // 160: manager.AssertComponent.Assertion.expected:type_name -> manager.AssertComponent.Expected
	91,  // 161: manager.PoolAccountComponent.SingleCondition.between:type_name -> manager.PoolAccountComponent.SingleCondition.Between
	92,  // 162: manager.PoolAccountComponent.SingleCondition.other:type_name -> manager.PoolAccountComponent.SingleCondition.Other
	2,   // 163: manager.PoolAccountComponent.GroupCondition.relationship:type_name -> manager.Relationship
	89,  // 164: manager.PoolAccountComponent.GroupCondition.conditions:type_name -> manager.PoolAccountComponent.Condition
	1,   // 165: manager.PoolAccountComponent.Condition.type:type_name -> manager.ConditionType
	87,  // 166: manager.PoolAccountComponent.Condition.single:type_name -> manager.PoolAccountComponent.SingleCondition
	88,  // 167: manager.PoolAccountComponent.Condition.group:type_name -> manager.PoolAccountComponent.GroupCondition
	17,  // 168: manager.DataProcessingComponent.Process.function:type_name -> manager.VariableFunction
	0,   // 169: manager.SqlExecutionComponent.Value.source:type_name -> manager.VariableSource
	14,  // 170: manager.SqlExecutionComponent.Value.manual:type_name -> manager.VariableManual
	15,  // 171: manager.SqlExecutionComponent.Value.export:type_name -> manager.VariableExport
	16,  // 172: manager.SqlExecutionComponent.Value.environment:type_name -> manager.VariableEnvironment
	17,  // 173: manager.SqlExecutionComponent.Value.function:type_name -> manager.VariableFunction
	5,   // 174: manager.SqlExecutionComponent.Sql.type:type_name -> manager.DataSourceType
	94,  // 175: manager.SqlExecutionComponent.Sql.host:type_name -> manager.SqlExecutionComponent.Value
	94,  // 176: manager.SqlExecutionComponent.Sql.port:type_name -> manager.SqlExecutionComponent.Value
	94,  // 177: manager.SqlExecutionComponent.Sql.user:type_name -> manager.SqlExecutionComponent.Value
	94,  // 178: manager.SqlExecutionComponent.Sql.password:type_name -> manager.SqlExecutionComponent.Value
	94,  // 179: manager.SqlExecutionComponent.Sql.database:type_name -> manager.SqlExecutionComponent.Value
	180, // [180:180] is the sub-list for method output_type
	180, // [180:180] is the sub-list for method input_type
	180, // [180:180] is the sub-list for extension type_name
	180, // [180:180] is the sub-list for extension extendee
	0,   // [0:180] is the sub-list for field type_name
}

func init() { file_manager_component_proto_init() }
func file_manager_component_proto_init() {
	if File_manager_component_proto != nil {
		return
	}
	file_manager_base_proto_init()
	file_manager_function_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_component_proto_rawDesc,
			NumEnums:      14,
			NumMessages:   82,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_component_proto_goTypes,
		DependencyIndexes: file_manager_component_proto_depIdxs,
		EnumInfos:         file_manager_component_proto_enumTypes,
		MessageInfos:      file_manager_component_proto_msgTypes,
	}.Build()
	File_manager_component_proto = out.File
	file_manager_component_proto_rawDesc = nil
	file_manager_component_proto_goTypes = nil
	file_manager_component_proto_depIdxs = nil
}
