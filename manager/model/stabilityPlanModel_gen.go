// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	stabilityPlanTableName           = "`stability_plan`"
	stabilityPlanFieldNames          = builder.RawFieldNames(&StabilityPlan{})
	stabilityPlanRows                = strings.Join(stabilityPlanFieldNames, ",")
	stabilityPlanRowsExpectAutoSet   = strings.Join(stringx.Remove(stabilityPlanFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	stabilityPlanRowsWithPlaceHolder = strings.Join(stringx.Remove(stabilityPlanFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerStabilityPlanIdPrefix              = "cache:manager:stabilityPlan:id:"
	cacheManagerStabilityPlanProjectIdPlanIdPrefix = "cache:manager:stabilityPlan:projectId:planId:"
)

type (
	stabilityPlanModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *StabilityPlan) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*StabilityPlan, error)
		FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*StabilityPlan, error)
		Update(ctx context.Context, session sqlx.Session, data *StabilityPlan) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultStabilityPlanModel struct {
		sqlc.CachedConn
		table string
	}

	StabilityPlan struct {
		Id              int64          `db:"id"`                // 自增ID
		ProjectId       string         `db:"project_id"`        // 项目ID
		CategoryId      string         `db:"category_id"`       // 所属分类ID
		PlanId          string         `db:"plan_id"`           // 计划ID
		Name            string         `db:"name"`              // 计划名称
		Description     sql.NullString `db:"description"`       // 计划描述
		State           int64          `db:"state"`             // 计划状态（生效、失效）
		Type            string         `db:"type"`              // 计划类型（手动、定时）
		PriorityType    int64          `db:"priority_type"`     // 优先级 oneof=0 1 2 3 4（Default、Middle、High、Ultra、Low)
		CronExpression  sql.NullString `db:"cron_expression"`   // 定时触发计划的Cron表达式
		Tags            sql.NullString `db:"tags"`              // 标签
		AccountConfigId sql.NullString `db:"account_config_id"` // 池账号配置ID
		DeviceType      int64          `db:"device_type"`       // 设备类型（真机、云手机）
		PlatformType    int64          `db:"platform_type"`     // 测试系统（Android、IOS）
		Devices         sql.NullString `db:"devices"`           // 指定设备编号
		PackageName     string         `db:"package_name"`      // 用于启动APP
		AppDownloadLink string         `db:"app_download_link"` // APP下载地址
		Duration        int64          `db:"duration"`          // 运行时长，单位为分钟
		Activities      sql.NullString `db:"activities"`        // activity白名单
		CustomScript    sql.NullString `db:"custom_script"`     // 自定义脚本
		MaintainedBy    sql.NullString `db:"maintained_by"`     // 维护者的用户ID
		Deleted         int64          `db:"deleted"`           // 逻辑删除标识（未删除、已删除）
		CreatedBy       string         `db:"created_by"`        // 创建者的用户ID
		UpdatedBy       string         `db:"updated_by"`        // 最近一次更新者的用户ID
		DeletedBy       sql.NullString `db:"deleted_by"`        // 删除者的用户ID
		CreatedAt       time.Time      `db:"created_at"`        // 创建时间
		UpdatedAt       time.Time      `db:"updated_at"`        // 更新时间
		DeletedAt       sql.NullTime   `db:"deleted_at"`        // 删除时间
	}
)

func newStabilityPlanModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultStabilityPlanModel {
	return &defaultStabilityPlanModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`stability_plan`",
	}
}

func (m *defaultStabilityPlanModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerStabilityPlanIdKey := fmt.Sprintf("%s%v", cacheManagerStabilityPlanIdPrefix, id)
	managerStabilityPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerStabilityPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerStabilityPlanIdKey, managerStabilityPlanProjectIdPlanIdKey)
	return err
}

func (m *defaultStabilityPlanModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerStabilityPlanIdKey := fmt.Sprintf("%s%v", cacheManagerStabilityPlanIdPrefix, id)
	managerStabilityPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerStabilityPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerStabilityPlanIdKey, managerStabilityPlanProjectIdPlanIdKey)
	return err
}

func (m *defaultStabilityPlanModel) FindOne(ctx context.Context, id int64) (*StabilityPlan, error) {
	managerStabilityPlanIdKey := fmt.Sprintf("%s%v", cacheManagerStabilityPlanIdPrefix, id)
	var resp StabilityPlan
	err := m.QueryRowCtx(ctx, &resp, managerStabilityPlanIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", stabilityPlanRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityPlanModel) FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*StabilityPlan, error) {
	managerStabilityPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerStabilityPlanProjectIdPlanIdPrefix, projectId, planId)
	var resp StabilityPlan
	err := m.QueryRowIndexCtx(ctx, &resp, managerStabilityPlanProjectIdPlanIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `plan_id` = ? and `deleted` = ? limit 1", stabilityPlanRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, planId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityPlanModel) Insert(ctx context.Context, session sqlx.Session, data *StabilityPlan) (sql.Result, error) {
	managerStabilityPlanIdKey := fmt.Sprintf("%s%v", cacheManagerStabilityPlanIdPrefix, data.Id)
	managerStabilityPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerStabilityPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, stabilityPlanRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.State, data.Type, data.PriorityType, data.CronExpression, data.Tags, data.AccountConfigId, data.DeviceType, data.PlatformType, data.Devices, data.PackageName, data.AppDownloadLink, data.Duration, data.Activities, data.CustomScript, data.MaintainedBy, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.State, data.Type, data.PriorityType, data.CronExpression, data.Tags, data.AccountConfigId, data.DeviceType, data.PlatformType, data.Devices, data.PackageName, data.AppDownloadLink, data.Duration, data.Activities, data.CustomScript, data.MaintainedBy, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerStabilityPlanIdKey, managerStabilityPlanProjectIdPlanIdKey)
}

func (m *defaultStabilityPlanModel) Update(ctx context.Context, session sqlx.Session, newData *StabilityPlan) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerStabilityPlanIdKey := fmt.Sprintf("%s%v", cacheManagerStabilityPlanIdPrefix, data.Id)
	managerStabilityPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerStabilityPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, stabilityPlanRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.PlanId, newData.Name, newData.Description, newData.State, newData.Type, newData.PriorityType, newData.CronExpression, newData.Tags, newData.AccountConfigId, newData.DeviceType, newData.PlatformType, newData.Devices, newData.PackageName, newData.AppDownloadLink, newData.Duration, newData.Activities, newData.CustomScript, newData.MaintainedBy, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.PlanId, newData.Name, newData.Description, newData.State, newData.Type, newData.PriorityType, newData.CronExpression, newData.Tags, newData.AccountConfigId, newData.DeviceType, newData.PlatformType, newData.Devices, newData.PackageName, newData.AppDownloadLink, newData.Duration, newData.Activities, newData.CustomScript, newData.MaintainedBy, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerStabilityPlanIdKey, managerStabilityPlanProjectIdPlanIdKey)
}

func (m *defaultStabilityPlanModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerStabilityPlanIdPrefix, primary)
}

func (m *defaultStabilityPlanModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", stabilityPlanRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultStabilityPlanModel) tableName() string {
	return m.table
}
