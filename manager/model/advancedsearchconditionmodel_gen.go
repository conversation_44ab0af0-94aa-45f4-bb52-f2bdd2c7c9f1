// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	advancedSearchConditionTableName           = "`advanced_search_condition`"
	advancedSearchConditionFieldNames          = builder.RawFieldNames(&AdvancedSearchCondition{})
	advancedSearchConditionRows                = strings.Join(advancedSearchConditionFieldNames, ",")
	advancedSearchConditionRowsExpectAutoSet   = strings.Join(stringx.Remove(advancedSearchConditionFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	advancedSearchConditionRowsWithPlaceHolder = strings.Join(stringx.Remove(advancedSearchConditionFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerAdvancedSearchConditionIdPrefix = "cache:manager:advancedSearchCondition:id:"
)

type (
	advancedSearchConditionModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *AdvancedSearchCondition) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*AdvancedSearchCondition, error)
		Update(ctx context.Context, session sqlx.Session, data *AdvancedSearchCondition) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultAdvancedSearchConditionModel struct {
		sqlc.CachedConn
		table string
	}

	AdvancedSearchCondition struct {
		Id          int64     `db:"id"`
		ConditionId string    `db:"condition_id"` // 条件ID
		FrontName   string    `db:"front_name"`   // 前端展示名称
		Compare     string    `db:"compare"`      // 例如：EQ、NE、GT
		Deleted     int64     `db:"deleted"`      // 是否已删除
		CreatedAt   time.Time `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time `db:"updated_at"`   // 更新时间
	}
)

func newAdvancedSearchConditionModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultAdvancedSearchConditionModel {
	return &defaultAdvancedSearchConditionModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`advanced_search_condition`",
	}
}

func (m *defaultAdvancedSearchConditionModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerAdvancedSearchConditionIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchConditionIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerAdvancedSearchConditionIdKey)
	return err
}

func (m *defaultAdvancedSearchConditionModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerAdvancedSearchConditionIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchConditionIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerAdvancedSearchConditionIdKey)
	return err
}

func (m *defaultAdvancedSearchConditionModel) FindOne(ctx context.Context, id int64) (*AdvancedSearchCondition, error) {
	managerAdvancedSearchConditionIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchConditionIdPrefix, id)
	var resp AdvancedSearchCondition
	err := m.QueryRowCtx(ctx, &resp, managerAdvancedSearchConditionIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", advancedSearchConditionRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAdvancedSearchConditionModel) Insert(ctx context.Context, session sqlx.Session, data *AdvancedSearchCondition) (sql.Result, error) {
	managerAdvancedSearchConditionIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchConditionIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, advancedSearchConditionRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ConditionId, data.FrontName, data.Compare, data.Deleted)
		}
		return conn.ExecCtx(ctx, query, data.ConditionId, data.FrontName, data.Compare, data.Deleted)
	}, managerAdvancedSearchConditionIdKey)
}

func (m *defaultAdvancedSearchConditionModel) Update(ctx context.Context, session sqlx.Session, data *AdvancedSearchCondition) (sql.Result, error) {

	managerAdvancedSearchConditionIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchConditionIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, advancedSearchConditionRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ConditionId, data.FrontName, data.Compare, data.Deleted, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ConditionId, data.FrontName, data.Compare, data.Deleted, data.Id)
	}, managerAdvancedSearchConditionIdKey)
}

func (m *defaultAdvancedSearchConditionModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerAdvancedSearchConditionIdPrefix, primary)
}

func (m *defaultAdvancedSearchConditionModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", advancedSearchConditionRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultAdvancedSearchConditionModel) tableName() string {
	return m.table
}
