// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	componentTableName           = "`component`"
	componentFieldNames          = builder.RawFieldNames(&Component{})
	componentRows                = strings.Join(componentFieldNames, ",")
	componentRowsExpectAutoSet   = strings.Join(stringx.Remove(componentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	componentRowsWithPlaceHolder = strings.Join(stringx.Remove(componentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerComponentIdPrefix                                                  = "cache:manager:component:id:"
	cacheManagerComponentProjectIdParentIdParentTypeParentVersionComponentIdPrefix = "cache:manager:component:projectId:parentId:parentType:parentVersion:componentId:"
)

type (
	componentModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Component) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Component, error)
		FindOneByProjectIdParentIdParentTypeParentVersionComponentId(ctx context.Context, projectId string, parentId string, parentType string, parentVersion string, componentId string) (*Component, error)
		Update(ctx context.Context, session sqlx.Session, data *Component) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultComponentModel struct {
		sqlc.CachedConn
		table string
	}

	Component struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ParentId      string         `db:"parent_id"`      // 父对象的ID（组件组ID、API用例ID、接口用例ID）
		ParentType    string         `db:"parent_type"`    // 父对象的类型（组件组、API用例、接口用例）
		ParentVersion string         `db:"parent_version"` // 父对象的版本（组件组版本、API用例版本、接口用例版本）
		ComponentId   string         `db:"component_id"`   // 组件ID
		ComponentType string         `db:"component_type"` // 组件类型
		Name          string         `db:"name"`           // 组件名称
		Description   sql.NullString `db:"description"`    // 组件描述
		Data          sql.NullString `db:"data"`           // 组件数据（不同类型的组件的数据结构不一样）
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newComponentModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultComponentModel {
	return &defaultComponentModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`component`",
	}
}

func (m *defaultComponentModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerComponentIdKey := fmt.Sprintf("%s%v", cacheManagerComponentIdPrefix, id)
	managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerComponentProjectIdParentIdParentTypeParentVersionComponentIdPrefix, data.ProjectId, data.ParentId, data.ParentType, data.ParentVersion, data.ComponentId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerComponentIdKey, managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey)
	return err
}

func (m *defaultComponentModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerComponentIdKey := fmt.Sprintf("%s%v", cacheManagerComponentIdPrefix, id)
	managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerComponentProjectIdParentIdParentTypeParentVersionComponentIdPrefix, data.ProjectId, data.ParentId, data.ParentType, data.ParentVersion, data.ComponentId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerComponentIdKey, managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey)
	return err
}

func (m *defaultComponentModel) FindOne(ctx context.Context, id int64) (*Component, error) {
	managerComponentIdKey := fmt.Sprintf("%s%v", cacheManagerComponentIdPrefix, id)
	var resp Component
	err := m.QueryRowCtx(ctx, &resp, managerComponentIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", componentRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultComponentModel) FindOneByProjectIdParentIdParentTypeParentVersionComponentId(ctx context.Context, projectId string, parentId string, parentType string, parentVersion string, componentId string) (*Component, error) {
	managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerComponentProjectIdParentIdParentTypeParentVersionComponentIdPrefix, projectId, parentId, parentType, parentVersion, componentId)
	var resp Component
	err := m.QueryRowIndexCtx(ctx, &resp, managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `parent_id` = ? and `parent_type` = ? and `parent_version` = ? and `component_id` = ? and `deleted` = ? limit 1", componentRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, parentId, parentType, parentVersion, componentId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultComponentModel) Insert(ctx context.Context, session sqlx.Session, data *Component) (sql.Result, error) {
	managerComponentIdKey := fmt.Sprintf("%s%v", cacheManagerComponentIdPrefix, data.Id)
	managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerComponentProjectIdParentIdParentTypeParentVersionComponentIdPrefix, data.ProjectId, data.ParentId, data.ParentType, data.ParentVersion, data.ComponentId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, componentRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ParentId, data.ParentType, data.ParentVersion, data.ComponentId, data.ComponentType, data.Name, data.Description, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ParentId, data.ParentType, data.ParentVersion, data.ComponentId, data.ComponentType, data.Name, data.Description, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerComponentIdKey, managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey)
}

func (m *defaultComponentModel) Update(ctx context.Context, session sqlx.Session, newData *Component) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerComponentIdKey := fmt.Sprintf("%s%v", cacheManagerComponentIdPrefix, data.Id)
	managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerComponentProjectIdParentIdParentTypeParentVersionComponentIdPrefix, data.ProjectId, data.ParentId, data.ParentType, data.ParentVersion, data.ComponentId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, componentRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ParentId, newData.ParentType, newData.ParentVersion, newData.ComponentId, newData.ComponentType, newData.Name, newData.Description, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ParentId, newData.ParentType, newData.ParentVersion, newData.ComponentId, newData.ComponentType, newData.Name, newData.Description, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerComponentIdKey, managerComponentProjectIdParentIdParentTypeParentVersionComponentIdKey)
}

func (m *defaultComponentModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerComponentIdPrefix, primary)
}

func (m *defaultComponentModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", componentRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultComponentModel) tableName() string {
	return m.table
}
