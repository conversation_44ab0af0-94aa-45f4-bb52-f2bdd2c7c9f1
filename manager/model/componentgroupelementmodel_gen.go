// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	componentGroupElementTableName           = "`component_group_element`"
	componentGroupElementFieldNames          = builder.RawFieldNames(&ComponentGroupElement{})
	componentGroupElementRows                = strings.Join(componentGroupElementFieldNames, ",")
	componentGroupElementRowsExpectAutoSet   = strings.Join(stringx.Remove(componentGroupElementFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	componentGroupElementRowsWithPlaceHolder = strings.Join(stringx.Remove(componentGroupElementFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerComponentGroupElementIdPrefix                                        = "cache:manager:componentGroupElement:id:"
	cacheManagerComponentGroupElementProjectIdComponentGroupIdVersionElementIdPrefix = "cache:manager:componentGroupElement:projectId:componentGroupId:version:elementId:"
)

type (
	componentGroupElementModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ComponentGroupElement) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ComponentGroupElement, error)
		FindOneByProjectIdComponentGroupIdVersionElementId(ctx context.Context, projectId string, componentGroupId string, version string, elementId string) (*ComponentGroupElement, error)
		Update(ctx context.Context, session sqlx.Session, data *ComponentGroupElement) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultComponentGroupElementModel struct {
		sqlc.CachedConn
		table string
	}

	ComponentGroupElement struct {
		Id               int64          `db:"id"`
		ProjectId        string         `db:"project_id"`         // 项目ID
		ComponentGroupId string         `db:"component_group_id"` // 组件组ID
		Version          string         `db:"version"`            // 组件组版本
		ElementId        string         `db:"element_id"`         // 元素ID
		ElementType      string         `db:"element_type"`       // 元素类型（点、线、框）
		Data             string         `db:"data"`               // 元素数据（不同类型的元素的数据结构不一样）
		Deleted          int64          `db:"deleted"`            // 逻辑删除标识（未删除、已删除）
		CreatedBy        string         `db:"created_by"`         // 创建者的用户ID
		UpdatedBy        string         `db:"updated_by"`         // 最近一次更新者的用户ID
		DeletedBy        sql.NullString `db:"deleted_by"`         // 删除者的用户ID
		CreatedAt        time.Time      `db:"created_at"`         // 创建时间
		UpdatedAt        time.Time      `db:"updated_at"`         // 更新时间
		DeletedAt        sql.NullTime   `db:"deleted_at"`         // 删除时间
	}
)

func newComponentGroupElementModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultComponentGroupElementModel {
	return &defaultComponentGroupElementModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`component_group_element`",
	}
}

func (m *defaultComponentGroupElementModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerComponentGroupElementIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, id)
	managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerComponentGroupElementProjectIdComponentGroupIdVersionElementIdPrefix, data.ProjectId, data.ComponentGroupId, data.Version, data.ElementId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerComponentGroupElementIdKey, managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey)
	return err
}

func (m *defaultComponentGroupElementModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerComponentGroupElementIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, id)
	managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerComponentGroupElementProjectIdComponentGroupIdVersionElementIdPrefix, data.ProjectId, data.ComponentGroupId, data.Version, data.ElementId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerComponentGroupElementIdKey, managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey)
	return err
}

func (m *defaultComponentGroupElementModel) FindOne(ctx context.Context, id int64) (*ComponentGroupElement, error) {
	managerComponentGroupElementIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, id)
	var resp ComponentGroupElement
	err := m.QueryRowCtx(ctx, &resp, managerComponentGroupElementIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", componentGroupElementRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultComponentGroupElementModel) FindOneByProjectIdComponentGroupIdVersionElementId(ctx context.Context, projectId string, componentGroupId string, version string, elementId string) (*ComponentGroupElement, error) {
	managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerComponentGroupElementProjectIdComponentGroupIdVersionElementIdPrefix, projectId, componentGroupId, version, elementId)
	var resp ComponentGroupElement
	err := m.QueryRowIndexCtx(ctx, &resp, managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `component_group_id` = ? and `version` = ? and `element_id` = ? and `deleted` = ? limit 1", componentGroupElementRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, componentGroupId, version, elementId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultComponentGroupElementModel) Insert(ctx context.Context, session sqlx.Session, data *ComponentGroupElement) (sql.Result, error) {
	managerComponentGroupElementIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, data.Id)
	managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerComponentGroupElementProjectIdComponentGroupIdVersionElementIdPrefix, data.ProjectId, data.ComponentGroupId, data.Version, data.ElementId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, componentGroupElementRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ComponentGroupId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ComponentGroupId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerComponentGroupElementIdKey, managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey)
}

func (m *defaultComponentGroupElementModel) Update(ctx context.Context, session sqlx.Session, newData *ComponentGroupElement) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerComponentGroupElementIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, data.Id)
	managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerComponentGroupElementProjectIdComponentGroupIdVersionElementIdPrefix, data.ProjectId, data.ComponentGroupId, data.Version, data.ElementId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, componentGroupElementRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ComponentGroupId, newData.Version, newData.ElementId, newData.ElementType, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ComponentGroupId, newData.Version, newData.ElementId, newData.ElementType, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerComponentGroupElementIdKey, managerComponentGroupElementProjectIdComponentGroupIdVersionElementIdKey)
}

func (m *defaultComponentGroupElementModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, primary)
}

func (m *defaultComponentGroupElementModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", componentGroupElementRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultComponentGroupElementModel) tableName() string {
	return m.table
}
