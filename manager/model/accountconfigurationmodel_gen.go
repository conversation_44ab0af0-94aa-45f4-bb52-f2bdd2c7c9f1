// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	accountConfigurationTableName           = "`account_configuration`"
	accountConfigurationFieldNames          = builder.RawFieldNames(&AccountConfiguration{})
	accountConfigurationRows                = strings.Join(accountConfigurationFieldNames, ",")
	accountConfigurationRowsExpectAutoSet   = strings.Join(stringx.Remove(accountConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	accountConfigurationRowsWithPlaceHolder = strings.Join(stringx.Remove(accountConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerAccountConfigurationIdPrefix                = "cache:manager:accountConfiguration:id:"
	cacheManagerAccountConfigurationProjectIdConfigIdPrefix = "cache:manager:accountConfiguration:projectId:configId:"
)

type (
	accountConfigurationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *AccountConfiguration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*AccountConfiguration, error)
		FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*AccountConfiguration, error)
		Update(ctx context.Context, session sqlx.Session, data *AccountConfiguration) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultAccountConfigurationModel struct {
		sqlc.CachedConn
		table string
	}

	AccountConfiguration struct {
		Id           int64          `db:"id"`
		ProjectId    string         `db:"project_id"`     // 项目ID
		ConfigId     string         `db:"config_id"`      // 池账号配置ID
		Name         string         `db:"name"`           // 池账号配置名称
		Description  sql.NullString `db:"description"`    // 池账号配置描述
		ProductType  int64          `db:"product_type"`   // 产品类型
		ProductName  string         `db:"product_name"`   // 产品名称
		PoolEnvTable string         `db:"pool_env_table"` // 账号池环境表名称
		PoolEnvName  string         `db:"pool_env_name"`  // 账号池环境名称
		Deleted      int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy    string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newAccountConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultAccountConfigurationModel {
	return &defaultAccountConfigurationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`account_configuration`",
	}
}

func (m *defaultAccountConfigurationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerAccountConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationIdPrefix, id)
	managerAccountConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerAccountConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerAccountConfigurationIdKey, managerAccountConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultAccountConfigurationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerAccountConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationIdPrefix, id)
	managerAccountConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerAccountConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerAccountConfigurationIdKey, managerAccountConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultAccountConfigurationModel) FindOne(ctx context.Context, id int64) (*AccountConfiguration, error) {
	managerAccountConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationIdPrefix, id)
	var resp AccountConfiguration
	err := m.QueryRowCtx(ctx, &resp, managerAccountConfigurationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", accountConfigurationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAccountConfigurationModel) FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*AccountConfiguration, error) {
	managerAccountConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerAccountConfigurationProjectIdConfigIdPrefix, projectId, configId)
	var resp AccountConfiguration
	err := m.QueryRowIndexCtx(ctx, &resp, managerAccountConfigurationProjectIdConfigIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `config_id` = ? and `deleted` = ? limit 1", accountConfigurationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, configId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAccountConfigurationModel) Insert(ctx context.Context, session sqlx.Session, data *AccountConfiguration) (sql.Result, error) {
	managerAccountConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationIdPrefix, data.Id)
	managerAccountConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerAccountConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, accountConfigurationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Name, data.Description, data.ProductType, data.ProductName, data.PoolEnvTable, data.PoolEnvName, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Name, data.Description, data.ProductType, data.ProductName, data.PoolEnvTable, data.PoolEnvName, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerAccountConfigurationIdKey, managerAccountConfigurationProjectIdConfigIdKey)
}

func (m *defaultAccountConfigurationModel) Update(ctx context.Context, session sqlx.Session, newData *AccountConfiguration) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerAccountConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationIdPrefix, data.Id)
	managerAccountConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerAccountConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, accountConfigurationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Name, newData.Description, newData.ProductType, newData.ProductName, newData.PoolEnvTable, newData.PoolEnvName, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Name, newData.Description, newData.ProductType, newData.ProductName, newData.PoolEnvTable, newData.PoolEnvName, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerAccountConfigurationIdKey, managerAccountConfigurationProjectIdConfigIdKey)
}

func (m *defaultAccountConfigurationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerAccountConfigurationIdPrefix, primary)
}

func (m *defaultAccountConfigurationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", accountConfigurationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultAccountConfigurationModel) tableName() string {
	return m.table
}
