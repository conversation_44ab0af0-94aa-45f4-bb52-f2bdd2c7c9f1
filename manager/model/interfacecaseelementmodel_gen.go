// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceCaseElementTableName           = "`interface_case_element`"
	interfaceCaseElementFieldNames          = builder.RawFieldNames(&InterfaceCaseElement{})
	interfaceCaseElementRows                = strings.Join(interfaceCaseElementFieldNames, ",")
	interfaceCaseElementRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceCaseElementFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceCaseElementRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceCaseElementFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceCaseElementIdPrefix                              = "cache:manager:interfaceCaseElement:id:"
	cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix = "cache:manager:interfaceCaseElement:projectId:caseId:version:elementId:"
)

type (
	interfaceCaseElementModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceCaseElement) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceCaseElement, error)
		FindOneByProjectIdCaseIdVersionElementId(ctx context.Context, projectId string, caseId string, version string, elementId string) (*InterfaceCaseElement, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceCaseElement) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceCaseElementModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceCaseElement struct {
		Id          int64          `db:"id"`
		ProjectId   string         `db:"project_id"`   // 项目ID
		CaseId      string         `db:"case_id"`      // 用例ID
		Version     string         `db:"version"`      // 用例版本
		ElementId   string         `db:"element_id"`   // 元素ID
		ElementType string         `db:"element_type"` // 元素类型（点、线、框）
		Data        string         `db:"data"`         // 元素数据（不同类型的元素的数据结构不一样）
		Deleted     int64          `db:"deleted"`      // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`   // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`   // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`   // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`   // 删除时间
	}
)

func newInterfaceCaseElementModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceCaseElementModel {
	return &defaultInterfaceCaseElementModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_case_element`",
	}
}

func (m *defaultInterfaceCaseElementModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, id)
	managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId, data.Version, data.ElementId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceCaseElementIdKey, managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey)
	return err
}

func (m *defaultInterfaceCaseElementModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, id)
	managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId, data.Version, data.ElementId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceCaseElementIdKey, managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey)
	return err
}

func (m *defaultInterfaceCaseElementModel) FindOne(ctx context.Context, id int64) (*InterfaceCaseElement, error) {
	managerInterfaceCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, id)
	var resp InterfaceCaseElement
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceCaseElementIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceCaseElementRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceCaseElementModel) FindOneByProjectIdCaseIdVersionElementId(ctx context.Context, projectId string, caseId string, version string, elementId string) (*InterfaceCaseElement, error) {
	managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix, projectId, caseId, version, elementId)
	var resp InterfaceCaseElement
	err := m.QueryRowIndexCtx(ctx, &resp, managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `case_id` = ? and `version` = ? and `element_id` = ? and `deleted` = ? limit 1", interfaceCaseElementRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, caseId, version, elementId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceCaseElementModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceCaseElement) (sql.Result, error) {
	managerInterfaceCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, data.Id)
	managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId, data.Version, data.ElementId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceCaseElementRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceCaseElementIdKey, managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey)
}

func (m *defaultInterfaceCaseElementModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceCaseElement) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, data.Id)
	managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId, data.Version, data.ElementId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceCaseElementRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.Version, newData.ElementId, newData.ElementType, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.Version, newData.ElementId, newData.ElementType, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerInterfaceCaseElementIdKey, managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey)
}

func (m *defaultInterfaceCaseElementModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, primary)
}

func (m *defaultInterfaceCaseElementModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceCaseElementRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceCaseElementModel) tableName() string {
	return m.table
}
