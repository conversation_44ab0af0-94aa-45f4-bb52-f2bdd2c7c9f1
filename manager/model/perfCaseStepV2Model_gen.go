// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfCaseStepV2TableName           = "`perf_case_step_v2`"
	perfCaseStepV2FieldNames          = builder.RawFieldNames(&PerfCaseStepV2{})
	perfCaseStepV2Rows                = strings.Join(perfCaseStepV2FieldNames, ",")
	perfCaseStepV2RowsExpectAutoSet   = strings.Join(stringx.Remove(perfCaseStepV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfCaseStepV2RowsWithPlaceHolder = strings.Join(stringx.Remove(perfCaseStepV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfCaseStepV2IdPrefix                    = "cache:manager:perfCaseStepV2:id:"
	cacheManagerPerfCaseStepV2ProjectIdCaseIdStepIdPrefix = "cache:manager:perfCaseStepV2:projectId:caseId:stepId:"
)

type (
	perfCaseStepV2Model interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfCaseStepV2) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfCaseStepV2, error)
		FindOneByProjectIdCaseIdStepId(ctx context.Context, projectId string, caseId string, stepId string) (*PerfCaseStepV2, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfCaseStepV2) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfCaseStepV2Model struct {
		sqlc.CachedConn
		table string
	}

	PerfCaseStepV2 struct {
		Id         int64          `db:"id"`
		ProjectId  string         `db:"project_id"`  // 项目ID
		CaseId     string         `db:"case_id"`     // 压测用例ID
		StepId     string         `db:"step_id"`     // 压测用例步骤ID
		Type       string         `db:"type"`        // 压测用例步骤类型（前置、串行、并行、后置）
		Index      int64          `db:"index"`       // 序号
		Name       string         `db:"name"`        // 压测用例步骤名称
		RateLimits sql.NullString `db:"rate_limits"` // 限流配置
		Url        string         `db:"url"`         // 请求URL
		Method     string         `db:"method"`      // 请求方法
		Headers    sql.NullString `db:"headers"`     // 请求头
		Body       string         `db:"body"`        // 请求体
		Exports    sql.NullString `db:"exports"`     // 提取参数
		Sleep      string         `db:"sleep"`       // 请求后休眠时间
		TargetRps  int64          `db:"target_rps"`  // 目标的RPS
		State      int64          `db:"state"`       // 计划状态（生效、失效）
		Deleted    int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy  string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy  string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy  sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt  time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt  time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt  sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newPerfCaseStepV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfCaseStepV2Model {
	return &defaultPerfCaseStepV2Model{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_case_step_v2`",
	}
}

func (m *defaultPerfCaseStepV2Model) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfCaseStepV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepV2IdPrefix, id)
	managerPerfCaseStepV2ProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepV2ProjectIdCaseIdStepIdPrefix, data.ProjectId, data.CaseId, data.StepId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfCaseStepV2IdKey, managerPerfCaseStepV2ProjectIdCaseIdStepIdKey)
	return err
}

func (m *defaultPerfCaseStepV2Model) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfCaseStepV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepV2IdPrefix, id)
	managerPerfCaseStepV2ProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepV2ProjectIdCaseIdStepIdPrefix, data.ProjectId, data.CaseId, data.StepId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfCaseStepV2IdKey, managerPerfCaseStepV2ProjectIdCaseIdStepIdKey)
	return err
}

func (m *defaultPerfCaseStepV2Model) FindOne(ctx context.Context, id int64) (*PerfCaseStepV2, error) {
	managerPerfCaseStepV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepV2IdPrefix, id)
	var resp PerfCaseStepV2
	err := m.QueryRowCtx(ctx, &resp, managerPerfCaseStepV2IdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfCaseStepV2Rows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfCaseStepV2Model) FindOneByProjectIdCaseIdStepId(ctx context.Context, projectId string, caseId string, stepId string) (*PerfCaseStepV2, error) {
	managerPerfCaseStepV2ProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepV2ProjectIdCaseIdStepIdPrefix, projectId, caseId, stepId)
	var resp PerfCaseStepV2
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfCaseStepV2ProjectIdCaseIdStepIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `case_id` = ? and `step_id` = ? and `deleted` = ? limit 1", perfCaseStepV2Rows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, caseId, stepId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfCaseStepV2Model) Insert(ctx context.Context, session sqlx.Session, data *PerfCaseStepV2) (sql.Result, error) {
	managerPerfCaseStepV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepV2IdPrefix, data.Id)
	managerPerfCaseStepV2ProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepV2ProjectIdCaseIdStepIdPrefix, data.ProjectId, data.CaseId, data.StepId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfCaseStepV2RowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.StepId, data.Type, data.Index, data.Name, data.RateLimits, data.Url, data.Method, data.Headers, data.Body, data.Exports, data.Sleep, data.TargetRps, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.StepId, data.Type, data.Index, data.Name, data.RateLimits, data.Url, data.Method, data.Headers, data.Body, data.Exports, data.Sleep, data.TargetRps, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfCaseStepV2IdKey, managerPerfCaseStepV2ProjectIdCaseIdStepIdKey)
}

func (m *defaultPerfCaseStepV2Model) Update(ctx context.Context, session sqlx.Session, newData *PerfCaseStepV2) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfCaseStepV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepV2IdPrefix, data.Id)
	managerPerfCaseStepV2ProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepV2ProjectIdCaseIdStepIdPrefix, data.ProjectId, data.CaseId, data.StepId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfCaseStepV2RowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.StepId, newData.Type, newData.Index, newData.Name, newData.RateLimits, newData.Url, newData.Method, newData.Headers, newData.Body, newData.Exports, newData.Sleep, newData.TargetRps, newData.State, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.StepId, newData.Type, newData.Index, newData.Name, newData.RateLimits, newData.Url, newData.Method, newData.Headers, newData.Body, newData.Exports, newData.Sleep, newData.TargetRps, newData.State, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfCaseStepV2IdKey, managerPerfCaseStepV2ProjectIdCaseIdStepIdKey)
}

func (m *defaultPerfCaseStepV2Model) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfCaseStepV2IdPrefix, primary)
}

func (m *defaultPerfCaseStepV2Model) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfCaseStepV2Rows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfCaseStepV2Model) tableName() string {
	return m.table
}
