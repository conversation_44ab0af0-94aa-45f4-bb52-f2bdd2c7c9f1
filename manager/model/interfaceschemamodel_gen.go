// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceSchemaTableName           = "`interface_schema`"
	interfaceSchemaFieldNames          = builder.RawFieldNames(&InterfaceSchema{})
	interfaceSchemaRows                = strings.Join(interfaceSchemaFieldNames, ",")
	interfaceSchemaRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceSchemaFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceSchemaRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceSchemaFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceSchemaIdPrefix                = "cache:manager:interfaceSchema:id:"
	cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix = "cache:manager:interfaceSchema:projectId:schemaId:"
)

type (
	interfaceSchemaModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceSchema) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceSchema, error)
		FindOneByProjectIdSchemaId(ctx context.Context, projectId string, schemaId string) (*InterfaceSchema, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceSchema) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceSchemaModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceSchema struct {
		Id          int64          `db:"id"`
		ProjectId   string         `db:"project_id"`  // 项目ID
		CategoryId  string         `db:"category_id"` // 所属分类ID
		SchemaId    string         `db:"schema_id"`   // 接口数据模型ID
		FullName    string         `db:"full_name"`   // 接口数据模型完整名称
		Name        string         `db:"name"`        // 接口数据模型名称
		Description sql.NullString `db:"description"` // 接口数据模型描述
		Mode        string         `db:"mode"`        // 创建方式（builtin、local、remote、manual）
		ImportType  sql.NullString `db:"import_type"` // 导入类型（OpenApi、gRPC、YApi、TT）
		Data        string         `db:"data"`        // 接口数据模型数据
		Deleted     int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newInterfaceSchemaModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceSchemaModel {
	return &defaultInterfaceSchemaModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_schema`",
	}
}

func (m *defaultInterfaceSchemaModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceSchemaIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, id)
	managerInterfaceSchemaProjectIdSchemaIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix, data.ProjectId, data.SchemaId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceSchemaIdKey, managerInterfaceSchemaProjectIdSchemaIdKey)
	return err
}

func (m *defaultInterfaceSchemaModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceSchemaIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, id)
	managerInterfaceSchemaProjectIdSchemaIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix, data.ProjectId, data.SchemaId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceSchemaIdKey, managerInterfaceSchemaProjectIdSchemaIdKey)
	return err
}

func (m *defaultInterfaceSchemaModel) FindOne(ctx context.Context, id int64) (*InterfaceSchema, error) {
	managerInterfaceSchemaIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, id)
	var resp InterfaceSchema
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceSchemaIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceSchemaRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceSchemaModel) FindOneByProjectIdSchemaId(ctx context.Context, projectId string, schemaId string) (*InterfaceSchema, error) {
	managerInterfaceSchemaProjectIdSchemaIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix, projectId, schemaId)
	var resp InterfaceSchema
	err := m.QueryRowIndexCtx(ctx, &resp, managerInterfaceSchemaProjectIdSchemaIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `schema_id` = ? and `deleted` = ? limit 1", interfaceSchemaRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, schemaId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceSchemaModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceSchema) (sql.Result, error) {
	managerInterfaceSchemaIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, data.Id)
	managerInterfaceSchemaProjectIdSchemaIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix, data.ProjectId, data.SchemaId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceSchemaRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.SchemaId, data.FullName, data.Name, data.Description, data.Mode, data.ImportType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.SchemaId, data.FullName, data.Name, data.Description, data.Mode, data.ImportType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceSchemaIdKey, managerInterfaceSchemaProjectIdSchemaIdKey)
}

func (m *defaultInterfaceSchemaModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceSchema) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceSchemaIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, data.Id)
	managerInterfaceSchemaProjectIdSchemaIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix, data.ProjectId, data.SchemaId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceSchemaRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.SchemaId, newData.FullName, newData.Name, newData.Description, newData.Mode, newData.ImportType, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.SchemaId, newData.FullName, newData.Name, newData.Description, newData.Mode, newData.ImportType, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerInterfaceSchemaIdKey, managerInterfaceSchemaProjectIdSchemaIdKey)
}

func (m *defaultInterfaceSchemaModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, primary)
}

func (m *defaultInterfaceSchemaModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceSchemaRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceSchemaModel) tableName() string {
	return m.table
}
