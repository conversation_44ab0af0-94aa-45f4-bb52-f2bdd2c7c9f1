// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfPlanCaseRelationshipTableName           = "`perf_plan_case_relationship`"
	perfPlanCaseRelationshipFieldNames          = builder.RawFieldNames(&PerfPlanCaseRelationship{})
	perfPlanCaseRelationshipRows                = strings.Join(perfPlanCaseRelationshipFieldNames, ",")
	perfPlanCaseRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(perfPlanCaseRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfPlanCaseRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(perfPlanCaseRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfPlanCaseRelationshipIdPrefix = "cache:manager:perfPlanCaseRelationship:id:"
)

type (
	perfPlanCaseRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfPlanCaseRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfPlanCaseRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfPlanCaseRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfPlanCaseRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	PerfPlanCaseRelationship struct {
		Id                int64          `db:"id"`
		ProjectId         string         `db:"project_id"`         // 项目ID
		PlanId            string         `db:"plan_id"`            // 压测计划ID
		CaseId            string         `db:"case_id"`            // 压测用例ID
		RateLimits        sql.NullString `db:"rate_limits"`        // 压测计划下的用例限流配置
		TargetRps         int64          `db:"target_rps"`         // 压测计划下用例的目标RPS
		PerfDataId        string         `db:"perf_data_id"`       // 压测数据ID
		CustomVu          int64          `db:"custom_vu"`          // 是否自定义虚拟用户数
		NumberOfVu        int64          `db:"number_of_vu"`       // 虚拟用户数
		CustomLg          int64          `db:"custom_lg"`          // 是否自定义施压机资源
		NumberOfLg        int64          `db:"number_of_lg"`       // 施压机数
		RequestsOfCpu     string         `db:"requests_of_cpu"`    // 最小分配的CPU资源
		RequestsOfMemory  string         `db:"requests_of_memory"` // 最小分配的内存资源
		LimitsOfCpu       string         `db:"limits_of_cpu"`      // 最大分配的CPU资源
		LimitsOfMemory    string         `db:"limits_of_memory"`   // 最大分配的内存资源
		EstimatedDuration int64          `db:"estimated_duration"` // 预估压测时长，单位为秒
		State             int64          `db:"state"`              // 引用状态（已使用、未使用）
		Deleted           int64          `db:"deleted"`            // 逻辑删除标识（未删除、已删除）
		CreatedBy         string         `db:"created_by"`         // 创建者的用户ID
		UpdatedBy         string         `db:"updated_by"`         // 最近一次更新者的用户ID
		DeletedBy         sql.NullString `db:"deleted_by"`         // 删除者的用户ID
		CreatedAt         time.Time      `db:"created_at"`         // 创建时间
		UpdatedAt         time.Time      `db:"updated_at"`         // 更新时间
		DeletedAt         sql.NullTime   `db:"deleted_at"`         // 删除时间
	}
)

func newPerfPlanCaseRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfPlanCaseRelationshipModel {
	return &defaultPerfPlanCaseRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_plan_case_relationship`",
	}
}

func (m *defaultPerfPlanCaseRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfPlanCaseRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanCaseRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfPlanCaseRelationshipIdKey)
	return err
}

func (m *defaultPerfPlanCaseRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfPlanCaseRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanCaseRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfPlanCaseRelationshipIdKey)
	return err
}

func (m *defaultPerfPlanCaseRelationshipModel) FindOne(ctx context.Context, id int64) (*PerfPlanCaseRelationship, error) {
	managerPerfPlanCaseRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanCaseRelationshipIdPrefix, id)
	var resp PerfPlanCaseRelationship
	err := m.QueryRowCtx(ctx, &resp, managerPerfPlanCaseRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfPlanCaseRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanCaseRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *PerfPlanCaseRelationship) (sql.Result, error) {
	managerPerfPlanCaseRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanCaseRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfPlanCaseRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.CaseId, data.RateLimits, data.TargetRps, data.PerfDataId, data.CustomVu, data.NumberOfVu, data.CustomLg, data.NumberOfLg, data.RequestsOfCpu, data.RequestsOfMemory, data.LimitsOfCpu, data.LimitsOfMemory, data.EstimatedDuration, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.CaseId, data.RateLimits, data.TargetRps, data.PerfDataId, data.CustomVu, data.NumberOfVu, data.CustomLg, data.NumberOfLg, data.RequestsOfCpu, data.RequestsOfMemory, data.LimitsOfCpu, data.LimitsOfMemory, data.EstimatedDuration, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfPlanCaseRelationshipIdKey)
}

func (m *defaultPerfPlanCaseRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *PerfPlanCaseRelationship) (sql.Result, error) {

	managerPerfPlanCaseRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanCaseRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfPlanCaseRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.CaseId, data.RateLimits, data.TargetRps, data.PerfDataId, data.CustomVu, data.NumberOfVu, data.CustomLg, data.NumberOfLg, data.RequestsOfCpu, data.RequestsOfMemory, data.LimitsOfCpu, data.LimitsOfMemory, data.EstimatedDuration, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.CaseId, data.RateLimits, data.TargetRps, data.PerfDataId, data.CustomVu, data.NumberOfVu, data.CustomLg, data.NumberOfLg, data.RequestsOfCpu, data.RequestsOfMemory, data.LimitsOfCpu, data.LimitsOfMemory, data.EstimatedDuration, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerPerfPlanCaseRelationshipIdKey)
}

func (m *defaultPerfPlanCaseRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfPlanCaseRelationshipIdPrefix, primary)
}

func (m *defaultPerfPlanCaseRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfPlanCaseRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfPlanCaseRelationshipModel) tableName() string {
	return m.table
}
