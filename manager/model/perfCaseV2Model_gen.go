// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfCaseV2TableName           = "`perf_case_v2`"
	perfCaseV2FieldNames          = builder.RawFieldNames(&PerfCaseV2{})
	perfCaseV2Rows                = strings.Join(perfCaseV2FieldNames, ",")
	perfCaseV2RowsExpectAutoSet   = strings.Join(stringx.Remove(perfCaseV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfCaseV2RowsWithPlaceHolder = strings.Join(stringx.Remove(perfCaseV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfCaseV2IdPrefix              = "cache:manager:perfCaseV2:id:"
	cacheManagerPerfCaseV2ProjectIdCaseIdPrefix = "cache:manager:perfCaseV2:projectId:caseId:"
)

type (
	perfCaseV2Model interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfCaseV2) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfCaseV2, error)
		FindOneByProjectIdCaseId(ctx context.Context, projectId string, caseId string) (*PerfCaseV2, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfCaseV2) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfCaseV2Model struct {
		sqlc.CachedConn
		table string
	}

	PerfCaseV2 struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`      // 项目ID
		CategoryId    string         `db:"category_id"`     // 所属分类ID
		CaseId        string         `db:"case_id"`         // 压测用例ID
		Name          string         `db:"name"`            // 压测用例名称
		Description   sql.NullString `db:"description"`     // 压测用例描述
		Tags          sql.NullString `db:"tags"`            // 标签
		Protocol      string         `db:"protocol"`        // 协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）
		RateLimits    string         `db:"rate_limits"`     // 限流配置
		SetupSteps    sql.NullString `db:"setup_steps"`     // 前置步骤
		SerialSteps   sql.NullString `db:"serial_steps"`    // 串行步骤
		ParallelSteps sql.NullString `db:"parallel_steps"`  // 并行步骤
		TeardownSteps sql.NullString `db:"teardown_steps"`  // 后置步骤
		NumberOfSteps int64          `db:"number_of_steps"` // 测试步骤数
		TargetRps     int64          `db:"target_rps"`      // 目标的RPS
		State         int64          `db:"state"`           // 计划状态（生效、失效）
		Deleted       int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		MaintainedBy  sql.NullString `db:"maintained_by"`   // 维护者的用户ID
		CreatedBy     string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newPerfCaseV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfCaseV2Model {
	return &defaultPerfCaseV2Model{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_case_v2`",
	}
}

func (m *defaultPerfCaseV2Model) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfCaseV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseV2IdPrefix, id)
	managerPerfCaseV2ProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseV2ProjectIdCaseIdPrefix, data.ProjectId, data.CaseId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfCaseV2IdKey, managerPerfCaseV2ProjectIdCaseIdKey)
	return err
}

func (m *defaultPerfCaseV2Model) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfCaseV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseV2IdPrefix, id)
	managerPerfCaseV2ProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseV2ProjectIdCaseIdPrefix, data.ProjectId, data.CaseId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfCaseV2IdKey, managerPerfCaseV2ProjectIdCaseIdKey)
	return err
}

func (m *defaultPerfCaseV2Model) FindOne(ctx context.Context, id int64) (*PerfCaseV2, error) {
	managerPerfCaseV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseV2IdPrefix, id)
	var resp PerfCaseV2
	err := m.QueryRowCtx(ctx, &resp, managerPerfCaseV2IdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfCaseV2Rows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfCaseV2Model) FindOneByProjectIdCaseId(ctx context.Context, projectId string, caseId string) (*PerfCaseV2, error) {
	managerPerfCaseV2ProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseV2ProjectIdCaseIdPrefix, projectId, caseId)
	var resp PerfCaseV2
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfCaseV2ProjectIdCaseIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `case_id` = ? and `deleted` = ? limit 1", perfCaseV2Rows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, caseId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfCaseV2Model) Insert(ctx context.Context, session sqlx.Session, data *PerfCaseV2) (sql.Result, error) {
	managerPerfCaseV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseV2IdPrefix, data.Id)
	managerPerfCaseV2ProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseV2ProjectIdCaseIdPrefix, data.ProjectId, data.CaseId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfCaseV2RowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.CaseId, data.Name, data.Description, data.Tags, data.Protocol, data.RateLimits, data.SetupSteps, data.SerialSteps, data.ParallelSteps, data.TeardownSteps, data.NumberOfSteps, data.TargetRps, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.CaseId, data.Name, data.Description, data.Tags, data.Protocol, data.RateLimits, data.SetupSteps, data.SerialSteps, data.ParallelSteps, data.TeardownSteps, data.NumberOfSteps, data.TargetRps, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfCaseV2IdKey, managerPerfCaseV2ProjectIdCaseIdKey)
}

func (m *defaultPerfCaseV2Model) Update(ctx context.Context, session sqlx.Session, newData *PerfCaseV2) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfCaseV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseV2IdPrefix, data.Id)
	managerPerfCaseV2ProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseV2ProjectIdCaseIdPrefix, data.ProjectId, data.CaseId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfCaseV2RowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.CaseId, newData.Name, newData.Description, newData.Tags, newData.Protocol, newData.RateLimits, newData.SetupSteps, newData.SerialSteps, newData.ParallelSteps, newData.TeardownSteps, newData.NumberOfSteps, newData.TargetRps, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.CaseId, newData.Name, newData.Description, newData.Tags, newData.Protocol, newData.RateLimits, newData.SetupSteps, newData.SerialSteps, newData.ParallelSteps, newData.TeardownSteps, newData.NumberOfSteps, newData.TargetRps, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfCaseV2IdKey, managerPerfCaseV2ProjectIdCaseIdKey)
}

func (m *defaultPerfCaseV2Model) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfCaseV2IdPrefix, primary)
}

func (m *defaultPerfCaseV2Model) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfCaseV2Rows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfCaseV2Model) tableName() string {
	return m.table
}
