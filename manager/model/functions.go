package model

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

func GetRootCategoryByType(ctx context.Context, categoryModel CategoryModel, projectId, tp string) (*Category, error) {
	var name string
	switch tp {
	case common.ConstCategoryTreeTypeInterfaceDocument:
		name = common.ConstCategoryRootAllDocument
	case common.ConstCategoryTreeTypeInterfaceSchema:
		name = common.ConstCategoryRootAllSchema
	case common.ConstCategoryTreeTypeComponentGroup:
		name = common.ConstCategoryRootAllComponentGroup
	case common.ConstCategoryTreeTypeApiCase:
		name = common.ConstCategoryRootAllApiCase
	case common.ConstCategoryTreeTypeApiSuite:
		name = common.ConstCategoryRootAllApiSuite
	case common.ConstCategoryTreeTypeApiPlan:
		name = common.ConstCategoryRootAllApiPlan
	case common.ConstCategoryTreeTypeUiPlan:
		name = common.ConstCategoryRootAllUiPlan
	case common.ConstCategoryTreeTypePerfCase:
		name = common.ConstCategoryRootAllPerfScenario
	case common.ConstCategoryTreeTypePerfPlan:
		name = common.ConstCategoryRootAllPerfPlan
	case common.ConstCategoryTreeTypeStabilityPlan:
		name = common.ConstCategoryRootAllStabilityPlan
	default:
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.DoesNotSupport, "the type of category tree[%s] doesn't support", tp,
			),
		)
	}

	root, err := categoryModel.FindOneByProjectIdTypeName(ctx, projectId, tp, name)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find root category with project_id[%s], type[%s] and name[%s], error: %+v",
				projectId, tp, name, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"root category with project_id[%s], type[%s] and name[%s] doesn't exist",
					projectId, tp, name,
				),
			)
		}
	}

	return root, nil
}

func GetCountByCategoryIdFunction(
	ctx context.Context, countModel CountByCategoryIdModel, categoryModel CategoryModel,
) (CountByCategoryIdFunc, error) {
	return func(projectId, categoryId string, drillDown bool) (int64, error) {
		return countModel.FindCountByCategoryId(
			ctx, categoryModel, FindCountByCategoryCondition{
				ProjectId:  projectId,
				CategoryId: categoryId,
				DrillDown:  drillDown,
			},
		)
	}, nil
}

func GetRootNodeOfGitProjectTree(ctx context.Context, m GitProjectTreeModel, projectID, configID string) (
	*GitProjectTree, error,
) {
	root, err := m.FindRootNodeOfTree(ctx, projectID, configID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find root node of git project tree with project_id[%s] and git_config_id[%s], error: %+v",
				projectID, configID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"root node of git project tree with project_id[%s] and git_config_id[%s] doesn't exist",
					projectID, configID,
				),
			)
		}
	}

	return root, nil
}
