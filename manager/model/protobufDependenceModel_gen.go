// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	protobufDependenceTableName           = "`protobuf_dependence`"
	protobufDependenceFieldNames          = builder.RawFieldNames(&ProtobufDependence{})
	protobufDependenceRows                = strings.Join(protobufDependenceFieldNames, ",")
	protobufDependenceRowsExpectAutoSet   = strings.Join(stringx.Remove(protobufDependenceFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	protobufDependenceRowsWithPlaceHolder = strings.Join(stringx.Remove(protobufDependenceFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerProtobufDependenceIdPrefix = "cache:manager:protobufDependence:id:"
)

type (
	protobufDependenceModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ProtobufDependence) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ProtobufDependence, error)
		Update(ctx context.Context, session sqlx.Session, data *ProtobufDependence) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultProtobufDependenceModel struct {
		sqlc.CachedConn
		table string
	}

	ProtobufDependence struct {
		Id          int64          `db:"id"`            // 自增ID
		ProjectId   string         `db:"project_id"`    // 项目ID
		ConfigId    string         `db:"config_id"`     // Protobuf配置ID
		DepConfigId string         `db:"dep_config_id"` // 依赖的Protobuf配置ID
		Deleted     int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newProtobufDependenceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultProtobufDependenceModel {
	return &defaultProtobufDependenceModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`protobuf_dependence`",
	}
}

func (m *defaultProtobufDependenceModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerProtobufDependenceIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufDependenceIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerProtobufDependenceIdKey)
	return err
}

func (m *defaultProtobufDependenceModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerProtobufDependenceIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufDependenceIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerProtobufDependenceIdKey)
	return err
}

func (m *defaultProtobufDependenceModel) FindOne(ctx context.Context, id int64) (*ProtobufDependence, error) {
	managerProtobufDependenceIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufDependenceIdPrefix, id)
	var resp ProtobufDependence
	err := m.QueryRowCtx(ctx, &resp, managerProtobufDependenceIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", protobufDependenceRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultProtobufDependenceModel) Insert(ctx context.Context, session sqlx.Session, data *ProtobufDependence) (sql.Result, error) {
	managerProtobufDependenceIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufDependenceIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, protobufDependenceRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.DepConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.DepConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerProtobufDependenceIdKey)
}

func (m *defaultProtobufDependenceModel) Update(ctx context.Context, session sqlx.Session, data *ProtobufDependence) (sql.Result, error) {

	managerProtobufDependenceIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufDependenceIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, protobufDependenceRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.DepConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.DepConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerProtobufDependenceIdKey)
}

func (m *defaultProtobufDependenceModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerProtobufDependenceIdPrefix, primary)
}

func (m *defaultProtobufDependenceModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", protobufDependenceRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultProtobufDependenceModel) tableName() string {
	return m.table
}
