// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceDocumentTableName           = "`interface_document`"
	interfaceDocumentFieldNames          = builder.RawFieldNames(&InterfaceDocument{})
	interfaceDocumentRows                = strings.Join(interfaceDocumentFieldNames, ",")
	interfaceDocumentRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceDocumentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceDocumentRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceDocumentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceDocumentIdPrefix                  = "cache:manager:interfaceDocument:id:"
	cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix = "cache:manager:interfaceDocument:projectId:documentId:"
)

type (
	interfaceDocumentModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceDocument) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceDocument, error)
		FindOneByProjectIdDocumentId(ctx context.Context, projectId string, documentId string) (*InterfaceDocument, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceDocument) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceDocumentModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceDocument struct {
		Id                int64          `db:"id"`
		ProjectId         string         `db:"project_id"`          // 项目ID
		CategoryId        string         `db:"category_id"`         // 所属分类ID
		DocumentId        string         `db:"document_id"`         // 接口ID
		Name              string         `db:"name"`                // 接口名称
		Description       sql.NullString `db:"description"`         // 接口描述
		Type              string         `db:"type"`                // 接口类型（HTTP、gRPC）
		Mode              string         `db:"mode"`                // 创建方式（builtin、local、remote、manual）
		ImportType        sql.NullString `db:"import_type"`         // 导入类型（OpenApi、gRPC、YApi、TT）
		Status            int64          `db:"status"`              // 接口状态（NULL、开发中、测试中、已发布、已废弃）
		Priority          int64          `db:"priority"`            // 优先级（NULL、P0、P1、P2、P3...）
		Tags              sql.NullString `db:"tags"`                // 标签
		State             int64          `db:"state"`               // 接口集合状态（生效、失效）
		CaseExecutionMode int64          `db:"case_execution_mode"` // 用例执行方式（并行、串行）
		Service           sql.NullString `db:"service"`             // 服务名称
		Path              string         `db:"path"`                // 接口路径
		Method            string         `db:"method"`              // 接口方法
		Data              string         `db:"data"`                // 接口详细数据
		Deleted           int64          `db:"deleted"`             // 逻辑删除标识（未删除、已删除）
		MaintainedBy      sql.NullString `db:"maintained_by"`       // 维护者的用户ID
		CreatedBy         string         `db:"created_by"`          // 创建者的用户ID
		UpdatedBy         string         `db:"updated_by"`          // 最近一次更新者的用户ID
		DeletedBy         sql.NullString `db:"deleted_by"`          // 删除者的用户ID
		CreatedAt         time.Time      `db:"created_at"`          // 创建时间
		UpdatedAt         time.Time      `db:"updated_at"`          // 更新时间
		DeletedAt         sql.NullTime   `db:"deleted_at"`          // 删除时间
	}
)

func newInterfaceDocumentModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceDocumentModel {
	return &defaultInterfaceDocumentModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_document`",
	}
}

func (m *defaultInterfaceDocumentModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceDocumentIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, id)
	managerInterfaceDocumentProjectIdDocumentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix, data.ProjectId, data.DocumentId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceDocumentIdKey, managerInterfaceDocumentProjectIdDocumentIdKey)
	return err
}

func (m *defaultInterfaceDocumentModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceDocumentIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, id)
	managerInterfaceDocumentProjectIdDocumentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix, data.ProjectId, data.DocumentId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceDocumentIdKey, managerInterfaceDocumentProjectIdDocumentIdKey)
	return err
}

func (m *defaultInterfaceDocumentModel) FindOne(ctx context.Context, id int64) (*InterfaceDocument, error) {
	managerInterfaceDocumentIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, id)
	var resp InterfaceDocument
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceDocumentIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceDocumentRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceDocumentModel) FindOneByProjectIdDocumentId(ctx context.Context, projectId string, documentId string) (*InterfaceDocument, error) {
	managerInterfaceDocumentProjectIdDocumentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix, projectId, documentId)
	var resp InterfaceDocument
	err := m.QueryRowIndexCtx(ctx, &resp, managerInterfaceDocumentProjectIdDocumentIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `document_id` = ? and `deleted` = ? limit 1", interfaceDocumentRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, documentId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceDocumentModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceDocument) (sql.Result, error) {
	managerInterfaceDocumentIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, data.Id)
	managerInterfaceDocumentProjectIdDocumentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix, data.ProjectId, data.DocumentId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceDocumentRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.DocumentId, data.Name, data.Description, data.Type, data.Mode, data.ImportType, data.Status, data.Priority, data.Tags, data.State, data.CaseExecutionMode, data.Service, data.Path, data.Method, data.Data, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.DocumentId, data.Name, data.Description, data.Type, data.Mode, data.ImportType, data.Status, data.Priority, data.Tags, data.State, data.CaseExecutionMode, data.Service, data.Path, data.Method, data.Data, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceDocumentIdKey, managerInterfaceDocumentProjectIdDocumentIdKey)
}

func (m *defaultInterfaceDocumentModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceDocument) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceDocumentIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, data.Id)
	managerInterfaceDocumentProjectIdDocumentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix, data.ProjectId, data.DocumentId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceDocumentRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.DocumentId, newData.Name, newData.Description, newData.Type, newData.Mode, newData.ImportType, newData.Status, newData.Priority, newData.Tags, newData.State, newData.CaseExecutionMode, newData.Service, newData.Path, newData.Method, newData.Data, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.DocumentId, newData.Name, newData.Description, newData.Type, newData.Mode, newData.ImportType, newData.Status, newData.Priority, newData.Tags, newData.State, newData.CaseExecutionMode, newData.Service, newData.Path, newData.Method, newData.Data, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerInterfaceDocumentIdKey, managerInterfaceDocumentProjectIdDocumentIdKey)
}

func (m *defaultInterfaceDocumentModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, primary)
}

func (m *defaultInterfaceDocumentModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceDocumentRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceDocumentModel) tableName() string {
	return m.table
}
