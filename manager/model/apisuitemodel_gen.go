// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	apiSuiteTableName           = "`api_suite`"
	apiSuiteFieldNames          = builder.RawFieldNames(&ApiSuite{})
	apiSuiteRows                = strings.Join(apiSuiteFieldNames, ",")
	apiSuiteRowsExpectAutoSet   = strings.Join(stringx.Remove(apiSuiteFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	apiSuiteRowsWithPlaceHolder = strings.Join(stringx.Remove(apiSuiteFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApiSuiteIdPrefix               = "cache:manager:apiSuite:id:"
	cacheManagerApiSuiteProjectIdSuiteIdPrefix = "cache:manager:apiSuite:projectId:suiteId:"
)

type (
	apiSuiteModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApiSuite) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApiSuite, error)
		FindOneByProjectIdSuiteId(ctx context.Context, projectId string, suiteId string) (*ApiSuite, error)
		Update(ctx context.Context, session sqlx.Session, data *ApiSuite) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApiSuiteModel struct {
		sqlc.CachedConn
		table string
	}

	ApiSuite struct {
		Id                int64          `db:"id"`
		ProjectId         string         `db:"project_id"`          // 项目ID
		CategoryId        string         `db:"category_id"`         // 所属分类ID
		SuiteId           string         `db:"suite_id"`            // 集合ID
		Name              string         `db:"name"`                // 集合名称
		Description       sql.NullString `db:"description"`         // 集合描述
		Priority          int64          `db:"priority"`            // 优先级（NULL、P0、P1、P2、P3...）
		Tags              sql.NullString `db:"tags"`                // 标签
		State             int64          `db:"state"`               // 集合状态（生效、失效）
		CaseExecutionMode int64          `db:"case_execution_mode"` // 用例执行方式（并行、串行）
		Deleted           int64          `db:"deleted"`             // 逻辑删除标识（未删除、已删除）
		MaintainedBy      sql.NullString `db:"maintained_by"`       // 维护者的用户ID
		CreatedBy         string         `db:"created_by"`          // 创建者的用户ID
		UpdatedBy         string         `db:"updated_by"`          // 最近一次更新者的用户ID
		DeletedBy         sql.NullString `db:"deleted_by"`          // 删除者的用户ID
		CreatedAt         time.Time      `db:"created_at"`          // 创建时间
		UpdatedAt         time.Time      `db:"updated_at"`          // 更新时间
		DeletedAt         sql.NullTime   `db:"deleted_at"`          // 删除时间
	}
)

func newApiSuiteModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApiSuiteModel {
	return &defaultApiSuiteModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`api_suite`",
	}
}

func (m *defaultApiSuiteModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiSuiteIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, id)
	managerApiSuiteProjectIdSuiteIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, data.ProjectId, data.SuiteId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApiSuiteIdKey, managerApiSuiteProjectIdSuiteIdKey)
	return err
}

func (m *defaultApiSuiteModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiSuiteIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, id)
	managerApiSuiteProjectIdSuiteIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, data.ProjectId, data.SuiteId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApiSuiteIdKey, managerApiSuiteProjectIdSuiteIdKey)
	return err
}

func (m *defaultApiSuiteModel) FindOne(ctx context.Context, id int64) (*ApiSuite, error) {
	managerApiSuiteIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, id)
	var resp ApiSuite
	err := m.QueryRowCtx(ctx, &resp, managerApiSuiteIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", apiSuiteRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiSuiteModel) FindOneByProjectIdSuiteId(ctx context.Context, projectId string, suiteId string) (*ApiSuite, error) {
	managerApiSuiteProjectIdSuiteIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, projectId, suiteId)
	var resp ApiSuite
	err := m.QueryRowIndexCtx(ctx, &resp, managerApiSuiteProjectIdSuiteIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `suite_id` = ? and `deleted` = ? limit 1", apiSuiteRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, suiteId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiSuiteModel) Insert(ctx context.Context, session sqlx.Session, data *ApiSuite) (sql.Result, error) {
	managerApiSuiteIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, data.Id)
	managerApiSuiteProjectIdSuiteIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, data.ProjectId, data.SuiteId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, apiSuiteRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.SuiteId, data.Name, data.Description, data.Priority, data.Tags, data.State, data.CaseExecutionMode, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.SuiteId, data.Name, data.Description, data.Priority, data.Tags, data.State, data.CaseExecutionMode, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApiSuiteIdKey, managerApiSuiteProjectIdSuiteIdKey)
}

func (m *defaultApiSuiteModel) Update(ctx context.Context, session sqlx.Session, newData *ApiSuite) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerApiSuiteIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, data.Id)
	managerApiSuiteProjectIdSuiteIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, data.ProjectId, data.SuiteId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, apiSuiteRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.SuiteId, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.State, newData.CaseExecutionMode, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.SuiteId, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.State, newData.CaseExecutionMode, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerApiSuiteIdKey, managerApiSuiteProjectIdSuiteIdKey)
}

func (m *defaultApiSuiteModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, primary)
}

func (m *defaultApiSuiteModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", apiSuiteRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApiSuiteModel) tableName() string {
	return m.table
}
