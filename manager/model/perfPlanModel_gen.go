// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfPlanTableName           = "`perf_plan`"
	perfPlanFieldNames          = builder.RawFieldNames(&PerfPlan{})
	perfPlanRows                = strings.Join(perfPlanFieldNames, ",")
	perfPlanRowsExpectAutoSet   = strings.Join(stringx.Remove(perfPlanFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfPlanRowsWithPlaceHolder = strings.Join(stringx.Remove(perfPlanFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfPlanIdPrefix              = "cache:manager:perfPlan:id:"
	cacheManagerPerfPlanProjectIdPlanIdPrefix = "cache:manager:perfPlan:projectId:planId:"
)

type (
	perfPlanModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfPlan) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfPlan, error)
		FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*PerfPlan, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfPlan) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfPlanModel struct {
		sqlc.CachedConn
		table string
	}

	PerfPlan struct {
		Id               int64          `db:"id"`
		ProjectId        string         `db:"project_id"`         // 项目ID
		PlanId           string         `db:"plan_id"`            // 计划ID
		Name             string         `db:"name"`               // 计划名称
		Description      sql.NullString `db:"description"`        // 计划描述
		Type             string         `db:"type"`               // 计划类型（手动、定时、接口）
		Tags             sql.NullString `db:"tags"`               // 标签
		Protocol         string         `db:"protocol"`           // 协议（TT私有协议、通用gRPC协议、通用HTTP协议）
		ProtobufConfigId sql.NullString `db:"protobuf_config_id"` // Protobuf项目配置ID
		GeneralConfigId  sql.NullString `db:"general_config_id"`  // 通用配置ID
		AccountConfigId  sql.NullString `db:"account_config_id"`  // 池账号配置ID
		Duration         int64          `db:"duration"`           // 压测持续时长，单位为秒
		TargetEnv        string         `db:"target_env"`         // 目标环境（生产环境、测试环境）
		Keepalive        string         `db:"keepalive"`          // 保活参数（登录、心跳的限流配置）
		Delay            int64          `db:"delay"`              // 延迟执行时间，单位为秒
		State            int64          `db:"state"`              // 计划状态（生效、失效）
		Deleted          int64          `db:"deleted"`            // 逻辑删除标识（未删除、已删除）
		MaintainedBy     sql.NullString `db:"maintained_by"`      // 维护者的用户ID
		CreatedBy        string         `db:"created_by"`         // 创建者的用户ID
		UpdatedBy        string         `db:"updated_by"`         // 最近一次更新者的用户ID
		DeletedBy        sql.NullString `db:"deleted_by"`         // 删除者的用户ID
		CreatedAt        time.Time      `db:"created_at"`         // 创建时间
		UpdatedAt        time.Time      `db:"updated_at"`         // 更新时间
		DeletedAt        sql.NullTime   `db:"deleted_at"`         // 删除时间
	}
)

func newPerfPlanModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfPlanModel {
	return &defaultPerfPlanModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_plan`",
	}
}

func (m *defaultPerfPlanModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfPlanIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanIdPrefix, id)
	managerPerfPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfPlanIdKey, managerPerfPlanProjectIdPlanIdKey)
	return err
}

func (m *defaultPerfPlanModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfPlanIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanIdPrefix, id)
	managerPerfPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfPlanIdKey, managerPerfPlanProjectIdPlanIdKey)
	return err
}

func (m *defaultPerfPlanModel) FindOne(ctx context.Context, id int64) (*PerfPlan, error) {
	managerPerfPlanIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanIdPrefix, id)
	var resp PerfPlan
	err := m.QueryRowCtx(ctx, &resp, managerPerfPlanIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfPlanRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanModel) FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*PerfPlan, error) {
	managerPerfPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanProjectIdPlanIdPrefix, projectId, planId)
	var resp PerfPlan
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfPlanProjectIdPlanIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `plan_id` = ? and `deleted` = ? limit 1", perfPlanRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, planId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanModel) Insert(ctx context.Context, session sqlx.Session, data *PerfPlan) (sql.Result, error) {
	managerPerfPlanIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanIdPrefix, data.Id)
	managerPerfPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfPlanRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.Name, data.Description, data.Type, data.Tags, data.Protocol, data.ProtobufConfigId, data.GeneralConfigId, data.AccountConfigId, data.Duration, data.TargetEnv, data.Keepalive, data.Delay, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.Name, data.Description, data.Type, data.Tags, data.Protocol, data.ProtobufConfigId, data.GeneralConfigId, data.AccountConfigId, data.Duration, data.TargetEnv, data.Keepalive, data.Delay, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfPlanIdKey, managerPerfPlanProjectIdPlanIdKey)
}

func (m *defaultPerfPlanModel) Update(ctx context.Context, session sqlx.Session, newData *PerfPlan) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfPlanIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanIdPrefix, data.Id)
	managerPerfPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfPlanRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.PlanId, newData.Name, newData.Description, newData.Type, newData.Tags, newData.Protocol, newData.ProtobufConfigId, newData.GeneralConfigId, newData.AccountConfigId, newData.Duration, newData.TargetEnv, newData.Keepalive, newData.Delay, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.PlanId, newData.Name, newData.Description, newData.Type, newData.Tags, newData.Protocol, newData.ProtobufConfigId, newData.GeneralConfigId, newData.AccountConfigId, newData.Duration, newData.TargetEnv, newData.Keepalive, newData.Delay, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfPlanIdKey, managerPerfPlanProjectIdPlanIdKey)
}

func (m *defaultPerfPlanModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfPlanIdPrefix, primary)
}

func (m *defaultPerfPlanModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfPlanRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfPlanModel) tableName() string {
	return m.table
}
