// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	tagReferenceRelationshipTableName           = "`tag_reference_relationship`"
	tagReferenceRelationshipFieldNames          = builder.RawFieldNames(&TagReferenceRelationship{})
	tagReferenceRelationshipRows                = strings.Join(tagReferenceRelationshipFieldNames, ",")
	tagReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(tagReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	tagReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(tagReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerTagReferenceRelationshipIdPrefix = "cache:manager:tagReferenceRelationship:id:"
)

type (
	tagReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *TagReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*TagReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *TagReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultTagReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	TagReferenceRelationship struct {
		Id               int64          `db:"id"`
		ProjectId        string         `db:"project_id"`        // 项目ID
		ReferenceType    string         `db:"reference_type"`    // 引用类型（组件组、API用例、API集合、API计划、接口文档、接口用例）
		ReferenceId      string         `db:"reference_id"`      // 引用ID（组件组ID、API用例ID、API集合ID、API计划ID、接口文档ID、接口用例ID）
		ReferenceVersion sql.NullString `db:"reference_version"` // 引用版本（API用例版本、接口用例版本）
		TagId            string         `db:"tag_id"`            // 标签ID
		Deleted          int64          `db:"deleted"`           // 逻辑删除标识（未删除、已删除）
		CreatedBy        string         `db:"created_by"`        // 创建者的用户ID
		UpdatedBy        string         `db:"updated_by"`        // 最近一次更新者的用户ID
		DeletedBy        sql.NullString `db:"deleted_by"`        // 删除者的用户ID
		CreatedAt        time.Time      `db:"created_at"`        // 创建时间
		UpdatedAt        time.Time      `db:"updated_at"`        // 更新时间
		DeletedAt        sql.NullTime   `db:"deleted_at"`        // 删除时间
	}
)

func newTagReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultTagReferenceRelationshipModel {
	return &defaultTagReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`tag_reference_relationship`",
	}
}

func (m *defaultTagReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerTagReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerTagReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerTagReferenceRelationshipIdKey)
	return err
}

func (m *defaultTagReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerTagReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerTagReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerTagReferenceRelationshipIdKey)
	return err
}

func (m *defaultTagReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*TagReferenceRelationship, error) {
	managerTagReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerTagReferenceRelationshipIdPrefix, id)
	var resp TagReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerTagReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", tagReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTagReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *TagReferenceRelationship) (sql.Result, error) {
	managerTagReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerTagReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, tagReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.TagId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.TagId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerTagReferenceRelationshipIdKey)
}

func (m *defaultTagReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *TagReferenceRelationship) (sql.Result, error) {

	managerTagReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerTagReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, tagReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.TagId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.TagId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerTagReferenceRelationshipIdKey)
}

func (m *defaultTagReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerTagReferenceRelationshipIdPrefix, primary)
}

func (m *defaultTagReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", tagReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultTagReferenceRelationshipModel) tableName() string {
	return m.table
}
