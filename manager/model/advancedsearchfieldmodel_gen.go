// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	advancedSearchFieldTableName           = "`advanced_search_field`"
	advancedSearchFieldFieldNames          = builder.RawFieldNames(&AdvancedSearchField{})
	advancedSearchFieldRows                = strings.Join(advancedSearchFieldFieldNames, ",")
	advancedSearchFieldRowsExpectAutoSet   = strings.Join(stringx.Remove(advancedSearchFieldFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	advancedSearchFieldRowsWithPlaceHolder = strings.Join(stringx.Remove(advancedSearchFieldFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerAdvancedSearchFieldIdPrefix = "cache:manager:advancedSearchField:id:"
)

type (
	advancedSearchFieldModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *AdvancedSearchField) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*AdvancedSearchField, error)
		Update(ctx context.Context, session sqlx.Session, data *AdvancedSearchField) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultAdvancedSearchFieldModel struct {
		sqlc.CachedConn
		table string
	}

	AdvancedSearchField struct {
		Id        int64     `db:"id"`
		ProjectId string    `db:"project_id"` // 项目ID
		FieldId   string    `db:"field_id"`   // 高级搜索字段ID
		FieldType string    `db:"field_type"` // 字段类型，给前端渲染用
		SceneType string    `db:"scene_type"` // 所属组件（如：API_SUITE, INTERFACE_DOCUMENT）
		FrontName string    `db:"front_name"` // 前端展示字段名称，可以是中文
		FieldName string    `db:"field_name"` // 对应表字段名
		Deleted   int64     `db:"deleted"`    // 是否已删除
		CreatedAt time.Time `db:"created_at"` // 创建时间
		UpdatedAt time.Time `db:"updated_at"` // 更新时间
	}
)

func newAdvancedSearchFieldModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultAdvancedSearchFieldModel {
	return &defaultAdvancedSearchFieldModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`advanced_search_field`",
	}
}

func (m *defaultAdvancedSearchFieldModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerAdvancedSearchFieldIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerAdvancedSearchFieldIdKey)
	return err
}

func (m *defaultAdvancedSearchFieldModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerAdvancedSearchFieldIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerAdvancedSearchFieldIdKey)
	return err
}

func (m *defaultAdvancedSearchFieldModel) FindOne(ctx context.Context, id int64) (*AdvancedSearchField, error) {
	managerAdvancedSearchFieldIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldIdPrefix, id)
	var resp AdvancedSearchField
	err := m.QueryRowCtx(ctx, &resp, managerAdvancedSearchFieldIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", advancedSearchFieldRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAdvancedSearchFieldModel) Insert(ctx context.Context, session sqlx.Session, data *AdvancedSearchField) (sql.Result, error) {
	managerAdvancedSearchFieldIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, advancedSearchFieldRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.FieldId, data.FieldType, data.SceneType, data.FrontName, data.FieldName, data.Deleted)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.FieldId, data.FieldType, data.SceneType, data.FrontName, data.FieldName, data.Deleted)
	}, managerAdvancedSearchFieldIdKey)
}

func (m *defaultAdvancedSearchFieldModel) Update(ctx context.Context, session sqlx.Session, data *AdvancedSearchField) (sql.Result, error) {

	managerAdvancedSearchFieldIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, advancedSearchFieldRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.FieldId, data.FieldType, data.SceneType, data.FrontName, data.FieldName, data.Deleted, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.FieldId, data.FieldType, data.SceneType, data.FrontName, data.FieldName, data.Deleted, data.Id)
	}, managerAdvancedSearchFieldIdKey)
}

func (m *defaultAdvancedSearchFieldModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldIdPrefix, primary)
}

func (m *defaultAdvancedSearchFieldModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", advancedSearchFieldRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultAdvancedSearchFieldModel) tableName() string {
	return m.table
}
