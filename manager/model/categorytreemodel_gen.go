// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	categoryTreeTableName           = "`category_tree`"
	categoryTreeFieldNames          = builder.RawFieldNames(&CategoryTree{})
	categoryTreeRows                = strings.Join(categoryTreeFieldNames, ",")
	categoryTreeRowsExpectAutoSet   = strings.Join(stringx.Remove(categoryTreeFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	categoryTreeRowsWithPlaceHolder = strings.Join(stringx.Remove(categoryTreeFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerCategoryTreeIdPrefix                                    = "cache:manager:categoryTree:id:"
	cacheManagerCategoryTreeProjectIdAncestorDescendantDepthIndexPrefix = "cache:manager:categoryTree:projectId:ancestor:descendant:depth:index:"
)

type (
	categoryTreeModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *CategoryTree) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*CategoryTree, error)
		FindOneByProjectIdAncestorDescendantDepthIndex(ctx context.Context, projectId string, ancestor string, descendant string, depth int64, index int64) (*CategoryTree, error)
		Update(ctx context.Context, session sqlx.Session, data *CategoryTree) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultCategoryTreeModel struct {
		sqlc.CachedConn
		table string
	}

	CategoryTree struct {
		Id         int64          `db:"id"`
		ProjectId  string         `db:"project_id"` // 项目ID
		Ancestor   string         `db:"ancestor"`   // 祖先分类ID
		Descendant string         `db:"descendant"` // 后代分类ID
		Depth      int64          `db:"depth"`      // 深度
		Index      int64          `db:"index"`      // 序号（同一深度下按序号升序排列）
		Deleted    int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy  string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy  string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy  sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt  time.Time      `db:"created_at"` // 创建时间
		UpdatedAt  time.Time      `db:"updated_at"` // 更新时间
		DeletedAt  sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newCategoryTreeModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultCategoryTreeModel {
	return &defaultCategoryTreeModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`category_tree`",
	}
}

func (m *defaultCategoryTreeModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerCategoryTreeIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryTreeIdPrefix, id)
	managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerCategoryTreeProjectIdAncestorDescendantDepthIndexPrefix, data.ProjectId, data.Ancestor, data.Descendant, data.Depth, data.Index)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerCategoryTreeIdKey, managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey)
	return err
}

func (m *defaultCategoryTreeModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerCategoryTreeIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryTreeIdPrefix, id)
	managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerCategoryTreeProjectIdAncestorDescendantDepthIndexPrefix, data.ProjectId, data.Ancestor, data.Descendant, data.Depth, data.Index)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerCategoryTreeIdKey, managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey)
	return err
}

func (m *defaultCategoryTreeModel) FindOne(ctx context.Context, id int64) (*CategoryTree, error) {
	managerCategoryTreeIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryTreeIdPrefix, id)
	var resp CategoryTree
	err := m.QueryRowCtx(ctx, &resp, managerCategoryTreeIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", categoryTreeRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCategoryTreeModel) FindOneByProjectIdAncestorDescendantDepthIndex(ctx context.Context, projectId string, ancestor string, descendant string, depth int64, index int64) (*CategoryTree, error) {
	managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerCategoryTreeProjectIdAncestorDescendantDepthIndexPrefix, projectId, ancestor, descendant, depth, index)
	var resp CategoryTree
	err := m.QueryRowIndexCtx(ctx, &resp, managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `ancestor` = ? and `descendant` = ? and `depth` = ? and `index` = ? and `deleted` = ? limit 1", categoryTreeRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, ancestor, descendant, depth, index, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCategoryTreeModel) Insert(ctx context.Context, session sqlx.Session, data *CategoryTree) (sql.Result, error) {
	managerCategoryTreeIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryTreeIdPrefix, data.Id)
	managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerCategoryTreeProjectIdAncestorDescendantDepthIndexPrefix, data.ProjectId, data.Ancestor, data.Descendant, data.Depth, data.Index)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, categoryTreeRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Ancestor, data.Descendant, data.Depth, data.Index, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Ancestor, data.Descendant, data.Depth, data.Index, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerCategoryTreeIdKey, managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey)
}

func (m *defaultCategoryTreeModel) Update(ctx context.Context, session sqlx.Session, newData *CategoryTree) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerCategoryTreeIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryTreeIdPrefix, data.Id)
	managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey := fmt.Sprintf("%s%v:%v:%v:%v:%v", cacheManagerCategoryTreeProjectIdAncestorDescendantDepthIndexPrefix, data.ProjectId, data.Ancestor, data.Descendant, data.Depth, data.Index)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, categoryTreeRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Ancestor, newData.Descendant, newData.Depth, newData.Index, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Ancestor, newData.Descendant, newData.Depth, newData.Index, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerCategoryTreeIdKey, managerCategoryTreeProjectIdAncestorDescendantDepthIndexKey)
}

func (m *defaultCategoryTreeModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerCategoryTreeIdPrefix, primary)
}

func (m *defaultCategoryTreeModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", categoryTreeRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultCategoryTreeModel) tableName() string {
	return m.table
}
