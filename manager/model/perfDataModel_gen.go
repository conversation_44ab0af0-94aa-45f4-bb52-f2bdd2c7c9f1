// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfDataTableName           = "`perf_data`"
	perfDataFieldNames          = builder.RawFieldNames(&PerfData{})
	perfDataRows                = strings.Join(perfDataFieldNames, ",")
	perfDataRowsExpectAutoSet   = strings.Join(stringx.Remove(perfDataFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfDataRowsWithPlaceHolder = strings.Join(stringx.Remove(perfDataFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfDataIdPrefix              = "cache:manager:perfData:id:"
	cacheManagerPerfDataProjectIdDataIdPrefix = "cache:manager:perfData:projectId:dataId:"
)

type (
	perfDataModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfData) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfData, error)
		FindOneByProjectIdDataId(ctx context.Context, projectId string, dataId string) (*PerfData, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfData) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfDataModel struct {
		sqlc.CachedConn
		table string
	}

	PerfData struct {
		Id           int64          `db:"id"`
		ProjectId    string         `db:"project_id"`    // 项目ID
		DataId       string         `db:"data_id"`       // 压测数据ID
		Name         string         `db:"name"`          // 压测数据名称
		Description  sql.NullString `db:"description"`   // 压测数据描述
		Extension    string         `db:"extension"`     // 文件扩展名
		Hash         string         `db:"hash"`          // 压测数据文件的一致性哈希值（MD5）
		Size         int64          `db:"size"`          // 压测数据文件的大小
		Path         string         `db:"path"`          // 压测数据文件的路径
		NumberOfVu   int64          `db:"number_of_vu"`  // 虚拟用户数
		Deleted      int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		MaintainedBy sql.NullString `db:"maintained_by"` // 维护者的用户ID
		CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newPerfDataModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfDataModel {
	return &defaultPerfDataModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_data`",
	}
}

func (m *defaultPerfDataModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfDataIdKey := fmt.Sprintf("%s%v", cacheManagerPerfDataIdPrefix, id)
	managerPerfDataProjectIdDataIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfDataProjectIdDataIdPrefix, data.ProjectId, data.DataId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfDataIdKey, managerPerfDataProjectIdDataIdKey)
	return err
}

func (m *defaultPerfDataModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfDataIdKey := fmt.Sprintf("%s%v", cacheManagerPerfDataIdPrefix, id)
	managerPerfDataProjectIdDataIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfDataProjectIdDataIdPrefix, data.ProjectId, data.DataId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfDataIdKey, managerPerfDataProjectIdDataIdKey)
	return err
}

func (m *defaultPerfDataModel) FindOne(ctx context.Context, id int64) (*PerfData, error) {
	managerPerfDataIdKey := fmt.Sprintf("%s%v", cacheManagerPerfDataIdPrefix, id)
	var resp PerfData
	err := m.QueryRowCtx(ctx, &resp, managerPerfDataIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfDataRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfDataModel) FindOneByProjectIdDataId(ctx context.Context, projectId string, dataId string) (*PerfData, error) {
	managerPerfDataProjectIdDataIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfDataProjectIdDataIdPrefix, projectId, dataId)
	var resp PerfData
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfDataProjectIdDataIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `data_id` = ? and `deleted` = ? limit 1", perfDataRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, dataId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfDataModel) Insert(ctx context.Context, session sqlx.Session, data *PerfData) (sql.Result, error) {
	managerPerfDataIdKey := fmt.Sprintf("%s%v", cacheManagerPerfDataIdPrefix, data.Id)
	managerPerfDataProjectIdDataIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfDataProjectIdDataIdPrefix, data.ProjectId, data.DataId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfDataRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.DataId, data.Name, data.Description, data.Extension, data.Hash, data.Size, data.Path, data.NumberOfVu, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.DataId, data.Name, data.Description, data.Extension, data.Hash, data.Size, data.Path, data.NumberOfVu, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfDataIdKey, managerPerfDataProjectIdDataIdKey)
}

func (m *defaultPerfDataModel) Update(ctx context.Context, session sqlx.Session, newData *PerfData) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfDataIdKey := fmt.Sprintf("%s%v", cacheManagerPerfDataIdPrefix, data.Id)
	managerPerfDataProjectIdDataIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfDataProjectIdDataIdPrefix, data.ProjectId, data.DataId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfDataRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.DataId, newData.Name, newData.Description, newData.Extension, newData.Hash, newData.Size, newData.Path, newData.NumberOfVu, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.DataId, newData.Name, newData.Description, newData.Extension, newData.Hash, newData.Size, newData.Path, newData.NumberOfVu, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfDataIdKey, managerPerfDataProjectIdDataIdKey)
}

func (m *defaultPerfDataModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfDataIdPrefix, primary)
}

func (m *defaultPerfDataModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfDataRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfDataModel) tableName() string {
	return m.table
}
