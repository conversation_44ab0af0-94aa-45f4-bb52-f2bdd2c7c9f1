// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	componentGroupTableName           = "`component_group`"
	componentGroupFieldNames          = builder.RawFieldNames(&ComponentGroup{})
	componentGroupRows                = strings.Join(componentGroupFieldNames, ",")
	componentGroupRowsExpectAutoSet   = strings.Join(stringx.Remove(componentGroupFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	componentGroupRowsWithPlaceHolder = strings.Join(stringx.Remove(componentGroupFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerComponentGroupIdPrefix                               = "cache:manager:componentGroup:id:"
	cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix = "cache:manager:componentGroup:projectId:componentGroupId:version:"
)

type (
	componentGroupModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ComponentGroup) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ComponentGroup, error)
		FindOneByProjectIdComponentGroupIdVersion(ctx context.Context, projectId string, componentGroupId string, version string) (*ComponentGroup, error)
		Update(ctx context.Context, session sqlx.Session, data *ComponentGroup) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultComponentGroupModel struct {
		sqlc.CachedConn
		table string
	}

	ComponentGroup struct {
		Id                 int64          `db:"id"`
		ProjectId          string         `db:"project_id"`           // 项目ID
		CategoryId         string         `db:"category_id"`          // 所属分类ID
		ComponentGroupId   string         `db:"component_group_id"`   // 组件组ID
		ComponentGroupType string         `db:"component_group_type"` // 组件组类型
		Name               string         `db:"name"`                 // 组件组名称
		Description        sql.NullString `db:"description"`          // 组件组描述
		Priority           int64          `db:"priority"`             // 优先级（NULL、P0、P1、P2、P3...）
		Tags               sql.NullString `db:"tags"`                 // 标签
		Imports            string         `db:"imports"`              // 组件组的输入参数列表
		Exports            string         `db:"exports"`              // 组件组的输出参数列表
		AccountConfig      string         `db:"account_config"`       // 池账号配置数
		Version            string         `db:"version"`              // 组件组版本
		Structure          string         `db:"structure"`            // 组件组中各节点的关系结构
		ReferenceStructure string         `db:"reference_structure"`  // 组件组中引用时涉及的各节点的关系结构
		Latest             int64          `db:"latest"`               // 是否最新版本
		Deleted            int64          `db:"deleted"`              // 逻辑删除标识（未删除、已删除）
		MaintainedBy       sql.NullString `db:"maintained_by"`        // 维护者的用户ID
		CreatedBy          string         `db:"created_by"`           // 创建者的用户ID
		UpdatedBy          string         `db:"updated_by"`           // 最近一次更新者的用户ID
		DeletedBy          sql.NullString `db:"deleted_by"`           // 删除者的用户ID
		CreatedAt          time.Time      `db:"created_at"`           // 创建时间
		UpdatedAt          time.Time      `db:"updated_at"`           // 更新时间
		DeletedAt          sql.NullTime   `db:"deleted_at"`           // 删除时间
	}
)

func newComponentGroupModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultComponentGroupModel {
	return &defaultComponentGroupModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`component_group`",
	}
}

func (m *defaultComponentGroupModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerComponentGroupIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, id)
	managerComponentGroupProjectIdComponentGroupIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix, data.ProjectId, data.ComponentGroupId, data.Version)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerComponentGroupIdKey, managerComponentGroupProjectIdComponentGroupIdVersionKey)
	return err
}

func (m *defaultComponentGroupModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerComponentGroupIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, id)
	managerComponentGroupProjectIdComponentGroupIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix, data.ProjectId, data.ComponentGroupId, data.Version)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerComponentGroupIdKey, managerComponentGroupProjectIdComponentGroupIdVersionKey)
	return err
}

func (m *defaultComponentGroupModel) FindOne(ctx context.Context, id int64) (*ComponentGroup, error) {
	managerComponentGroupIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, id)
	var resp ComponentGroup
	err := m.QueryRowCtx(ctx, &resp, managerComponentGroupIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", componentGroupRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultComponentGroupModel) FindOneByProjectIdComponentGroupIdVersion(ctx context.Context, projectId string, componentGroupId string, version string) (*ComponentGroup, error) {
	managerComponentGroupProjectIdComponentGroupIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix, projectId, componentGroupId, version)
	var resp ComponentGroup
	err := m.QueryRowIndexCtx(ctx, &resp, managerComponentGroupProjectIdComponentGroupIdVersionKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `component_group_id` = ? and `version` = ? and `deleted` = ? limit 1", componentGroupRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, componentGroupId, version, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultComponentGroupModel) Insert(ctx context.Context, session sqlx.Session, data *ComponentGroup) (sql.Result, error) {
	managerComponentGroupIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, data.Id)
	managerComponentGroupProjectIdComponentGroupIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix, data.ProjectId, data.ComponentGroupId, data.Version)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, componentGroupRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.ComponentGroupId, data.ComponentGroupType, data.Name, data.Description, data.Priority, data.Tags, data.Imports, data.Exports, data.AccountConfig, data.Version, data.Structure, data.ReferenceStructure, data.Latest, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.ComponentGroupId, data.ComponentGroupType, data.Name, data.Description, data.Priority, data.Tags, data.Imports, data.Exports, data.AccountConfig, data.Version, data.Structure, data.ReferenceStructure, data.Latest, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerComponentGroupIdKey, managerComponentGroupProjectIdComponentGroupIdVersionKey)
}

func (m *defaultComponentGroupModel) Update(ctx context.Context, session sqlx.Session, newData *ComponentGroup) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerComponentGroupIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, data.Id)
	managerComponentGroupProjectIdComponentGroupIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix, data.ProjectId, data.ComponentGroupId, data.Version)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, componentGroupRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.ComponentGroupId, newData.ComponentGroupType, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.Imports, newData.Exports, newData.AccountConfig, newData.Version, newData.Structure, newData.ReferenceStructure, newData.Latest, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.ComponentGroupId, newData.ComponentGroupType, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.Imports, newData.Exports, newData.AccountConfig, newData.Version, newData.Structure, newData.ReferenceStructure, newData.Latest, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerComponentGroupIdKey, managerComponentGroupProjectIdComponentGroupIdVersionKey)
}

func (m *defaultComponentGroupModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, primary)
}

func (m *defaultComponentGroupModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", componentGroupRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultComponentGroupModel) tableName() string {
	return m.table
}
