// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	advancedSearchFieldRelationshipTableName           = "`advanced_search_field_relationship`"
	advancedSearchFieldRelationshipFieldNames          = builder.RawFieldNames(&AdvancedSearchFieldRelationship{})
	advancedSearchFieldRelationshipRows                = strings.Join(advancedSearchFieldRelationshipFieldNames, ",")
	advancedSearchFieldRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(advancedSearchFieldRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	advancedSearchFieldRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(advancedSearchFieldRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerAdvancedSearchFieldRelationshipIdPrefix = "cache:manager:advancedSearchFieldRelationship:id:"
)

type (
	advancedSearchFieldRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *AdvancedSearchFieldRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*AdvancedSearchFieldRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *AdvancedSearchFieldRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultAdvancedSearchFieldRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	AdvancedSearchFieldRelationship struct {
		Id          int64          `db:"id"`
		ProjectId   sql.NullString `db:"project_id"`   // 项目ID
		FieldId     string         `db:"field_id"`     // 字段ID
		ConditionId string         `db:"condition_id"` // 对比条件ID
		Deleted     int64          `db:"deleted"`      // 是否已删除
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间
	}
)

func newAdvancedSearchFieldRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultAdvancedSearchFieldRelationshipModel {
	return &defaultAdvancedSearchFieldRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`advanced_search_field_relationship`",
	}
}

func (m *defaultAdvancedSearchFieldRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerAdvancedSearchFieldRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerAdvancedSearchFieldRelationshipIdKey)
	return err
}

func (m *defaultAdvancedSearchFieldRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerAdvancedSearchFieldRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerAdvancedSearchFieldRelationshipIdKey)
	return err
}

func (m *defaultAdvancedSearchFieldRelationshipModel) FindOne(ctx context.Context, id int64) (*AdvancedSearchFieldRelationship, error) {
	managerAdvancedSearchFieldRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldRelationshipIdPrefix, id)
	var resp AdvancedSearchFieldRelationship
	err := m.QueryRowCtx(ctx, &resp, managerAdvancedSearchFieldRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", advancedSearchFieldRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAdvancedSearchFieldRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *AdvancedSearchFieldRelationship) (sql.Result, error) {
	managerAdvancedSearchFieldRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, advancedSearchFieldRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.FieldId, data.ConditionId, data.Deleted)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.FieldId, data.ConditionId, data.Deleted)
	}, managerAdvancedSearchFieldRelationshipIdKey)
}

func (m *defaultAdvancedSearchFieldRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *AdvancedSearchFieldRelationship) (sql.Result, error) {

	managerAdvancedSearchFieldRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, advancedSearchFieldRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.FieldId, data.ConditionId, data.Deleted, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.FieldId, data.ConditionId, data.Deleted, data.Id)
	}, managerAdvancedSearchFieldRelationshipIdKey)
}

func (m *defaultAdvancedSearchFieldRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerAdvancedSearchFieldRelationshipIdPrefix, primary)
}

func (m *defaultAdvancedSearchFieldRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", advancedSearchFieldRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultAdvancedSearchFieldRelationshipModel) tableName() string {
	return m.table
}
