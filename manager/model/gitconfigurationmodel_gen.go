// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	gitConfigurationTableName           = "`git_configuration`"
	gitConfigurationFieldNames          = builder.RawFieldNames(&GitConfiguration{})
	gitConfigurationRows                = strings.Join(gitConfigurationFieldNames, ",")
	gitConfigurationRowsExpectAutoSet   = strings.Join(stringx.Remove(gitConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	gitConfigurationRowsWithPlaceHolder = strings.Join(stringx.Remove(gitConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerGitConfigurationIdPrefix                = "cache:manager:gitConfiguration:id:"
	cacheManagerGitConfigurationProjectIdConfigIdPrefix = "cache:manager:gitConfiguration:projectId:configId:"
)

type (
	gitConfigurationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *GitConfiguration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*GitConfiguration, error)
		FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*GitConfiguration, error)
		Update(ctx context.Context, session sqlx.Session, data *GitConfiguration) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultGitConfigurationModel struct {
		sqlc.CachedConn
		table string
	}

	GitConfiguration struct {
		Id          int64          `db:"id"`           // 自增ID
		ProjectId   string         `db:"project_id"`   // 项目ID
		ConfigId    string         `db:"config_id"`    // Git配置ID
		Type        string         `db:"type"`         // Git类型（GitLab、GitHub、Gitee）
		Name        string         `db:"name"`         // Git配置名称
		Description sql.NullString `db:"description"`  // Git配置描述
		Url         string         `db:"url"`          // Git项目URL（http）
		AccessToken string         `db:"access_token"` // Git项目访问令牌
		Branch      string         `db:"branch"`       // Git项目分支名称
		Purpose     string         `db:"purpose"`      // 用途（API测试、UI测试）
		Deleted     int64          `db:"deleted"`      // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`   // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`   // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`   // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`   // 删除时间
	}
)

func newGitConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultGitConfigurationModel {
	return &defaultGitConfigurationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`git_configuration`",
	}
}

func (m *defaultGitConfigurationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerGitConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, id)
	managerGitConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerGitConfigurationIdKey, managerGitConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultGitConfigurationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerGitConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, id)
	managerGitConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerGitConfigurationIdKey, managerGitConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultGitConfigurationModel) FindOne(ctx context.Context, id int64) (*GitConfiguration, error) {
	managerGitConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, id)
	var resp GitConfiguration
	err := m.QueryRowCtx(ctx, &resp, managerGitConfigurationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", gitConfigurationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultGitConfigurationModel) FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*GitConfiguration, error) {
	managerGitConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, projectId, configId)
	var resp GitConfiguration
	err := m.QueryRowIndexCtx(ctx, &resp, managerGitConfigurationProjectIdConfigIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `config_id` = ? and `deleted` = ? limit 1", gitConfigurationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, configId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultGitConfigurationModel) Insert(ctx context.Context, session sqlx.Session, data *GitConfiguration) (sql.Result, error) {
	managerGitConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, data.Id)
	managerGitConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, gitConfigurationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Type, data.Name, data.Description, data.Url, data.AccessToken, data.Branch, data.Purpose, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Type, data.Name, data.Description, data.Url, data.AccessToken, data.Branch, data.Purpose, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerGitConfigurationIdKey, managerGitConfigurationProjectIdConfigIdKey)
}

func (m *defaultGitConfigurationModel) Update(ctx context.Context, session sqlx.Session, newData *GitConfiguration) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerGitConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, data.Id)
	managerGitConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, gitConfigurationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Type, newData.Name, newData.Description, newData.Url, newData.AccessToken, newData.Branch, newData.Purpose, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Type, newData.Name, newData.Description, newData.Url, newData.AccessToken, newData.Branch, newData.Purpose, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerGitConfigurationIdKey, managerGitConfigurationProjectIdConfigIdKey)
}

func (m *defaultGitConfigurationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, primary)
}

func (m *defaultGitConfigurationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", gitConfigurationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultGitConfigurationModel) tableName() string {
	return m.table
}
