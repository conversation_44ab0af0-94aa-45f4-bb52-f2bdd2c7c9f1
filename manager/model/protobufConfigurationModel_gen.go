// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	protobufConfigurationTableName           = "`protobuf_configuration`"
	protobufConfigurationFieldNames          = builder.RawFieldNames(&ProtobufConfiguration{})
	protobufConfigurationRows                = strings.Join(protobufConfigurationFieldNames, ",")
	protobufConfigurationRowsExpectAutoSet   = strings.Join(stringx.Remove(protobufConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	protobufConfigurationRowsWithPlaceHolder = strings.Join(stringx.Remove(protobufConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerProtobufConfigurationIdPrefix                = "cache:manager:protobufConfiguration:id:"
	cacheManagerProtobufConfigurationProjectIdConfigIdPrefix = "cache:manager:protobufConfiguration:projectId:configId:"
)

type (
	protobufConfigurationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ProtobufConfiguration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ProtobufConfiguration, error)
		FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*ProtobufConfiguration, error)
		Update(ctx context.Context, session sqlx.Session, data *ProtobufConfiguration) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultProtobufConfigurationModel struct {
		sqlc.CachedConn
		table string
	}

	ProtobufConfiguration struct {
		Id           int64          `db:"id"`            // 自增ID
		ProjectId    string         `db:"project_id"`    // 项目ID
		ConfigId     string         `db:"config_id"`     // Protobuf配置ID
		Name         string         `db:"name"`          // Protobuf配置名称
		Description  sql.NullString `db:"description"`   // Protobuf配置描述
		GitConfigId  string         `db:"git_config_id"` // Git配置ID
		ImportPath   string         `db:"import_path"`   // 导入路径（相对路径）
		ExcludePaths sql.NullString `db:"exclude_paths"` // 排除的路径（相对路径）
		ExcludeFiles sql.NullString `db:"exclude_files"` // 排除的文件（相对路径）
		Deleted      int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newProtobufConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultProtobufConfigurationModel {
	return &defaultProtobufConfigurationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`protobuf_configuration`",
	}
}

func (m *defaultProtobufConfigurationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerProtobufConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufConfigurationIdPrefix, id)
	managerProtobufConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerProtobufConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerProtobufConfigurationIdKey, managerProtobufConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultProtobufConfigurationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerProtobufConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufConfigurationIdPrefix, id)
	managerProtobufConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerProtobufConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerProtobufConfigurationIdKey, managerProtobufConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultProtobufConfigurationModel) FindOne(ctx context.Context, id int64) (*ProtobufConfiguration, error) {
	managerProtobufConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufConfigurationIdPrefix, id)
	var resp ProtobufConfiguration
	err := m.QueryRowCtx(ctx, &resp, managerProtobufConfigurationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", protobufConfigurationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultProtobufConfigurationModel) FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*ProtobufConfiguration, error) {
	managerProtobufConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerProtobufConfigurationProjectIdConfigIdPrefix, projectId, configId)
	var resp ProtobufConfiguration
	err := m.QueryRowIndexCtx(ctx, &resp, managerProtobufConfigurationProjectIdConfigIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `config_id` = ? and `deleted` = ? limit 1", protobufConfigurationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, configId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultProtobufConfigurationModel) Insert(ctx context.Context, session sqlx.Session, data *ProtobufConfiguration) (sql.Result, error) {
	managerProtobufConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufConfigurationIdPrefix, data.Id)
	managerProtobufConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerProtobufConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, protobufConfigurationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Name, data.Description, data.GitConfigId, data.ImportPath, data.ExcludePaths, data.ExcludeFiles, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Name, data.Description, data.GitConfigId, data.ImportPath, data.ExcludePaths, data.ExcludeFiles, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerProtobufConfigurationIdKey, managerProtobufConfigurationProjectIdConfigIdKey)
}

func (m *defaultProtobufConfigurationModel) Update(ctx context.Context, session sqlx.Session, newData *ProtobufConfiguration) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerProtobufConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerProtobufConfigurationIdPrefix, data.Id)
	managerProtobufConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerProtobufConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, protobufConfigurationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Name, newData.Description, newData.GitConfigId, newData.ImportPath, newData.ExcludePaths, newData.ExcludeFiles, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Name, newData.Description, newData.GitConfigId, newData.ImportPath, newData.ExcludePaths, newData.ExcludeFiles, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerProtobufConfigurationIdKey, managerProtobufConfigurationProjectIdConfigIdKey)
}

func (m *defaultProtobufConfigurationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerProtobufConfigurationIdPrefix, primary)
}

func (m *defaultProtobufConfigurationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", protobufConfigurationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultProtobufConfigurationModel) tableName() string {
	return m.table
}
