// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceConfigurationTableName           = "`interface_configuration`"
	interfaceConfigurationFieldNames          = builder.RawFieldNames(&InterfaceConfiguration{})
	interfaceConfigurationRows                = strings.Join(interfaceConfigurationFieldNames, ",")
	interfaceConfigurationRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceConfigurationRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceConfigurationIdPrefix                          = "cache:manager:interfaceConfiguration:id:"
	cacheManagerInterfaceConfigurationProjectIdDocumentIdConfigIdPrefix = "cache:manager:interfaceConfiguration:projectId:documentId:configId:"
)

type (
	interfaceConfigurationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceConfiguration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceConfiguration, error)
		FindOneByProjectIdDocumentIdConfigId(ctx context.Context, projectId string, documentId string, configId string) (*InterfaceConfiguration, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceConfiguration) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceConfigurationModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceConfiguration struct {
		Id               int64          `db:"id"`
		ProjectId        string         `db:"project_id"`        // 项目ID
		DocumentId       string         `db:"document_id"`       // 接口ID
		ConfigId         string         `db:"config_id"`         // 配置ID
		Name             string         `db:"name"`              // 配置名称
		Description      sql.NullString `db:"description"`       // 配置描述
		Path             string         `db:"path"`              // 配置的请求路径
		Method           string         `db:"method"`            // 配置的请求方法
		Data             string         `db:"data"`              // 配置的详细数据
		InputParameters  string         `db:"input_parameters"`  // 配置的输入参数列表
		OutputParameters string         `db:"output_parameters"` // 配置的输出参数列表
		Deleted          int64          `db:"deleted"`           // 逻辑删除标识（未删除、已删除）
		CreatedBy        string         `db:"created_by"`        // 创建者的用户ID
		UpdatedBy        string         `db:"updated_by"`        // 最近一次更新者的用户ID
		DeletedBy        sql.NullString `db:"deleted_by"`        // 删除者的用户ID
		CreatedAt        time.Time      `db:"created_at"`        // 创建时间
		UpdatedAt        time.Time      `db:"updated_at"`        // 更新时间
		DeletedAt        sql.NullTime   `db:"deleted_at"`        // 删除时间
	}
)

func newInterfaceConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceConfigurationModel {
	return &defaultInterfaceConfigurationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_configuration`",
	}
}

func (m *defaultInterfaceConfigurationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceConfigurationIdPrefix, id)
	managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceConfigurationProjectIdDocumentIdConfigIdPrefix, data.ProjectId, data.DocumentId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceConfigurationIdKey, managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey)
	return err
}

func (m *defaultInterfaceConfigurationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceConfigurationIdPrefix, id)
	managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceConfigurationProjectIdDocumentIdConfigIdPrefix, data.ProjectId, data.DocumentId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceConfigurationIdKey, managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey)
	return err
}

func (m *defaultInterfaceConfigurationModel) FindOne(ctx context.Context, id int64) (*InterfaceConfiguration, error) {
	managerInterfaceConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceConfigurationIdPrefix, id)
	var resp InterfaceConfiguration
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceConfigurationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceConfigurationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceConfigurationModel) FindOneByProjectIdDocumentIdConfigId(ctx context.Context, projectId string, documentId string, configId string) (*InterfaceConfiguration, error) {
	managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceConfigurationProjectIdDocumentIdConfigIdPrefix, projectId, documentId, configId)
	var resp InterfaceConfiguration
	err := m.QueryRowIndexCtx(ctx, &resp, managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `document_id` = ? and `config_id` = ? and `deleted` = ? limit 1", interfaceConfigurationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, documentId, configId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceConfigurationModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceConfiguration) (sql.Result, error) {
	managerInterfaceConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceConfigurationIdPrefix, data.Id)
	managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceConfigurationProjectIdDocumentIdConfigIdPrefix, data.ProjectId, data.DocumentId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceConfigurationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.DocumentId, data.ConfigId, data.Name, data.Description, data.Path, data.Method, data.Data, data.InputParameters, data.OutputParameters, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.DocumentId, data.ConfigId, data.Name, data.Description, data.Path, data.Method, data.Data, data.InputParameters, data.OutputParameters, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceConfigurationIdKey, managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey)
}

func (m *defaultInterfaceConfigurationModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceConfiguration) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceConfigurationIdPrefix, data.Id)
	managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceConfigurationProjectIdDocumentIdConfigIdPrefix, data.ProjectId, data.DocumentId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceConfigurationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.DocumentId, newData.ConfigId, newData.Name, newData.Description, newData.Path, newData.Method, newData.Data, newData.InputParameters, newData.OutputParameters, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.DocumentId, newData.ConfigId, newData.Name, newData.Description, newData.Path, newData.Method, newData.Data, newData.InputParameters, newData.OutputParameters, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerInterfaceConfigurationIdKey, managerInterfaceConfigurationProjectIdDocumentIdConfigIdKey)
}

func (m *defaultInterfaceConfigurationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceConfigurationIdPrefix, primary)
}

func (m *defaultInterfaceConfigurationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceConfigurationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceConfigurationModel) tableName() string {
	return m.table
}
