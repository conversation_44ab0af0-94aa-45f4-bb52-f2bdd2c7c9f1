// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	apiCaseTableName           = "`api_case`"
	apiCaseFieldNames          = builder.RawFieldNames(&ApiCase{})
	apiCaseRows                = strings.Join(apiCaseFieldNames, ",")
	apiCaseRowsExpectAutoSet   = strings.Join(stringx.Remove(apiCaseFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	apiCaseRowsWithPlaceHolder = strings.Join(stringx.Remove(apiCaseFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApiCaseIdPrefix                     = "cache:manager:apiCase:id:"
	cacheManagerApiCaseProjectIdCaseIdVersionPrefix = "cache:manager:apiCase:projectId:caseId:version:"
)

type (
	apiCaseModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApiCase) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApiCase, error)
		FindOneByProjectIdCaseIdVersion(ctx context.Context, projectId string, caseId string, version string) (*ApiCase, error)
		Update(ctx context.Context, session sqlx.Session, data *ApiCase) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApiCaseModel struct {
		sqlc.CachedConn
		table string
	}

	ApiCase struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		CategoryId    string         `db:"category_id"`    // 所属分类ID
		CaseId        string         `db:"case_id"`        // 用例ID
		Name          string         `db:"name"`           // 用例名称
		Description   sql.NullString `db:"description"`    // 用例描述
		Priority      int64          `db:"priority"`       // 优先级（NULL、P0、P1、P2、P3...）
		Tags          sql.NullString `db:"tags"`           // 标签
		State         string         `db:"state"`          // 用例状态（新、待实现、待维护、待审核、已上线）
		AccountConfig string         `db:"account_config"` // 池账号配置数
		Version       string         `db:"version"`        // 用例版本
		Structure     string         `db:"structure"`      // 用例中各节点的关系结构
		Latest        int64          `db:"latest"`         // 是否最新版本
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		MaintainedBy  sql.NullString `db:"maintained_by"`  // 维护者的用户ID
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newApiCaseModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApiCaseModel {
	return &defaultApiCaseModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`api_case`",
	}
}

func (m *defaultApiCaseModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiCaseIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, id)
	managerApiCaseProjectIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, data.ProjectId, data.CaseId, data.Version)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApiCaseIdKey, managerApiCaseProjectIdCaseIdVersionKey)
	return err
}

func (m *defaultApiCaseModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiCaseIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, id)
	managerApiCaseProjectIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, data.ProjectId, data.CaseId, data.Version)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApiCaseIdKey, managerApiCaseProjectIdCaseIdVersionKey)
	return err
}

func (m *defaultApiCaseModel) FindOne(ctx context.Context, id int64) (*ApiCase, error) {
	managerApiCaseIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, id)
	var resp ApiCase
	err := m.QueryRowCtx(ctx, &resp, managerApiCaseIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", apiCaseRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiCaseModel) FindOneByProjectIdCaseIdVersion(ctx context.Context, projectId string, caseId string, version string) (*ApiCase, error) {
	managerApiCaseProjectIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, projectId, caseId, version)
	var resp ApiCase
	err := m.QueryRowIndexCtx(ctx, &resp, managerApiCaseProjectIdCaseIdVersionKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `case_id` = ? and `version` = ? and `deleted` = ? limit 1", apiCaseRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, caseId, version, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiCaseModel) Insert(ctx context.Context, session sqlx.Session, data *ApiCase) (sql.Result, error) {
	managerApiCaseIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, data.Id)
	managerApiCaseProjectIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, data.ProjectId, data.CaseId, data.Version)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, apiCaseRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.CaseId, data.Name, data.Description, data.Priority, data.Tags, data.State, data.AccountConfig, data.Version, data.Structure, data.Latest, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.CaseId, data.Name, data.Description, data.Priority, data.Tags, data.State, data.AccountConfig, data.Version, data.Structure, data.Latest, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApiCaseIdKey, managerApiCaseProjectIdCaseIdVersionKey)
}

func (m *defaultApiCaseModel) Update(ctx context.Context, session sqlx.Session, newData *ApiCase) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerApiCaseIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, data.Id)
	managerApiCaseProjectIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, data.ProjectId, data.CaseId, data.Version)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, apiCaseRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.CaseId, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.State, newData.AccountConfig, newData.Version, newData.Structure, newData.Latest, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.CaseId, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.State, newData.AccountConfig, newData.Version, newData.Structure, newData.Latest, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerApiCaseIdKey, managerApiCaseProjectIdCaseIdVersionKey)
}

func (m *defaultApiCaseModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, primary)
}

func (m *defaultApiCaseModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", apiCaseRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApiCaseModel) tableName() string {
	return m.table
}
