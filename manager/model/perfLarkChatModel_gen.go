// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfLarkChatTableName           = "`perf_lark_chat`"
	perfLarkChatFieldNames          = builder.RawFieldNames(&PerfLarkChat{})
	perfLarkChatRows                = strings.Join(perfLarkChatFieldNames, ",")
	perfLarkChatRowsExpectAutoSet   = strings.Join(stringx.Remove(perfLarkChatFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfLarkChatRowsWithPlaceHolder = strings.Join(stringx.Remove(perfLarkChatFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfLarkChatIdPrefix = "cache:manager:perfLarkChat:id:"
)

type (
	perfLarkChatModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfLarkChat) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfLarkChat, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfLarkChat) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfLarkChatModel struct {
		sqlc.CachedConn
		table string
	}

	PerfLarkChat struct {
		Id          int64          `db:"id"`          // 自增ID
		ProjectId   string         `db:"project_id"`  // 项目ID
		ChatId      string         `db:"chat_id"`     // 飞书群组ID
		Name        string         `db:"name"`        // 飞书群组名称
		Avatar      sql.NullString `db:"avatar"`      // 飞书群组头像URL
		Description sql.NullString `db:"description"` // 飞书群组描述
		External    int64          `db:"external"`    // 是否是外部群
		Status      string         `db:"status"`      // 飞书群组状态（正常、解散、解散并保留）
		Deleted     int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newPerfLarkChatModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfLarkChatModel {
	return &defaultPerfLarkChatModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_lark_chat`",
	}
}

func (m *defaultPerfLarkChatModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfLarkChatIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkChatIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfLarkChatIdKey)
	return err
}

func (m *defaultPerfLarkChatModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfLarkChatIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkChatIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfLarkChatIdKey)
	return err
}

func (m *defaultPerfLarkChatModel) FindOne(ctx context.Context, id int64) (*PerfLarkChat, error) {
	managerPerfLarkChatIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkChatIdPrefix, id)
	var resp PerfLarkChat
	err := m.QueryRowCtx(ctx, &resp, managerPerfLarkChatIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfLarkChatRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfLarkChatModel) Insert(ctx context.Context, session sqlx.Session, data *PerfLarkChat) (sql.Result, error) {
	managerPerfLarkChatIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkChatIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfLarkChatRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ChatId, data.Name, data.Avatar, data.Description, data.External, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ChatId, data.Name, data.Avatar, data.Description, data.External, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfLarkChatIdKey)
}

func (m *defaultPerfLarkChatModel) Update(ctx context.Context, session sqlx.Session, data *PerfLarkChat) (sql.Result, error) {

	managerPerfLarkChatIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkChatIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfLarkChatRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ChatId, data.Name, data.Avatar, data.Description, data.External, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ChatId, data.Name, data.Avatar, data.Description, data.External, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerPerfLarkChatIdKey)
}

func (m *defaultPerfLarkChatModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfLarkChatIdPrefix, primary)
}

func (m *defaultPerfLarkChatModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfLarkChatRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfLarkChatModel) tableName() string {
	return m.table
}
