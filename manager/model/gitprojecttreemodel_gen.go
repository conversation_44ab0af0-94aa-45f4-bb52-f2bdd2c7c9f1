// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	gitProjectTreeTableName           = "`git_project_tree`"
	gitProjectTreeFieldNames          = builder.RawFieldNames(&GitProjectTree{})
	gitProjectTreeRows                = strings.Join(gitProjectTreeFieldNames, ",")
	gitProjectTreeRowsExpectAutoSet   = strings.Join(stringx.Remove(gitProjectTreeFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	gitProjectTreeRowsWithPlaceHolder = strings.Join(stringx.Remove(gitProjectTreeFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerGitProjectTreeIdPrefix = "cache:manager:gitProjectTree:id:"
)

type (
	gitProjectTreeModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *GitProjectTree) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*GitProjectTree, error)
		Update(ctx context.Context, session sqlx.Session, data *GitProjectTree) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultGitProjectTreeModel struct {
		sqlc.CachedConn
		table string
	}

	GitProjectTree struct {
		Id          int64          `db:"id"`            // 自增ID
		ProjectId   string         `db:"project_id"`    // 项目ID
		GitConfigId string         `db:"git_config_id"` // Git配置ID
		Path        string         `db:"path"`          // 节点路径（相对于根路径）
		ParentPath  sql.NullString `db:"parent_path"`   // 父节点路径
		Name        string         `db:"name"`          // 节点名称
		Alias       sql.NullString `db:"alias"`         // 节点别名
		Type        string         `db:"type"`          // 节点类型（目录、文件、包、模块、类、函数）
		Tags        sql.NullString `db:"tags"`          // 标签
		TriggeredBy string         `db:"triggered_by"`  // 触发者的用户ID
		TriggeredAt time.Time      `db:"triggered_at"`  // 触发时间
		Deleted     int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newGitProjectTreeModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultGitProjectTreeModel {
	return &defaultGitProjectTreeModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`git_project_tree`",
	}
}

func (m *defaultGitProjectTreeModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerGitProjectTreeIdKey := fmt.Sprintf("%s%v", cacheManagerGitProjectTreeIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerGitProjectTreeIdKey)
	return err
}

func (m *defaultGitProjectTreeModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerGitProjectTreeIdKey := fmt.Sprintf("%s%v", cacheManagerGitProjectTreeIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerGitProjectTreeIdKey)
	return err
}

func (m *defaultGitProjectTreeModel) FindOne(ctx context.Context, id int64) (*GitProjectTree, error) {
	managerGitProjectTreeIdKey := fmt.Sprintf("%s%v", cacheManagerGitProjectTreeIdPrefix, id)
	var resp GitProjectTree
	err := m.QueryRowCtx(ctx, &resp, managerGitProjectTreeIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", gitProjectTreeRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultGitProjectTreeModel) Insert(ctx context.Context, session sqlx.Session, data *GitProjectTree) (sql.Result, error) {
	managerGitProjectTreeIdKey := fmt.Sprintf("%s%v", cacheManagerGitProjectTreeIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, gitProjectTreeRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.GitConfigId, data.Path, data.ParentPath, data.Name, data.Alias, data.Type, data.Tags, data.TriggeredBy, data.TriggeredAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.GitConfigId, data.Path, data.ParentPath, data.Name, data.Alias, data.Type, data.Tags, data.TriggeredBy, data.TriggeredAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerGitProjectTreeIdKey)
}

func (m *defaultGitProjectTreeModel) Update(ctx context.Context, session sqlx.Session, data *GitProjectTree) (sql.Result, error) {

	managerGitProjectTreeIdKey := fmt.Sprintf("%s%v", cacheManagerGitProjectTreeIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, gitProjectTreeRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.GitConfigId, data.Path, data.ParentPath, data.Name, data.Alias, data.Type, data.Tags, data.TriggeredBy, data.TriggeredAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.GitConfigId, data.Path, data.ParentPath, data.Name, data.Alias, data.Type, data.Tags, data.TriggeredBy, data.TriggeredAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerGitProjectTreeIdKey)
}

func (m *defaultGitProjectTreeModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerGitProjectTreeIdPrefix, primary)
}

func (m *defaultGitProjectTreeModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", gitProjectTreeRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultGitProjectTreeModel) tableName() string {
	return m.table
}
