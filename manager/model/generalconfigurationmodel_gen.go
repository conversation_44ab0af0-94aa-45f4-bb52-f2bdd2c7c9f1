// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	generalConfigurationTableName           = "`general_configuration`"
	generalConfigurationFieldNames          = builder.RawFieldNames(&GeneralConfiguration{})
	generalConfigurationRows                = strings.Join(generalConfigurationFieldNames, ",")
	generalConfigurationRowsExpectAutoSet   = strings.Join(stringx.Remove(generalConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	generalConfigurationRowsWithPlaceHolder = strings.Join(stringx.Remove(generalConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerGeneralConfigurationIdPrefix                = "cache:manager:generalConfiguration:id:"
	cacheManagerGeneralConfigurationProjectIdConfigIdPrefix = "cache:manager:generalConfiguration:projectId:configId:"
)

type (
	generalConfigurationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *GeneralConfiguration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*GeneralConfiguration, error)
		FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*GeneralConfiguration, error)
		Update(ctx context.Context, session sqlx.Session, data *GeneralConfiguration) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultGeneralConfigurationModel struct {
		sqlc.CachedConn
		table string
	}

	GeneralConfiguration struct {
		Id          int64          `db:"id"`
		ProjectId   string         `db:"project_id"`  // 项目ID
		ConfigId    string         `db:"config_id"`   // 通用配置ID
		Type        string         `db:"type"`        // 类型（接口测试、压力测试）
		Name        string         `db:"name"`        // 通用配置名称
		Description sql.NullString `db:"description"` // 通用配置描述
		BaseUrl     sql.NullString `db:"base_url"`    // HTTP请求基础URL
		Verify      int64          `db:"verify"`      // 是否验证服务器的TLS证书
		Variables   string         `db:"variables"`   // 变量列表
		Deleted     int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newGeneralConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultGeneralConfigurationModel {
	return &defaultGeneralConfigurationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`general_configuration`",
	}
}

func (m *defaultGeneralConfigurationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerGeneralConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGeneralConfigurationIdPrefix, id)
	managerGeneralConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGeneralConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerGeneralConfigurationIdKey, managerGeneralConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultGeneralConfigurationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerGeneralConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGeneralConfigurationIdPrefix, id)
	managerGeneralConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGeneralConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerGeneralConfigurationIdKey, managerGeneralConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultGeneralConfigurationModel) FindOne(ctx context.Context, id int64) (*GeneralConfiguration, error) {
	managerGeneralConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGeneralConfigurationIdPrefix, id)
	var resp GeneralConfiguration
	err := m.QueryRowCtx(ctx, &resp, managerGeneralConfigurationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", generalConfigurationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultGeneralConfigurationModel) FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*GeneralConfiguration, error) {
	managerGeneralConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGeneralConfigurationProjectIdConfigIdPrefix, projectId, configId)
	var resp GeneralConfiguration
	err := m.QueryRowIndexCtx(ctx, &resp, managerGeneralConfigurationProjectIdConfigIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `config_id` = ? and `deleted` = ? limit 1", generalConfigurationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, configId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultGeneralConfigurationModel) Insert(ctx context.Context, session sqlx.Session, data *GeneralConfiguration) (sql.Result, error) {
	managerGeneralConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGeneralConfigurationIdPrefix, data.Id)
	managerGeneralConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGeneralConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, generalConfigurationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Type, data.Name, data.Description, data.BaseUrl, data.Verify, data.Variables, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Type, data.Name, data.Description, data.BaseUrl, data.Verify, data.Variables, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerGeneralConfigurationIdKey, managerGeneralConfigurationProjectIdConfigIdKey)
}

func (m *defaultGeneralConfigurationModel) Update(ctx context.Context, session sqlx.Session, newData *GeneralConfiguration) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerGeneralConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGeneralConfigurationIdPrefix, data.Id)
	managerGeneralConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerGeneralConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, generalConfigurationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Type, newData.Name, newData.Description, newData.BaseUrl, newData.Verify, newData.Variables, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Type, newData.Name, newData.Description, newData.BaseUrl, newData.Verify, newData.Variables, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerGeneralConfigurationIdKey, managerGeneralConfigurationProjectIdConfigIdKey)
}

func (m *defaultGeneralConfigurationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerGeneralConfigurationIdPrefix, primary)
}

func (m *defaultGeneralConfigurationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", generalConfigurationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultGeneralConfigurationModel) tableName() string {
	return m.table
}
