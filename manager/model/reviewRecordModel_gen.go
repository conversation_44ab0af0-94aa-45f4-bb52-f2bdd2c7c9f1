// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	reviewRecordTableName           = "`review_record`"
	reviewRecordFieldNames          = builder.RawFieldNames(&ReviewRecord{})
	reviewRecordRows                = strings.Join(reviewRecordFieldNames, ",")
	reviewRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(reviewRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	reviewRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(reviewRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerReviewRecordIdPrefix                = "cache:manager:reviewRecord:id:"
	cacheManagerReviewRecordProjectIdReviewIdPrefix = "cache:manager:reviewRecord:projectId:reviewId:"
)

type (
	reviewRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ReviewRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ReviewRecord, error)
		FindOneByProjectIdReviewId(ctx context.Context, projectId string, reviewId string) (*ReviewRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *ReviewRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultReviewRecordModel struct {
		sqlc.CachedConn
		table string
	}

	ReviewRecord struct {
		Id                int64          `db:"id"`                 // 自增ID
		ProjectId         string         `db:"project_id"`         // 项目ID
		ReviewId          string         `db:"review_id"`          // 审核ID
		ResourceType      string         `db:"resource_type"`      // 资源类型（业务组件、前置组件、后置组件、场景用例、接口用例）
		ResourceId        string         `db:"resource_id"`        // 资源ID（组件组ID、场景用例ID、接口用例ID）
		ResourceStatus    string         `db:"resource_status"`    // 资源状态（申请审核时的状态，如：待实现、待维护）
		RemarkOfPending   sql.NullString `db:"remark_of_pending"`  // 申请时的备注
		RemarkOfRevoked   sql.NullString `db:"remark_of_revoked"`  // 撤回时的备注
		RemarkOfReviewed  sql.NullString `db:"remark_of_reviewed"` // 审批时的备注
		AssignedReviewers string         `db:"assigned_reviewers"` // 指派的审核者
		Status            string         `db:"status"`             // 审核状态（待审核、已撤销、通过、驳回）
		Deleted           int64          `db:"deleted"`            // 逻辑删除标识（未删除、已删除）
		CreatedBy         string         `db:"created_by"`         // 创建者的用户ID
		UpdatedBy         string         `db:"updated_by"`         // 最近一次更新者的用户ID
		DeletedBy         sql.NullString `db:"deleted_by"`         // 删除者的用户ID
		CreatedAt         time.Time      `db:"created_at"`         // 创建时间
		UpdatedAt         time.Time      `db:"updated_at"`         // 更新时间
		DeletedAt         sql.NullTime   `db:"deleted_at"`         // 删除时间
	}
)

func newReviewRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultReviewRecordModel {
	return &defaultReviewRecordModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`review_record`",
	}
}

func (m *defaultReviewRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerReviewRecordIdKey := fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, id)
	managerReviewRecordProjectIdReviewIdKey := fmt.Sprintf("%s%v:%v", cacheManagerReviewRecordProjectIdReviewIdPrefix, data.ProjectId, data.ReviewId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerReviewRecordIdKey, managerReviewRecordProjectIdReviewIdKey)
	return err
}

func (m *defaultReviewRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerReviewRecordIdKey := fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, id)
	managerReviewRecordProjectIdReviewIdKey := fmt.Sprintf("%s%v:%v", cacheManagerReviewRecordProjectIdReviewIdPrefix, data.ProjectId, data.ReviewId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerReviewRecordIdKey, managerReviewRecordProjectIdReviewIdKey)
	return err
}

func (m *defaultReviewRecordModel) FindOne(ctx context.Context, id int64) (*ReviewRecord, error) {
	managerReviewRecordIdKey := fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, id)
	var resp ReviewRecord
	err := m.QueryRowCtx(ctx, &resp, managerReviewRecordIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", reviewRecordRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultReviewRecordModel) FindOneByProjectIdReviewId(ctx context.Context, projectId string, reviewId string) (*ReviewRecord, error) {
	managerReviewRecordProjectIdReviewIdKey := fmt.Sprintf("%s%v:%v", cacheManagerReviewRecordProjectIdReviewIdPrefix, projectId, reviewId)
	var resp ReviewRecord
	err := m.QueryRowIndexCtx(ctx, &resp, managerReviewRecordProjectIdReviewIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `review_id` = ? and `deleted` = ? limit 1", reviewRecordRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, reviewId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultReviewRecordModel) Insert(ctx context.Context, session sqlx.Session, data *ReviewRecord) (sql.Result, error) {
	managerReviewRecordIdKey := fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, data.Id)
	managerReviewRecordProjectIdReviewIdKey := fmt.Sprintf("%s%v:%v", cacheManagerReviewRecordProjectIdReviewIdPrefix, data.ProjectId, data.ReviewId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, reviewRecordRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReviewId, data.ResourceType, data.ResourceId, data.ResourceStatus, data.RemarkOfPending, data.RemarkOfRevoked, data.RemarkOfReviewed, data.AssignedReviewers, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReviewId, data.ResourceType, data.ResourceId, data.ResourceStatus, data.RemarkOfPending, data.RemarkOfRevoked, data.RemarkOfReviewed, data.AssignedReviewers, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerReviewRecordIdKey, managerReviewRecordProjectIdReviewIdKey)
}

func (m *defaultReviewRecordModel) Update(ctx context.Context, session sqlx.Session, newData *ReviewRecord) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerReviewRecordIdKey := fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, data.Id)
	managerReviewRecordProjectIdReviewIdKey := fmt.Sprintf("%s%v:%v", cacheManagerReviewRecordProjectIdReviewIdPrefix, data.ProjectId, data.ReviewId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, reviewRecordRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ReviewId, newData.ResourceType, newData.ResourceId, newData.ResourceStatus, newData.RemarkOfPending, newData.RemarkOfRevoked, newData.RemarkOfReviewed, newData.AssignedReviewers, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ReviewId, newData.ResourceType, newData.ResourceId, newData.ResourceStatus, newData.RemarkOfPending, newData.RemarkOfRevoked, newData.RemarkOfReviewed, newData.AssignedReviewers, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerReviewRecordIdKey, managerReviewRecordProjectIdReviewIdKey)
}

func (m *defaultReviewRecordModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, primary)
}

func (m *defaultReviewRecordModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", reviewRecordRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultReviewRecordModel) tableName() string {
	return m.table
}
