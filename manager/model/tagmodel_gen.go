// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	tagTableName           = "`tag`"
	tagFieldNames          = builder.RawFieldNames(&Tag{})
	tagRows                = strings.Join(tagFieldNames, ",")
	tagRowsExpectAutoSet   = strings.Join(stringx.Remove(tagFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	tagRowsWithPlaceHolder = strings.Join(stringx.Remove(tagFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerTagIdPrefix             = "cache:manager:tag:id:"
	cacheManagerTagProjectIdTagIdPrefix = "cache:manager:tag:projectId:tagId:"
)

type (
	tagModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Tag) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Tag, error)
		FindOneByProjectIdTagId(ctx context.Context, projectId string, tagId string) (*Tag, error)
		Update(ctx context.Context, session sqlx.Session, data *Tag) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultTagModel struct {
		sqlc.CachedConn
		table string
	}

	Tag struct {
		Id          int64          `db:"id"`
		ProjectId   string         `db:"project_id"`  // 项目ID
		Type        string         `db:"type"`        // 标签类型（组件组、用例、集合、计划、接口文档）
		TagId       string         `db:"tag_id"`      // 标签ID
		Name        string         `db:"name"`        // 标签名称
		Description sql.NullString `db:"description"` // 标签描述
		Status      int64          `db:"status"`      // 标签状态（生效、失效）
		Deleted     int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newTagModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultTagModel {
	return &defaultTagModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`tag`",
	}
}

func (m *defaultTagModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerTagIdKey := fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, id)
	managerTagProjectIdTagIdKey := fmt.Sprintf("%s%v:%v", cacheManagerTagProjectIdTagIdPrefix, data.ProjectId, data.TagId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerTagIdKey, managerTagProjectIdTagIdKey)
	return err
}

func (m *defaultTagModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerTagIdKey := fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, id)
	managerTagProjectIdTagIdKey := fmt.Sprintf("%s%v:%v", cacheManagerTagProjectIdTagIdPrefix, data.ProjectId, data.TagId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerTagIdKey, managerTagProjectIdTagIdKey)
	return err
}

func (m *defaultTagModel) FindOne(ctx context.Context, id int64) (*Tag, error) {
	managerTagIdKey := fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, id)
	var resp Tag
	err := m.QueryRowCtx(ctx, &resp, managerTagIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", tagRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTagModel) FindOneByProjectIdTagId(ctx context.Context, projectId string, tagId string) (*Tag, error) {
	managerTagProjectIdTagIdKey := fmt.Sprintf("%s%v:%v", cacheManagerTagProjectIdTagIdPrefix, projectId, tagId)
	var resp Tag
	err := m.QueryRowIndexCtx(ctx, &resp, managerTagProjectIdTagIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `tag_id` = ? and `deleted` = ? limit 1", tagRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, tagId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTagModel) Insert(ctx context.Context, session sqlx.Session, data *Tag) (sql.Result, error) {
	managerTagIdKey := fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, data.Id)
	managerTagProjectIdTagIdKey := fmt.Sprintf("%s%v:%v", cacheManagerTagProjectIdTagIdPrefix, data.ProjectId, data.TagId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, tagRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Type, data.TagId, data.Name, data.Description, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Type, data.TagId, data.Name, data.Description, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerTagIdKey, managerTagProjectIdTagIdKey)
}

func (m *defaultTagModel) Update(ctx context.Context, session sqlx.Session, newData *Tag) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerTagIdKey := fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, data.Id)
	managerTagProjectIdTagIdKey := fmt.Sprintf("%s%v:%v", cacheManagerTagProjectIdTagIdPrefix, data.ProjectId, data.TagId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, tagRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Type, newData.TagId, newData.Name, newData.Description, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Type, newData.TagId, newData.Name, newData.Description, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerTagIdKey, managerTagProjectIdTagIdKey)
}

func (m *defaultTagModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, primary)
}

func (m *defaultTagModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", tagRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultTagModel) tableName() string {
	return m.table
}
