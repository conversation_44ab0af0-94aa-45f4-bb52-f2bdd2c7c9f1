// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiPlanReferenceRelationshipTableName           = "`ui_plan_reference_relationship`"
	uiPlanReferenceRelationshipFieldNames          = builder.RawFieldNames(&UiPlanReferenceRelationship{})
	uiPlanReferenceRelationshipRows                = strings.Join(uiPlanReferenceRelationshipFieldNames, ",")
	uiPlanReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(uiPlanReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiPlanReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(uiPlanReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerUiPlanReferenceRelationshipIdPrefix = "cache:manager:uiPlanReferenceRelationship:id:"
)

type (
	uiPlanReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiPlanReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiPlanReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *UiPlanReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiPlanReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	UiPlanReferenceRelationship struct {
		Id          int64          `db:"id"`            // 自增ID
		ProjectId   string         `db:"project_id"`    // 项目ID
		GitConfigId string         `db:"git_config_id"` // Git配置ID
		Path        string         `db:"path"`          // 节点路径（相对于根路径）
		PlanId      string         `db:"plan_id"`       // 计划ID
		Deleted     int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newUiPlanReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUiPlanReferenceRelationshipModel {
	return &defaultUiPlanReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`ui_plan_reference_relationship`",
	}
}

func (m *defaultUiPlanReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerUiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerUiPlanReferenceRelationshipIdKey)
	return err
}

func (m *defaultUiPlanReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerUiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerUiPlanReferenceRelationshipIdKey)
	return err
}

func (m *defaultUiPlanReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*UiPlanReferenceRelationship, error) {
	managerUiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanReferenceRelationshipIdPrefix, id)
	var resp UiPlanReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerUiPlanReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiPlanReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiPlanReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *UiPlanReferenceRelationship) (sql.Result, error) {
	managerUiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiPlanReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.GitConfigId, data.Path, data.PlanId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.GitConfigId, data.Path, data.PlanId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerUiPlanReferenceRelationshipIdKey)
}

func (m *defaultUiPlanReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *UiPlanReferenceRelationship) (sql.Result, error) {

	managerUiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiPlanReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.GitConfigId, data.Path, data.PlanId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.GitConfigId, data.Path, data.PlanId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerUiPlanReferenceRelationshipIdKey)
}

func (m *defaultUiPlanReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerUiPlanReferenceRelationshipIdPrefix, primary)
}

func (m *defaultUiPlanReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", uiPlanReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUiPlanReferenceRelationshipModel) tableName() string {
	return m.table
}
