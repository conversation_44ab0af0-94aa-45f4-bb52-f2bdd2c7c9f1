// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	projectDeviceReferenceRelationshipTableName           = "`project_device_reference_relationship`"
	projectDeviceReferenceRelationshipFieldNames          = builder.RawFieldNames(&ProjectDeviceReferenceRelationship{})
	projectDeviceReferenceRelationshipRows                = strings.Join(projectDeviceReferenceRelationshipFieldNames, ",")
	projectDeviceReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(projectDeviceReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	projectDeviceReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(projectDeviceReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerProjectDeviceReferenceRelationshipIdPrefix = "cache:manager:projectDeviceReferenceRelationship:id:"
)

type (
	projectDeviceReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ProjectDeviceReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ProjectDeviceReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *ProjectDeviceReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultProjectDeviceReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	ProjectDeviceReferenceRelationship struct {
		Id            int64          `db:"id"`             // 自增ID
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（UI测试计划、稳定性测试计划）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（UI测试计划ID、稳定性测试计划ID）
		Udid          string         `db:"udid"`           // 设备编号
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newProjectDeviceReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultProjectDeviceReferenceRelationshipModel {
	return &defaultProjectDeviceReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`project_device_reference_relationship`",
	}
}

func (m *defaultProjectDeviceReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerProjectDeviceReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerProjectDeviceReferenceRelationshipIdKey)
	return err
}

func (m *defaultProjectDeviceReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerProjectDeviceReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerProjectDeviceReferenceRelationshipIdKey)
	return err
}

func (m *defaultProjectDeviceReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*ProjectDeviceReferenceRelationship, error) {
	managerProjectDeviceReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceReferenceRelationshipIdPrefix, id)
	var resp ProjectDeviceReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerProjectDeviceReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", projectDeviceReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultProjectDeviceReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *ProjectDeviceReferenceRelationship) (sql.Result, error) {
	managerProjectDeviceReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, projectDeviceReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.Udid, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.Udid, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerProjectDeviceReferenceRelationshipIdKey)
}

func (m *defaultProjectDeviceReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *ProjectDeviceReferenceRelationship) (sql.Result, error) {

	managerProjectDeviceReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerProjectDeviceReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, projectDeviceReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.Udid, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.Udid, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerProjectDeviceReferenceRelationshipIdKey)
}

func (m *defaultProjectDeviceReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerProjectDeviceReferenceRelationshipIdPrefix, primary)
}

func (m *defaultProjectDeviceReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", projectDeviceReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultProjectDeviceReferenceRelationshipModel) tableName() string {
	return m.table
}
