// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	componentGroupReferenceRelationshipTableName           = "`component_group_reference_relationship`"
	componentGroupReferenceRelationshipFieldNames          = builder.RawFieldNames(&ComponentGroupReferenceRelationship{})
	componentGroupReferenceRelationshipRows                = strings.Join(componentGroupReferenceRelationshipFieldNames, ",")
	componentGroupReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(componentGroupReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	componentGroupReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(componentGroupReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerComponentGroupReferenceRelationshipIdPrefix = "cache:manager:componentGroupReferenceRelationship:id:"
)

type (
	componentGroupReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ComponentGroupReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ComponentGroupReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *ComponentGroupReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultComponentGroupReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	ComponentGroupReferenceRelationship struct {
		Id               int64          `db:"id"`
		ProjectId        string         `db:"project_id"`         // 项目ID
		ReferenceType    string         `db:"reference_type"`     // 引用类型（组件组、API用例、接口用例）
		ReferenceId      string         `db:"reference_id"`       // 引用ID（组件组ID、API用例ID、接口用例ID）
		ReferenceVersion string         `db:"reference_version"`  // 引用版本（组件组版本、API用例版本、接口用例版本）
		ComponentGroupId string         `db:"component_group_id"` // 组件组ID，即被引用的ID
		Deleted          int64          `db:"deleted"`            // 逻辑删除标识（未删除、已删除）
		CreatedBy        string         `db:"created_by"`         // 创建者的用户ID
		UpdatedBy        string         `db:"updated_by"`         // 最近一次更新者的用户ID
		DeletedBy        sql.NullString `db:"deleted_by"`         // 删除者的用户ID
		CreatedAt        time.Time      `db:"created_at"`         // 创建时间
		UpdatedAt        time.Time      `db:"updated_at"`         // 更新时间
		DeletedAt        sql.NullTime   `db:"deleted_at"`         // 删除时间
	}
)

func newComponentGroupReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultComponentGroupReferenceRelationshipModel {
	return &defaultComponentGroupReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`component_group_reference_relationship`",
	}
}

func (m *defaultComponentGroupReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerComponentGroupReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerComponentGroupReferenceRelationshipIdKey)
	return err
}

func (m *defaultComponentGroupReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerComponentGroupReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerComponentGroupReferenceRelationshipIdKey)
	return err
}

func (m *defaultComponentGroupReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*ComponentGroupReferenceRelationship, error) {
	managerComponentGroupReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupReferenceRelationshipIdPrefix, id)
	var resp ComponentGroupReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerComponentGroupReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", componentGroupReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultComponentGroupReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *ComponentGroupReferenceRelationship) (sql.Result, error) {
	managerComponentGroupReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, componentGroupReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.ComponentGroupId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.ComponentGroupId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerComponentGroupReferenceRelationshipIdKey)
}

func (m *defaultComponentGroupReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *ComponentGroupReferenceRelationship) (sql.Result, error) {

	managerComponentGroupReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, componentGroupReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.ComponentGroupId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.ComponentGroupId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerComponentGroupReferenceRelationshipIdKey)
}

func (m *defaultComponentGroupReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerComponentGroupReferenceRelationshipIdPrefix, primary)
}

func (m *defaultComponentGroupReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", componentGroupReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultComponentGroupReferenceRelationshipModel) tableName() string {
	return m.table
}
