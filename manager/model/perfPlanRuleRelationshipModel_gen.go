// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfPlanRuleRelationshipTableName           = "`perf_plan_rule_relationship`"
	perfPlanRuleRelationshipFieldNames          = builder.RawFieldNames(&PerfPlanRuleRelationship{})
	perfPlanRuleRelationshipRows                = strings.Join(perfPlanRuleRelationshipFieldNames, ",")
	perfPlanRuleRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(perfPlanRuleRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfPlanRuleRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(perfPlanRuleRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfPlanRuleRelationshipIdPrefix = "cache:manager:perfPlanRuleRelationship:id:"
)

type (
	perfPlanRuleRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfPlanRuleRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfPlanRuleRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfPlanRuleRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfPlanRuleRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	PerfPlanRuleRelationship struct {
		Id        int64          `db:"id"`
		ProjectId string         `db:"project_id"` // 项目ID
		PlanId    string         `db:"plan_id"`    // 压测计划ID
		RuleId    string         `db:"rule_id"`    // 压测停止规则ID
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newPerfPlanRuleRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfPlanRuleRelationshipModel {
	return &defaultPerfPlanRuleRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_plan_rule_relationship`",
	}
}

func (m *defaultPerfPlanRuleRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfPlanRuleRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanRuleRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfPlanRuleRelationshipIdKey)
	return err
}

func (m *defaultPerfPlanRuleRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfPlanRuleRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanRuleRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfPlanRuleRelationshipIdKey)
	return err
}

func (m *defaultPerfPlanRuleRelationshipModel) FindOne(ctx context.Context, id int64) (*PerfPlanRuleRelationship, error) {
	managerPerfPlanRuleRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanRuleRelationshipIdPrefix, id)
	var resp PerfPlanRuleRelationship
	err := m.QueryRowCtx(ctx, &resp, managerPerfPlanRuleRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfPlanRuleRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanRuleRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *PerfPlanRuleRelationship) (sql.Result, error) {
	managerPerfPlanRuleRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanRuleRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, perfPlanRuleRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfPlanRuleRelationshipIdKey)
}

func (m *defaultPerfPlanRuleRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *PerfPlanRuleRelationship) (sql.Result, error) {

	managerPerfPlanRuleRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanRuleRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfPlanRuleRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerPerfPlanRuleRelationshipIdKey)
}

func (m *defaultPerfPlanRuleRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfPlanRuleRelationshipIdPrefix, primary)
}

func (m *defaultPerfPlanRuleRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfPlanRuleRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfPlanRuleRelationshipModel) tableName() string {
	return m.table
}
