package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ StabilityPlanModel = (*customStabilityPlanModel)(nil)

	stabilityPlanInsertFields = stringx.Remove(stabilityPlanFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// StabilityPlanModel is an interface to be customized, add more methods here,
	// and implement the added methods in customStabilityPlanModel.
	StabilityPlanModel interface {
		stabilityPlanModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *StabilityPlan) squirrel.InsertBuilder
		UpdateBuilder(data *StabilityPlan) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityPlan, error)

		RemoveByPlanID(ctx context.Context, session sqlx.Session, projectID, planID string) (sql.Result, error)

		GenerateSearchStabilityPlanSqlBuilder(req SearchStabilityPlanReq) (
			searchStabilityPlanSelectBuilder, searchStabilityPlanCountBuilder,
		)
		FindCountStabilityPlans(ctx context.Context, countBuilder searchStabilityPlanCountBuilder) (int64, error)
		FindStabilityPlans(ctx context.Context, selectBuilder searchStabilityPlanSelectBuilder) ([]*StabilityPlan, error)

		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
	}

	customStabilityPlanModel struct {
		*defaultStabilityPlanModel

		conn sqlx.SqlConn
	}
)

// NewStabilityPlanModel returns a model for the database table.
func NewStabilityPlanModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) StabilityPlanModel {
	return &customStabilityPlanModel{
		defaultStabilityPlanModel: newStabilityPlanModel(conn, c, opts...),
		conn:                      conn,
	}
}

func (m *customStabilityPlanModel) Table() string {
	return m.table
}

func (m *customStabilityPlanModel) Fields() []string {
	return stabilityPlanFieldNames
}

func (m *customStabilityPlanModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customStabilityPlanModel) InsertBuilder(data *StabilityPlan) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(stabilityPlanInsertFields...).Values()
}

func (m *customStabilityPlanModel) UpdateBuilder(data *StabilityPlan) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customStabilityPlanModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(stabilityPlanFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityPlanModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityPlanModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customStabilityPlanModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityPlan, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*StabilityPlan
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customStabilityPlanModel) getKeysByPlanID(ctx context.Context, projectID, planID string) []string {
	perfPlan, err := m.FindOneByProjectIdPlanId(ctx, projectID, planID)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerStabilityPlanIdPrefix, perfPlan.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerStabilityPlanProjectIdPlanIdPrefix, perfPlan.ProjectId, perfPlan.PlanId),
	}
}

func (m *customStabilityPlanModel) RemoveByPlanID(
	ctx context.Context, session sqlx.Session, projectID, planID string,
) (sql.Result, error) {
	keys := m.getKeysByPlanID(ctx, projectID, planID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `stability_plan`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectID, planID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

type searchStabilityPlanSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchStabilityPlanCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customStabilityPlanModel) GenerateSearchStabilityPlanSqlBuilder(req SearchStabilityPlanReq) (
	searchStabilityPlanSelectBuilder, searchStabilityPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t.*
		FROM stability_plan AS t
			LEFT JOIN (
				SELECT t1.*
			    FROM category AS t1
					INNER JOIN category_tree AS t2 ON
						t1.`project_id` = t2.`project_id` AND
						t1.`category_id` = t2.`descendant`
			    WHERE t1.`project_id` = ?
					AND t1.`type` = ?
					AND t2.`ancestor` = ?
					AND t1.`deleted` = ?
					AND t2.`deleted` = ?
			    ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
			) AS t1 ON t.`category_id` = t1.`category_id`
		WHERE t.`project_id` = ?
			AND t.`deleted` = ?
			AND t1.`category_id` IS NOT NULL;
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select(utils.AddTableNameToFields(aliasT, m.Fields())...).
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			req.ProjectID, constants.NotDeleted,
		)
	scb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			req.ProjectID, constants.NotDeleted,
		)

	if req.DrillDown {
		sub := req.CategoryModel.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     req.ProjectID,
				Type:          common.ConstCategoryTreeTypeStabilityPlan,
				CategoryId:    req.CategoryID,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
		scb = scb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), req.CategoryID)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), req.CategoryID)
	}

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT,
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, aliasT, sqlbuilder.WithCondition(m, req.Condition))

	return searchStabilityPlanSelectBuilder{SelectBuilder: sb}, searchStabilityPlanCountBuilder{SelectBuilder: scb}
}

func (m *customStabilityPlanModel) FindCountStabilityPlans(ctx context.Context, countBuilder searchStabilityPlanCountBuilder) (
	int64, error,
) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customStabilityPlanModel) FindStabilityPlans(
	ctx context.Context, selectBuilder searchStabilityPlanSelectBuilder,
) ([]*StabilityPlan, error) {
	var resp []*StabilityPlan

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customStabilityPlanModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeStabilityPlan,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}
