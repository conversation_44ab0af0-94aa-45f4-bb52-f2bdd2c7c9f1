// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	apiSuiteReferenceRelationshipTableName           = "`api_suite_reference_relationship`"
	apiSuiteReferenceRelationshipFieldNames          = builder.RawFieldNames(&ApiSuiteReferenceRelationship{})
	apiSuiteReferenceRelationshipRows                = strings.Join(apiSuiteReferenceRelationshipFieldNames, ",")
	apiSuiteReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(apiSuiteReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	apiSuiteReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(apiSuiteReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApiSuiteReferenceRelationshipIdPrefix = "cache:manager:apiSuiteReferenceRelationship:id:"
)

type (
	apiSuiteReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApiSuiteReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApiSuiteReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *ApiSuiteReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApiSuiteReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	ApiSuiteReferenceRelationship struct {
		Id            int64          `db:"id"`             // 自增ID
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（用例类型）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（用例ID）
		SuiteId       string         `db:"suite_id"`       // API集合ID
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newApiSuiteReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApiSuiteReferenceRelationshipModel {
	return &defaultApiSuiteReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`api_suite_reference_relationship`",
	}
}

func (m *defaultApiSuiteReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerApiSuiteReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApiSuiteReferenceRelationshipIdKey)
	return err
}

func (m *defaultApiSuiteReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerApiSuiteReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApiSuiteReferenceRelationshipIdKey)
	return err
}

func (m *defaultApiSuiteReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*ApiSuiteReferenceRelationship, error) {
	managerApiSuiteReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteReferenceRelationshipIdPrefix, id)
	var resp ApiSuiteReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerApiSuiteReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", apiSuiteReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiSuiteReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *ApiSuiteReferenceRelationship) (sql.Result, error) {
	managerApiSuiteReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, apiSuiteReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.SuiteId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.SuiteId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApiSuiteReferenceRelationshipIdKey)
}

func (m *defaultApiSuiteReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *ApiSuiteReferenceRelationship) (sql.Result, error) {

	managerApiSuiteReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, apiSuiteReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.SuiteId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.SuiteId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerApiSuiteReferenceRelationshipIdKey)
}

func (m *defaultApiSuiteReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApiSuiteReferenceRelationshipIdPrefix, primary)
}

func (m *defaultApiSuiteReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", apiSuiteReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApiSuiteReferenceRelationshipModel) tableName() string {
	return m.table
}
