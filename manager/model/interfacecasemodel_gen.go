// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceCaseTableName           = "`interface_case`"
	interfaceCaseFieldNames          = builder.RawFieldNames(&InterfaceCase{})
	interfaceCaseRows                = strings.Join(interfaceCaseFieldNames, ",")
	interfaceCaseRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceCaseFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceCaseRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceCaseFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceCaseIdPrefix                               = "cache:manager:interfaceCase:id:"
	cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix = "cache:manager:interfaceCase:projectId:documentId:caseId:version:"
)

type (
	interfaceCaseModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceCase) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceCase, error)
		FindOneByProjectIdDocumentIdCaseIdVersion(ctx context.Context, projectId string, documentId string, caseId string, version string) (*InterfaceCase, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceCase) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceCaseModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceCase struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		DocumentId    string         `db:"document_id"`    // 接口ID
		CaseId        string         `db:"case_id"`        // 用例ID
		Name          string         `db:"name"`           // 用例名称
		Description   sql.NullString `db:"description"`    // 用例描述
		Priority      int64          `db:"priority"`       // 优先级（NULL、P0、P1、P2、P3...）
		Tags          sql.NullString `db:"tags"`           // 标签
		State         string         `db:"state"`          // 用例状态（新、待实现、待维护、待审核、已上线）
		AccountConfig string         `db:"account_config"` // 池账号配置数
		Version       string         `db:"version"`        // 用例版本
		Structure     string         `db:"structure"`      // 用例中各节点的关系结构
		Latest        int64          `db:"latest"`         // 是否最新版本
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		MaintainedBy  sql.NullString `db:"maintained_by"`  // 维护者的用户ID
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newInterfaceCaseModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceCaseModel {
	return &defaultInterfaceCaseModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_case`",
	}
}

func (m *defaultInterfaceCaseModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceCaseIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, id)
	managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, data.ProjectId, data.DocumentId, data.CaseId, data.Version)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceCaseIdKey, managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey)
	return err
}

func (m *defaultInterfaceCaseModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceCaseIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, id)
	managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, data.ProjectId, data.DocumentId, data.CaseId, data.Version)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceCaseIdKey, managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey)
	return err
}

func (m *defaultInterfaceCaseModel) FindOne(ctx context.Context, id int64) (*InterfaceCase, error) {
	managerInterfaceCaseIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, id)
	var resp InterfaceCase
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceCaseIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceCaseRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceCaseModel) FindOneByProjectIdDocumentIdCaseIdVersion(ctx context.Context, projectId string, documentId string, caseId string, version string) (*InterfaceCase, error) {
	managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, projectId, documentId, caseId, version)
	var resp InterfaceCase
	err := m.QueryRowIndexCtx(ctx, &resp, managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `document_id` = ? and `case_id` = ? and `version` = ? and `deleted` = ? limit 1", interfaceCaseRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, documentId, caseId, version, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceCaseModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceCase) (sql.Result, error) {
	managerInterfaceCaseIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, data.Id)
	managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, data.ProjectId, data.DocumentId, data.CaseId, data.Version)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceCaseRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.DocumentId, data.CaseId, data.Name, data.Description, data.Priority, data.Tags, data.State, data.AccountConfig, data.Version, data.Structure, data.Latest, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.DocumentId, data.CaseId, data.Name, data.Description, data.Priority, data.Tags, data.State, data.AccountConfig, data.Version, data.Structure, data.Latest, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceCaseIdKey, managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey)
}

func (m *defaultInterfaceCaseModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceCase) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceCaseIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, data.Id)
	managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, data.ProjectId, data.DocumentId, data.CaseId, data.Version)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceCaseRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.DocumentId, newData.CaseId, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.State, newData.AccountConfig, newData.Version, newData.Structure, newData.Latest, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.DocumentId, newData.CaseId, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.State, newData.AccountConfig, newData.Version, newData.Structure, newData.Latest, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerInterfaceCaseIdKey, managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey)
}

func (m *defaultInterfaceCaseModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, primary)
}

func (m *defaultInterfaceCaseModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceCaseRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceCaseModel) tableName() string {
	return m.table
}
