// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfStopRuleTableName           = "`perf_stop_rule`"
	perfStopRuleFieldNames          = builder.RawFieldNames(&PerfStopRule{})
	perfStopRuleRows                = strings.Join(perfStopRuleFieldNames, ",")
	perfStopRuleRowsExpectAutoSet   = strings.Join(stringx.Remove(perfStopRuleFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfStopRuleRowsWithPlaceHolder = strings.Join(stringx.Remove(perfStopRuleFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfStopRuleIdPrefix              = "cache:manager:perfStopRule:id:"
	cacheManagerPerfStopRuleProjectIdRuleIdPrefix = "cache:manager:perfStopRule:projectId:ruleId:"
)

type (
	perfStopRuleModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfStopRule) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfStopRule, error)
		FindOneByProjectIdRuleId(ctx context.Context, projectId string, ruleId string) (*PerfStopRule, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfStopRule) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfStopRuleModel struct {
		sqlc.CachedConn
		table string
	}

	PerfStopRule struct {
		Id          int64          `db:"id"`          // 自增ID
		ProjectId   string         `db:"project_id"`  // 项目ID
		RuleId      string         `db:"rule_id"`     // 压测停止规则ID
		Name        string         `db:"name"`        // 压测停止规则名称
		Description sql.NullString `db:"description"` // 压测停止规则描述
		MetricType  string         `db:"metric_type"` // 指标类型
		Threshold   float64        `db:"threshold"`   // 阈值，保留2位小数（不同的指标类型单位不同）
		Duration    int64          `db:"duration"`    // 持续时间，单位为秒
		State       int64          `db:"state"`       // 规则状态（生效、失效）
		Deleted     int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newPerfStopRuleModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfStopRuleModel {
	return &defaultPerfStopRuleModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_stop_rule`",
	}
}

func (m *defaultPerfStopRuleModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfStopRuleIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleIdPrefix, id)
	managerPerfStopRuleProjectIdRuleIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfStopRuleProjectIdRuleIdPrefix, data.ProjectId, data.RuleId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfStopRuleIdKey, managerPerfStopRuleProjectIdRuleIdKey)
	return err
}

func (m *defaultPerfStopRuleModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfStopRuleIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleIdPrefix, id)
	managerPerfStopRuleProjectIdRuleIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfStopRuleProjectIdRuleIdPrefix, data.ProjectId, data.RuleId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfStopRuleIdKey, managerPerfStopRuleProjectIdRuleIdKey)
	return err
}

func (m *defaultPerfStopRuleModel) FindOne(ctx context.Context, id int64) (*PerfStopRule, error) {
	managerPerfStopRuleIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleIdPrefix, id)
	var resp PerfStopRule
	err := m.QueryRowCtx(ctx, &resp, managerPerfStopRuleIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfStopRuleRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfStopRuleModel) FindOneByProjectIdRuleId(ctx context.Context, projectId string, ruleId string) (*PerfStopRule, error) {
	managerPerfStopRuleProjectIdRuleIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfStopRuleProjectIdRuleIdPrefix, projectId, ruleId)
	var resp PerfStopRule
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfStopRuleProjectIdRuleIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `rule_id` = ? and `deleted` = ? limit 1", perfStopRuleRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, ruleId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfStopRuleModel) Insert(ctx context.Context, session sqlx.Session, data *PerfStopRule) (sql.Result, error) {
	managerPerfStopRuleIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleIdPrefix, data.Id)
	managerPerfStopRuleProjectIdRuleIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfStopRuleProjectIdRuleIdPrefix, data.ProjectId, data.RuleId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfStopRuleRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.RuleId, data.Name, data.Description, data.MetricType, data.Threshold, data.Duration, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.RuleId, data.Name, data.Description, data.MetricType, data.Threshold, data.Duration, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfStopRuleIdKey, managerPerfStopRuleProjectIdRuleIdKey)
}

func (m *defaultPerfStopRuleModel) Update(ctx context.Context, session sqlx.Session, newData *PerfStopRule) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfStopRuleIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleIdPrefix, data.Id)
	managerPerfStopRuleProjectIdRuleIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfStopRuleProjectIdRuleIdPrefix, data.ProjectId, data.RuleId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfStopRuleRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.RuleId, newData.Name, newData.Description, newData.MetricType, newData.Threshold, newData.Duration, newData.State, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.RuleId, newData.Name, newData.Description, newData.MetricType, newData.Threshold, newData.Duration, newData.State, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfStopRuleIdKey, managerPerfStopRuleProjectIdRuleIdKey)
}

func (m *defaultPerfStopRuleModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfStopRuleIdPrefix, primary)
}

func (m *defaultPerfStopRuleModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfStopRuleRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfStopRuleModel) tableName() string {
	return m.table
}
