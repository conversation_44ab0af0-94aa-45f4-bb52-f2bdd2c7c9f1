// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfLarkMemberTableName           = "`perf_lark_member`"
	perfLarkMemberFieldNames          = builder.RawFieldNames(&PerfLarkMember{})
	perfLarkMemberRows                = strings.Join(perfLarkMemberFieldNames, ",")
	perfLarkMemberRowsExpectAutoSet   = strings.Join(stringx.Remove(perfLarkMemberFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfLarkMemberRowsWithPlaceHolder = strings.Join(stringx.Remove(perfLarkMemberFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfLarkMemberIdPrefix               = "cache:manager:perfLarkMember:id:"
	cacheManagerPerfLarkMemberProjectIdAccountPrefix = "cache:manager:perfLarkMember:projectId:account:"
)

type (
	perfLarkMemberModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfLarkMember) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfLarkMember, error)
		FindOneByProjectIdAccount(ctx context.Context, projectId string, account string) (*PerfLarkMember, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfLarkMember) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfLarkMemberModel struct {
		sqlc.CachedConn
		table string
	}

	PerfLarkMember struct {
		Id           int64          `db:"id"`             // 自增ID
		ProjectId    string         `db:"project_id"`     // 项目ID
		Account      string         `db:"account"`        // 用户名（工号）
		Fullname     string         `db:"fullname"`       // 姓名
		FullDeptName sql.NullString `db:"full_dept_name"` // 完整部门名称
		Email        sql.NullString `db:"email"`          // 邮箱
		LarkUserId   sql.NullString `db:"lark_user_id"`   // 飞书用户ID
		Deleted      int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy    string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newPerfLarkMemberModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfLarkMemberModel {
	return &defaultPerfLarkMemberModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_lark_member`",
	}
}

func (m *defaultPerfLarkMemberModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfLarkMemberIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkMemberIdPrefix, id)
	managerPerfLarkMemberProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfLarkMemberProjectIdAccountPrefix, data.ProjectId, data.Account)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfLarkMemberIdKey, managerPerfLarkMemberProjectIdAccountKey)
	return err
}

func (m *defaultPerfLarkMemberModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfLarkMemberIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkMemberIdPrefix, id)
	managerPerfLarkMemberProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfLarkMemberProjectIdAccountPrefix, data.ProjectId, data.Account)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfLarkMemberIdKey, managerPerfLarkMemberProjectIdAccountKey)
	return err
}

func (m *defaultPerfLarkMemberModel) FindOne(ctx context.Context, id int64) (*PerfLarkMember, error) {
	managerPerfLarkMemberIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkMemberIdPrefix, id)
	var resp PerfLarkMember
	err := m.QueryRowCtx(ctx, &resp, managerPerfLarkMemberIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfLarkMemberRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfLarkMemberModel) FindOneByProjectIdAccount(ctx context.Context, projectId string, account string) (*PerfLarkMember, error) {
	managerPerfLarkMemberProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfLarkMemberProjectIdAccountPrefix, projectId, account)
	var resp PerfLarkMember
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfLarkMemberProjectIdAccountKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `account` = ? and `deleted` = ? limit 1", perfLarkMemberRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, account, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfLarkMemberModel) Insert(ctx context.Context, session sqlx.Session, data *PerfLarkMember) (sql.Result, error) {
	managerPerfLarkMemberIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkMemberIdPrefix, data.Id)
	managerPerfLarkMemberProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfLarkMemberProjectIdAccountPrefix, data.ProjectId, data.Account)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfLarkMemberRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Account, data.Fullname, data.FullDeptName, data.Email, data.LarkUserId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Account, data.Fullname, data.FullDeptName, data.Email, data.LarkUserId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfLarkMemberIdKey, managerPerfLarkMemberProjectIdAccountKey)
}

func (m *defaultPerfLarkMemberModel) Update(ctx context.Context, session sqlx.Session, newData *PerfLarkMember) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfLarkMemberIdKey := fmt.Sprintf("%s%v", cacheManagerPerfLarkMemberIdPrefix, data.Id)
	managerPerfLarkMemberProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfLarkMemberProjectIdAccountPrefix, data.ProjectId, data.Account)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfLarkMemberRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Account, newData.Fullname, newData.FullDeptName, newData.Email, newData.LarkUserId, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Account, newData.Fullname, newData.FullDeptName, newData.Email, newData.LarkUserId, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfLarkMemberIdKey, managerPerfLarkMemberProjectIdAccountKey)
}

func (m *defaultPerfLarkMemberModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfLarkMemberIdPrefix, primary)
}

func (m *defaultPerfLarkMemberModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfLarkMemberRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfLarkMemberModel) tableName() string {
	return m.table
}
