// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceUpdateConfigurationTableName           = "`interface_update_configuration`"
	interfaceUpdateConfigurationFieldNames          = builder.RawFieldNames(&InterfaceUpdateConfiguration{})
	interfaceUpdateConfigurationRows                = strings.Join(interfaceUpdateConfigurationFieldNames, ",")
	interfaceUpdateConfigurationRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceUpdateConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceUpdateConfigurationRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceUpdateConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceUpdateConfigurationIdPrefix                     = "cache:manager:interfaceUpdateConfiguration:id:"
	cacheManagerInterfaceUpdateConfigurationProjectIdTypeLocalPathPrefix = "cache:manager:interfaceUpdateConfiguration:projectId:type:localPath:"
)

type (
	interfaceUpdateConfigurationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceUpdateConfiguration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceUpdateConfiguration, error)
		FindOneByProjectIdTypeLocalPath(ctx context.Context, projectId string, tp string, localPath string) (*InterfaceUpdateConfiguration, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceUpdateConfiguration) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceUpdateConfigurationModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceUpdateConfiguration struct {
		Id            int64          `db:"id"`              // 自增ID
		ProjectId     string         `db:"project_id"`      // 项目ID
		Type          string         `db:"type"`            // 接口类型（OpenApi、gRPC、YApi、TT、TTMeta、Recommend）
		LocalPath     string         `db:"local_path"`      // 本地路径
		DepLocalPaths sql.NullString `db:"dep_local_paths"` // 依赖的本地路径
		Deleted       int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newInterfaceUpdateConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceUpdateConfigurationModel {
	return &defaultInterfaceUpdateConfigurationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_update_configuration`",
	}
}

func (m *defaultInterfaceUpdateConfigurationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceUpdateConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceUpdateConfigurationIdPrefix, id)
	managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceUpdateConfigurationProjectIdTypeLocalPathPrefix, data.ProjectId, data.Type, data.LocalPath)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceUpdateConfigurationIdKey, managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey)
	return err
}

func (m *defaultInterfaceUpdateConfigurationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceUpdateConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceUpdateConfigurationIdPrefix, id)
	managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceUpdateConfigurationProjectIdTypeLocalPathPrefix, data.ProjectId, data.Type, data.LocalPath)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceUpdateConfigurationIdKey, managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey)
	return err
}

func (m *defaultInterfaceUpdateConfigurationModel) FindOne(ctx context.Context, id int64) (*InterfaceUpdateConfiguration, error) {
	managerInterfaceUpdateConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceUpdateConfigurationIdPrefix, id)
	var resp InterfaceUpdateConfiguration
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceUpdateConfigurationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceUpdateConfigurationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceUpdateConfigurationModel) FindOneByProjectIdTypeLocalPath(ctx context.Context, projectId string, tp string, localPath string) (*InterfaceUpdateConfiguration, error) {
	managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceUpdateConfigurationProjectIdTypeLocalPathPrefix, projectId, tp, localPath)
	var resp InterfaceUpdateConfiguration
	err := m.QueryRowIndexCtx(ctx, &resp, managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `type` = ? and `local_path` = ? and `deleted` = ? limit 1", interfaceUpdateConfigurationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, tp, localPath, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceUpdateConfigurationModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceUpdateConfiguration) (sql.Result, error) {
	managerInterfaceUpdateConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceUpdateConfigurationIdPrefix, data.Id)
	managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceUpdateConfigurationProjectIdTypeLocalPathPrefix, data.ProjectId, data.Type, data.LocalPath)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceUpdateConfigurationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Type, data.LocalPath, data.DepLocalPaths, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Type, data.LocalPath, data.DepLocalPaths, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceUpdateConfigurationIdKey, managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey)
}

func (m *defaultInterfaceUpdateConfigurationModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceUpdateConfiguration) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceUpdateConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceUpdateConfigurationIdPrefix, data.Id)
	managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceUpdateConfigurationProjectIdTypeLocalPathPrefix, data.ProjectId, data.Type, data.LocalPath)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceUpdateConfigurationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Type, newData.LocalPath, newData.DepLocalPaths, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Type, newData.LocalPath, newData.DepLocalPaths, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerInterfaceUpdateConfigurationIdKey, managerInterfaceUpdateConfigurationProjectIdTypeLocalPathKey)
}

func (m *defaultInterfaceUpdateConfigurationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceUpdateConfigurationIdPrefix, primary)
}

func (m *defaultInterfaceUpdateConfigurationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceUpdateConfigurationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceUpdateConfigurationModel) tableName() string {
	return m.table
}
