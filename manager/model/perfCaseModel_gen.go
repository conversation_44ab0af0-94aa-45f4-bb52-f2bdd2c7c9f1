// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfCaseTableName           = "`perf_case`"
	perfCaseFieldNames          = builder.RawFieldNames(&PerfCase{})
	perfCaseRows                = strings.Join(perfCaseFieldNames, ",")
	perfCaseRowsExpectAutoSet   = strings.Join(stringx.Remove(perfCaseFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfCaseRowsWithPlaceHolder = strings.Join(stringx.Remove(perfCaseFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfCaseIdPrefix              = "cache:manager:perfCase:id:"
	cacheManagerPerfCaseProjectIdCaseIdPrefix = "cache:manager:perfCase:projectId:caseId:"
)

type (
	perfCaseModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfCase) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfCase, error)
		FindOneByProjectIdCaseId(ctx context.Context, projectId string, caseId string) (*PerfCase, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfCase) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfCaseModel struct {
		sqlc.CachedConn
		table string
	}

	PerfCase struct {
		Id               int64          `db:"id"`
		ProjectId        string         `db:"project_id"`         // 项目ID
		CaseId           string         `db:"case_id"`            // 压测用例ID
		Name             string         `db:"name"`               // 压测用例名称
		Description      sql.NullString `db:"description"`        // 压测用例描述
		Extension        string         `db:"extension"`          // 文件扩展名
		Hash             string         `db:"hash"`               // 压测用例文件的一致性哈希值（MD5）
		Size             int64          `db:"size"`               // 压测用例文件的大小
		Path             string         `db:"path"`               // 压测用例文件的路径
		TargetRps        int64          `db:"target_rps"`         // 目标的RPS
		InitialRps       int64          `db:"initial_rps"`        // 初始的RPS
		StepHeight       int64          `db:"step_height"`        // 每次改变RPS的量
		StepDuration     string         `db:"step_duration"`      // 改变后的RPS的持续时间
		PerfDataId       string         `db:"perf_data_id"`       // 压测数据ID
		NumberOfVu       int64          `db:"number_of_vu"`       // 虚拟用户数
		NumberOfLg       int64          `db:"number_of_lg"`       // 施压机数
		RequestsOfCpu    string         `db:"requests_of_cpu"`    // 最小分配的CPU资源
		RequestsOfMemory string         `db:"requests_of_memory"` // 最小分配的内存资源
		LimitsOfCpu      string         `db:"limits_of_cpu"`      // 最大分配的CPU资源
		LimitsOfMemory   string         `db:"limits_of_memory"`   // 最大分配的内存资源
		State            int64          `db:"state"`              // 计划状态（生效、失效）
		Deleted          int64          `db:"deleted"`            // 逻辑删除标识（未删除、已删除）
		MaintainedBy     sql.NullString `db:"maintained_by"`      // 维护者的用户ID
		CreatedBy        string         `db:"created_by"`         // 创建者的用户ID
		UpdatedBy        string         `db:"updated_by"`         // 最近一次更新者的用户ID
		DeletedBy        sql.NullString `db:"deleted_by"`         // 删除者的用户ID
		CreatedAt        time.Time      `db:"created_at"`         // 创建时间
		UpdatedAt        time.Time      `db:"updated_at"`         // 更新时间
		DeletedAt        sql.NullTime   `db:"deleted_at"`         // 删除时间
	}
)

func newPerfCaseModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfCaseModel {
	return &defaultPerfCaseModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_case`",
	}
}

func (m *defaultPerfCaseModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfCaseIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseIdPrefix, id)
	managerPerfCaseProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseProjectIdCaseIdPrefix, data.ProjectId, data.CaseId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfCaseIdKey, managerPerfCaseProjectIdCaseIdKey)
	return err
}

func (m *defaultPerfCaseModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfCaseIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseIdPrefix, id)
	managerPerfCaseProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseProjectIdCaseIdPrefix, data.ProjectId, data.CaseId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfCaseIdKey, managerPerfCaseProjectIdCaseIdKey)
	return err
}

func (m *defaultPerfCaseModel) FindOne(ctx context.Context, id int64) (*PerfCase, error) {
	managerPerfCaseIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseIdPrefix, id)
	var resp PerfCase
	err := m.QueryRowCtx(ctx, &resp, managerPerfCaseIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfCaseRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfCaseModel) FindOneByProjectIdCaseId(ctx context.Context, projectId string, caseId string) (*PerfCase, error) {
	managerPerfCaseProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseProjectIdCaseIdPrefix, projectId, caseId)
	var resp PerfCase
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfCaseProjectIdCaseIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `case_id` = ? and `deleted` = ? limit 1", perfCaseRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, caseId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfCaseModel) Insert(ctx context.Context, session sqlx.Session, data *PerfCase) (sql.Result, error) {
	managerPerfCaseIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseIdPrefix, data.Id)
	managerPerfCaseProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseProjectIdCaseIdPrefix, data.ProjectId, data.CaseId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfCaseRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.Name, data.Description, data.Extension, data.Hash, data.Size, data.Path, data.TargetRps, data.InitialRps, data.StepHeight, data.StepDuration, data.PerfDataId, data.NumberOfVu, data.NumberOfLg, data.RequestsOfCpu, data.RequestsOfMemory, data.LimitsOfCpu, data.LimitsOfMemory, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.Name, data.Description, data.Extension, data.Hash, data.Size, data.Path, data.TargetRps, data.InitialRps, data.StepHeight, data.StepDuration, data.PerfDataId, data.NumberOfVu, data.NumberOfLg, data.RequestsOfCpu, data.RequestsOfMemory, data.LimitsOfCpu, data.LimitsOfMemory, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfCaseIdKey, managerPerfCaseProjectIdCaseIdKey)
}

func (m *defaultPerfCaseModel) Update(ctx context.Context, session sqlx.Session, newData *PerfCase) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfCaseIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseIdPrefix, data.Id)
	managerPerfCaseProjectIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfCaseProjectIdCaseIdPrefix, data.ProjectId, data.CaseId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfCaseRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.Name, newData.Description, newData.Extension, newData.Hash, newData.Size, newData.Path, newData.TargetRps, newData.InitialRps, newData.StepHeight, newData.StepDuration, newData.PerfDataId, newData.NumberOfVu, newData.NumberOfLg, newData.RequestsOfCpu, newData.RequestsOfMemory, newData.LimitsOfCpu, newData.LimitsOfMemory, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.Name, newData.Description, newData.Extension, newData.Hash, newData.Size, newData.Path, newData.TargetRps, newData.InitialRps, newData.StepHeight, newData.StepDuration, newData.PerfDataId, newData.NumberOfVu, newData.NumberOfLg, newData.RequestsOfCpu, newData.RequestsOfMemory, newData.LimitsOfCpu, newData.LimitsOfMemory, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfCaseIdKey, managerPerfCaseProjectIdCaseIdKey)
}

func (m *defaultPerfCaseModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfCaseIdPrefix, primary)
}

func (m *defaultPerfCaseModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfCaseRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfCaseModel) tableName() string {
	return m.table
}
