// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfPlanReferenceRelationshipTableName           = "`perf_plan_reference_relationship`"
	perfPlanReferenceRelationshipFieldNames          = builder.RawFieldNames(&PerfPlanReferenceRelationship{})
	perfPlanReferenceRelationshipRows                = strings.Join(perfPlanReferenceRelationshipFieldNames, ",")
	perfPlanReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(perfPlanReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfPlanReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(perfPlanReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfPlanReferenceRelationshipIdPrefix = "cache:manager:perfPlanReferenceRelationship:id:"
)

type (
	perfPlanReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfPlanReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfPlanReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfPlanReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfPlanReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	PerfPlanReferenceRelationship struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（压测用例）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（压测用例ID）
		PlanId        string         `db:"plan_id"`        // 计划ID
		State         int64          `db:"state"`          // 引用状态（已使用、未使用）
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newPerfPlanReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfPlanReferenceRelationshipModel {
	return &defaultPerfPlanReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_plan_reference_relationship`",
	}
}

func (m *defaultPerfPlanReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfPlanReferenceRelationshipIdKey)
	return err
}

func (m *defaultPerfPlanReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfPlanReferenceRelationshipIdKey)
	return err
}

func (m *defaultPerfPlanReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*PerfPlanReferenceRelationship, error) {
	managerPerfPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanReferenceRelationshipIdPrefix, id)
	var resp PerfPlanReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerPerfPlanReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfPlanReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *PerfPlanReferenceRelationship) (sql.Result, error) {
	managerPerfPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfPlanReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfPlanReferenceRelationshipIdKey)
}

func (m *defaultPerfPlanReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *PerfPlanReferenceRelationship) (sql.Result, error) {

	managerPerfPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfPlanReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerPerfPlanReferenceRelationshipIdKey)
}

func (m *defaultPerfPlanReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfPlanReferenceRelationshipIdPrefix, primary)
}

func (m *defaultPerfPlanReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfPlanReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfPlanReferenceRelationshipModel) tableName() string {
	return m.table
}
