// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	apiPlanReferenceRelationshipTableName           = "`api_plan_reference_relationship`"
	apiPlanReferenceRelationshipFieldNames          = builder.RawFieldNames(&ApiPlanReferenceRelationship{})
	apiPlanReferenceRelationshipRows                = strings.Join(apiPlanReferenceRelationshipFieldNames, ",")
	apiPlanReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(apiPlanReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	apiPlanReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(apiPlanReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApiPlanReferenceRelationshipIdPrefix = "cache:manager:apiPlanReferenceRelationship:id:"
)

type (
	apiPlanReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApiPlanReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApiPlanReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *ApiPlanReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApiPlanReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	ApiPlanReferenceRelationship struct {
		Id                int64          `db:"id"`
		ProjectId         string         `db:"project_id"`          // 项目ID
		ReferenceParentId sql.NullString `db:"reference_parent_id"` // 引用父ID（API集合ID、接口ID）
		ReferenceType     string         `db:"reference_type"`      // 引用类型（API用例、API集合、接口文档、接口用例）
		ReferenceId       string         `db:"reference_id"`        // 引用ID（API用例ID、API集合ID、接口ID、接口用例ID）
		PlanId            string         `db:"plan_id"`             // 计划ID
		State             int64          `db:"state"`               // 引用状态（已使用、未使用）
		Deleted           int64          `db:"deleted"`             // 逻辑删除标识（未删除、已删除）
		CreatedBy         string         `db:"created_by"`          // 创建者的用户ID
		UpdatedBy         string         `db:"updated_by"`          // 最近一次更新者的用户ID
		DeletedBy         sql.NullString `db:"deleted_by"`          // 删除者的用户ID
		CreatedAt         time.Time      `db:"created_at"`          // 创建时间
		UpdatedAt         time.Time      `db:"updated_at"`          // 更新时间
		DeletedAt         sql.NullTime   `db:"deleted_at"`          // 删除时间
	}
)

func newApiPlanReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApiPlanReferenceRelationshipModel {
	return &defaultApiPlanReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`api_plan_reference_relationship`",
	}
}

func (m *defaultApiPlanReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerApiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApiPlanReferenceRelationshipIdKey)
	return err
}

func (m *defaultApiPlanReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerApiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApiPlanReferenceRelationshipIdKey)
	return err
}

func (m *defaultApiPlanReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*ApiPlanReferenceRelationship, error) {
	managerApiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, id)
	var resp ApiPlanReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerApiPlanReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", apiPlanReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiPlanReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *ApiPlanReferenceRelationship) (sql.Result, error) {
	managerApiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, apiPlanReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceParentId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceParentId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApiPlanReferenceRelationshipIdKey)
}

func (m *defaultApiPlanReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *ApiPlanReferenceRelationship) (sql.Result, error) {

	managerApiPlanReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, apiPlanReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceParentId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceParentId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerApiPlanReferenceRelationshipIdKey)
}

func (m *defaultApiPlanReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, primary)
}

func (m *defaultApiPlanReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", apiPlanReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApiPlanReferenceRelationshipModel) tableName() string {
	return m.table
}
