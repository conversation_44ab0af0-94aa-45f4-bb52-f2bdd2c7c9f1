// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceMetricsReferenceTableName           = "`interface_metrics_reference`"
	interfaceMetricsReferenceFieldNames          = builder.RawFieldNames(&InterfaceMetricsReference{})
	interfaceMetricsReferenceRows                = strings.Join(interfaceMetricsReferenceFieldNames, ",")
	interfaceMetricsReferenceRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceMetricsReferenceFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceMetricsReferenceRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceMetricsReferenceFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceMetricsReferenceIdPrefix                      = "cache:manager:interfaceMetricsReference:id:"
	cacheManagerInterfaceMetricsReferenceProjectIdProtocolMethodPrefix = "cache:manager:interfaceMetricsReference:projectId:protocol:method:"
)

type (
	interfaceMetricsReferenceModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceMetricsReference) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceMetricsReference, error)
		FindOneByProjectIdProtocolMethod(ctx context.Context, projectId string, protocol string, method string) (*InterfaceMetricsReference, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceMetricsReference) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceMetricsReferenceModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceMetricsReference struct {
		Id           int64          `db:"id"`            // 自增ID
		ProjectId    string         `db:"project_id"`    // 项目ID
		Protocol     string         `db:"protocol"`      // 协议
		Method       string         `db:"method"`        // 接口名称
		ReferenceQps sql.NullInt64  `db:"reference_qps"` // 用例数量
		Deleted      int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newInterfaceMetricsReferenceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceMetricsReferenceModel {
	return &defaultInterfaceMetricsReferenceModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_metrics_reference`",
	}
}

func (m *defaultInterfaceMetricsReferenceModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceMetricsReferenceIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, id)
	managerInterfaceMetricsReferenceProjectIdProtocolMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceMetricsReferenceProjectIdProtocolMethodPrefix, data.ProjectId, data.Protocol, data.Method)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceMetricsReferenceIdKey, managerInterfaceMetricsReferenceProjectIdProtocolMethodKey)
	return err
}

func (m *defaultInterfaceMetricsReferenceModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceMetricsReferenceIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, id)
	managerInterfaceMetricsReferenceProjectIdProtocolMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceMetricsReferenceProjectIdProtocolMethodPrefix, data.ProjectId, data.Protocol, data.Method)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceMetricsReferenceIdKey, managerInterfaceMetricsReferenceProjectIdProtocolMethodKey)
	return err
}

func (m *defaultInterfaceMetricsReferenceModel) FindOne(ctx context.Context, id int64) (*InterfaceMetricsReference, error) {
	managerInterfaceMetricsReferenceIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, id)
	var resp InterfaceMetricsReference
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceMetricsReferenceIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceMetricsReferenceRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceMetricsReferenceModel) FindOneByProjectIdProtocolMethod(ctx context.Context, projectId string, protocol string, method string) (*InterfaceMetricsReference, error) {
	managerInterfaceMetricsReferenceProjectIdProtocolMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceMetricsReferenceProjectIdProtocolMethodPrefix, projectId, protocol, method)
	var resp InterfaceMetricsReference
	err := m.QueryRowIndexCtx(ctx, &resp, managerInterfaceMetricsReferenceProjectIdProtocolMethodKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `protocol` = ? and `method` = ? and `deleted` = ? limit 1", interfaceMetricsReferenceRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, protocol, method, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceMetricsReferenceModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceMetricsReference) (sql.Result, error) {
	managerInterfaceMetricsReferenceIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, data.Id)
	managerInterfaceMetricsReferenceProjectIdProtocolMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceMetricsReferenceProjectIdProtocolMethodPrefix, data.ProjectId, data.Protocol, data.Method)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceMetricsReferenceRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Protocol, data.Method, data.ReferenceQps, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Protocol, data.Method, data.ReferenceQps, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceMetricsReferenceIdKey, managerInterfaceMetricsReferenceProjectIdProtocolMethodKey)
}

func (m *defaultInterfaceMetricsReferenceModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceMetricsReference) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceMetricsReferenceIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, data.Id)
	managerInterfaceMetricsReferenceProjectIdProtocolMethodKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceMetricsReferenceProjectIdProtocolMethodPrefix, data.ProjectId, data.Protocol, data.Method)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceMetricsReferenceRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Protocol, newData.Method, newData.ReferenceQps, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Protocol, newData.Method, newData.ReferenceQps, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerInterfaceMetricsReferenceIdKey, managerInterfaceMetricsReferenceProjectIdProtocolMethodKey)
}

func (m *defaultInterfaceMetricsReferenceModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, primary)
}

func (m *defaultInterfaceMetricsReferenceModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceMetricsReferenceRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceMetricsReferenceModel) tableName() string {
	return m.table
}
