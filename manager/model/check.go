package model

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

func CheckProjectByProjectId(ctx context.Context, m ProjectModel, projectId string) (*Project, error) {
	r, err := m.FindOneByProjectId(ctx, projectId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()), "failed to find project with project_id[%s], error: %+v",
				projectId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf("project with project_id[%s] doesn't exist", projectId),
				),
			)
		}
	}

	return r, nil
}

func CheckCategoryByCategoryId(ctx context.Context, m CategoryModel, projectId, tp, categoryId string) (
	*Category, error,
) {
	r, err := m.FindOneByProjectIdTypeCategoryId(ctx, projectId, tp, categoryId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find category with project_id[%s], type[%s] and category_id[%s], error: %+v", projectId, tp,
				categoryId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"category with project_id[%s], type[%s] and category_id[%s] doesn't exist", projectId, tp,
						categoryId,
					),
				),
			)
		}
	}

	return r, nil
}

func CheckTagByTagId(ctx context.Context, m TagModel, projectId, tagId string) (*Tag, error) {
	r, err := m.FindOneByProjectIdTagId(ctx, projectId, tagId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find tag with project_id[%s] and tag_id[%s], error: %+v", projectId, tagId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists,
					fmt.Sprintf("tag with project_id[%s] and tag_id[%s] doesn't exist", projectId, tagId),
				),
			)
		}
	}
	return r, nil
}

func CheckFunctionByNameAndType(ctx context.Context, m FunctionModel, projectId, name, tp string) (*Function, error) {
	r, err := m.FindLatestOneNoCache(ctx, projectId, name, tp)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find data processing function with project_id[%s], name[%s] and type[%s], error: %+v",
				projectId, name, tp, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"data processing function with project_id[%s], name[%s] and type[%s] doesn't exist", projectId,
						name, tp,
					),
				),
			)
		}
	}

	return r, nil
}

func CheckGeneralConfigByConfigId(
	ctx context.Context, m GeneralConfigurationModel, projectId, configId string,
) (*GeneralConfiguration, error) {
	r, err := m.FindOneByProjectIdConfigId(ctx, projectId, configId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find general configuration with project_id[%s] and config_id[%s], error: %+v", projectId,
				configId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"general configuration with project_id[%s] and config_id[%s] doesn't exist", projectId,
						configId,
					),
				),
			)
		}
	}

	return r, nil
}

func CheckAccountConfigByConfigId(
	ctx context.Context, m AccountConfigurationModel, projectId, configId string,
) (*AccountConfiguration, error) {
	r, err := m.FindOneByProjectIdConfigId(ctx, projectId, configId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find account configuration with project_id[%s] and config_id[%s], error: %+v", projectId,
				configId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"account configuration with project_id[%s] and config_id[%s] doesn't exist", projectId,
						configId,
					),
				),
			)
		}
	}

	return r, nil
}

func CheckInterfaceDocumentByDocumentId(
	ctx context.Context, m InterfaceDocumentModel, projectId, documentId string,
) (*InterfaceDocument, error) {
	r, err := m.FindOneByProjectIdDocumentId(ctx, projectId, documentId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find interface document with project_id[%s] and document_id[%s], error: %+v", projectId,
				documentId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"interface document with project_id[%s] and document_id[%s] doesn't exist", projectId,
						documentId,
					),
				),
			)
		}
	}

	return r, nil
}

func CheckInterfaceSchemaBySchemaId(
	ctx context.Context, m InterfaceSchemaModel, projectId, schemaId string,
) (*InterfaceSchema, error) {
	r, err := m.FindOneByProjectIdSchemaId(ctx, projectId, schemaId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find interface schema with project_id[%s] and schema_id[%s], error: %+v", projectId,
				schemaId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"interface schema with project_id[%s] and schema_id[%s] doesn't exist", projectId, schemaId,
					),
				),
			)
		}
	}

	return r, nil
}

func CheckInterfaceConfigByConfigId(
	ctx context.Context, m InterfaceConfigurationModel, projectId, documentId, configId string,
) (*InterfaceConfiguration, error) {
	r, err := m.FindOneByProjectIdDocumentIdConfigId(ctx, projectId, documentId, configId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find interface configuration with project_id[%s], document_id[%s] and config_id[%s], error: %+v",
				projectId, documentId, configId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"interface configuration with project_id[%s], document_id[%s] and config_id[%s] doesn't exist",
						projectId, documentId, configId,
					),
				),
			)
		}
	}

	return r, nil
}

func CheckInterfaceCaseByCaseId(
	ctx context.Context, m InterfaceCaseModel, projectId, documentId, caseId, version string,
) (*InterfaceCase, error) {
	var msg string

	if version == "" {
		msg = fmt.Sprintf(
			"interface case with project_id[%s], document_id[%s] and case_id[%s]", projectId, documentId, caseId,
		)
	} else if documentId != "" {
		msg = fmt.Sprintf(
			"interface case with project_id[%s], document_id[%s], case_id[%s] and version[%s]", projectId, documentId,
			caseId, version,
		)
	} else {
		msg = fmt.Sprintf("interface case with project_id[%s], case_id[%s] and version[%s]", projectId, caseId, version)
	}

	r, err := m.FindOneByProjectIdDocumentIdCaseIdVersion(ctx, projectId, documentId, caseId, version)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find %s, error: %+v", msg, err)
		} else {
			return nil, errors.WithStack(errorx.Err(errorx.NotExists, fmt.Sprintf("%s doesn't exist", msg)))
		}
	}

	return r, nil
}

func CheckComponentGroupByComponentGroupId(
	ctx context.Context, m ComponentGroupModel, projectId, componentGroupId, version string,
) (*ComponentGroup, error) {
	var fn func() (*ComponentGroup, error)
	var msg string

	if version == "" {
		fn = func() (*ComponentGroup, error) {
			return m.FindLatestOneNoCache(ctx, projectId, componentGroupId)
		}
		msg = fmt.Sprintf(
			"latest component group with project_id[%s] and component_group_id[%s]", projectId, componentGroupId,
		)
	} else {
		fn = func() (*ComponentGroup, error) {
			return m.FindOneByProjectIdComponentGroupIdVersion(ctx, projectId, componentGroupId, version)
		}
		msg = fmt.Sprintf(
			"component group with project_id[%s], component_group_id[%s] and version[%s]", projectId, componentGroupId,
			version,
		)
	}

	r, err := fn()
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find %s, error: %+v", msg, err)
		} else {
			return nil, errors.WithStack(errorx.Err(errorx.NotExists, fmt.Sprintf("%s doesn't exist", msg)))
		}
	}

	return r, nil
}

func CheckApiCaseByCaseId(ctx context.Context, m ApiCaseModel, projectId, caseId, version string) (*ApiCase, error) {
	var fn func() (*ApiCase, error)
	var msg string

	if version == "" {
		fn = func() (*ApiCase, error) {
			return m.FindLatestOneNoCache(ctx, projectId, caseId)
		}
		msg = fmt.Sprintf("latest api case with project_id[%s] and case_id[%s]", projectId, caseId)
	} else {
		fn = func() (*ApiCase, error) {
			return m.FindOneByProjectIdCaseIdVersion(ctx, projectId, caseId, version)
		}
		msg = fmt.Sprintf("api case with project_id[%s], case_id[%s] and version[%s]", projectId, caseId, version)
	}

	r, err := fn()
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find %s, error: %+v", msg, err)
		} else {
			return nil, errors.WithStack(errorx.Err(errorx.NotExists, fmt.Sprintf("%s doesn't exist", msg)))
		}
	}

	return r, nil
}

func CheckComponentByComponentId(
	ctx context.Context, m ComponentModel, projectId, parentId, parentType, parentVersion, componentId string,
) (*Component, error) {
	r, err := m.FindOneByProjectIdParentIdParentTypeParentVersionComponentId(
		ctx, projectId, parentId, parentType, parentVersion, componentId,
	)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find component with project_id[%s], parent_id[%s], parent_type[%s], parent_version[%s] and component_id[%s], error: %+v",
				projectId, parentId, parentType, parentVersion, componentId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"component with project_id[%s], parent_id[%s], parent_type[%s], parent_version[%s] and component_id[%s] doesn't exist",
						projectId, parentId, parentType, parentVersion, componentId,
					),
				),
			)
		}
	}

	return r, nil
}

func CheckApiSuiteBySuiteId(ctx context.Context, m ApiSuiteModel, projectId, suiteId string) (*ApiSuite, error) {
	r, err := m.FindOneByProjectIdSuiteId(ctx, projectId, suiteId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api suite with project_id[%s] and suite_id[%s], error: %+v", projectId, suiteId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists,
					fmt.Sprintf("api suite with project_id[%s] and suite_id[%s] doesn't exist", projectId, suiteId),
				),
			)
		}
	}

	return r, nil
}

func CheckApiPlanByPlanId(ctx context.Context, m ApiPlanModel, projectId, planId string) (*ApiPlan, error) {
	r, err := m.FindOneByProjectIdPlanId(ctx, projectId, planId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api plan with project_id[%s] and plan_id[%s], error: %+v", projectId, planId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists,
					fmt.Sprintf("api plan with project_id[%s] and plan_id[%s] doesn't exist", projectId, planId),
				),
			)
		}
	}

	return r, nil
}

func CheckNotifyByNotifyId(ctx context.Context, m NotifyModel, ProjectID, NotifyId string) (*Notify, error) {
	r, err := m.FindOneByProjectIdNotifyId(ctx, ProjectID, NotifyId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()), "failed to find api plan with notify_id[%s], error: %+v",
				NotifyId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf("api plan with notify_id[%s] doesn't exist", NotifyId),
				),
			)
		}
	}

	return r, nil
}

func CheckGitConfigByConfigID(
	ctx context.Context, m GitConfigurationModel, projectID, configID string,
) (*GitConfiguration, error) {
	r, err := m.FindOneByProjectIdConfigId(ctx, projectID, configID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find git configuration with project_id[%s] and config_id[%s], error: %+v",
				projectID, configID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"git configuration with project_id[%s] and config_id[%s] doesn't exist",
					projectID, configID,
				),
			)
		}
	}

	return r, nil
}

func CheckGitProjectTreeByPath(
	ctx context.Context, m GitProjectTreeModel, projectID, configID, path string,
) (*GitProjectTree, error) {
	r, err := m.FindOneByPath(ctx, projectID, configID, path)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find git project tree node with project_id[%s], config_id[%s] and path[%s], error: %+v",
				projectID, configID, path, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"git project tree node with project_id[%s], config_id[%s] and path[%s] doesn't exist",
					projectID, configID, path,
				),
			)
		}
	}

	return r, nil
}

func CheckUiPlanByPlanId(ctx context.Context, m UiPlanModel, projectId, planId string) (*UiPlan, error) {
	r, err := m.FindOneByProjectIdPlanId(ctx, projectId, planId)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find ui plan with project_id[%s] and plan_id[%s], error: %+v",
				projectId, planId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"ui plan with project_id[%s] and plan_id[%s] doesn't exist",
					projectId, planId,
				),
			)
		}
	}

	return r, nil
}

func CheckReviewRecordByReviewID(ctx context.Context, m ReviewRecordModel, projectID, reviewID string) (
	*FullReviewRecord, error,
) {
	r, err := m.FindFullReviewRecordByProjectIDReviewID(ctx, projectID, reviewID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find review record with project_id[%s] and review_id[%s], error: %+v",
				projectID, reviewID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"review record with project_id[%s] and review_id[%s] doesn't exist",
					projectID, reviewID,
				),
			)
		}
	}

	return r, nil
}

func CheckProtobufConfigByConfigID(
	ctx context.Context, m ProtobufConfigurationModel, projectID, configID string,
) (*ProtobufConfiguration, error) {
	r, err := m.FindOneByProjectIdConfigId(ctx, projectID, configID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find protobuf configuration, project_id: %s, config_id: %s, error: %+v",
				projectID, configID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the protobuf configuration doesn't exist, project_id: %s, config_id: %s",
					projectID, configID,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfDataByDataID(ctx context.Context, m PerfDataModel, projectID, dataID string) (*PerfData, error) {
	r, err := m.FindOneByProjectIdDataId(ctx, projectID, dataID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf data, project_id: %s, data_id: %s, error: %+v",
				projectID, dataID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf data doesn't exist, project_id: %s, data_id: %s",
					projectID, dataID,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfCaseByCaseID(ctx context.Context, m PerfCaseModel, projectID, caseID string) (*PerfCase, error) {
	r, err := m.FindOneByProjectIdCaseId(ctx, projectID, caseID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf case, project_id: %s, case_id: %s, error: %+v",
				projectID, caseID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf case doesn't exist, project_id: %s, case_id: %s",
					projectID, caseID,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfCaseV2ByCaseID(ctx context.Context, m PerfCaseV2Model, projectID, caseID string) (*PerfCaseV2, error) {
	r, err := m.FindOneByProjectIdCaseId(ctx, projectID, caseID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf case, project_id: %s, case_id: %s, error: %+v",
				projectID, caseID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf case doesn't exist, project_id: %s, case_id: %s",
					projectID, caseID,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfPlanByPlanID(ctx context.Context, m PerfPlanModel, projectID, planID string) (*PerfPlan, error) {
	r, err := m.FindOneByProjectIdPlanId(ctx, projectID, planID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf plan, project_id: %s, plan_id: %s, error: %+v",
				projectID, planID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf plan doesn't exist, project_id: %s, plan_id: %s",
					projectID, planID,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfPlanV2ByPlanID(ctx context.Context, m PerfPlanV2Model, projectID, planID string) (*PerfPlanV2, error) {
	r, err := m.FindOneByProjectIdPlanId(ctx, projectID, planID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf plan, project_id: %s, plan_id: %s, error: %+v",
				projectID, planID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf plan doesn't exist, project_id: %s, plan_id: %s",
					projectID, planID,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfStopRuleByRuleID(ctx context.Context, m PerfStopRuleModel, projectID, ruleID string) (
	*PerfStopRule, error,
) {
	r, err := m.FindOneByProjectIdRuleId(ctx, projectID, ruleID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf stop rule, project_id: %s, rule_id: %s, error: %+v",
				projectID, ruleID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf stop rule doesn't exist, project_id: %s, rule_id: %s",
					projectID, ruleID,
				),
			)
		}
	}

	return r, nil
}

func CheckInterfaceMetricsReferenceByMethod(
	ctx context.Context, m InterfaceMetricsReferenceModel, projectId, protocol, method string,
) (
	*InterfaceMetricsReference, error,
) {
	r, err := m.FindOneByProjectIdProtocolMethod(ctx, projectId, protocol, method)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find interface metrics reference, project_id: %s, protocol: %s, method: %s, error: %+v",
				projectId, protocol, method, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the interface metrics reference doesn't exist, project_id: %s, protocol: %s, method: %s",
					projectId, protocol, method,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfLarkMemberByAccount(ctx context.Context, m PerfLarkMemberModel, projectID, account string) (
	*PerfLarkMember, error,
) {
	r, err := m.FindOneByProjectIdAccount(ctx, projectID, account)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf lark member, project_id: %s, account: %s, error: %+v",
				projectID, account, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf lark member doesn't exist, project_id: %s, account: %s",
					projectID, account,
				),
			)
		}
	}

	return r, nil
}

func CheckProjectDeviceByUDID(ctx context.Context, m ProjectDeviceModel, projectID, udid string) (
	*ProjectDevice, error,
) {
	r, err := m.FindOneByProjectIdUdid(ctx, projectID, udid)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find project device, project_id: %s, udid: %s, error: %+v",
				projectID, udid, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the project device doesn't exist, project_id: %s, udid: %s",
					projectID, udid,
				),
			)
		}
	}

	return r, nil
}

func CheckStabilityPlanByPlanId(ctx context.Context, m StabilityPlanModel, projectID, planID string) (*StabilityPlan, error) {
	r, err := m.FindOneByProjectIdPlanId(ctx, projectID, planID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find stability plan, project_id: %s, plan_id: %s, error: %+v",
				projectID, planID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the stability plan doesn't exist, project_id: %s, plan_id: %s",
					projectID, planID,
				),
			)
		}
	}

	return r, nil
}
