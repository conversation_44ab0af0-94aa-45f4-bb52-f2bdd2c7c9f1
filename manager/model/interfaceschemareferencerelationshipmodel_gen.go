// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceSchemaReferenceRelationshipTableName           = "`interface_schema_reference_relationship`"
	interfaceSchemaReferenceRelationshipFieldNames          = builder.RawFieldNames(&InterfaceSchemaReferenceRelationship{})
	interfaceSchemaReferenceRelationshipRows                = strings.Join(interfaceSchemaReferenceRelationshipFieldNames, ",")
	interfaceSchemaReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceSchemaReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceSchemaReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceSchemaReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceSchemaReferenceRelationshipIdPrefix = "cache:manager:interfaceSchemaReferenceRelationship:id:"
)

type (
	interfaceSchemaReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceSchemaReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceSchemaReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceSchemaReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceSchemaReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceSchemaReferenceRelationship struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（接口文档、数据模型）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（文档ID、模型ID）
		SchemaId      string         `db:"schema_id"`      // 接口数据模型ID
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newInterfaceSchemaReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceSchemaReferenceRelationshipModel {
	return &defaultInterfaceSchemaReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_schema_reference_relationship`",
	}
}

func (m *defaultInterfaceSchemaReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerInterfaceSchemaReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceSchemaReferenceRelationshipIdKey)
	return err
}

func (m *defaultInterfaceSchemaReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerInterfaceSchemaReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceSchemaReferenceRelationshipIdKey)
	return err
}

func (m *defaultInterfaceSchemaReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*InterfaceSchemaReferenceRelationship, error) {
	managerInterfaceSchemaReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaReferenceRelationshipIdPrefix, id)
	var resp InterfaceSchemaReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceSchemaReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceSchemaReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceSchemaReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceSchemaReferenceRelationship) (sql.Result, error) {
	managerInterfaceSchemaReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceSchemaReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.SchemaId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.SchemaId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceSchemaReferenceRelationshipIdKey)
}

func (m *defaultInterfaceSchemaReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *InterfaceSchemaReferenceRelationship) (sql.Result, error) {

	managerInterfaceSchemaReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceSchemaReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.SchemaId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.SchemaId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerInterfaceSchemaReferenceRelationshipIdKey)
}

func (m *defaultInterfaceSchemaReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaReferenceRelationshipIdPrefix, primary)
}

func (m *defaultInterfaceSchemaReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceSchemaReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceSchemaReferenceRelationshipModel) tableName() string {
	return m.table
}
