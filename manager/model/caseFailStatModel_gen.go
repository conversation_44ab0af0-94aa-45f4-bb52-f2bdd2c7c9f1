// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	caseFailStatTableName           = "`case_fail_stat`"
	caseFailStatFieldNames          = builder.RawFieldNames(&CaseFailStat{})
	caseFailStatRows                = strings.Join(caseFailStatFieldNames, ",")
	caseFailStatRowsExpectAutoSet   = strings.Join(stringx.Remove(caseFailStatFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	caseFailStatRowsWithPlaceHolder = strings.Join(stringx.Remove(caseFailStatFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerCaseFailStatIdPrefix = "cache:manager:caseFailStat:id:"
)

type (
	caseFailStatModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *CaseFailStat) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*CaseFailStat, error)
		Update(ctx context.Context, session sqlx.Session, data *CaseFailStat) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultCaseFailStatModel struct {
		sqlc.CachedConn
		table string
	}

	CaseFailStat struct {
		Id        int64          `db:"id"`         // 自增ID
		ProjectId string         `db:"project_id"` // 项目ID
		CaseId    string         `db:"case_id"`    // 用例ID
		BranchId  string         `db:"branch_id"`  // 分支ID
		CaseType  string         `db:"case_type"`  // 用例类型[API_CASE，INTERFACE_CASE]
		FailCount int64          `db:"fail_count"` // 失败次数
		Version   int64          `db:"version"`    // version
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newCaseFailStatModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultCaseFailStatModel {
	return &defaultCaseFailStatModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`case_fail_stat`",
	}
}

func (m *defaultCaseFailStatModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerCaseFailStatIdKey := fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerCaseFailStatIdKey)
	return err
}

func (m *defaultCaseFailStatModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerCaseFailStatIdKey := fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerCaseFailStatIdKey)
	return err
}

func (m *defaultCaseFailStatModel) FindOne(ctx context.Context, id int64) (*CaseFailStat, error) {
	managerCaseFailStatIdKey := fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, id)
	var resp CaseFailStat
	err := m.QueryRowCtx(ctx, &resp, managerCaseFailStatIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", caseFailStatRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCaseFailStatModel) Insert(ctx context.Context, session sqlx.Session, data *CaseFailStat) (sql.Result, error) {
	managerCaseFailStatIdKey := fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, caseFailStatRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.BranchId, data.CaseType, data.FailCount, data.Version, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.BranchId, data.CaseType, data.FailCount, data.Version, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerCaseFailStatIdKey)
}

func (m *defaultCaseFailStatModel) Update(ctx context.Context, session sqlx.Session, data *CaseFailStat) (sql.Result, error) {

	managerCaseFailStatIdKey := fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, caseFailStatRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.BranchId, data.CaseType, data.FailCount, data.Version, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.BranchId, data.CaseType, data.FailCount, data.Version, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerCaseFailStatIdKey)
}

func (m *defaultCaseFailStatModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, primary)
}

func (m *defaultCaseFailStatModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", caseFailStatRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultCaseFailStatModel) tableName() string {
	return m.table
}
