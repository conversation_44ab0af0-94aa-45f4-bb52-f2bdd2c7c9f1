// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiPlanTableName           = "`ui_plan`"
	uiPlanFieldNames          = builder.RawFieldNames(&UiPlan{})
	uiPlanRows                = strings.Join(uiPlanFieldNames, ",")
	uiPlanRowsExpectAutoSet   = strings.Join(stringx.Remove(uiPlanFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiPlanRowsWithPlaceHolder = strings.Join(stringx.Remove(uiPlanFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerUiPlanIdPrefix              = "cache:manager:uiPlan:id:"
	cacheManagerUiPlanProjectIdPlanIdPrefix = "cache:manager:uiPlan:projectId:planId:"
)

type (
	uiPlanModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiPlan) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiPlan, error)
		FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*UiPlan, error)
		Update(ctx context.Context, session sqlx.Session, data *UiPlan) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiPlanModel struct {
		sqlc.CachedConn
		table string
	}

	UiPlan struct {
		Id                   int64          `db:"id"`                    // 自增ID
		ProjectId            string         `db:"project_id"`            // 项目ID
		CategoryId           string         `db:"category_id"`           // 所属分类ID
		PlanId               string         `db:"plan_id"`               // 计划ID
		Name                 string         `db:"name"`                  // 计划名称
		Description          sql.NullString `db:"description"`           // 计划描述
		Type                 string         `db:"type"`                  // 计划类型（手动、定时、接口）
		CronExpression       sql.NullString `db:"cron_expression"`       // 定时触发计划的Cron表达式
		PriorityType         int64          `db:"priority_type"`         // 优先级（Default、Middle、High、Ultra、Low)
		GitConfigId          string         `db:"git_config_id"`         // Git配置ID
		ExecutionMode        int64          `db:"execution_mode"`        // 用例执行方式（并行、串行）
		DeviceType           int64          `db:"device_type"`           // 设备类型（真机、云手机）
		PlatformType         int64          `db:"platform_type"`         // 平台类型（Android、IOS）
		Devices              sql.NullString `db:"devices"`               // 指定的设备
		Together             int64          `db:"together"`              // 所选设备是否一起执行计划下的用例
		PackageName          string         `db:"package_name"`          // 用于启动APP
		AppName              sql.NullString `db:"app_name"`              // 应用名称
		CallbackUrl          sql.NullString `db:"callback_url"`          // 回调地址
		AppDownloadLink      string         `db:"app_download_link"`     // APP下载地址
		AppVersion           sql.NullString `db:"app_version"`           // APP版本
		TestLanguage         int64          `db:"test_language"`         // 测试语言 0：Python 1：Golang
		TestLanguageVersion  string         `db:"test_language_version"` // 测试语言版本
		TestFramework        int64          `db:"test_framework"`        // 测试框架 1：pytest
		TestArgs             sql.NullString `db:"test_args"`             // 附加参数
		ExecutionEnvironment sql.NullString `db:"execution_environment"` // 执行环境
		FailRetry            int64          `db:"fail_retry"`            // 失败重试（0次、1次、2次）
		State                int64          `db:"state"`                 // 计划状态（1生效、2失效）
		Deleted              int64          `db:"deleted"`               // 逻辑删除标识（未删除、已删除）
		MaintainedBy         sql.NullString `db:"maintained_by"`         // 维护者的用户ID
		CreatedBy            string         `db:"created_by"`            // 创建者的用户ID
		UpdatedBy            string         `db:"updated_by"`            // 最近一次更新者的用户ID
		DeletedBy            sql.NullString `db:"deleted_by"`            // 删除者的用户ID
		CreatedAt            time.Time      `db:"created_at"`            // 创建时间
		UpdatedAt            time.Time      `db:"updated_at"`            // 更新时间
		DeletedAt            sql.NullTime   `db:"deleted_at"`            // 删除时间
	}
)

func newUiPlanModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUiPlanModel {
	return &defaultUiPlanModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`ui_plan`",
	}
}

func (m *defaultUiPlanModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerUiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, id)
	managerUiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerUiPlanIdKey, managerUiPlanProjectIdPlanIdKey)
	return err
}

func (m *defaultUiPlanModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerUiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, id)
	managerUiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerUiPlanIdKey, managerUiPlanProjectIdPlanIdKey)
	return err
}

func (m *defaultUiPlanModel) FindOne(ctx context.Context, id int64) (*UiPlan, error) {
	managerUiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, id)
	var resp UiPlan
	err := m.QueryRowCtx(ctx, &resp, managerUiPlanIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiPlanRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiPlanModel) FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*UiPlan, error) {
	managerUiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, projectId, planId)
	var resp UiPlan
	err := m.QueryRowIndexCtx(ctx, &resp, managerUiPlanProjectIdPlanIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `plan_id` = ? and `deleted` = ? limit 1", uiPlanRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, planId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiPlanModel) Insert(ctx context.Context, session sqlx.Session, data *UiPlan) (sql.Result, error) {
	managerUiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, data.Id)
	managerUiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiPlanRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Type, data.CronExpression, data.PriorityType, data.GitConfigId, data.ExecutionMode, data.DeviceType, data.PlatformType, data.Devices, data.Together, data.PackageName, data.AppName, data.CallbackUrl, data.AppDownloadLink, data.AppVersion, data.TestLanguage, data.TestLanguageVersion, data.TestFramework, data.TestArgs, data.ExecutionEnvironment, data.FailRetry, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Type, data.CronExpression, data.PriorityType, data.GitConfigId, data.ExecutionMode, data.DeviceType, data.PlatformType, data.Devices, data.Together, data.PackageName, data.AppName, data.CallbackUrl, data.AppDownloadLink, data.AppVersion, data.TestLanguage, data.TestLanguageVersion, data.TestFramework, data.TestArgs, data.ExecutionEnvironment, data.FailRetry, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerUiPlanIdKey, managerUiPlanProjectIdPlanIdKey)
}

func (m *defaultUiPlanModel) Update(ctx context.Context, session sqlx.Session, newData *UiPlan) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerUiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, data.Id)
	managerUiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiPlanRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.PlanId, newData.Name, newData.Description, newData.Type, newData.CronExpression, newData.PriorityType, newData.GitConfigId, newData.ExecutionMode, newData.DeviceType, newData.PlatformType, newData.Devices, newData.Together, newData.PackageName, newData.AppName, newData.CallbackUrl, newData.AppDownloadLink, newData.AppVersion, newData.TestLanguage, newData.TestLanguageVersion, newData.TestFramework, newData.TestArgs, newData.ExecutionEnvironment, newData.FailRetry, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.PlanId, newData.Name, newData.Description, newData.Type, newData.CronExpression, newData.PriorityType, newData.GitConfigId, newData.ExecutionMode, newData.DeviceType, newData.PlatformType, newData.Devices, newData.Together, newData.PackageName, newData.AppName, newData.CallbackUrl, newData.AppDownloadLink, newData.AppVersion, newData.TestLanguage, newData.TestLanguageVersion, newData.TestFramework, newData.TestArgs, newData.ExecutionEnvironment, newData.FailRetry, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerUiPlanIdKey, managerUiPlanProjectIdPlanIdKey)
}

func (m *defaultUiPlanModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, primary)
}

func (m *defaultUiPlanModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", uiPlanRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUiPlanModel) tableName() string {
	return m.table
}
