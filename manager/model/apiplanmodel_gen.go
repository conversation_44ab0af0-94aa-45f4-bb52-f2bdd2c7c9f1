// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	apiPlanTableName           = "`api_plan`"
	apiPlanFieldNames          = builder.RawFieldNames(&ApiPlan{})
	apiPlanRows                = strings.Join(apiPlanFieldNames, ",")
	apiPlanRowsExpectAutoSet   = strings.Join(stringx.Remove(apiPlanFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	apiPlanRowsWithPlaceHolder = strings.Join(stringx.Remove(apiPlanFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApiPlanIdPrefix              = "cache:manager:apiPlan:id:"
	cacheManagerApiPlanProjectIdPlanIdPrefix = "cache:manager:apiPlan:projectId:planId:"
)

type (
	apiPlanModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApiPlan) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApiPlan, error)
		FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*ApiPlan, error)
		Update(ctx context.Context, session sqlx.Session, data *ApiPlan) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApiPlanModel struct {
		sqlc.CachedConn
		table string
	}

	ApiPlan struct {
		Id                 int64          `db:"id"`
		ProjectId          string         `db:"project_id"`           // 项目ID
		CategoryId         string         `db:"category_id"`          // 所属分类ID
		PlanId             string         `db:"plan_id"`              // 计划ID
		Name               string         `db:"name"`                 // 计划名称
		Description        sql.NullString `db:"description"`          // 计划描述
		Priority           int64          `db:"priority"`             // 优先级（NULL、P0、P1、P2、P3...）
		Tags               sql.NullString `db:"tags"`                 // 标签
		State              int64          `db:"state"`                // 计划状态（生效、失效）
		Type               string         `db:"type"`                 // 计划类型（手动、定时、接口）
		Purpose            string         `db:"purpose"`              // 计划用途（普通、精准测试）
		CronExpression     sql.NullString `db:"cron_expression"`      // 定时触发计划的Cron表达式
		GeneralConfigId    sql.NullString `db:"general_config_id"`    // 通用配置ID
		SuiteExecutionMode int64          `db:"suite_execution_mode"` // 集合执行方式（并行、串行）
		CaseExecutionMode  int64          `db:"case_execution_mode"`  // 用例执行方式（并行、串行）
		Deleted            int64          `db:"deleted"`              // 逻辑删除标识（未删除、已删除）
		MaintainedBy       sql.NullString `db:"maintained_by"`        // 维护者的用户ID
		CreatedBy          string         `db:"created_by"`           // 创建者的用户ID
		UpdatedBy          string         `db:"updated_by"`           // 最近一次更新者的用户ID
		DeletedBy          sql.NullString `db:"deleted_by"`           // 删除者的用户ID
		CreatedAt          time.Time      `db:"created_at"`           // 创建时间
		UpdatedAt          time.Time      `db:"updated_at"`           // 更新时间
		DeletedAt          sql.NullTime   `db:"deleted_at"`           // 删除时间
	}
)

func newApiPlanModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApiPlanModel {
	return &defaultApiPlanModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`api_plan`",
	}
}

func (m *defaultApiPlanModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, id)
	managerApiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApiPlanIdKey, managerApiPlanProjectIdPlanIdKey)
	return err
}

func (m *defaultApiPlanModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, id)
	managerApiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApiPlanIdKey, managerApiPlanProjectIdPlanIdKey)
	return err
}

func (m *defaultApiPlanModel) FindOne(ctx context.Context, id int64) (*ApiPlan, error) {
	managerApiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, id)
	var resp ApiPlan
	err := m.QueryRowCtx(ctx, &resp, managerApiPlanIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", apiPlanRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiPlanModel) FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*ApiPlan, error) {
	managerApiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, projectId, planId)
	var resp ApiPlan
	err := m.QueryRowIndexCtx(ctx, &resp, managerApiPlanProjectIdPlanIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `plan_id` = ? and `deleted` = ? limit 1", apiPlanRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, planId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiPlanModel) Insert(ctx context.Context, session sqlx.Session, data *ApiPlan) (sql.Result, error) {
	managerApiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, data.Id)
	managerApiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, apiPlanRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Priority, data.Tags, data.State, data.Type, data.Purpose, data.CronExpression, data.GeneralConfigId, data.SuiteExecutionMode, data.CaseExecutionMode, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Priority, data.Tags, data.State, data.Type, data.Purpose, data.CronExpression, data.GeneralConfigId, data.SuiteExecutionMode, data.CaseExecutionMode, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApiPlanIdKey, managerApiPlanProjectIdPlanIdKey)
}

func (m *defaultApiPlanModel) Update(ctx context.Context, session sqlx.Session, newData *ApiPlan) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerApiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, data.Id)
	managerApiPlanProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, apiPlanRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.PlanId, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.State, newData.Type, newData.Purpose, newData.CronExpression, newData.GeneralConfigId, newData.SuiteExecutionMode, newData.CaseExecutionMode, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.PlanId, newData.Name, newData.Description, newData.Priority, newData.Tags, newData.State, newData.Type, newData.Purpose, newData.CronExpression, newData.GeneralConfigId, newData.SuiteExecutionMode, newData.CaseExecutionMode, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerApiPlanIdKey, managerApiPlanProjectIdPlanIdKey)
}

func (m *defaultApiPlanModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, primary)
}

func (m *defaultApiPlanModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", apiPlanRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApiPlanModel) tableName() string {
	return m.table
}
