// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	accountConfigurationReferenceRelationshipTableName           = "`account_configuration_reference_relationship`"
	accountConfigurationReferenceRelationshipFieldNames          = builder.RawFieldNames(&AccountConfigurationReferenceRelationship{})
	accountConfigurationReferenceRelationshipRows                = strings.Join(accountConfigurationReferenceRelationshipFieldNames, ",")
	accountConfigurationReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(accountConfigurationReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	accountConfigurationReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(accountConfigurationReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerAccountConfigurationReferenceRelationshipIdPrefix = "cache:manager:accountConfigurationReferenceRelationship:id:"
)

type (
	accountConfigurationReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *AccountConfigurationReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*AccountConfigurationReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *AccountConfigurationReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultAccountConfigurationReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	AccountConfigurationReferenceRelationship struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（API计划）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（API计划ID）
		ConfigId      string         `db:"config_id"`      // 配置ID
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newAccountConfigurationReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultAccountConfigurationReferenceRelationshipModel {
	return &defaultAccountConfigurationReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`account_configuration_reference_relationship`",
	}
}

func (m *defaultAccountConfigurationReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerAccountConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerAccountConfigurationReferenceRelationshipIdKey)
	return err
}

func (m *defaultAccountConfigurationReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerAccountConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerAccountConfigurationReferenceRelationshipIdKey)
	return err
}

func (m *defaultAccountConfigurationReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*AccountConfigurationReferenceRelationship, error) {
	managerAccountConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationReferenceRelationshipIdPrefix, id)
	var resp AccountConfigurationReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerAccountConfigurationReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", accountConfigurationReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAccountConfigurationReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *AccountConfigurationReferenceRelationship) (sql.Result, error) {
	managerAccountConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, accountConfigurationReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerAccountConfigurationReferenceRelationshipIdKey)
}

func (m *defaultAccountConfigurationReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *AccountConfigurationReferenceRelationship) (sql.Result, error) {

	managerAccountConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerAccountConfigurationReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, accountConfigurationReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerAccountConfigurationReferenceRelationshipIdKey)
}

func (m *defaultAccountConfigurationReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerAccountConfigurationReferenceRelationshipIdPrefix, primary)
}

func (m *defaultAccountConfigurationReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", accountConfigurationReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultAccountConfigurationReferenceRelationshipModel) tableName() string {
	return m.table
}
