// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	apiCaseElementTableName           = "`api_case_element`"
	apiCaseElementFieldNames          = builder.RawFieldNames(&ApiCaseElement{})
	apiCaseElementRows                = strings.Join(apiCaseElementFieldNames, ",")
	apiCaseElementRowsExpectAutoSet   = strings.Join(stringx.Remove(apiCaseElementFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	apiCaseElementRowsWithPlaceHolder = strings.Join(stringx.Remove(apiCaseElementFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApiCaseElementIdPrefix                              = "cache:manager:apiCaseElement:id:"
	cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix = "cache:manager:apiCaseElement:projectId:caseId:version:elementId:"
)

type (
	apiCaseElementModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApiCaseElement) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApiCaseElement, error)
		FindOneByProjectIdCaseIdVersionElementId(ctx context.Context, projectId string, caseId string, version string, elementId string) (*ApiCaseElement, error)
		Update(ctx context.Context, session sqlx.Session, data *ApiCaseElement) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApiCaseElementModel struct {
		sqlc.CachedConn
		table string
	}

	ApiCaseElement struct {
		Id          int64          `db:"id"`
		ProjectId   string         `db:"project_id"`   // 项目ID
		CaseId      string         `db:"case_id"`      // 用例ID
		Version     string         `db:"version"`      // 用例版本
		ElementId   string         `db:"element_id"`   // 元素ID
		ElementType string         `db:"element_type"` // 元素类型（点、线、框）
		Data        string         `db:"data"`         // 元素数据（不同类型的元素的数据结构不一样）
		Deleted     int64          `db:"deleted"`      // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`   // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`   // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`   // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`   // 删除时间
	}
)

func newApiCaseElementModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApiCaseElementModel {
	return &defaultApiCaseElementModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`api_case_element`",
	}
}

func (m *defaultApiCaseElementModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, id)
	managerApiCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId, data.Version, data.ElementId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApiCaseElementIdKey, managerApiCaseElementProjectIdCaseIdVersionElementIdKey)
	return err
}

func (m *defaultApiCaseElementModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, id)
	managerApiCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId, data.Version, data.ElementId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApiCaseElementIdKey, managerApiCaseElementProjectIdCaseIdVersionElementIdKey)
	return err
}

func (m *defaultApiCaseElementModel) FindOne(ctx context.Context, id int64) (*ApiCaseElement, error) {
	managerApiCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, id)
	var resp ApiCaseElement
	err := m.QueryRowCtx(ctx, &resp, managerApiCaseElementIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", apiCaseElementRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiCaseElementModel) FindOneByProjectIdCaseIdVersionElementId(ctx context.Context, projectId string, caseId string, version string, elementId string) (*ApiCaseElement, error) {
	managerApiCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix, projectId, caseId, version, elementId)
	var resp ApiCaseElement
	err := m.QueryRowIndexCtx(ctx, &resp, managerApiCaseElementProjectIdCaseIdVersionElementIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `case_id` = ? and `version` = ? and `element_id` = ? and `deleted` = ? limit 1", apiCaseElementRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, caseId, version, elementId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiCaseElementModel) Insert(ctx context.Context, session sqlx.Session, data *ApiCaseElement) (sql.Result, error) {
	managerApiCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, data.Id)
	managerApiCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId, data.Version, data.ElementId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, apiCaseElementRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApiCaseElementIdKey, managerApiCaseElementProjectIdCaseIdVersionElementIdKey)
}

func (m *defaultApiCaseElementModel) Update(ctx context.Context, session sqlx.Session, newData *ApiCaseElement) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerApiCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, data.Id)
	managerApiCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId, data.Version, data.ElementId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, apiCaseElementRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.Version, newData.ElementId, newData.ElementType, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.Version, newData.ElementId, newData.ElementType, newData.Data, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerApiCaseElementIdKey, managerApiCaseElementProjectIdCaseIdVersionElementIdKey)
}

func (m *defaultApiCaseElementModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, primary)
}

func (m *defaultApiCaseElementModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", apiCaseElementRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApiCaseElementModel) tableName() string {
	return m.table
}
