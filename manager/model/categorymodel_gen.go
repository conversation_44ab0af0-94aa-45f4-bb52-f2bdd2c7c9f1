// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	categoryTableName           = "`category`"
	categoryFieldNames          = builder.RawFieldNames(&Category{})
	categoryRows                = strings.Join(categoryFieldNames, ",")
	categoryRowsExpectAutoSet   = strings.Join(stringx.Remove(categoryFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	categoryRowsWithPlaceHolder = strings.Join(stringx.Remove(categoryFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerCategoryIdPrefix                      = "cache:manager:category:id:"
	cacheManagerCategoryProjectIdTypeCategoryIdPrefix = "cache:manager:category:projectId:type:categoryId:"
)

type (
	categoryModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Category) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Category, error)
		FindOneByProjectIdTypeCategoryId(ctx context.Context, projectId string, tp string, categoryId string) (*Category, error)
		Update(ctx context.Context, session sqlx.Session, data *Category) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultCategoryModel struct {
		sqlc.CachedConn
		table string
	}

	Category struct {
		Id           int64          `db:"id"`
		ProjectId    string         `db:"project_id"`    // 项目ID
		Type         string         `db:"type"`          // 类型，即所属的分类树类型（接口定义、组件组、用例、集合）
		CategoryId   string         `db:"category_id"`   // 分类ID
		CategoryType string         `db:"category_type"` // 分类类型（目录、文件（即叶子节点））
		RootType     sql.NullString `db:"root_type"`     // 根分类类型（组件组：业务组件、前后置组件，接口定义：接口文档、数据模型）
		NodeType     sql.NullString `db:"node_type"`     // 节点类型（组件组：业务单请求组件、业务行为组组件、前置组件、后置组件，接口定义：文档、数据模型）
		NodeId       sql.NullString `db:"node_id"`       // 节点ID
		Name         string         `db:"name"`          // 分类名称
		Description  sql.NullString `db:"description"`   // 分类描述
		Builtin      int64          `db:"builtin"`       // 是否内建分类
		Deleted      int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newCategoryModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultCategoryModel {
	return &defaultCategoryModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`category`",
	}
}

func (m *defaultCategoryModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerCategoryIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryIdPrefix, id)
	managerCategoryProjectIdTypeCategoryIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerCategoryProjectIdTypeCategoryIdPrefix, data.ProjectId, data.Type, data.CategoryId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerCategoryIdKey, managerCategoryProjectIdTypeCategoryIdKey)
	return err
}

func (m *defaultCategoryModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerCategoryIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryIdPrefix, id)
	managerCategoryProjectIdTypeCategoryIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerCategoryProjectIdTypeCategoryIdPrefix, data.ProjectId, data.Type, data.CategoryId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerCategoryIdKey, managerCategoryProjectIdTypeCategoryIdKey)
	return err
}

func (m *defaultCategoryModel) FindOne(ctx context.Context, id int64) (*Category, error) {
	managerCategoryIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryIdPrefix, id)
	var resp Category
	err := m.QueryRowCtx(ctx, &resp, managerCategoryIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", categoryRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCategoryModel) FindOneByProjectIdTypeCategoryId(ctx context.Context, projectId string, tp string, categoryId string) (*Category, error) {
	managerCategoryProjectIdTypeCategoryIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerCategoryProjectIdTypeCategoryIdPrefix, projectId, tp, categoryId)
	var resp Category
	err := m.QueryRowIndexCtx(ctx, &resp, managerCategoryProjectIdTypeCategoryIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `type` = ? and `category_id` = ? and `deleted` = ? limit 1", categoryRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, tp, categoryId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCategoryModel) Insert(ctx context.Context, session sqlx.Session, data *Category) (sql.Result, error) {
	managerCategoryIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryIdPrefix, data.Id)
	managerCategoryProjectIdTypeCategoryIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerCategoryProjectIdTypeCategoryIdPrefix, data.ProjectId, data.Type, data.CategoryId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, categoryRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Type, data.CategoryId, data.CategoryType, data.RootType, data.NodeType, data.NodeId, data.Name, data.Description, data.Builtin, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Type, data.CategoryId, data.CategoryType, data.RootType, data.NodeType, data.NodeId, data.Name, data.Description, data.Builtin, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerCategoryIdKey, managerCategoryProjectIdTypeCategoryIdKey)
}

func (m *defaultCategoryModel) Update(ctx context.Context, session sqlx.Session, newData *Category) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerCategoryIdKey := fmt.Sprintf("%s%v", cacheManagerCategoryIdPrefix, data.Id)
	managerCategoryProjectIdTypeCategoryIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerCategoryProjectIdTypeCategoryIdPrefix, data.ProjectId, data.Type, data.CategoryId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, categoryRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Type, newData.CategoryId, newData.CategoryType, newData.RootType, newData.NodeType, newData.NodeId, newData.Name, newData.Description, newData.Builtin, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Type, newData.CategoryId, newData.CategoryType, newData.RootType, newData.NodeType, newData.NodeId, newData.Name, newData.Description, newData.Builtin, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerCategoryIdKey, managerCategoryProjectIdTypeCategoryIdKey)
}

func (m *defaultCategoryModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerCategoryIdPrefix, primary)
}

func (m *defaultCategoryModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", categoryRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultCategoryModel) tableName() string {
	return m.table
}
