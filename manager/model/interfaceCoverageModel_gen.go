// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceCoverageTableName           = "`interface_coverage`"
	interfaceCoverageFieldNames          = builder.RawFieldNames(&InterfaceCoverage{})
	interfaceCoverageRows                = strings.Join(interfaceCoverageFieldNames, ",")
	interfaceCoverageRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceCoverageFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceCoverageRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceCoverageFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerInterfaceCoverageIdPrefix                     = "cache:manager:interfaceCoverage:id:"
	cacheManagerInterfaceCoverageProjectIdTeamCountedAtPrefix = "cache:manager:interfaceCoverage:projectId:team:countedAt:"
)

type (
	interfaceCoverageModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceCoverage) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceCoverage, error)
		FindOneByProjectIdTeamCountedAt(ctx context.Context, projectId string, team string, countedAt time.Time) (*InterfaceCoverage, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceCoverage) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceCoverageModel struct {
		sqlc.CachedConn
		table string
	}

	InterfaceCoverage struct {
		Id            int64          `db:"id"`              // 自增ID
		ProjectId     string         `db:"project_id"`      // 项目ID
		Team          string         `db:"team"`            // 团队名称
		NumberOfApis  int64          `db:"number_of_apis"`  // 接口数量
		NumberOfCases int64          `db:"number_of_cases"` // 用例数量
		CountedAt     time.Time      `db:"counted_at"`      // 统计日期
		Deleted       int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newInterfaceCoverageModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultInterfaceCoverageModel {
	return &defaultInterfaceCoverageModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`interface_coverage`",
	}
}

func (m *defaultInterfaceCoverageModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceCoverageIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCoverageIdPrefix, id)
	managerInterfaceCoverageProjectIdTeamCountedAtKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceCoverageProjectIdTeamCountedAtPrefix, data.ProjectId, data.Team, data.CountedAt)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerInterfaceCoverageIdKey, managerInterfaceCoverageProjectIdTeamCountedAtKey)
	return err
}

func (m *defaultInterfaceCoverageModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceCoverageIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCoverageIdPrefix, id)
	managerInterfaceCoverageProjectIdTeamCountedAtKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceCoverageProjectIdTeamCountedAtPrefix, data.ProjectId, data.Team, data.CountedAt)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerInterfaceCoverageIdKey, managerInterfaceCoverageProjectIdTeamCountedAtKey)
	return err
}

func (m *defaultInterfaceCoverageModel) FindOne(ctx context.Context, id int64) (*InterfaceCoverage, error) {
	managerInterfaceCoverageIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCoverageIdPrefix, id)
	var resp InterfaceCoverage
	err := m.QueryRowCtx(ctx, &resp, managerInterfaceCoverageIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceCoverageRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceCoverageModel) FindOneByProjectIdTeamCountedAt(ctx context.Context, projectId string, team string, countedAt time.Time) (*InterfaceCoverage, error) {
	managerInterfaceCoverageProjectIdTeamCountedAtKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceCoverageProjectIdTeamCountedAtPrefix, projectId, team, countedAt)
	var resp InterfaceCoverage
	err := m.QueryRowIndexCtx(ctx, &resp, managerInterfaceCoverageProjectIdTeamCountedAtKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `team` = ? and `counted_at` = ? and `deleted` = ? limit 1", interfaceCoverageRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, team, countedAt, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceCoverageModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceCoverage) (sql.Result, error) {
	managerInterfaceCoverageIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCoverageIdPrefix, data.Id)
	managerInterfaceCoverageProjectIdTeamCountedAtKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceCoverageProjectIdTeamCountedAtPrefix, data.ProjectId, data.Team, data.CountedAt)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceCoverageRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Team, data.NumberOfApis, data.NumberOfCases, data.CountedAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Team, data.NumberOfApis, data.NumberOfCases, data.CountedAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerInterfaceCoverageIdKey, managerInterfaceCoverageProjectIdTeamCountedAtKey)
}

func (m *defaultInterfaceCoverageModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceCoverage) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceCoverageIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCoverageIdPrefix, data.Id)
	managerInterfaceCoverageProjectIdTeamCountedAtKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerInterfaceCoverageProjectIdTeamCountedAtPrefix, data.ProjectId, data.Team, data.CountedAt)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceCoverageRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Team, newData.NumberOfApis, newData.NumberOfCases, newData.CountedAt, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Team, newData.NumberOfApis, newData.NumberOfCases, newData.CountedAt, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerInterfaceCoverageIdKey, managerInterfaceCoverageProjectIdTeamCountedAtKey)
}

func (m *defaultInterfaceCoverageModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerInterfaceCoverageIdPrefix, primary)
}

func (m *defaultInterfaceCoverageModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", interfaceCoverageRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultInterfaceCoverageModel) tableName() string {
	return m.table
}
