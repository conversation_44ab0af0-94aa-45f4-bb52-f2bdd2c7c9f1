// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfCaseStepTableName           = "`perf_case_step`"
	perfCaseStepFieldNames          = builder.RawFieldNames(&PerfCaseStep{})
	perfCaseStepRows                = strings.Join(perfCaseStepFieldNames, ",")
	perfCaseStepRowsExpectAutoSet   = strings.Join(stringx.Remove(perfCaseStepFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfCaseStepRowsWithPlaceHolder = strings.Join(stringx.Remove(perfCaseStepFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfCaseStepIdPrefix                    = "cache:manager:perfCaseStep:id:"
	cacheManagerPerfCaseStepProjectIdCaseIdStepIdPrefix = "cache:manager:perfCaseStep:projectId:caseId:stepId:"
)

type (
	perfCaseStepModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfCaseStep) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfCaseStep, error)
		FindOneByProjectIdCaseIdStepId(ctx context.Context, projectId string, caseId string, stepId string) (*PerfCaseStep, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfCaseStep) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfCaseStepModel struct {
		sqlc.CachedConn
		table string
	}

	PerfCaseStep struct {
		Id           int64          `db:"id"`
		ProjectId    string         `db:"project_id"`    // 项目ID
		CaseId       string         `db:"case_id"`       // 压测用例ID
		StepId       string         `db:"step_id"`       // 压测用例步骤ID
		Type         string         `db:"type"`          // 压测用例步骤类型（前置、串行、并行、后置）
		Name         string         `db:"name"`          // 压测用例步骤名称
		TargetRps    int64          `db:"target_rps"`    // 目标的RPS
		InitialRps   int64          `db:"initial_rps"`   // 初始的RPS
		StepHeight   int64          `db:"step_height"`   // 每次改变RPS的量
		StepDuration string         `db:"step_duration"` // 改变后的RPS的持续时间
		Url          string         `db:"url"`           // 请求URL
		Method       string         `db:"method"`        // 请求方法
		Headers      sql.NullString `db:"headers"`       // 请求头
		Body         string         `db:"body"`          // 请求体
		Exports      sql.NullString `db:"exports"`       // 提取参数
		Sleep        string         `db:"sleep"`         // 请求后休眠时间
		State        int64          `db:"state"`         // 计划状态（生效、失效）
		Deleted      int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newPerfCaseStepModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfCaseStepModel {
	return &defaultPerfCaseStepModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_case_step`",
	}
}

func (m *defaultPerfCaseStepModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfCaseStepIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepIdPrefix, id)
	managerPerfCaseStepProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepProjectIdCaseIdStepIdPrefix, data.ProjectId, data.CaseId, data.StepId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfCaseStepIdKey, managerPerfCaseStepProjectIdCaseIdStepIdKey)
	return err
}

func (m *defaultPerfCaseStepModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfCaseStepIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepIdPrefix, id)
	managerPerfCaseStepProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepProjectIdCaseIdStepIdPrefix, data.ProjectId, data.CaseId, data.StepId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfCaseStepIdKey, managerPerfCaseStepProjectIdCaseIdStepIdKey)
	return err
}

func (m *defaultPerfCaseStepModel) FindOne(ctx context.Context, id int64) (*PerfCaseStep, error) {
	managerPerfCaseStepIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepIdPrefix, id)
	var resp PerfCaseStep
	err := m.QueryRowCtx(ctx, &resp, managerPerfCaseStepIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfCaseStepRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfCaseStepModel) FindOneByProjectIdCaseIdStepId(ctx context.Context, projectId string, caseId string, stepId string) (*PerfCaseStep, error) {
	managerPerfCaseStepProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepProjectIdCaseIdStepIdPrefix, projectId, caseId, stepId)
	var resp PerfCaseStep
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfCaseStepProjectIdCaseIdStepIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `case_id` = ? and `step_id` = ? and `deleted` = ? limit 1", perfCaseStepRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, caseId, stepId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfCaseStepModel) Insert(ctx context.Context, session sqlx.Session, data *PerfCaseStep) (sql.Result, error) {
	managerPerfCaseStepIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepIdPrefix, data.Id)
	managerPerfCaseStepProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepProjectIdCaseIdStepIdPrefix, data.ProjectId, data.CaseId, data.StepId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfCaseStepRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.StepId, data.Type, data.Name, data.TargetRps, data.InitialRps, data.StepHeight, data.StepDuration, data.Url, data.Method, data.Headers, data.Body, data.Exports, data.Sleep, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CaseId, data.StepId, data.Type, data.Name, data.TargetRps, data.InitialRps, data.StepHeight, data.StepDuration, data.Url, data.Method, data.Headers, data.Body, data.Exports, data.Sleep, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfCaseStepIdKey, managerPerfCaseStepProjectIdCaseIdStepIdKey)
}

func (m *defaultPerfCaseStepModel) Update(ctx context.Context, session sqlx.Session, newData *PerfCaseStep) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfCaseStepIdKey := fmt.Sprintf("%s%v", cacheManagerPerfCaseStepIdPrefix, data.Id)
	managerPerfCaseStepProjectIdCaseIdStepIdKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerPerfCaseStepProjectIdCaseIdStepIdPrefix, data.ProjectId, data.CaseId, data.StepId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfCaseStepRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.StepId, newData.Type, newData.Name, newData.TargetRps, newData.InitialRps, newData.StepHeight, newData.StepDuration, newData.Url, newData.Method, newData.Headers, newData.Body, newData.Exports, newData.Sleep, newData.State, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CaseId, newData.StepId, newData.Type, newData.Name, newData.TargetRps, newData.InitialRps, newData.StepHeight, newData.StepDuration, newData.Url, newData.Method, newData.Headers, newData.Body, newData.Exports, newData.Sleep, newData.State, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfCaseStepIdKey, managerPerfCaseStepProjectIdCaseIdStepIdKey)
}

func (m *defaultPerfCaseStepModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfCaseStepIdPrefix, primary)
}

func (m *defaultPerfCaseStepModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfCaseStepRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfCaseStepModel) tableName() string {
	return m.table
}
