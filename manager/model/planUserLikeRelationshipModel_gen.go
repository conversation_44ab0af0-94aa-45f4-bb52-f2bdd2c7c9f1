// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	planUserLikeRelationshipTableName           = "`plan_user_like_relationship`"
	planUserLikeRelationshipFieldNames          = builder.RawFieldNames(&PlanUserLikeRelationship{})
	planUserLikeRelationshipRows                = strings.Join(planUserLikeRelationshipFieldNames, ",")
	planUserLikeRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(planUserLikeRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	planUserLikeRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(planUserLikeRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPlanUserLikeRelationshipIdPrefix = "cache:manager:planUserLikeRelationship:id:"
)

type (
	planUserLikeRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PlanUserLikeRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PlanUserLikeRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *PlanUserLikeRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPlanUserLikeRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	PlanUserLikeRelationship struct {
		Id        int64          `db:"id"`         // 自增ID
		ProjectId string         `db:"project_id"` // 项目ID
		PlanId    string         `db:"plan_id"`    // 计划ID
		Account   string         `db:"account"`    // 用户id同创建者的用户ID
		PlanType  int64          `db:"plan_type"`  // API:0,UI:1
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newPlanUserLikeRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPlanUserLikeRelationshipModel {
	return &defaultPlanUserLikeRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`plan_user_like_relationship`",
	}
}

func (m *defaultPlanUserLikeRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPlanUserLikeRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPlanUserLikeRelationshipIdKey)
	return err
}

func (m *defaultPlanUserLikeRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPlanUserLikeRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPlanUserLikeRelationshipIdKey)
	return err
}

func (m *defaultPlanUserLikeRelationshipModel) FindOne(ctx context.Context, id int64) (*PlanUserLikeRelationship, error) {
	managerPlanUserLikeRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, id)
	var resp PlanUserLikeRelationship
	err := m.QueryRowCtx(ctx, &resp, managerPlanUserLikeRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", planUserLikeRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPlanUserLikeRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *PlanUserLikeRelationship) (sql.Result, error) {
	managerPlanUserLikeRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, planUserLikeRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.Account, data.PlanType, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.Account, data.PlanType, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPlanUserLikeRelationshipIdKey)
}

func (m *defaultPlanUserLikeRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *PlanUserLikeRelationship) (sql.Result, error) {

	managerPlanUserLikeRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, planUserLikeRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.Account, data.PlanType, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.Account, data.PlanType, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerPlanUserLikeRelationshipIdKey)
}

func (m *defaultPlanUserLikeRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, primary)
}

func (m *defaultPlanUserLikeRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", planUserLikeRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPlanUserLikeRelationshipModel) tableName() string {
	return m.table
}
