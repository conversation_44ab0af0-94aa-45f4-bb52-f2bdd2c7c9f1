// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	notifyTableName           = "`notify`"
	notifyFieldNames          = builder.RawFieldNames(&Notify{})
	notifyRows                = strings.Join(notifyFieldNames, ",")
	notifyRowsExpectAutoSet   = strings.Join(stringx.Remove(notifyFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	notifyRowsWithPlaceHolder = strings.Join(stringx.Remove(notifyFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerNotifyIdPrefix                = "cache:manager:notify:id:"
	cacheManagerNotifyProjectIdNotifyIdPrefix = "cache:manager:notify:projectId:notifyId:"
)

type (
	notifyModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Notify) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Notify, error)
		FindOneByProjectIdNotifyId(ctx context.Context, projectId string, notifyId string) (*Notify, error)
		Update(ctx context.Context, session sqlx.Session, data *Notify) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultNotifyModel struct {
		sqlc.CachedConn
		table string
	}

	Notify struct {
		Id           int64          `db:"id"`
		ProjectId    string         `db:"project_id"`    // 项目ID
		PlanId       string         `db:"plan_id"`       // 计划ID
		NotifyId     string         `db:"notify_id"`     // 通知ID
		NotifyMode   string         `db:"notify_mode"`   // 通知方式(ALWAYS_NOTIFY/ONLY_FALSE_NOTIFY)
		NotifyType   string         `db:"notify_type"`   // 通知类型：邮箱/飞书群
		ReceiverName string         `db:"receiver_name"` // 接受者名称
		Receiver     string         `db:"receiver"`      // 接受者主体
		Deleted      int64          `db:"deleted"`
		CreatedBy    string         `db:"created_by"` // 创建者ID
		UpdatedBy    string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"` // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"` // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newNotifyModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultNotifyModel {
	return &defaultNotifyModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`notify`",
	}
}

func (m *defaultNotifyModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerNotifyIdKey := fmt.Sprintf("%s%v", cacheManagerNotifyIdPrefix, id)
	managerNotifyProjectIdNotifyIdKey := fmt.Sprintf("%s%v:%v", cacheManagerNotifyProjectIdNotifyIdPrefix, data.ProjectId, data.NotifyId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerNotifyIdKey, managerNotifyProjectIdNotifyIdKey)
	return err
}

func (m *defaultNotifyModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerNotifyIdKey := fmt.Sprintf("%s%v", cacheManagerNotifyIdPrefix, id)
	managerNotifyProjectIdNotifyIdKey := fmt.Sprintf("%s%v:%v", cacheManagerNotifyProjectIdNotifyIdPrefix, data.ProjectId, data.NotifyId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerNotifyIdKey, managerNotifyProjectIdNotifyIdKey)
	return err
}

func (m *defaultNotifyModel) FindOne(ctx context.Context, id int64) (*Notify, error) {
	managerNotifyIdKey := fmt.Sprintf("%s%v", cacheManagerNotifyIdPrefix, id)
	var resp Notify
	err := m.QueryRowCtx(ctx, &resp, managerNotifyIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", notifyRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultNotifyModel) FindOneByProjectIdNotifyId(ctx context.Context, projectId string, notifyId string) (*Notify, error) {
	managerNotifyProjectIdNotifyIdKey := fmt.Sprintf("%s%v:%v", cacheManagerNotifyProjectIdNotifyIdPrefix, projectId, notifyId)
	var resp Notify
	err := m.QueryRowIndexCtx(ctx, &resp, managerNotifyProjectIdNotifyIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `notify_id` = ? and `deleted` = ? limit 1", notifyRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, notifyId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultNotifyModel) Insert(ctx context.Context, session sqlx.Session, data *Notify) (sql.Result, error) {
	managerNotifyIdKey := fmt.Sprintf("%s%v", cacheManagerNotifyIdPrefix, data.Id)
	managerNotifyProjectIdNotifyIdKey := fmt.Sprintf("%s%v:%v", cacheManagerNotifyProjectIdNotifyIdPrefix, data.ProjectId, data.NotifyId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, notifyRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.NotifyId, data.NotifyMode, data.NotifyType, data.ReceiverName, data.Receiver, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.NotifyId, data.NotifyMode, data.NotifyType, data.ReceiverName, data.Receiver, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerNotifyIdKey, managerNotifyProjectIdNotifyIdKey)
}

func (m *defaultNotifyModel) Update(ctx context.Context, session sqlx.Session, newData *Notify) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerNotifyIdKey := fmt.Sprintf("%s%v", cacheManagerNotifyIdPrefix, data.Id)
	managerNotifyProjectIdNotifyIdKey := fmt.Sprintf("%s%v:%v", cacheManagerNotifyProjectIdNotifyIdPrefix, data.ProjectId, data.NotifyId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, notifyRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.PlanId, newData.NotifyId, newData.NotifyMode, newData.NotifyType, newData.ReceiverName, newData.Receiver, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.PlanId, newData.NotifyId, newData.NotifyMode, newData.NotifyType, newData.ReceiverName, newData.Receiver, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerNotifyIdKey, managerNotifyProjectIdNotifyIdKey)
}

func (m *defaultNotifyModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerNotifyIdPrefix, primary)
}

func (m *defaultNotifyModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", notifyRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultNotifyModel) tableName() string {
	return m.table
}
