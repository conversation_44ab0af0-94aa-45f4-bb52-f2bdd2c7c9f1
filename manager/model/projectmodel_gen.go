// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	projectTableName           = "`project`"
	projectFieldNames          = builder.RawFieldNames(&Project{})
	projectRows                = strings.Join(projectFieldNames, ",")
	projectRowsExpectAutoSet   = strings.Join(stringx.Remove(projectFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	projectRowsWithPlaceHolder = strings.Join(stringx.Remove(projectFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerProjectIdPrefix        = "cache:manager:project:id:"
	cacheManagerProjectProjectIdPrefix = "cache:manager:project:projectId:"
)

type (
	projectModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Project) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Project, error)
		FindOneByProjectId(ctx context.Context, projectId string) (*Project, error)
		Update(ctx context.Context, session sqlx.Session, data *Project) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultProjectModel struct {
		sqlc.CachedConn
		table string
	}

	Project struct {
		Id                int64          `db:"id"`
		ProjectId         string         `db:"project_id"`          // 项目ID
		Name              string         `db:"name"`                // 组件名称
		Description       sql.NullString `db:"description"`         // 组件描述
		ReviewEnabled     int64          `db:"review_enabled"`      // 是否开启用例审核功能
		CoverageEnabled   int64          `db:"coverage_enabled"`    // 是否开启接口用例覆盖率统计功能
		CoverageLarkChats sql.NullString `db:"coverage_lark_chats"` // 接口用例覆盖率飞书通知群组
		Deleted           int64          `db:"deleted"`             // 逻辑删除标识（未删除、已删除）
		CreatedBy         string         `db:"created_by"`          // 创建者的用户ID
		UpdatedBy         string         `db:"updated_by"`          // 最近一次更新者的用户ID
		DeletedBy         sql.NullString `db:"deleted_by"`          // 删除者的用户ID
		CreatedAt         time.Time      `db:"created_at"`          // 创建时间
		UpdatedAt         time.Time      `db:"updated_at"`          // 更新时间
		DeletedAt         sql.NullTime   `db:"deleted_at"`          // 删除时间
	}
)

func newProjectModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultProjectModel {
	return &defaultProjectModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`project`",
	}
}

func (m *defaultProjectModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectIdPrefix, id)
	managerProjectProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectProjectIdPrefix, data.ProjectId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerProjectIdKey, managerProjectProjectIdKey)
	return err
}

func (m *defaultProjectModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectIdPrefix, id)
	managerProjectProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectProjectIdPrefix, data.ProjectId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerProjectIdKey, managerProjectProjectIdKey)
	return err
}

func (m *defaultProjectModel) FindOne(ctx context.Context, id int64) (*Project, error) {
	managerProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectIdPrefix, id)
	var resp Project
	err := m.QueryRowCtx(ctx, &resp, managerProjectIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", projectRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultProjectModel) FindOneByProjectId(ctx context.Context, projectId string) (*Project, error) {
	managerProjectProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectProjectIdPrefix, projectId)
	var resp Project
	err := m.QueryRowIndexCtx(ctx, &resp, managerProjectProjectIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `deleted` = ? limit 1", projectRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultProjectModel) Insert(ctx context.Context, session sqlx.Session, data *Project) (sql.Result, error) {
	managerProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectIdPrefix, data.Id)
	managerProjectProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectProjectIdPrefix, data.ProjectId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, projectRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Name, data.Description, data.ReviewEnabled, data.CoverageEnabled, data.CoverageLarkChats, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Name, data.Description, data.ReviewEnabled, data.CoverageEnabled, data.CoverageLarkChats, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerProjectIdKey, managerProjectProjectIdKey)
}

func (m *defaultProjectModel) Update(ctx context.Context, session sqlx.Session, newData *Project) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectIdPrefix, data.Id)
	managerProjectProjectIdKey := fmt.Sprintf("%s%v", cacheManagerProjectProjectIdPrefix, data.ProjectId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, projectRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Name, newData.Description, newData.ReviewEnabled, newData.CoverageEnabled, newData.CoverageLarkChats, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Name, newData.Description, newData.ReviewEnabled, newData.CoverageEnabled, newData.CoverageLarkChats, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerProjectIdKey, managerProjectProjectIdKey)
}

func (m *defaultProjectModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerProjectIdPrefix, primary)
}

func (m *defaultProjectModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", projectRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultProjectModel) tableName() string {
	return m.table
}
