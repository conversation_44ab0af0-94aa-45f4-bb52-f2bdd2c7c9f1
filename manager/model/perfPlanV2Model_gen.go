// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfPlanV2TableName           = "`perf_plan_v2`"
	perfPlanV2FieldNames          = builder.RawFieldNames(&PerfPlanV2{})
	perfPlanV2Rows                = strings.Join(perfPlanV2FieldNames, ",")
	perfPlanV2RowsExpectAutoSet   = strings.Join(stringx.Remove(perfPlanV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfPlanV2RowsWithPlaceHolder = strings.Join(stringx.Remove(perfPlanV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfPlanV2IdPrefix              = "cache:manager:perfPlanV2:id:"
	cacheManagerPerfPlanV2ProjectIdPlanIdPrefix = "cache:manager:perfPlanV2:projectId:planId:"
)

type (
	perfPlanV2Model interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfPlanV2) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfPlanV2, error)
		FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*PerfPlanV2, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfPlanV2) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfPlanV2Model struct {
		sqlc.CachedConn
		table string
	}

	PerfPlanV2 struct {
		Id                   int64          `db:"id"`
		ProjectId            string         `db:"project_id"`            // 项目ID
		CategoryId           string         `db:"category_id"`           // 所属分类ID
		PlanId               string         `db:"plan_id"`               // 计划ID
		Name                 string         `db:"name"`                  // 计划名称
		Description          sql.NullString `db:"description"`           // 计划描述
		Type                 string         `db:"type"`                  // 计划类型（手动、定时、接口）
		CronExpression       sql.NullString `db:"cron_expression"`       // 定时触发计划的Cron表达式
		Tags                 sql.NullString `db:"tags"`                  // 标签
		Protocol             string         `db:"protocol"`              // 协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）
		TargetEnv            string         `db:"target_env"`            // 目标环境（生产环境、测试环境）
		ProtobufConfigId     sql.NullString `db:"protobuf_config_id"`    // Deprecated: Protobuf项目配置ID
		GeneralConfigId      sql.NullString `db:"general_config_id"`     // 通用配置ID
		AccountConfigId      sql.NullString `db:"account_config_id"`     // 池账号配置ID
		AuthRateLimits       sql.NullString `db:"auth_rate_limits"`      // 登录的限流配置
		CustomDuration       int64          `db:"custom_duration"`       // 是否自定义压测持续时长
		Duration             int64          `db:"duration"`              // 压测持续时长，单位为秒
		CreateLarkChat       int64          `db:"create_lark_chat"`      // 是否需要自动拉群
		LarkChatId           sql.NullString `db:"lark_chat_id"`          // 通过自动拉群创建的飞书群ID
		AdvancedNotification int64          `db:"advanced_notification"` // 是否提前通知
		State                int64          `db:"state"`                 // 计划状态（生效、失效）
		Deleted              int64          `db:"deleted"`               // 逻辑删除标识（未删除、已删除）
		MaintainedBy         sql.NullString `db:"maintained_by"`         // 维护者的用户ID
		CreatedBy            string         `db:"created_by"`            // 创建者的用户ID
		UpdatedBy            string         `db:"updated_by"`            // 最近一次更新者的用户ID
		DeletedBy            sql.NullString `db:"deleted_by"`            // 删除者的用户ID
		CreatedAt            time.Time      `db:"created_at"`            // 创建时间
		UpdatedAt            time.Time      `db:"updated_at"`            // 更新时间
		DeletedAt            sql.NullTime   `db:"deleted_at"`            // 删除时间
	}
)

func newPerfPlanV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfPlanV2Model {
	return &defaultPerfPlanV2Model{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_plan_v2`",
	}
}

func (m *defaultPerfPlanV2Model) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfPlanV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanV2IdPrefix, id)
	managerPerfPlanV2ProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanV2ProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfPlanV2IdKey, managerPerfPlanV2ProjectIdPlanIdKey)
	return err
}

func (m *defaultPerfPlanV2Model) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPerfPlanV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanV2IdPrefix, id)
	managerPerfPlanV2ProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanV2ProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfPlanV2IdKey, managerPerfPlanV2ProjectIdPlanIdKey)
	return err
}

func (m *defaultPerfPlanV2Model) FindOne(ctx context.Context, id int64) (*PerfPlanV2, error) {
	managerPerfPlanV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanV2IdPrefix, id)
	var resp PerfPlanV2
	err := m.QueryRowCtx(ctx, &resp, managerPerfPlanV2IdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfPlanV2Rows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanV2Model) FindOneByProjectIdPlanId(ctx context.Context, projectId string, planId string) (*PerfPlanV2, error) {
	managerPerfPlanV2ProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanV2ProjectIdPlanIdPrefix, projectId, planId)
	var resp PerfPlanV2
	err := m.QueryRowIndexCtx(ctx, &resp, managerPerfPlanV2ProjectIdPlanIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `plan_id` = ? and `deleted` = ? limit 1", perfPlanV2Rows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, planId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanV2Model) Insert(ctx context.Context, session sqlx.Session, data *PerfPlanV2) (sql.Result, error) {
	managerPerfPlanV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanV2IdPrefix, data.Id)
	managerPerfPlanV2ProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanV2ProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfPlanV2RowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Type, data.CronExpression, data.Tags, data.Protocol, data.TargetEnv, data.ProtobufConfigId, data.GeneralConfigId, data.AccountConfigId, data.AuthRateLimits, data.CustomDuration, data.Duration, data.CreateLarkChat, data.LarkChatId, data.AdvancedNotification, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Type, data.CronExpression, data.Tags, data.Protocol, data.TargetEnv, data.ProtobufConfigId, data.GeneralConfigId, data.AccountConfigId, data.AuthRateLimits, data.CustomDuration, data.Duration, data.CreateLarkChat, data.LarkChatId, data.AdvancedNotification, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfPlanV2IdKey, managerPerfPlanV2ProjectIdPlanIdKey)
}

func (m *defaultPerfPlanV2Model) Update(ctx context.Context, session sqlx.Session, newData *PerfPlanV2) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPerfPlanV2IdKey := fmt.Sprintf("%s%v", cacheManagerPerfPlanV2IdPrefix, data.Id)
	managerPerfPlanV2ProjectIdPlanIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanV2ProjectIdPlanIdPrefix, data.ProjectId, data.PlanId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfPlanV2RowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.PlanId, newData.Name, newData.Description, newData.Type, newData.CronExpression, newData.Tags, newData.Protocol, newData.TargetEnv, newData.ProtobufConfigId, newData.GeneralConfigId, newData.AccountConfigId, newData.AuthRateLimits, newData.CustomDuration, newData.Duration, newData.CreateLarkChat, newData.LarkChatId, newData.AdvancedNotification, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.PlanId, newData.Name, newData.Description, newData.Type, newData.CronExpression, newData.Tags, newData.Protocol, newData.TargetEnv, newData.ProtobufConfigId, newData.GeneralConfigId, newData.AccountConfigId, newData.AuthRateLimits, newData.CustomDuration, newData.Duration, newData.CreateLarkChat, newData.LarkChatId, newData.AdvancedNotification, newData.State, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPerfPlanV2IdKey, managerPerfPlanV2ProjectIdPlanIdKey)
}

func (m *defaultPerfPlanV2Model) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfPlanV2IdPrefix, primary)
}

func (m *defaultPerfPlanV2Model) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfPlanV2Rows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfPlanV2Model) tableName() string {
	return m.table
}
