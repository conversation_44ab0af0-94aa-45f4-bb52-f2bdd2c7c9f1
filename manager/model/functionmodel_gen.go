// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	functionTableName           = "`function`"
	functionFieldNames          = builder.RawFieldNames(&Function{})
	functionRows                = strings.Join(functionFieldNames, ",")
	functionRowsExpectAutoSet   = strings.Join(stringx.Remove(functionFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	functionRowsWithPlaceHolder = strings.Join(stringx.Remove(functionFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerFunctionIdPrefix                       = "cache:manager:function:id:"
	cacheManagerFunctionProjectIdNameTypeVersionPrefix = "cache:manager:function:projectId:name:type:version:"
)

type (
	functionModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Function) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Function, error)
		FindOneByProjectIdNameTypeVersion(ctx context.Context, projectId string, name string, tp string, version string) (*Function, error)
		Update(ctx context.Context, session sqlx.Session, data *Function) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultFunctionModel struct {
		sqlc.CachedConn
		table string
	}

	Function struct {
		Id          int64          `db:"id"`
		ProjectId   string         `db:"project_id"`  // 项目ID
		Name        string         `db:"name"`        // 函数名称
		Type        string         `db:"type"`        // 函数类型（内置、自定义）
		Category    string         `db:"category"`    // 函数分类
		Description sql.NullString `db:"description"` // 函数描述
		Language    string         `db:"language"`    // 编程语言
		Content     sql.NullString `db:"content"`     // 函数内容
		Parameters  string         `db:"parameters"`  // 参数列表
		Returns     string         `db:"returns"`     // 返回值列表
		Example     sql.NullString `db:"example"`     // 函数使用例子
		Version     string         `db:"version"`     // 函数版本
		Latest      int64          `db:"latest"`      // 是否最新版本
		Deleted     int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newFunctionModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultFunctionModel {
	return &defaultFunctionModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`function`",
	}
}

func (m *defaultFunctionModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerFunctionIdKey := fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, id)
	managerFunctionProjectIdNameTypeVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeVersionPrefix, data.ProjectId, data.Name, data.Type, data.Version)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerFunctionIdKey, managerFunctionProjectIdNameTypeVersionKey)
	return err
}

func (m *defaultFunctionModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerFunctionIdKey := fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, id)
	managerFunctionProjectIdNameTypeVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeVersionPrefix, data.ProjectId, data.Name, data.Type, data.Version)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerFunctionIdKey, managerFunctionProjectIdNameTypeVersionKey)
	return err
}

func (m *defaultFunctionModel) FindOne(ctx context.Context, id int64) (*Function, error) {
	managerFunctionIdKey := fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, id)
	var resp Function
	err := m.QueryRowCtx(ctx, &resp, managerFunctionIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", functionRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFunctionModel) FindOneByProjectIdNameTypeVersion(ctx context.Context, projectId string, name string, tp string, version string) (*Function, error) {
	managerFunctionProjectIdNameTypeVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeVersionPrefix, projectId, name, tp, version)
	var resp Function
	err := m.QueryRowIndexCtx(ctx, &resp, managerFunctionProjectIdNameTypeVersionKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `name` = ? and `type` = ? and `version` = ? and `deleted` = ? limit 1", functionRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, name, tp, version, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFunctionModel) Insert(ctx context.Context, session sqlx.Session, data *Function) (sql.Result, error) {
	managerFunctionIdKey := fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, data.Id)
	managerFunctionProjectIdNameTypeVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeVersionPrefix, data.ProjectId, data.Name, data.Type, data.Version)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, functionRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Name, data.Type, data.Category, data.Description, data.Language, data.Content, data.Parameters, data.Returns, data.Example, data.Version, data.Latest, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Name, data.Type, data.Category, data.Description, data.Language, data.Content, data.Parameters, data.Returns, data.Example, data.Version, data.Latest, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerFunctionIdKey, managerFunctionProjectIdNameTypeVersionKey)
}

func (m *defaultFunctionModel) Update(ctx context.Context, session sqlx.Session, newData *Function) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerFunctionIdKey := fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, data.Id)
	managerFunctionProjectIdNameTypeVersionKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeVersionPrefix, data.ProjectId, data.Name, data.Type, data.Version)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, functionRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Name, newData.Type, newData.Category, newData.Description, newData.Language, newData.Content, newData.Parameters, newData.Returns, newData.Example, newData.Version, newData.Latest, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Name, newData.Type, newData.Category, newData.Description, newData.Language, newData.Content, newData.Parameters, newData.Returns, newData.Example, newData.Version, newData.Latest, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerFunctionIdKey, managerFunctionProjectIdNameTypeVersionKey)
}

func (m *defaultFunctionModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, primary)
}

func (m *defaultFunctionModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", functionRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultFunctionModel) tableName() string {
	return m.table
}
