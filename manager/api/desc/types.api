syntax = "v1"

// 点元素
type Node {
    Id           string                 `json:"id"`
    Type         string                 `json:"type"`
    Label        string                 `json:"label"`
    ItemType     string                 `json:"itemType"`
    Width        float64                `json:"width"`
    Height       float64                `json:"height"`
    Size         []float64              `json:"size"`
    X            float64                `json:"x"`
    Y            float64                `json:"y"`
    AnchorPoints [][]float64            `json:"anchorPoints"`
    Icon         string                 `json:"icon"`
    Style        map[string]interface{} `json:"style"`
    ComboId      string                 `json:"comboId,optional"`
    Data         map[string]interface{} `json:"data,optional"`
    Order        int32                  `json:"_order,optional"`
    LayoutOrder  int32                  `json:"layoutOrder,optional"`
}

// 线元素
type Edge {
    Id              string                 `json:"id"`
    Type            string                 `json:"type"`
    Label           string                 `json:"label"`
    Source          string                 `json:"source"`
    SourceAnchor    float64                `json:"sourceAnchor"`
    Target          string                 `json:"target"`
    TargetAnchor    float64                `json:"targetAnchor"`
    LineAppendWidth int32                  `json:"lineAppendWidth"`
    Clazz           string                 `json:"clazz,optional"`
    Attrs           map[string]interface{} `json:"attrs"`
    Style           map[string]interface{} `json:"style"`
    LabelCfg        map[string]interface{} `json:"labelCfg"`
    StartPoint      map[string]interface{} `json:"startPoint"`
    EndPoint        map[string]interface{} `json:"endPoint"`
}

// 框元素
type Combo {
    Id           string                 `json:"id"`
    Type         string                 `json:"type"`
    Label        string                 `json:"label"`
    ItemType     string                 `json:"itemType"`
    X            float64                `json:"x"`
    Y            float64                `json:"y"`
    AnchorPoints [][]float64            `json:"anchorPoints"`
    Icon         string                 `json:"icon"`
    LabelCfg     map[string]interface{} `json:"labelCfg"`
    Style        map[string]interface{} `json:"style"`
    Depth        int32                  `json:"depth"`
    Padding      []float64              `json:"padding"`
    Collapsed    bool                   `json:"collapsed"`
    Children     []*ComboChild          `json:"children"`
    Data         map[string]interface{} `json:"data,optional"`
}

// 框中元素
type ComboChild {
    Id       string `json:"id"`
    ComboId  string `json:"comboId"`
    ItemType string `json:"itemType"`
    Depth    int32  `json:"depth"`
}

type Relation {
    Id          string        `json:"id"`
    Type        string        `json:"type"`
    Children    [][]*Relation `json:"children,omitempty,optional"`
    ReferenceId string        `json:"reference_id,omitempty,optional"`
}

type AccountConfig {
    Whole map[string]string `json:"whole"`
    Combo map[string]string `json:"combo"`
}

type Condition {
    Single *SingleCondition `json:"single,omitempty,optional"`
    Group  *GroupCondition  `json:"group,omitempty,optional"`
}

type GroupCondition {
    Relationship string       `json:"relationship" validate:"oneof=AND OR"`
    Conditions   []*Condition `json:"conditions"`
}

type Between {
    Start string `json:"start"`
    End   string `json:"end"`
}

type Other {
    Value string `json:"value"`
}

type SingleCondition {
    Field   string   `json:"field" validate:"required"`
    Compare string   `json:"compare,default=EQ" validate:"oneof=EQ NE LT LE GT GE LIKE IN BETWEEN"`
    In      []string `json:"in,omitempty,optional"`
    Between *Between `json:"between,omitempty,optional"`
    Other   *Other   `json:"other,omitempty,optional"`
}

type Pagination {
    CurrentPage uint64 `json:"current_page,default=1" validate:"gte=1"`
    PageSize    uint64 `json:"page_size,default=10" validate:"gte=1"`
}

type SortField {
    Field string `json:"field" validate:"required"`
    Order string `json:"order,default=ASC"`
}

type KeyValuePair {
    Key   string `json:"key" validate:"required"`
    Value string `json:"value" validate:"required"`
}

type FuncParameter {
    Name        string             `json:"name"`
    Source      int64              `json:"source"`
    Manual      *VariableValue     `json:"manual"`
    Export      *VariableNodeValue `json:"export"`
    Environment *VariableValue     `json:"environment"`
    Function    *VariableFuncValue `json:"function,omitempty,optional"`
}

type VariableValue {
    Value string `json:"value" validate:"required"`
}

type VariableNodeValue {
    NodeId string `json:"node_id" validate:"required"`
    VariableValue
}

type VariableFuncValue {
    Name       string           `json:"name" validate:"required"`
    Type       int64            `json:"type,default=1,options=0|1" validate:"required,oneof=0 1"`
    Parameters []*FuncParameter `json:"parameters"`
}

type ListChat {}

type RateLimit {
	TargetRps    int64  `json:"target_rps" validate:"gt=0"`               // 目标的RPS
	InitialRps   int64  `json:"initial_rps" validate:"omitempty,gt=0"`    // 初始的RPS
	StepHeight   int64  `json:"step_height" validate:"omitempty,ne=0"`    // 每次改变RPS的量
	StepDuration string `json:"step_duration" validate:"omitempty,gte=2"` // 改变后的RPS的持续时间
}

type RateLimitV2 {
	TargetRps      int64  `json:"target_rps" validate:"gt=0"`                 // 目标的RPS
	InitialRps     int64  `json:"initial_rps" validate:"omitempty,gt=0"`      // 初始的RPS
	ChangeDuration string `json:"change_duration" validate:"omitempty,gte=2"` // 从初始的RPS到目标的RPS的总耗时
	TargetDuration string `json:"target_duration" validate:"omitempty,gte=2"` // 到达目标的RPS后的持续时间
}

type ExportVariable {
	Name       string `json:"name" validate:"required"`
	Expression string `json:"expression" validate:"required"`
}

type PerfCaseStepV2 {
	Name         string             `json:"name" validate:"required"`
	RateLimits   []*RateLimitV2     `json:"rate_limits" validate:"gte=1"`
	Url          string             `json:"url,optional" validate:"omitempty"`
	Method       string             `json:"method" validate:"required"`
	Headers      map[string]string  `json:"headers,optional" validate:"omitempty"`
	Body         string             `json:"body" validate:"omitempty"`
	Exports      []*ExportVariable  `json:"exports,optional" validate:"omitempty"`
	Sleep        string             `json:"sleep,optional" validate:"omitempty,gte=2"`
    ReferenceQps *int64             `json:"reference_qps,optional" validate:"omitempty,gt=0"` // QPS参考值
}

type BasicPerfData {
	PerfDataId string `json:"perf_data_id" validate:"omitempty"`       // 压测数据ID
	CustomVu   bool   `json:"custom_vu"`                               // 是否自定义虚拟用户数
	NumberOfVu uint32 `json:"number_of_vu" validate:"omitempty,gte=0"` // 虚拟用户数
}

type LoadGenerator {
	CustomLg         bool   `json:"custom_lg"`                                     // 是否自定义施压机资源
	NumberOfLg       uint32 `json:"number_of_lg" validate:"omitempty,gte=0"`       // 施压机数量
	RequestsOfCpu    string `json:"requests_of_cpu" validate:"omitempty,gte=1"`    // 最小分配的CPU资源
	RequestsOfMemory string `json:"requests_of_memory" validate:"omitempty,gte=1"` // 最小分配的内存资源
	LimitsOfCpu      string `json:"limits_of_cpu" validate:"omitempty,gte=1"`      // 最大分配的CPU资源
	LimitsOfMemory   string `json:"limits_of_memory" validate:"omitempty,gte=1"`   // 最大分配的内存资源
}

type LarkChat {
	ChatId string `json:"chat_id" validate:"eq=35,startswith=oc_" zh:"飞书群组ID"`
	Name   string `json:"name" validate:"gte=1,lte=64" zh:"飞书群组名称"`
}

type FullUserInfo {
    Account      string `json:"account"`
    Fullname     string `json:"fullname"`
    DeptId       string `json:"dept_id"`
    DeptName     string `json:"dept_name"`
    FullDeptName string `json:"full_dept_name"`
    Email        string `json:"email"`
    Mobile       string `json:"mobile"`
    Photo        string `json:"photo"`
    Enabled      bool   `json:"enabled"`
}

type StabilityCustomScript {
	Image string `json:"image" validate:"omitempty,gte=0" zh:"脚本镜像"`
}