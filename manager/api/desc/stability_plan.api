syntax = "v1"

import "stability_plan_types.api"

@server (
    prefix: manager/v1
    group: stabilityPlan
)
service manager {
    @doc "create stability test plan"
    @handler createStabilityPlan
    post /stability_plan/create (CreateStabilityPlanReq) returns (CreateStabilityPlanResp)

    @doc "delete stability test plan"
    @handler removeStabilityPlan
    put /stability_plan/remove (RemoveStabilityPlanReq) returns (RemoveStabilityPlanResp)

    @doc "modify stability test plan"
    @handler modifyStabilityPlan
    put /stability_plan/modify (ModifyStabilityPlanReq) returns (ModifyStabilityPlanResp)

    @doc "search stability test plans"
    @handler searchStabilityPlan
    post /stability_plan/search (SearchStabilityPlanReq) returns (SearchStabilityPlanResp)

    @doc "view stability test plan"
    @handler viewStabilityPlan
    get /stability_plan/view (ViewStabilityPlanReq) returns (ViewStabilityPlanResp)
}
