syntax = "v1"

import "types.api"

type GitConfiguration {
    ProjectId   string        `json:"project_id"`
    ConfigId    string        `json:"config_id"`
    Type        string        `json:"type"`
    Name        string        `json:"name"`
    Description string        `json:"description"`
    Url         string        `json:"url"`
    AccessToken string        `json:"access_token"`
    Branch      string        `json:"branch"`
    Purpose     string        `json:"purpose"`
    CreatedBy   *FullUserInfo `json:"created_by"`
    UpdatedBy   *FullUserInfo `json:"updated_by"`
    CreatedAt   int64         `json:"created_at"`
    UpdatedAt   int64         `json:"updated_at"`
}

type (
    CreateGitConfigReq {
        ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
        Type        string `json:"type,default=GitLab" validate:"required,oneof=GitLab GitHub Gitee" zh:"Git类型（GitLab、GitHub、Gitee）"`
        Name        string `json:"name" validate:"gte=1,lte=64" zh:"Git配置名称"`
        Description string `json:"description" validate:"lte=255" zh:"Git配置描述"`
        Url         string `json:"url" validate:"url,lte=255" zh:"Git项目URL（http）"`
        AccessToken string `json:"access_token,omitempty,optional" validate:"omitempty,lte=128" zh:"Git项目访问令牌"`
        Branch      string `json:"branch" validate:"required,lte=128" zh:"Git项目分支名称"`
        Purpose     string `json:"purpose" validate:"required,oneof=API UI" zh:"用途（API测试、UI测试）"`
    }
    CreateGitConfigResp {
        ConfigId string `json:"config_id"`
    }
)

type (
    RemoveGitConfigReq {
        ProjectId string   `json:"project_id" zh:"项目ID"`
        ConfigIds []string `json:"config_ids" zh:"Git配置ID列表"`
    }
    RemoveGitConfigResp {}
)

type (
    ModifyGitConfigReq {
        ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
        ConfigId    string `json:"config_id" validate:"required" zh:"Git配置ID"`
        Name        string `json:"name" validate:"gte=1,lte=64" zh:"Git配置名称"`
        Description string `json:"description" validate:"lte=255" zh:"Git配置描述"`
        AccessToken string `json:"access_token,omitempty,optional" validate:"omitempty,lte=128" zh:"Git项目访问令牌"`
    }
    ModifyGitConfigResp {}
)

type (
    SearchGitConfigReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchGitConfigResp {
        CurrentPage uint64              `json:"current_page"`
        PageSize    uint64              `json:"page_size"`
        TotalCount  uint64              `json:"total_count"`
        TotalPage   uint64              `json:"total_page"`
        Items       []*GitConfiguration `json:"items"`
    }
)

type (
    ViewGitConfigReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        ConfigId  string `form:"config_id" validate:"required" zh:"Git配置ID"`
    }
    ViewGitConfigResp {
        *GitConfiguration
    }
)

type (
    TestGitConfigReq {
        Url         string `json:"url" validate:"url,lte=255" zh:"Git项目URL（http）"`
        AccessToken string `json:"access_token" validate:"lte=128" zh:"Git项目访问令牌"`
    }
    TestGitConfigResp {
        Branches []string `json:"branches"`
    }
)

type (
    SyncGitConfigReq {
        ProjectID string `json:"project_id" validate:"required" zh:"项目ID"`
        ConfigID  string `json:"config_id" validate:"required" zh:"Git配置ID"`
    }
    SyncGitConfigResp {}
)

type (
    SyncGitConfigByWebhookReq {
        GitUrl       string `json:"git_url" validate:"required" zh:"项目地址"`
        TargetBranch string `json:"target_branch" validate:"required" zh:"目标分支"`
        Email        string `json:"email" validate:"required" zh:"邮箱"`
    }
    SyncGitConfigByWebhookResp {}
)
