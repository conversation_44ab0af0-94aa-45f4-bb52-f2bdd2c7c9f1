syntax = "v1"

import "types.api"

type Category {
    ProjectId    string        `json:"project_id"`
    CategoryId   string        `json:"category_id"`
    Type         string        `json:"type"`
    CategoryType string        `json:"category_type"`
    RootType     string        `json:"root_type"`
    NodeType     string        `json:"node_type"`
    NodeId       string        `json:"node_id"`
    Name         string        `json:"name"`
    Description  string        `json:"description"`
    Builtin      bool          `json:"builtin"`
    CreatedBy    *FullUserInfo `json:"created_by"`
    UpdatedBy    *FullUserInfo `json:"updated_by"`
    CreatedAt    int64         `json:"created_at"`
    UpdatedAt    int64         `json:"updated_at"`
    Amount       int64         `json:"amount"`
    Children     []*Category   `json:"children"`
}

type (
    CreateCategoryReq {
        ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
        Type        string `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN" zh:"分类树类型"`
        Name        string `json:"name" validate:"gte=1,lte=64" zh:"分类名称"`
        Description string `json:"description" validate:"lte=255" zh:"分类描述"`
        ParentId    string `json:"parent_id" validate:"required" zh:"父分类ID"`
        Index       int64  `json:"index,default=0" zh:"分类所在层的序号"`
    }
    CreateCategoryResp {
        CategoryId string `json:"category_id"`
    }
)

type (
    RemoveCategoryReq {
        ProjectId  string `json:"project_id" validate:"required" zh:"项目ID"`
        Type       string `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN" zh:"分类树类型"`
        CategoryId string `json:"category_id" validate:"required" zh:"分类ID"`
    }
    RemoveCategoryResp {}
)

type (
    ModifyCategoryReq {
        ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
        Type        string `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN" zh:"分类树类型"`
        CategoryId  string `json:"category_id" validate:"required" zh:"分类ID"`
        Name        string `json:"name" validate:"gte=1,lte=64" zh:"分类名称"`
        Description string `json:"description" validate:"lte=255" zh:"分类描述"`
    }
    ModifyCategoryResp {}
)

type (
    SearchCategoryReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Type       string       `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN" zh:"分类树类型"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchCategoryResp {
        CurrentPage uint64      `json:"current_page"`
        PageSize    uint64      `json:"page_size"`
        TotalCount  uint64      `json:"total_count"`
        TotalPage   uint64      `json:"total_page"`
        Items       []*Category `json:"items"`
    }
)

type (
    MoveCategoryTreeReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        Type      string `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN" zh:"分类树类型"`
        MoveType  string `json:"move_type" validate:"required,oneof=BEFORE AFTER INNER" zh:"移到类型"`
        SourceId  string `json:"source_id" validate:"required" zh:"源分类ID"`
        TargetId  string `json:"target_id" validate:"required" zh:"目标分类ID"`
        SiblingId string `json:"sibling_id" zh:"兄弟分类ID"`
    }
    MoveCategoryTreeResp {}
)

type (
    GetCategoryTreeReq {
        ProjectId     string `form:"project_id" validate:"required" zh:"项目ID"`
        Type          string `form:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN" zh:"分类树类型"`
        CategoryId    string `form:"category_id,optional" zh:"分类ID"`
        Depth         uint32 `form:"depth" validate:"gte=0" zh:"获取分类树的最大深度"`
        OnlyDirectory bool   `form:"only_directory,default=false"`
        IncludeSelf   bool   `form:"include_self,omitempty,optional"`
    }
    GetCategoryTreeResp {} // GetCategoryTreeResp = []*Category
)
