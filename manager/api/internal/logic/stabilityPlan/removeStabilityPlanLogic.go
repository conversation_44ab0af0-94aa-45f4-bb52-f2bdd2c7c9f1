package stabilityPlan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveStabilityPlanLogic struct {
	*BaseLogic
}

// delete stability test plan
func NewRemoveStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveStabilityPlanLogic {
	return &RemoveStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveStabilityPlanLogic) RemoveStabilityPlan(req *types.RemoveStabilityPlanReq) (
	resp *types.RemoveStabilityPlanResp, err error,
) {
	in := &pb.RemoveStabilityPlanReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerStabilityPlanPRC.RemoveStabilityPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveStabilityPlanResp{}, nil
}
