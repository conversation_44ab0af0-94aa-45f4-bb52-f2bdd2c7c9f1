package stabilityPlan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyStabilityPlanLogic struct {
	*BaseLogic
}

// modify stability test plan
func NewModifyStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyStabilityPlanLogic {
	return &ModifyStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyStabilityPlanLogic) ModifyStabilityPlan(req *types.ModifyStabilityPlanReq) (
	resp *types.ModifyStabilityPlanResp, err error,
) {
	in := &pb.ModifyStabilityPlanReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	in.Devices = convertCustomDevicesRpc(req.DeviceUdids)
	in.CustomScript = convertCustomScriptRpc(req.CustomScript)

	_, err = l.svcCtx.ManagerStabilityPlanPRC.ModifyStabilityPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyStabilityPlanResp{}, nil
}
