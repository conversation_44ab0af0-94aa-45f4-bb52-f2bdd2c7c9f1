package stabilityPlan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateStabilityPlanLogic struct {
	*BaseLogic
}

// create stability test plan
func NewCreateStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateStabilityPlanLogic {
	return &CreateStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateStabilityPlanLogic) CreateStabilityPlan(req *types.CreateStabilityPlanReq) (
	resp *types.CreateStabilityPlanResp, err error,
) {
	in := &pb.CreateStabilityPlanReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	in.Devices = convertCustomDevicesRpc(req.DeviceUdids)
	in.CustomScript = convertCustomScriptRpc(req.CustomScript)

	out, err := l.svcCtx.ManagerStabilityPlanPRC.CreateStabilityPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateStabilityPlanResp{PlanId: out.GetPlan().GetPlanId()}, nil
}
