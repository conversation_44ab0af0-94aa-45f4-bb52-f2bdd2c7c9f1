package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type StabilityPlan struct {
	ProjectId       string                       `json:"project_id"`        // 项目ID
	CategoryId      string                       `json:"category_id"`       // 分类ID
	PlanId          string                       `json:"plan_id"`           // 计划ID
	Name            string                       `json:"name"`              // 计划名称
	Description     string                       `json:"description"`       // 计划描述
	State           int8                         `json:"state"`             // 状态
	Type            string                       `json:"type"`              // 计划类型
	PriorityType    int8                         `json:"priority_type"`     // 优先级（Default、Middle、High、Ultra、Low)
	CronExpression  string                       `json:"cron_expression"`   // 定时触发的Cron表达式
	Tags            []string                     `json:"tags"`              // 计划标签
	AccountConfigId string                       `json:"account_config_id"` // 池账号配置ID
	DeviceType      int8                         `json:"device_type"`       // 设备类型（真机、云手机）
	PlatformType    int8                         `json:"platform_type"`     // 测试系统（Android、IOS）
	DeviceUdids     []string                     `json:"device_udids"`      // 设备列表
	PackageName     string                       `json:"package_name"`      // 包名，用于启动app
	AppDownloadLink string                       `json:"app_download_link"` // APP下载地址
	Duration        uint32                       `json:"duration"`          // 运行时长（单位：分钟）
	Activities      []string                     `json:"activities"`        // 启动的Activity
	CustomScript    *types.StabilityCustomScript `json:"custom_script"`     // 自定义脚本
	MaintainedBy    *userinfo.FullUserInfo       `json:"maintained_by"`     // 维护者
	CreatedBy       *userinfo.FullUserInfo       `json:"created_by"`        // 创建者
	UpdatedBy       *userinfo.FullUserInfo       `json:"updated_by"`        // 更新者
	CreatedAt       int64                        `json:"created_at"`        // 创建时间
	UpdatedAt       int64                        `json:"updated_at"`        // 更新时间
}

type CreateStabilityPlanReq struct {
	ProjectId       string                       `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId      string                       `json:"category_id" validate:"required" zh:"分类ID"`
	Name            string                       `json:"name" validate:"gte=1,lte=64" zh:"计划名称"`
	Description     string                       `json:"description" validate:"lte=255" zh:"计划描述"`
	State           int8                         `json:"state" validate:"oneof=1 2" zh:"生效1，失效2"`
	Type            string                       `json:"type" validate:"oneof=MANUAL SCHEDULE INTERFACE" zh:"计划类型"`
	PriorityType    int8                         `json:"priority_type,omitempty" validate:"oneof=0 1 2 3 4" zh:"优先级（Default、Middle、High、Ultra、Low)"`
	CronExpression  string                       `json:"cron_expression" validate:"omitempty,gte=9,lte=128" zh:"定时触发的Cron表达式"`
	Tags            []string                     `json:"tags" validate:"gte=0" zh:"计划标签"`
	AccountConfigId string                       `json:"account_config_id" validate:"omitempty,gte=1,lte=64" zh:"池账号配置ID"`
	DeviceType      int8                         `json:"device_type" validate:"oneof=1 2" zh:"设备类型（真机、云手机）"`
	PlatformType    int8                         `json:"platform_type" validate:"oneof=1 2" zh:"测试系统（Android、IOS）"`
	DeviceUdids     []string                     `json:"device_udids" validate:"omitempty,gte=0" zh:"设备列表"`
	PackageName     string                       `json:"package_name" validate:"lte=255" zh:"包名，用于启动app"`
	AppDownloadLink string                       `json:"app_download_link" validate:"lte=255" zh:"APP下载地址"`
	Duration        uint32                       `json:"duration" validate:"omitempty,gte=0,lte=3600" zh:"运行时长（单位：分钟）"`
	Activities      []string                     `json:"activities" validate:"omitempty,gte=0" zh:"启动的Activity"`
	CustomScript    *types.StabilityCustomScript `json:"custom_script,omitempty" zh:"自定义脚本"`
	LarkChats       []*types.LarkChat            `json:"lark_chats" validate:"omitempty" zh:"飞书通知群组列表"`
	MaintainedBy    string                       `json:"maintained_by" validate:"lte=64" zh:"计划维护者"`
}

type CreateStabilityPlanResp struct {
	PlanId string `json:"plan_id"`
}

type RemoveStabilityPlanReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	PlanIds   []string `json:"plan_ids" validate:"gt=0" zh:"计划ID列表"`
}

type RemoveStabilityPlanResp struct{}

type ModifyStabilityPlanReq struct {
	ProjectId       string                       `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId      string                       `json:"category_id" validate:"required" zh:"分类ID"`
	PlanId          string                       `json:"plan_id" validate:"required" zh:"计划ID"`
	Name            string                       `json:"name" validate:"gte=1,lte=64" zh:"计划名称"`
	Description     string                       `json:"description" validate:"lte=255" zh:"计划描述"`
	State           int8                         `json:"state" validate:"oneof=1 2" zh:"生效1，失效2"`
	Type            string                       `json:"type" validate:"oneof=MANUAL SCHEDULE INTERFACE" zh:"计划类型"`
	PriorityType    int8                         `json:"priority_type,omitempty" validate:"oneof=0 1 2 3 4" zh:"优先级（Default、Middle、High、Ultra、Low)"`
	CronExpression  string                       `json:"cron_expression" validate:"omitempty,gte=9,lte=128" zh:"定时触发的Cron表达式"`
	Tags            []string                     `json:"tags" validate:"gte=0" zh:"计划标签"`
	AccountConfigId string                       `json:"account_config_id" validate:"omitempty,gte=1,lte=64" zh:"池账号配置ID"`
	DeviceType      int8                         `json:"device_type" validate:"oneof=1 2" zh:"设备类型（真机、云手机）"`
	PlatformType    int8                         `json:"platform_type" validate:"oneof=1 2" zh:"测试系统（Android、IOS）"`
	DeviceUdids     []string                     `json:"device_udids" validate:"omitempty,gte=0" zh:"设备列表"`
	PackageName     string                       `json:"package_name" validate:"lte=255" zh:"包名，用于启动app"`
	AppDownloadLink string                       `json:"app_download_link" validate:"lte=255" zh:"APP下载地址"`
	Duration        uint32                       `json:"duration" validate:"omitempty,gte=0,lte=3600" zh:"运行时长（单位：分钟）"`
	Activities      []string                     `json:"activities" validate:"omitempty,gte=0" zh:"启动的Activity"`
	CustomScript    *types.StabilityCustomScript `json:"custom_script,omitempty" zh:"自定义脚本"`
	LarkChats       []*types.LarkChat            `json:"lark_chats" validate:"omitempty,gte=0" zh:"飞书通知群组列表"`
	MaintainedBy    string                       `json:"maintained_by" validate:"lte=64" zh:"计划维护者"`
}

type ModifyStabilityPlanResp struct{}

type SearchStabilityPlanReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId string           `json:"category_id,omitempty" zh:"分类ID"`
	Condition  *api.Condition   `json:"condition,omitempty" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty" zh:"查询排序"`
}

type SearchStabilityPlanResp struct {
	CurrentPage uint64           `json:"current_page"`
	PageSize    uint64           `json:"page_size"`
	TotalCount  uint64           `json:"total_count"`
	TotalPage   uint64           `json:"total_page"`
	Items       []*StabilityPlan `json:"items"`
}

type ViewStabilityPlanReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	PlanId    string `form:"plan_id" validate:"required" zh:"计划ID"`
}

type ViewStabilityPlanResp struct {
	*StabilityPlan
}
