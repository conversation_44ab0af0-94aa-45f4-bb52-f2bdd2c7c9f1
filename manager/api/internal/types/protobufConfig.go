package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type ProtobufConfiguration struct {
	ProjectId    string                 `json:"project_id"`
	ConfigId     string                 `json:"config_id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	GitConfigId  string                 `json:"git_config_id"`
	ImportPath   string                 `json:"import_path"`
	ExcludePaths []string               `json:"exclude_paths"`
	ExcludeFiles []string               `json:"exclude_files"`
	Dependencies []string               `json:"dependencies"`
	CreatedBy    *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy    *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt    int64                  `json:"created_at"`
	UpdatedAt    int64                  `json:"updated_at"`
}

type CreateProtobufConfigReq struct {
	ProjectId    string   `json:"project_id" validate:"required" zh:"项目ID"`
	Name         string   `json:"name" validate:"gte=1,lte=64" zh:"Protobuf配置名称"`
	Description  string   `json:"description" validate:"lte=255" zh:"Protobuf配置描述"`
	GitConfigId  string   `json:"git_config_id" validate:"required" zh:"Git配置ID"`
	ImportPath   string   `json:"import_path,optional,default=." validate:"lte=255" zh:"导入路径"`
	ExcludePaths []string `json:"exclude_paths,optional" validate:"gte=0" zh:"排除的路径列表"`
	ExcludeFiles []string `json:"exclude_files,optional" validate:"gte=0" zh:"排除的文件列表"`
	Dependencies []string `json:"dependencies,optional" validate:"gte=0" zh:"依赖列表"`
}

type CreateProtobufConfigResp struct {
	ConfigId string `json:"config_id"`
}

type RemoveProtobufConfigReq struct {
	ProjectId string   `json:"project_id" zh:"项目ID"`
	ConfigIds []string `json:"config_ids" zh:"Protobuf配置ID列表"`
}

type RemoveProtobufConfigResp struct{}

type ModifyProtobufConfigReq struct {
	ProjectId    string   `json:"project_id" validate:"required" zh:"项目ID"`
	ConfigId     string   `json:"config_id" validate:"required" zh:"Protobuf配置ID"`
	Name         string   `json:"name" validate:"gte=1,lte=64" zh:"Protobuf配置名称"`
	Description  string   `json:"description" validate:"lte=255" zh:"Protobuf配置描述"`
	GitConfigId  string   `json:"git_config_id" validate:"required" zh:"Git配置ID"`
	ImportPath   string   `json:"import_path,optional,default=." validate:"lte=255" zh:"导入路径"`
	ExcludePaths []string `json:"exclude_paths,optional" validate:"gte=0" zh:"排除的路径列表"`
	ExcludeFiles []string `json:"exclude_files,optional" validate:"gte=0" zh:"排除的文件列表"`
	Dependencies []string `json:"dependencies,optional" validate:"gte=0" zh:"依赖列表"`
}

type ModifyProtobufConfigResp struct{}

type SearchProtobufConfigReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchProtobufConfigResp struct {
	CurrentPage uint64                   `json:"current_page"`
	PageSize    uint64                   `json:"page_size"`
	TotalCount  uint64                   `json:"total_count"`
	TotalPage   uint64                   `json:"total_page"`
	Items       []*ProtobufConfiguration `json:"items"`
}

type ViewProtobufConfigReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	ConfigId  string `form:"config_id" validate:"required" zh:"Protobuf配置ID"`
}

type ViewProtobufConfigResp struct {
	*ProtobufConfiguration
}
