package task

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PublishLogic struct {
	logx.Logger
	ctx         context.Context
	svcCtx      *svc.ServiceContext
	currentUser *userinfo.UserInfo
}

func NewPublishLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PublishLogic {
	return &PublishLogic{
		currentUser: userinfo.FromContext(ctx),
		Logger:      logx.WithContext(ctx),
		ctx:         ctx,
		svcCtx:      svcCtx,
	}
}

func (l *PublishLogic) apiExecutionDataType2PublishType(typ managerpb.ApiExecutionDataType) pb.PublishType {
	m := map[managerpb.ApiExecutionDataType]pb.PublishType{
		managerpb.ApiExecutionDataType_API_COMPONENT_GROUP: pb.PublishType_PublishType_API_COMPONENT_GROUP,
		managerpb.ApiExecutionDataType_API_CASE:            pb.PublishType_PublishType_API_CASE,
		managerpb.ApiExecutionDataType_API_SUITE:           pb.PublishType_PublishType_API_SUITE,
		managerpb.ApiExecutionDataType_API_PLAN:            pb.PublishType_PublishType_API_PLAN,
		managerpb.ApiExecutionDataType_INTERFACE_CASE:      pb.PublishType_PublishType_INTERFACE_CASE,
		managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT:  pb.PublishType_PublishType_INTERFACE_DOCUMENT,
		managerpb.ApiExecutionDataType_UI_CASE:             pb.PublishType_PublishType_UI_CASE,
		managerpb.ApiExecutionDataType_UI_SUITE:            pb.PublishType_PublishType_UI_SUITE,
		managerpb.ApiExecutionDataType_UI_PLAN:             pb.PublishType_PublishType_UI_PLAN,
		managerpb.ApiExecutionDataType_PERF_PLAN:           pb.PublishType_PublishType_PERF_PLAN,
		managerpb.ApiExecutionDataType_STABILITY_PLAN:      pb.PublishType_PublishType_STABILITY_PLAN,
	}
	return m[typ]
}

func (l *PublishLogic) Publish(in *types.PublishReq) (resp *types.PublishResp, err error) {
	req := &pb.PublishReq{
		TriggerMode: commonpb.TriggerMode_MANUAL,
		ProjectId:   in.ProjectId,
		ExecuteType: managerpb.ApiExecutionDataType(in.Type),
		PublishType: l.apiExecutionDataType2PublishType(managerpb.ApiExecutionDataType(in.Type)),
		UserId:      l.currentUser.Account,
	}

	err = l.genPublishData(req.GetPublishType(), in, req)
	if err != nil {
		return nil, errorx.Err(errorx.GrpcCanceled, fmt.Sprintf("%s", err))
	}

	res, err := l.svcCtx.DispatcherRpc.Publish(l.ctx, req)
	if err != nil {
		return nil, errorx.Err(errorx.GrpcCanceled, fmt.Sprintf("%s", err))
	}

	return &types.PublishResp{TaskId: res.GetTaskId(), ExecuteId: res.GetExecuteId(), Version: res.GetVersion()}, nil
}

func (l *PublishLogic) genPublishData(typ pb.PublishType, in *types.PublishReq, req *pb.PublishReq) error {
	switch typ {
	case pb.PublishType_PublishType_API_COMPONENT_GROUP:
		req.Data = &pb.PublishReq_ComponentGroup{
			ComponentGroup: &pb.ComponentGroupPublishInfo{
				ComponentGroupId: in.Id,
				GeneralConfig:    l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig:    l.genAccountConfig(in.AccountConfig),
				Version:          in.Version,
			},
		}
	case pb.PublishType_PublishType_API_CASE:
		req.Data = &pb.PublishReq_Case{
			Case: &pb.CasePublishInfo{
				CaseId:        in.Id,
				GeneralConfig: l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig: l.genAccountConfig(in.AccountConfig),
				Version:       in.Version,
			},
		}
	case pb.PublishType_PublishType_API_SUITE:
		req.Data = &pb.PublishReq_Suite{
			Suite: &pb.SuitePublishInfo{
				SuiteId:       in.Id,
				GeneralConfig: l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig: l.genAccountConfig(in.AccountConfig),
				// CaseExecutionMode:
			},
		}
	case pb.PublishType_PublishType_API_PLAN:
		req.Data = &pb.PublishReq_Plan{
			Plan: &pb.PlanPublishInfo{
				PlanId: in.Id,
			},
		}
	case pb.PublishType_PublishType_INTERFACE_DOCUMENT:
		cases, err := l.genInterfaceCases(in)
		if err != nil {
			return err
		}

		req.Data = &pb.PublishReq_Interface{
			Interface: &pb.InterfaceDocumentPublishInfo{
				InterfaceDocumentId: in.Id,
				GeneralConfig:       l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig:       l.genAccountConfig(in.AccountConfig),
				InterfaceCases:      cases,
			},
		}
	case pb.PublishType_PublishType_INTERFACE_CASE:
		req.Data = &pb.PublishReq_InterfaceCase{
			InterfaceCase: &pb.InterfaceCasePublishInfo{
				InterfaceCaseId: in.Id,
				GeneralConfig:   l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig:   l.genAccountConfig(in.AccountConfig),
				Version:         in.Version,
			},
		}
	case pb.PublishType_PublishType_UI_CASE:
		req.Data = &pb.PublishReq_UiCase{
			UiCase: &pb.UICasePublishInfo{
				UiCaseId: in.Id,
			},
		}
	case pb.PublishType_PublishType_UI_SUITE:
		req.Data = &pb.PublishReq_UiSuite{
			UiSuite: &pb.UISuitePublishInfo{
				UiSuiteId: in.Id,
			},
		}
	case pb.PublishType_PublishType_UI_PLAN:
		req.Data = &pb.PublishReq_UiPlan{
			UiPlan: &pb.UIPlanPublishInfo{
				UiPlanId: in.Id,
			},
		}
	case pb.PublishType_PublishType_PERF_PLAN:
		req.Data = &pb.PublishReq_PerfPlan{
			PerfPlan: &pb.PerfPlanPublishInfo{
				PerfPlanId: in.Id,
				PerfPlanInfo: &pb.PerfPlanInfo{
					ExecuteType:             commonpb.PerfTaskType_DEBUG,
					EstimatedTime:           0,
					SendPreviewNotification: false,
				},
			},
		}
	case pb.PublishType_PublishType_STABILITY_PLAN:
		req.Data = &pb.PublishReq_StabilityPlan{
			StabilityPlan: &pb.StabilityPlanPublishInfo{
				StabilityPlanId: in.Id,
			},
		}
	}

	return nil
}

func (l *PublishLogic) genInterfaceCases(in *types.PublishReq) ([]*managerpb.ApiExecutionData, error) {
	if len(in.InterfaceCaseIds) == 0 {
		return nil, nil
	}

	cases := make([]*managerpb.ApiExecutionData, len(in.InterfaceCaseIds))
	for idx, item := range in.InterfaceCaseIds {
		apiExecutionData, err := l.getApiExecutionData(
			in.ProjectId, managerpb.ApiExecutionDataType(item.Type), item.Id, item.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("manager GetApiExecutionData fail: %s", err)
		}
		cases[idx] = apiExecutionData
	}
	return cases, nil
}

func (l *PublishLogic) getApiExecutionData(
	productId string, typ managerpb.ApiExecutionDataType, id, version string,
) (resp *managerpb.ApiExecutionData, err error) {
	resp, err = l.svcCtx.ManagerRpc.GetApiExecutionData(
		l.ctx,
		&managerpb.GetApiExecutionDataReq{
			ProjectId: productId,
			Id:        id,
			Type:      typ,
			Version:   version,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "获取执行数据失败, error: %s", err)
	}

	return resp, nil
}

func (l *PublishLogic) genGeneralConfig(in *commontypes.ApiGeneralConfig) *commonpb.GeneralConfig {
	config := &commonpb.GeneralConfig{
		ProjectId:   in.ProjectId,
		ConfigId:    in.ConfigId,
		Name:        in.Name,
		Description: in.Description,
		BaseUrl:     in.BaseUrl,
		Verify:      in.Verify,
		Variables:   make([]*commonpb.GeneralConfigVar, len(in.Variables)),
	}

	for idx, kv := range in.Variables {
		config.Variables[idx] = &commonpb.GeneralConfigVar{
			Key:   kv.Key,
			Value: kv.Value,
		}
	}

	return config
}

func (l *PublishLogic) genAccountConfig(in []commontypes.ApiAccountConfig) []*commonpb.AccountConfig {
	configs := make([]*commonpb.AccountConfig, len(in))
	for idx, kv := range in {
		configs[idx] = &commonpb.AccountConfig{
			ProjectId:    kv.ProjectId,
			ConfigId:     kv.ConfigId,
			Name:         kv.Name,
			Description:  kv.Description,
			ProductType:  kv.ProductType,
			ProductName:  kv.ProductName,
			PoolEnvTable: kv.PoolEnvTable,
			PoolEnvName:  kv.PoolEnvName,
		}
	}
	return configs
}

func (l *PublishLogic) genInterfaceCaseIds(in *types.PublishReq) []*pb.ComponentExecuteKey {
	// 接口用例特殊逻辑
	if managerpb.ApiExecutionDataType(in.Type) != managerpb.ApiExecutionDataType_INTERFACE_CASE {
		return nil
	}

	keys := make([]*pb.ComponentExecuteKey, len(in.InterfaceCaseIds))
	for idx, item := range in.InterfaceCaseIds {
		keys[idx] = &pb.ComponentExecuteKey{
			Key: &pb.ComponentKey{
				ComponentId:   item.Id,
				ComponentType: managerpb.ApiExecutionDataType(item.Type),
				Version:       item.Version,
			},
		}
	}
	return keys
}
