// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: dispatcher.proto

package dispatcher

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type (
	PublishReq              = pb.PublishReq
	PublishReq_ApproverUser = pb.PublishReq_ApproverUser
	PublishReq_SubEnvInfo   = pb.PublishReq_SubEnvInfo
	PublishReq_TriggerUser  = pb.PublishReq_TriggerUser
	PublishResp             = pb.PublishResp
	SearchTaskInfoItem      = pb.SearchTaskInfoItem
	SearchTaskInfoReq       = pb.SearchTaskInfoReq
	SearchTaskInfoResp      = pb.SearchTaskInfoResp
	StopMetadata            = pb.StopMetadata
	StopReq                 = pb.StopReq
	StopResp                = pb.StopResp

	Dispatcher interface {
		Publish(ctx context.Context, in *PublishReq, opts ...grpc.CallOption) (*PublishResp, error)
		Stop(ctx context.Context, in *StopReq, opts ...grpc.CallOption) (*StopResp, error)
		SearchTaskInfo(ctx context.Context, in *SearchTaskInfoReq, opts ...grpc.CallOption) (*SearchTaskInfoResp, error)
	}

	defaultDispatcher struct {
		cli zrpc.Client
	}
)

func NewDispatcher(cli zrpc.Client) Dispatcher {
	return &defaultDispatcher{
		cli: cli,
	}
}

func (m *defaultDispatcher) Publish(ctx context.Context, in *PublishReq, opts ...grpc.CallOption) (*PublishResp, error) {
	client := pb.NewDispatcherClient(m.cli.Conn())
	return client.Publish(ctx, in, opts...)
}

func (m *defaultDispatcher) Stop(ctx context.Context, in *StopReq, opts ...grpc.CallOption) (*StopResp, error) {
	client := pb.NewDispatcherClient(m.cli.Conn())
	return client.Stop(ctx, in, opts...)
}

func (m *defaultDispatcher) SearchTaskInfo(ctx context.Context, in *SearchTaskInfoReq, opts ...grpc.CallOption) (*SearchTaskInfoResp, error) {
	client := pb.NewDispatcherClient(m.cli.Conn())
	return client.SearchTaskInfo(ctx, in, opts...)
}
