package dispatcherlogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type StabilityPlanPublisher struct {
	*BasePublisher

	apiExecutionData *managerpb.ApiExecutionData
}

func NewStabilityPlanPublisher(ctx context.Context, svcCtx *svc.ServiceContext) *StabilityPlanPublisher {
	return &StabilityPlanPublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *StabilityPlanPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	return nil
}

func (l *StabilityPlanPublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *StabilityPlanPublisher) handleMgrData(in *pb.PublishReq) (*managerpb.ApiExecutionData, error) {
	return &managerpb.ApiExecutionData{}, nil
}

func (l *StabilityPlanPublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *StabilityPlanPublisher) getDistributeReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.DistributeReq {
	req := &pb.DistributeReq{}
	return req
}

func (l *StabilityPlanPublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyStabilityPlanRecordResp, err error) {
	return
}

func (l *StabilityPlanPublisher) getStabilitySuites(data *managerpb.ApiExecutionData) []*managerpb.ApiExecutionData {
	if len(data.GetChildren()) == 0 {
		return make([]*managerpb.ApiExecutionData, 0)
	}

	return data.GetChildren()[0].GetChild()
}

// IsValid 是否有效
func (l *StabilityPlanPublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	return data.GetUiPlan().GetState() != managerpb.CommonState_CS_DISABLE
}

// Panic 异常处理
func (l *StabilityPlanPublisher) Panic(in *pb.PublishReq, err error) {
	return
}
