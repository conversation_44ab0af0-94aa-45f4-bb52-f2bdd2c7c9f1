// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/publish.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublishType int32

const (
	PublishType_PublishType_UNKNOWN                      PublishType = 0
	PublishType_PublishType_API_COMPONENT_GROUP          PublishType = 1  // API组件组
	PublishType_PublishType_API_CASE                     PublishType = 2  // API测试用例
	PublishType_PublishType_API_SUITE                    PublishType = 3  // API测试集合
	PublishType_PublishType_API_PLAN                     PublishType = 4  // API测试计划
	PublishType_PublishType_INTERFACE_CASE               PublishType = 5  // 接口用例
	PublishType_PublishType_INTERFACE_DOCUMENT           PublishType = 6  // 接口文档（即接口集合）
	PublishType_PublishType_UI_CASE                      PublishType = 7  // UI测试用例
	PublishType_PublishType_UI_SUITE                     PublishType = 8  // UI测试集合
	PublishType_PublishType_UI_PLAN                      PublishType = 9  // UI测试计划
	PublishType_PublishType_API_PRECISION_SUITE          PublishType = 10 // Deprecated: API精准测试集合
	PublishType_PublishType_PRECISION_INTERFACE_DOCUMENT PublishType = 11 // Deprecated: API精准测试接口文档
	PublishType_PublishType_API_SERVICE                  PublishType = 12 // API精准测试服务集合
	PublishType_PublishType_PERF_CASE                    PublishType = 13 // 压力测试用例
	PublishType_PublishType_PERF_SUITE                   PublishType = 14 // 压力测试集合
	PublishType_PublishType_PERF_PLAN                    PublishType = 15 // 压力测试计划
	PublishType_PublishType_STABILITY_PLAN               PublishType = 16 // 稳定性测试计划
)

// Enum value maps for PublishType.
var (
	PublishType_name = map[int32]string{
		0:  "PublishType_UNKNOWN",
		1:  "PublishType_API_COMPONENT_GROUP",
		2:  "PublishType_API_CASE",
		3:  "PublishType_API_SUITE",
		4:  "PublishType_API_PLAN",
		5:  "PublishType_INTERFACE_CASE",
		6:  "PublishType_INTERFACE_DOCUMENT",
		7:  "PublishType_UI_CASE",
		8:  "PublishType_UI_SUITE",
		9:  "PublishType_UI_PLAN",
		10: "PublishType_API_PRECISION_SUITE",
		11: "PublishType_PRECISION_INTERFACE_DOCUMENT",
		12: "PublishType_API_SERVICE",
		13: "PublishType_PERF_CASE",
		14: "PublishType_PERF_SUITE",
		15: "PublishType_PERF_PLAN",
		16: "PublishType_STABILITY_PLAN",
	}
	PublishType_value = map[string]int32{
		"PublishType_UNKNOWN":                      0,
		"PublishType_API_COMPONENT_GROUP":          1,
		"PublishType_API_CASE":                     2,
		"PublishType_API_SUITE":                    3,
		"PublishType_API_PLAN":                     4,
		"PublishType_INTERFACE_CASE":               5,
		"PublishType_INTERFACE_DOCUMENT":           6,
		"PublishType_UI_CASE":                      7,
		"PublishType_UI_SUITE":                     8,
		"PublishType_UI_PLAN":                      9,
		"PublishType_API_PRECISION_SUITE":          10,
		"PublishType_PRECISION_INTERFACE_DOCUMENT": 11,
		"PublishType_API_SERVICE":                  12,
		"PublishType_PERF_CASE":                    13,
		"PublishType_PERF_SUITE":                   14,
		"PublishType_PERF_PLAN":                    15,
		"PublishType_STABILITY_PLAN":               16,
	}
)

func (x PublishType) Enum() *PublishType {
	p := new(PublishType)
	*p = x
	return p
}

func (x PublishType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PublishType) Descriptor() protoreflect.EnumDescriptor {
	return file_dispatcher_publish_proto_enumTypes[0].Descriptor()
}

func (PublishType) Type() protoreflect.EnumType {
	return &file_dispatcher_publish_proto_enumTypes[0]
}

func (x PublishType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PublishType.Descriptor instead.
func (PublishType) EnumDescriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{0}
}

type PlanPublishInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PlanId          string                 `protobuf:"bytes,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	CallbackUrl     string                 `protobuf:"bytes,2,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	CallbackTimeout int64                  `protobuf:"varint,3,opt,name=callback_timeout,json=callbackTimeout,proto3" json:"callback_timeout,omitempty"`
	// repeated string services = 4;
	ApiPlanInfo   *ApiPlanInfo `protobuf:"bytes,5,opt,name=api_plan_info,json=apiPlanInfo,proto3" json:"api_plan_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlanPublishInfo) Reset() {
	*x = PlanPublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlanPublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanPublishInfo) ProtoMessage() {}

func (x *PlanPublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanPublishInfo.ProtoReflect.Descriptor instead.
func (*PlanPublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{0}
}

func (x *PlanPublishInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PlanPublishInfo) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *PlanPublishInfo) GetCallbackTimeout() int64 {
	if x != nil {
		return x.CallbackTimeout
	}
	return 0
}

func (x *PlanPublishInfo) GetApiPlanInfo() *ApiPlanInfo {
	if x != nil {
		return x.ApiPlanInfo
	}
	return nil
}

type InterfaceDocumentPublishInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	CaseExecutionMode   pb.ExecutionMode       `protobuf:"varint,1,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	InterfaceDocumentId string                 `protobuf:"bytes,2,opt,name=interface_document_id,json=interfaceDocumentId,proto3" json:"interface_document_id,omitempty"`                       // 接口文档ID
	InterfaceExecuteId  string                 `protobuf:"bytes,3,opt,name=interface_execute_id,json=interfaceExecuteId,proto3" json:"interface_execute_id,omitempty"`                          // 接口执行ID
	GeneralConfig       *pb1.GeneralConfig     `protobuf:"bytes,4,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                                           // 通用配置信息
	AccountConfig       []*pb1.AccountConfig   `protobuf:"bytes,5,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                                           // 池账号信息
	InterfaceCases      []*pb.ApiExecutionData `protobuf:"bytes,6,rep,name=interface_cases,json=interfaceCases,proto3" json:"interface_cases,omitempty"`                                        // 接口用例列表
	PlanId              string                 `protobuf:"bytes,7,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                // 计划ID
	PlanExecuteId       string                 `protobuf:"bytes,8,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                                         // 计划执行ID
	PlanName            string                 `protobuf:"bytes,9,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                                          // 计划名称
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *InterfaceDocumentPublishInfo) Reset() {
	*x = InterfaceDocumentPublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceDocumentPublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceDocumentPublishInfo) ProtoMessage() {}

func (x *InterfaceDocumentPublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceDocumentPublishInfo.ProtoReflect.Descriptor instead.
func (*InterfaceDocumentPublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{1}
}

func (x *InterfaceDocumentPublishInfo) GetCaseExecutionMode() pb.ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return pb.ExecutionMode(0)
}

func (x *InterfaceDocumentPublishInfo) GetInterfaceDocumentId() string {
	if x != nil {
		return x.InterfaceDocumentId
	}
	return ""
}

func (x *InterfaceDocumentPublishInfo) GetInterfaceExecuteId() string {
	if x != nil {
		return x.InterfaceExecuteId
	}
	return ""
}

func (x *InterfaceDocumentPublishInfo) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *InterfaceDocumentPublishInfo) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *InterfaceDocumentPublishInfo) GetInterfaceCases() []*pb.ApiExecutionData {
	if x != nil {
		return x.InterfaceCases
	}
	return nil
}

func (x *InterfaceDocumentPublishInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *InterfaceDocumentPublishInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *InterfaceDocumentPublishInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type SuitePublishInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CaseExecutionMode pb.ExecutionMode       `protobuf:"varint,1,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	SuiteId           string                 `protobuf:"bytes,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                                                             // 集合ID
	SuiteExecuteId    string                 `protobuf:"bytes,3,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"`                                      // 集合执行ID
	GeneralConfig     *pb1.GeneralConfig     `protobuf:"bytes,4,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                                           // 通用配置信息
	AccountConfig     []*pb1.AccountConfig   `protobuf:"bytes,5,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                                           // 池账号信息
	PlanId            string                 `protobuf:"bytes,6,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                // 计划ID
	PlanExecuteId     string                 `protobuf:"bytes,7,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                                         // 计划执行ID
	Cases             []*pb.ApiExecutionData `protobuf:"bytes,8,rep,name=cases,proto3" json:"cases,omitempty"`                                                                                // 用例列表
	PlanName          string                 `protobuf:"bytes,9,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                                          // 计划名称
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SuitePublishInfo) Reset() {
	*x = SuitePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuitePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuitePublishInfo) ProtoMessage() {}

func (x *SuitePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuitePublishInfo.ProtoReflect.Descriptor instead.
func (*SuitePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{2}
}

func (x *SuitePublishInfo) GetCaseExecutionMode() pb.ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return pb.ExecutionMode(0)
}

func (x *SuitePublishInfo) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *SuitePublishInfo) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *SuitePublishInfo) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *SuitePublishInfo) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *SuitePublishInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *SuitePublishInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *SuitePublishInfo) GetCases() []*pb.ApiExecutionData {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *SuitePublishInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type CasePublishInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CaseId         string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                           // 用例ID
	CaseExecuteId  string                 `protobuf:"bytes,2,opt,name=case_execute_id,json=caseExecuteId,proto3" json:"case_execute_id,omitempty"`    // 用例执行ID
	GeneralConfig  *pb1.GeneralConfig     `protobuf:"bytes,3,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`      // 通用配置信息
	AccountConfig  []*pb1.AccountConfig   `protobuf:"bytes,4,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`      // 池账号信息
	Version        string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`                                       // 版本
	SuiteId        string                 `protobuf:"bytes,6,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                        // 集合ID
	SuiteExecuteId string                 `protobuf:"bytes,7,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"` // 集合执行ID
	CaseName       string                 `protobuf:"bytes,21,opt,name=case_name,json=caseName,proto3" json:"case_name,omitempty"`                    // 用例名称
	MaintainedBy   string                 `protobuf:"bytes,22,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`        // 用例维护人ID
	SuiteName      string                 `protobuf:"bytes,23,opt,name=suite_name,json=suiteName,proto3" json:"suite_name,omitempty"`                 // 集合名称
	PlanId         string                 `protobuf:"bytes,24,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                          // 计划ID
	PlanExecuteId  string                 `protobuf:"bytes,25,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`   // 计划执行ID
	PlanName       string                 `protobuf:"bytes,26,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                    // 计划名称
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CasePublishInfo) Reset() {
	*x = CasePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CasePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CasePublishInfo) ProtoMessage() {}

func (x *CasePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CasePublishInfo.ProtoReflect.Descriptor instead.
func (*CasePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{3}
}

func (x *CasePublishInfo) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CasePublishInfo) GetCaseExecuteId() string {
	if x != nil {
		return x.CaseExecuteId
	}
	return ""
}

func (x *CasePublishInfo) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *CasePublishInfo) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *CasePublishInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CasePublishInfo) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *CasePublishInfo) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *CasePublishInfo) GetCaseName() string {
	if x != nil {
		return x.CaseName
	}
	return ""
}

func (x *CasePublishInfo) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *CasePublishInfo) GetSuiteName() string {
	if x != nil {
		return x.SuiteName
	}
	return ""
}

func (x *CasePublishInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *CasePublishInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *CasePublishInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type InterfaceCasePublishInfo struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	InterfaceCaseId        string                 `protobuf:"bytes,1,opt,name=interface_case_id,json=interfaceCaseId,proto3" json:"interface_case_id,omitempty"`                        // 接口用例ID
	InterfaceCaseExecuteId string                 `protobuf:"bytes,2,opt,name=interface_case_execute_id,json=interfaceCaseExecuteId,proto3" json:"interface_case_execute_id,omitempty"` // 接口用例执行ID
	GeneralConfig          *pb1.GeneralConfig     `protobuf:"bytes,3,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                                // 通用配置信息
	AccountConfig          []*pb1.AccountConfig   `protobuf:"bytes,4,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                                // 池账号信息
	Version                string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`                                                                 // 版本
	InterfaceId            string                 `protobuf:"bytes,6,opt,name=interface_id,json=interfaceId,proto3" json:"interface_id,omitempty"`                                      // 接口ID
	InterfaceExecuteId     string                 `protobuf:"bytes,7,opt,name=interface_execute_id,json=interfaceExecuteId,proto3" json:"interface_execute_id,omitempty"`               // 接口执行ID
	DocumentId             string                 `protobuf:"bytes,8,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`                                         // 接口文档ID
	CaseName               string                 `protobuf:"bytes,21,opt,name=case_name,json=caseName,proto3" json:"case_name,omitempty"`                                              // 接口用例名称
	MaintainedBy           string                 `protobuf:"bytes,22,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                  // 接口用例维护人ID
	DocumentName           string                 `protobuf:"bytes,23,opt,name=document_name,json=documentName,proto3" json:"document_name,omitempty"`                                  // 接口文档名称
	PlanId                 string                 `protobuf:"bytes,24,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                    // 计划ID
	PlanExecuteId          string                 `protobuf:"bytes,25,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                             // 计划执行ID
	PlanName               string                 `protobuf:"bytes,26,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                              // 计划名称
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *InterfaceCasePublishInfo) Reset() {
	*x = InterfaceCasePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceCasePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceCasePublishInfo) ProtoMessage() {}

func (x *InterfaceCasePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceCasePublishInfo.ProtoReflect.Descriptor instead.
func (*InterfaceCasePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{4}
}

func (x *InterfaceCasePublishInfo) GetInterfaceCaseId() string {
	if x != nil {
		return x.InterfaceCaseId
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetInterfaceCaseExecuteId() string {
	if x != nil {
		return x.InterfaceCaseExecuteId
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *InterfaceCasePublishInfo) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *InterfaceCasePublishInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetInterfaceId() string {
	if x != nil {
		return x.InterfaceId
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetInterfaceExecuteId() string {
	if x != nil {
		return x.InterfaceExecuteId
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetCaseName() string {
	if x != nil {
		return x.CaseName
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetDocumentName() string {
	if x != nil {
		return x.DocumentName
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *InterfaceCasePublishInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type ComponentGroupPublishInfo struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	ComponentGroupId        string                 `protobuf:"bytes,1,opt,name=component_group_id,json=componentGroupId,proto3" json:"component_group_id,omitempty"`                        // 业务组件ID
	ComponentGroupExecuteId string                 `protobuf:"bytes,2,opt,name=component_group_execute_id,json=componentGroupExecuteId,proto3" json:"component_group_execute_id,omitempty"` // 业务组件执行ID
	GeneralConfig           *pb1.GeneralConfig     `protobuf:"bytes,3,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                                   // 通用配置信息
	AccountConfig           []*pb1.AccountConfig   `protobuf:"bytes,4,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                                   // 池账号信息
	Version                 string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`                                                                    // 版本
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *ComponentGroupPublishInfo) Reset() {
	*x = ComponentGroupPublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComponentGroupPublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentGroupPublishInfo) ProtoMessage() {}

func (x *ComponentGroupPublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentGroupPublishInfo.ProtoReflect.Descriptor instead.
func (*ComponentGroupPublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{5}
}

func (x *ComponentGroupPublishInfo) GetComponentGroupId() string {
	if x != nil {
		return x.ComponentGroupId
	}
	return ""
}

func (x *ComponentGroupPublishInfo) GetComponentGroupExecuteId() string {
	if x != nil {
		return x.ComponentGroupExecuteId
	}
	return ""
}

func (x *ComponentGroupPublishInfo) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *ComponentGroupPublishInfo) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *ComponentGroupPublishInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type UICasePublishInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	UiCaseId         string                 `protobuf:"bytes,1,opt,name=ui_case_id,json=uiCaseId,proto3" json:"ui_case_id,omitempty"`                           // 用例id
	UiCaseExecuteId  string                 `protobuf:"bytes,2,opt,name=ui_case_execute_id,json=uiCaseExecuteId,proto3" json:"ui_case_execute_id,omitempty"`    // 用例执行id
	UiSuiteId        string                 `protobuf:"bytes,3,opt,name=ui_suite_id,json=uiSuiteId,proto3" json:"ui_suite_id,omitempty"`                        // 父集合id
	UiSuiteExecuteId string                 `protobuf:"bytes,4,opt,name=ui_suite_execute_id,json=uiSuiteExecuteId,proto3" json:"ui_suite_execute_id,omitempty"` // 父集合执行id
	UiPlanId         string                 `protobuf:"bytes,5,opt,name=ui_plan_id,json=uiPlanId,proto3" json:"ui_plan_id,omitempty"`                           // 父计划id
	UiPlanExecuteId  string                 `protobuf:"bytes,6,opt,name=ui_plan_execute_id,json=uiPlanExecuteId,proto3" json:"ui_plan_execute_id,omitempty"`    // 父计划执行id
	UiCase           *pb.ApiExecutionData   `protobuf:"bytes,7,opt,name=ui_case,json=uiCase,proto3" json:"ui_case,omitempty"`
	MetaData         *pb.UIPlanMetaData     `protobuf:"bytes,8,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"` // ui用例数据
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UICasePublishInfo) Reset() {
	*x = UICasePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UICasePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UICasePublishInfo) ProtoMessage() {}

func (x *UICasePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UICasePublishInfo.ProtoReflect.Descriptor instead.
func (*UICasePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{6}
}

func (x *UICasePublishInfo) GetUiCaseId() string {
	if x != nil {
		return x.UiCaseId
	}
	return ""
}

func (x *UICasePublishInfo) GetUiCaseExecuteId() string {
	if x != nil {
		return x.UiCaseExecuteId
	}
	return ""
}

func (x *UICasePublishInfo) GetUiSuiteId() string {
	if x != nil {
		return x.UiSuiteId
	}
	return ""
}

func (x *UICasePublishInfo) GetUiSuiteExecuteId() string {
	if x != nil {
		return x.UiSuiteExecuteId
	}
	return ""
}

func (x *UICasePublishInfo) GetUiPlanId() string {
	if x != nil {
		return x.UiPlanId
	}
	return ""
}

func (x *UICasePublishInfo) GetUiPlanExecuteId() string {
	if x != nil {
		return x.UiPlanExecuteId
	}
	return ""
}

func (x *UICasePublishInfo) GetUiCase() *pb.ApiExecutionData {
	if x != nil {
		return x.UiCase
	}
	return nil
}

func (x *UICasePublishInfo) GetMetaData() *pb.UIPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

type UISuitePublishInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CaseExecutionMode pb.ExecutionMode       `protobuf:"varint,1,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	UiSuiteId         string                 `protobuf:"bytes,2,opt,name=ui_suite_id,json=uiSuiteId,proto3" json:"ui_suite_id,omitempty"`                                                     // 集合id
	UiSuiteExecuteId  string                 `protobuf:"bytes,3,opt,name=ui_suite_execute_id,json=uiSuiteExecuteId,proto3" json:"ui_suite_execute_id,omitempty"`                              // 集合执行id
	UiPlanId          string                 `protobuf:"bytes,4,opt,name=ui_plan_id,json=uiPlanId,proto3" json:"ui_plan_id,omitempty"`                                                        // 父计划id
	UiPlanExecuteId   string                 `protobuf:"bytes,5,opt,name=ui_plan_execute_id,json=uiPlanExecuteId,proto3" json:"ui_plan_execute_id,omitempty"`                                 // 父计划执行id
	UiCases           []*pb.ApiExecutionData `protobuf:"bytes,6,rep,name=ui_cases,json=uiCases,proto3" json:"ui_cases,omitempty"`                                                             // 用例列表
	UiSuite           *pb.ApiExecutionData   `protobuf:"bytes,7,opt,name=ui_suite,json=uiSuite,proto3" json:"ui_suite,omitempty"`
	MetaData          *pb.UIPlanMetaData     `protobuf:"bytes,8,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"` // ui集合数据
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UISuitePublishInfo) Reset() {
	*x = UISuitePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UISuitePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UISuitePublishInfo) ProtoMessage() {}

func (x *UISuitePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UISuitePublishInfo.ProtoReflect.Descriptor instead.
func (*UISuitePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{7}
}

func (x *UISuitePublishInfo) GetCaseExecutionMode() pb.ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return pb.ExecutionMode(0)
}

func (x *UISuitePublishInfo) GetUiSuiteId() string {
	if x != nil {
		return x.UiSuiteId
	}
	return ""
}

func (x *UISuitePublishInfo) GetUiSuiteExecuteId() string {
	if x != nil {
		return x.UiSuiteExecuteId
	}
	return ""
}

func (x *UISuitePublishInfo) GetUiPlanId() string {
	if x != nil {
		return x.UiPlanId
	}
	return ""
}

func (x *UISuitePublishInfo) GetUiPlanExecuteId() string {
	if x != nil {
		return x.UiPlanExecuteId
	}
	return ""
}

func (x *UISuitePublishInfo) GetUiCases() []*pb.ApiExecutionData {
	if x != nil {
		return x.UiCases
	}
	return nil
}

func (x *UISuitePublishInfo) GetUiSuite() *pb.ApiExecutionData {
	if x != nil {
		return x.UiSuite
	}
	return nil
}

func (x *UISuitePublishInfo) GetMetaData() *pb.UIPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

type UIPlanPublishInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UiPlanId        string                 `protobuf:"bytes,1,opt,name=ui_plan_id,json=uiPlanId,proto3" json:"ui_plan_id,omitempty"`
	CallbackUrl     string                 `protobuf:"bytes,2,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	CallbackTimeout int64                  `protobuf:"varint,3,opt,name=callback_timeout,json=callbackTimeout,proto3" json:"callback_timeout,omitempty"`
	UiPlanInfo      *UiPlanInfo            `protobuf:"bytes,4,opt,name=ui_plan_info,json=uiPlanInfo,proto3" json:"ui_plan_info,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UIPlanPublishInfo) Reset() {
	*x = UIPlanPublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIPlanPublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIPlanPublishInfo) ProtoMessage() {}

func (x *UIPlanPublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIPlanPublishInfo.ProtoReflect.Descriptor instead.
func (*UIPlanPublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{8}
}

func (x *UIPlanPublishInfo) GetUiPlanId() string {
	if x != nil {
		return x.UiPlanId
	}
	return ""
}

func (x *UIPlanPublishInfo) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *UIPlanPublishInfo) GetCallbackTimeout() int64 {
	if x != nil {
		return x.CallbackTimeout
	}
	return 0
}

func (x *UIPlanPublishInfo) GetUiPlanInfo() *UiPlanInfo {
	if x != nil {
		return x.UiPlanInfo
	}
	return nil
}

type PrecisionSuitePublishInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CaseExecutionMode pb.ExecutionMode       `protobuf:"varint,1,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	SuiteId           string                 `protobuf:"bytes,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                                                             // 集合ID
	SuiteExecuteId    string                 `protobuf:"bytes,3,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"`                                      // 集合执行ID
	GeneralConfig     *pb1.GeneralConfig     `protobuf:"bytes,4,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                                           // 通用配置信息
	AccountConfig     []*pb1.AccountConfig   `protobuf:"bytes,5,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                                           // 池账号信息
	PlanId            string                 `protobuf:"bytes,6,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                // 计划ID
	PlanExecuteId     string                 `protobuf:"bytes,7,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                                         // 计划执行ID
	Cases             []*pb.ApiExecutionData `protobuf:"bytes,8,rep,name=cases,proto3" json:"cases,omitempty"`                                                                                // 用例列表
	SuiteName         string                 `protobuf:"bytes,9,opt,name=suite_name,json=suiteName,proto3" json:"suite_name,omitempty"`                                                       // 集合名称
	PlanName          string                 `protobuf:"bytes,10,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                                         // 计划名称
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PrecisionSuitePublishInfo) Reset() {
	*x = PrecisionSuitePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrecisionSuitePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrecisionSuitePublishInfo) ProtoMessage() {}

func (x *PrecisionSuitePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrecisionSuitePublishInfo.ProtoReflect.Descriptor instead.
func (*PrecisionSuitePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{9}
}

func (x *PrecisionSuitePublishInfo) GetCaseExecutionMode() pb.ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return pb.ExecutionMode(0)
}

func (x *PrecisionSuitePublishInfo) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *PrecisionSuitePublishInfo) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *PrecisionSuitePublishInfo) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *PrecisionSuitePublishInfo) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *PrecisionSuitePublishInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PrecisionSuitePublishInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *PrecisionSuitePublishInfo) GetCases() []*pb.ApiExecutionData {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *PrecisionSuitePublishInfo) GetSuiteName() string {
	if x != nil {
		return x.SuiteName
	}
	return ""
}

func (x *PrecisionSuitePublishInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type PrecisionInterfaceDocumentPublishInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	CaseExecutionMode   pb.ExecutionMode       `protobuf:"varint,1,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	InterfaceDocumentId string                 `protobuf:"bytes,2,opt,name=interface_document_id,json=interfaceDocumentId,proto3" json:"interface_document_id,omitempty"`                       // 接口文档ID
	InterfaceExecuteId  string                 `protobuf:"bytes,3,opt,name=interface_execute_id,json=interfaceExecuteId,proto3" json:"interface_execute_id,omitempty"`                          // 接口执行ID
	GeneralConfig       *pb1.GeneralConfig     `protobuf:"bytes,4,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                                           // 通用配置信息
	AccountConfig       []*pb1.AccountConfig   `protobuf:"bytes,5,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                                           // 池账号信息
	InterfaceCases      []*pb.ApiExecutionData `protobuf:"bytes,6,rep,name=interface_cases,json=interfaceCases,proto3" json:"interface_cases,omitempty"`                                        // 接口用例列表
	PlanId              string                 `protobuf:"bytes,7,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                // 计划ID
	PlanExecuteId       string                 `protobuf:"bytes,8,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                                         // 计划执行ID
	DocumentName        string                 `protobuf:"bytes,9,opt,name=document_name,json=documentName,proto3" json:"document_name,omitempty"`                                              // 接口文档名称
	PlanName            string                 `protobuf:"bytes,10,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                                         // 计划名称
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PrecisionInterfaceDocumentPublishInfo) Reset() {
	*x = PrecisionInterfaceDocumentPublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrecisionInterfaceDocumentPublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrecisionInterfaceDocumentPublishInfo) ProtoMessage() {}

func (x *PrecisionInterfaceDocumentPublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrecisionInterfaceDocumentPublishInfo.ProtoReflect.Descriptor instead.
func (*PrecisionInterfaceDocumentPublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{10}
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetCaseExecutionMode() pb.ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return pb.ExecutionMode(0)
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetInterfaceDocumentId() string {
	if x != nil {
		return x.InterfaceDocumentId
	}
	return ""
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetInterfaceExecuteId() string {
	if x != nil {
		return x.InterfaceExecuteId
	}
	return ""
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetInterfaceCases() []*pb.ApiExecutionData {
	if x != nil {
		return x.InterfaceCases
	}
	return nil
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetDocumentName() string {
	if x != nil {
		return x.DocumentName
	}
	return ""
}

func (x *PrecisionInterfaceDocumentPublishInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type ServicePublishInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ServiceId        string                 `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`                        // 服务ID
	ServiceExecuteId string                 `protobuf:"bytes,2,opt,name=service_execute_id,json=serviceExecuteId,proto3" json:"service_execute_id,omitempty"` // 服务执行ID
	ServiceName      string                 `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`                  // 服务名称
	GeneralConfig    *pb1.GeneralConfig     `protobuf:"bytes,4,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`            // 通用配置信息
	AccountConfig    []*pb1.AccountConfig   `protobuf:"bytes,5,rep,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`            // 池账号信息
	PlanId           string                 `protobuf:"bytes,6,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                 // 计划ID
	PlanExecuteId    string                 `protobuf:"bytes,7,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`          // 计划执行ID
	Cases            []*pb.ApiExecutionData `protobuf:"bytes,8,rep,name=cases,proto3" json:"cases,omitempty"`                                                 // 用例列表
	InterfaceCases   []*pb.ApiExecutionData `protobuf:"bytes,9,rep,name=interface_cases,json=interfaceCases,proto3" json:"interface_cases,omitempty"`         // 接口用例列表
	PlanName         string                 `protobuf:"bytes,10,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                          // 计划名称
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ServicePublishInfo) Reset() {
	*x = ServicePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServicePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServicePublishInfo) ProtoMessage() {}

func (x *ServicePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServicePublishInfo.ProtoReflect.Descriptor instead.
func (*ServicePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{11}
}

func (x *ServicePublishInfo) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServicePublishInfo) GetServiceExecuteId() string {
	if x != nil {
		return x.ServiceExecuteId
	}
	return ""
}

func (x *ServicePublishInfo) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServicePublishInfo) GetGeneralConfig() *pb1.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *ServicePublishInfo) GetAccountConfig() []*pb1.AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *ServicePublishInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *ServicePublishInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *ServicePublishInfo) GetCases() []*pb.ApiExecutionData {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *ServicePublishInfo) GetInterfaceCases() []*pb.ApiExecutionData {
	if x != nil {
		return x.InterfaceCases
	}
	return nil
}

func (x *ServicePublishInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

type PerfCasePublishInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PerfCaseId         string                 `protobuf:"bytes,1,opt,name=perf_case_id,json=perfCaseId,proto3" json:"perf_case_id,omitempty"`                           // 用例ID
	PerfCaseExecuteId  string                 `protobuf:"bytes,2,opt,name=perf_case_execute_id,json=perfCaseExecuteId,proto3" json:"perf_case_execute_id,omitempty"`    // 用例执行ID
	PerfSuiteId        string                 `protobuf:"bytes,3,opt,name=perf_suite_id,json=perfSuiteId,proto3" json:"perf_suite_id,omitempty"`                        // 集合ID
	PerfSuiteExecuteId string                 `protobuf:"bytes,4,opt,name=perf_suite_execute_id,json=perfSuiteExecuteId,proto3" json:"perf_suite_execute_id,omitempty"` // 集合执行ID
	PerfPlanId         string                 `protobuf:"bytes,5,opt,name=perf_plan_id,json=perfPlanId,proto3" json:"perf_plan_id,omitempty"`                           // 计划ID
	PerfPlanExecuteId  string                 `protobuf:"bytes,6,opt,name=perf_plan_execute_id,json=perfPlanExecuteId,proto3" json:"perf_plan_execute_id,omitempty"`    // 计划执行ID
	PerfCase           *pb.ApiExecutionData   `protobuf:"bytes,11,opt,name=perf_case,json=perfCase,proto3" json:"perf_case,omitempty"`                                  // 用例数据
	MetaData           *pb.PerfPlanMetaData   `protobuf:"bytes,12,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                                  // 计划元数据
	PerfPlanInfo       *PerfPlanInfo          `protobuf:"bytes,21,opt,name=perf_plan_info,json=perfPlanInfo,proto3" json:"perf_plan_info,omitempty"`                    // 计划执行信息
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PerfCasePublishInfo) Reset() {
	*x = PerfCasePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCasePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCasePublishInfo) ProtoMessage() {}

func (x *PerfCasePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCasePublishInfo.ProtoReflect.Descriptor instead.
func (*PerfCasePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{12}
}

func (x *PerfCasePublishInfo) GetPerfCaseId() string {
	if x != nil {
		return x.PerfCaseId
	}
	return ""
}

func (x *PerfCasePublishInfo) GetPerfCaseExecuteId() string {
	if x != nil {
		return x.PerfCaseExecuteId
	}
	return ""
}

func (x *PerfCasePublishInfo) GetPerfSuiteId() string {
	if x != nil {
		return x.PerfSuiteId
	}
	return ""
}

func (x *PerfCasePublishInfo) GetPerfSuiteExecuteId() string {
	if x != nil {
		return x.PerfSuiteExecuteId
	}
	return ""
}

func (x *PerfCasePublishInfo) GetPerfPlanId() string {
	if x != nil {
		return x.PerfPlanId
	}
	return ""
}

func (x *PerfCasePublishInfo) GetPerfPlanExecuteId() string {
	if x != nil {
		return x.PerfPlanExecuteId
	}
	return ""
}

func (x *PerfCasePublishInfo) GetPerfCase() *pb.ApiExecutionData {
	if x != nil {
		return x.PerfCase
	}
	return nil
}

func (x *PerfCasePublishInfo) GetMetaData() *pb.PerfPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *PerfCasePublishInfo) GetPerfPlanInfo() *PerfPlanInfo {
	if x != nil {
		return x.PerfPlanInfo
	}
	return nil
}

type PerfSuitePublishInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PerfSuiteId        string                 `protobuf:"bytes,1,opt,name=perf_suite_id,json=perfSuiteId,proto3" json:"perf_suite_id,omitempty"`                                                // 集合ID
	PerfSuiteExecuteId string                 `protobuf:"bytes,2,opt,name=perf_suite_execute_id,json=perfSuiteExecuteId,proto3" json:"perf_suite_execute_id,omitempty"`                         // 集合执行ID
	PerfPlanId         string                 `protobuf:"bytes,3,opt,name=perf_plan_id,json=perfPlanId,proto3" json:"perf_plan_id,omitempty"`                                                   // 计划ID
	PerfPlanExecuteId  string                 `protobuf:"bytes,4,opt,name=perf_plan_execute_id,json=perfPlanExecuteId,proto3" json:"perf_plan_execute_id,omitempty"`                            // 计划执行ID
	PerfSuite          *pb.ApiExecutionData   `protobuf:"bytes,11,opt,name=perf_suite,json=perfSuite,proto3" json:"perf_suite,omitempty"`                                                       // 集合数据
	PerfCases          []*pb.ApiExecutionData `protobuf:"bytes,12,rep,name=perf_cases,json=perfCases,proto3" json:"perf_cases,omitempty"`                                                       // 用例数据
	CaseExecutionMode  pb.ExecutionMode       `protobuf:"varint,13,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	MetaData           *pb.PerfPlanMetaData   `protobuf:"bytes,14,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`                                                          // 计划元数据
	PerfPlanInfo       *PerfPlanInfo          `protobuf:"bytes,21,opt,name=perf_plan_info,json=perfPlanInfo,proto3" json:"perf_plan_info,omitempty"`                                            // 计划执行信息
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PerfSuitePublishInfo) Reset() {
	*x = PerfSuitePublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfSuitePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfSuitePublishInfo) ProtoMessage() {}

func (x *PerfSuitePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfSuitePublishInfo.ProtoReflect.Descriptor instead.
func (*PerfSuitePublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{13}
}

func (x *PerfSuitePublishInfo) GetPerfSuiteId() string {
	if x != nil {
		return x.PerfSuiteId
	}
	return ""
}

func (x *PerfSuitePublishInfo) GetPerfSuiteExecuteId() string {
	if x != nil {
		return x.PerfSuiteExecuteId
	}
	return ""
}

func (x *PerfSuitePublishInfo) GetPerfPlanId() string {
	if x != nil {
		return x.PerfPlanId
	}
	return ""
}

func (x *PerfSuitePublishInfo) GetPerfPlanExecuteId() string {
	if x != nil {
		return x.PerfPlanExecuteId
	}
	return ""
}

func (x *PerfSuitePublishInfo) GetPerfSuite() *pb.ApiExecutionData {
	if x != nil {
		return x.PerfSuite
	}
	return nil
}

func (x *PerfSuitePublishInfo) GetPerfCases() []*pb.ApiExecutionData {
	if x != nil {
		return x.PerfCases
	}
	return nil
}

func (x *PerfSuitePublishInfo) GetCaseExecutionMode() pb.ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return pb.ExecutionMode(0)
}

func (x *PerfSuitePublishInfo) GetMetaData() *pb.PerfPlanMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *PerfSuitePublishInfo) GetPerfPlanInfo() *PerfPlanInfo {
	if x != nil {
		return x.PerfPlanInfo
	}
	return nil
}

type PerfPlanPublishInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PerfPlanId      string                 `protobuf:"bytes,1,opt,name=perf_plan_id,json=perfPlanId,proto3" json:"perf_plan_id,omitempty"`                // 计划ID
	CallbackUrl     string                 `protobuf:"bytes,11,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`              // 回调地址
	CallbackTimeout int64                  `protobuf:"varint,12,opt,name=callback_timeout,json=callbackTimeout,proto3" json:"callback_timeout,omitempty"` // 回调超时时间
	PerfPlanInfo    *PerfPlanInfo          `protobuf:"bytes,21,opt,name=perf_plan_info,json=perfPlanInfo,proto3" json:"perf_plan_info,omitempty"`         // 计划执行信息
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PerfPlanPublishInfo) Reset() {
	*x = PerfPlanPublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlanPublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlanPublishInfo) ProtoMessage() {}

func (x *PerfPlanPublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlanPublishInfo.ProtoReflect.Descriptor instead.
func (*PerfPlanPublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{14}
}

func (x *PerfPlanPublishInfo) GetPerfPlanId() string {
	if x != nil {
		return x.PerfPlanId
	}
	return ""
}

func (x *PerfPlanPublishInfo) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *PerfPlanPublishInfo) GetCallbackTimeout() int64 {
	if x != nil {
		return x.CallbackTimeout
	}
	return 0
}

func (x *PerfPlanPublishInfo) GetPerfPlanInfo() *PerfPlanInfo {
	if x != nil {
		return x.PerfPlanInfo
	}
	return nil
}

type StabilityPlanPublishInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	StabilityPlanId string                 `protobuf:"bytes,1,opt,name=stability_plan_id,json=stabilityPlanId,proto3" json:"stability_plan_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StabilityPlanPublishInfo) Reset() {
	*x = StabilityPlanPublishInfo{}
	mi := &file_dispatcher_publish_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityPlanPublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityPlanPublishInfo) ProtoMessage() {}

func (x *StabilityPlanPublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_publish_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityPlanPublishInfo.ProtoReflect.Descriptor instead.
func (*StabilityPlanPublishInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_publish_proto_rawDescGZIP(), []int{15}
}

func (x *StabilityPlanPublishInfo) GetStabilityPlanId() string {
	if x != nil {
		return x.StabilityPlanId
	}
	return ""
}

var File_dispatcher_publish_proto protoreflect.FileDescriptor

var file_dispatcher_publish_proto_rawDesc = []byte{
	0x0a, 0x18, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x15, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbb, 0x01, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x6e, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61,
	0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x3b, 0x0a, 0x0d, 0x61, 0x70, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0b, 0x61, 0x70, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x04,
	0x08, 0x04, 0x10, 0x05, 0x22, 0xea, 0x03, 0x0a, 0x1c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61,
	0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a,
	0x15, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x42, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61,
	0x73, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xaa, 0x03, 0x0a, 0x10, 0x53, 0x75, 0x69, 0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73,
	0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xec,
	0x03, 0x0a, 0x0f, 0x43, 0x61, 0x73, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd2, 0x04,
	0x0a, 0x18, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x66, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x9c, 0x02, 0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x3b,
	0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x17, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0xe2, 0x02, 0x0a, 0x11, 0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x43,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x75, 0x69, 0x43, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12,
	0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x69, 0x50,
	0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x07,
	0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x75, 0x69, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x34, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49,
	0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x22, 0x98, 0x03, 0x0a, 0x12, 0x55, 0x49, 0x53, 0x75, 0x69,
	0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a,
	0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x69, 0x53, 0x75,
	0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e,
	0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x34, 0x0a, 0x08, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x75, 0x69,
	0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x08, 0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x07, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x6d,
	0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x22, 0xb9, 0x01, 0x0a, 0x11, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x50,
	0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x12, 0x38, 0x0a, 0x0c, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x75, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xd2, 0x03,
	0x0a, 0x19, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x69, 0x74, 0x65,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a, 0x13, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41,
	0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x98, 0x04, 0x0a, 0x25, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a, 0x13,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x42, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61,
	0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c,
	0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd3, 0x03,
	0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x2f, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x12, 0x42, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0xc2, 0x03, 0x0a, 0x13, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0c, 0x70,
	0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a,
	0x14, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72,
	0x66, 0x43, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x0d, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72,
	0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x66, 0x5f,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x36, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x66,
	0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xf4, 0x03, 0x0a, 0x14, 0x50, 0x65, 0x72,
	0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75,
	0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x65,
	0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c,
	0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0a, 0x70,
	0x65, 0x72, 0x66, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x70, 0x65, 0x72, 0x66,
	0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12,
	0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x3e, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0xc5, 0x01, 0x0a, 0x13, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x10,
	0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x3e, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x66, 0x5f,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72,
	0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x50,
	0x6c, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x46, 0x0a, 0x18, 0x53, 0x74, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x2a,
	0x8c, 0x04, 0x0a, 0x0b, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x17, 0x0a, 0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x01, 0x12, 0x18, 0x0a,
	0x14, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49,
	0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45,
	0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x06,
	0x12, 0x17, 0x0a, 0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x55, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x07, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x53, 0x55, 0x49, 0x54,
	0x45, 0x10, 0x08, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x55, 0x49, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x09, 0x12, 0x23, 0x0a, 0x1f,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x49, 0x5f,
	0x50, 0x52, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10,
	0x0a, 0x12, 0x2c, 0x0a, 0x28, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x50, 0x52, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0b, 0x12,
	0x1b, 0x0a, 0x17, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41,
	0x50, 0x49, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x45, 0x52, 0x46,
	0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x0d, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x5f, 0x53, 0x55, 0x49, 0x54,
	0x45, 0x10, 0x0e, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x0f, 0x12, 0x1e,
	0x0a, 0x1a, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x54,
	0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x10, 0x42, 0x44,
	0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_publish_proto_rawDescOnce sync.Once
	file_dispatcher_publish_proto_rawDescData = file_dispatcher_publish_proto_rawDesc
)

func file_dispatcher_publish_proto_rawDescGZIP() []byte {
	file_dispatcher_publish_proto_rawDescOnce.Do(func() {
		file_dispatcher_publish_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_publish_proto_rawDescData)
	})
	return file_dispatcher_publish_proto_rawDescData
}

var file_dispatcher_publish_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_dispatcher_publish_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_dispatcher_publish_proto_goTypes = []any{
	(PublishType)(0),                              // 0: dispatcher.PublishType
	(*PlanPublishInfo)(nil),                       // 1: dispatcher.PlanPublishInfo
	(*InterfaceDocumentPublishInfo)(nil),          // 2: dispatcher.InterfaceDocumentPublishInfo
	(*SuitePublishInfo)(nil),                      // 3: dispatcher.SuitePublishInfo
	(*CasePublishInfo)(nil),                       // 4: dispatcher.CasePublishInfo
	(*InterfaceCasePublishInfo)(nil),              // 5: dispatcher.InterfaceCasePublishInfo
	(*ComponentGroupPublishInfo)(nil),             // 6: dispatcher.ComponentGroupPublishInfo
	(*UICasePublishInfo)(nil),                     // 7: dispatcher.UICasePublishInfo
	(*UISuitePublishInfo)(nil),                    // 8: dispatcher.UISuitePublishInfo
	(*UIPlanPublishInfo)(nil),                     // 9: dispatcher.UIPlanPublishInfo
	(*PrecisionSuitePublishInfo)(nil),             // 10: dispatcher.PrecisionSuitePublishInfo
	(*PrecisionInterfaceDocumentPublishInfo)(nil), // 11: dispatcher.PrecisionInterfaceDocumentPublishInfo
	(*ServicePublishInfo)(nil),                    // 12: dispatcher.ServicePublishInfo
	(*PerfCasePublishInfo)(nil),                   // 13: dispatcher.PerfCasePublishInfo
	(*PerfSuitePublishInfo)(nil),                  // 14: dispatcher.PerfSuitePublishInfo
	(*PerfPlanPublishInfo)(nil),                   // 15: dispatcher.PerfPlanPublishInfo
	(*StabilityPlanPublishInfo)(nil),              // 16: dispatcher.StabilityPlanPublishInfo
	(*ApiPlanInfo)(nil),                           // 17: dispatcher.ApiPlanInfo
	(pb.ExecutionMode)(0),                         // 18: manager.ExecutionMode
	(*pb1.GeneralConfig)(nil),                     // 19: common.GeneralConfig
	(*pb1.AccountConfig)(nil),                     // 20: common.AccountConfig
	(*pb.ApiExecutionData)(nil),                   // 21: manager.ApiExecutionData
	(*pb.UIPlanMetaData)(nil),                     // 22: manager.UIPlanMetaData
	(*UiPlanInfo)(nil),                            // 23: dispatcher.UiPlanInfo
	(*pb.PerfPlanMetaData)(nil),                   // 24: manager.PerfPlanMetaData
	(*PerfPlanInfo)(nil),                          // 25: dispatcher.PerfPlanInfo
}
var file_dispatcher_publish_proto_depIdxs = []int32{
	17, // 0: dispatcher.PlanPublishInfo.api_plan_info:type_name -> dispatcher.ApiPlanInfo
	18, // 1: dispatcher.InterfaceDocumentPublishInfo.case_execution_mode:type_name -> manager.ExecutionMode
	19, // 2: dispatcher.InterfaceDocumentPublishInfo.general_config:type_name -> common.GeneralConfig
	20, // 3: dispatcher.InterfaceDocumentPublishInfo.account_config:type_name -> common.AccountConfig
	21, // 4: dispatcher.InterfaceDocumentPublishInfo.interface_cases:type_name -> manager.ApiExecutionData
	18, // 5: dispatcher.SuitePublishInfo.case_execution_mode:type_name -> manager.ExecutionMode
	19, // 6: dispatcher.SuitePublishInfo.general_config:type_name -> common.GeneralConfig
	20, // 7: dispatcher.SuitePublishInfo.account_config:type_name -> common.AccountConfig
	21, // 8: dispatcher.SuitePublishInfo.cases:type_name -> manager.ApiExecutionData
	19, // 9: dispatcher.CasePublishInfo.general_config:type_name -> common.GeneralConfig
	20, // 10: dispatcher.CasePublishInfo.account_config:type_name -> common.AccountConfig
	19, // 11: dispatcher.InterfaceCasePublishInfo.general_config:type_name -> common.GeneralConfig
	20, // 12: dispatcher.InterfaceCasePublishInfo.account_config:type_name -> common.AccountConfig
	19, // 13: dispatcher.ComponentGroupPublishInfo.general_config:type_name -> common.GeneralConfig
	20, // 14: dispatcher.ComponentGroupPublishInfo.account_config:type_name -> common.AccountConfig
	21, // 15: dispatcher.UICasePublishInfo.ui_case:type_name -> manager.ApiExecutionData
	22, // 16: dispatcher.UICasePublishInfo.meta_data:type_name -> manager.UIPlanMetaData
	18, // 17: dispatcher.UISuitePublishInfo.case_execution_mode:type_name -> manager.ExecutionMode
	21, // 18: dispatcher.UISuitePublishInfo.ui_cases:type_name -> manager.ApiExecutionData
	21, // 19: dispatcher.UISuitePublishInfo.ui_suite:type_name -> manager.ApiExecutionData
	22, // 20: dispatcher.UISuitePublishInfo.meta_data:type_name -> manager.UIPlanMetaData
	23, // 21: dispatcher.UIPlanPublishInfo.ui_plan_info:type_name -> dispatcher.UiPlanInfo
	18, // 22: dispatcher.PrecisionSuitePublishInfo.case_execution_mode:type_name -> manager.ExecutionMode
	19, // 23: dispatcher.PrecisionSuitePublishInfo.general_config:type_name -> common.GeneralConfig
	20, // 24: dispatcher.PrecisionSuitePublishInfo.account_config:type_name -> common.AccountConfig
	21, // 25: dispatcher.PrecisionSuitePublishInfo.cases:type_name -> manager.ApiExecutionData
	18, // 26: dispatcher.PrecisionInterfaceDocumentPublishInfo.case_execution_mode:type_name -> manager.ExecutionMode
	19, // 27: dispatcher.PrecisionInterfaceDocumentPublishInfo.general_config:type_name -> common.GeneralConfig
	20, // 28: dispatcher.PrecisionInterfaceDocumentPublishInfo.account_config:type_name -> common.AccountConfig
	21, // 29: dispatcher.PrecisionInterfaceDocumentPublishInfo.interface_cases:type_name -> manager.ApiExecutionData
	19, // 30: dispatcher.ServicePublishInfo.general_config:type_name -> common.GeneralConfig
	20, // 31: dispatcher.ServicePublishInfo.account_config:type_name -> common.AccountConfig
	21, // 32: dispatcher.ServicePublishInfo.cases:type_name -> manager.ApiExecutionData
	21, // 33: dispatcher.ServicePublishInfo.interface_cases:type_name -> manager.ApiExecutionData
	21, // 34: dispatcher.PerfCasePublishInfo.perf_case:type_name -> manager.ApiExecutionData
	24, // 35: dispatcher.PerfCasePublishInfo.meta_data:type_name -> manager.PerfPlanMetaData
	25, // 36: dispatcher.PerfCasePublishInfo.perf_plan_info:type_name -> dispatcher.PerfPlanInfo
	21, // 37: dispatcher.PerfSuitePublishInfo.perf_suite:type_name -> manager.ApiExecutionData
	21, // 38: dispatcher.PerfSuitePublishInfo.perf_cases:type_name -> manager.ApiExecutionData
	18, // 39: dispatcher.PerfSuitePublishInfo.case_execution_mode:type_name -> manager.ExecutionMode
	24, // 40: dispatcher.PerfSuitePublishInfo.meta_data:type_name -> manager.PerfPlanMetaData
	25, // 41: dispatcher.PerfSuitePublishInfo.perf_plan_info:type_name -> dispatcher.PerfPlanInfo
	25, // 42: dispatcher.PerfPlanPublishInfo.perf_plan_info:type_name -> dispatcher.PerfPlanInfo
	43, // [43:43] is the sub-list for method output_type
	43, // [43:43] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_dispatcher_publish_proto_init() }
func file_dispatcher_publish_proto_init() {
	if File_dispatcher_publish_proto != nil {
		return
	}
	file_dispatcher_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_publish_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_publish_proto_goTypes,
		DependencyIndexes: file_dispatcher_publish_proto_depIdxs,
		EnumInfos:         file_dispatcher_publish_proto_enumTypes,
		MessageInfos:      file_dispatcher_publish_proto_msgTypes,
	}.Build()
	File_dispatcher_publish_proto = out.File
	file_dispatcher_publish_proto_rawDesc = nil
	file_dispatcher_publish_proto_goTypes = nil
	file_dispatcher_publish_proto_depIdxs = nil
}
