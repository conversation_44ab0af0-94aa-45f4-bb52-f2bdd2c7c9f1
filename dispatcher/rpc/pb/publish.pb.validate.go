// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/publish.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.ExecutionMode(0)
)

// Validate checks the field values on PlanPublishInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PlanPublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlanPublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlanPublishInfoMultiError, or nil if none found.
func (m *PlanPublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PlanPublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanId

	// no validation rules for CallbackUrl

	// no validation rules for CallbackTimeout

	if all {
		switch v := interface{}(m.GetApiPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlanPublishInfoValidationError{
					field:  "ApiPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlanPublishInfoValidationError{
					field:  "ApiPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApiPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlanPublishInfoValidationError{
				field:  "ApiPlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PlanPublishInfoMultiError(errors)
	}

	return nil
}

// PlanPublishInfoMultiError is an error wrapping multiple validation errors
// returned by PlanPublishInfo.ValidateAll() if the designated constraints
// aren't met.
type PlanPublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlanPublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlanPublishInfoMultiError) AllErrors() []error { return m }

// PlanPublishInfoValidationError is the validation error returned by
// PlanPublishInfo.Validate if the designated constraints aren't met.
type PlanPublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlanPublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlanPublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlanPublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlanPublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlanPublishInfoValidationError) ErrorName() string { return "PlanPublishInfoValidationError" }

// Error satisfies the builtin error interface
func (e PlanPublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlanPublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlanPublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlanPublishInfoValidationError{}

// Validate checks the field values on InterfaceDocumentPublishInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceDocumentPublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceDocumentPublishInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceDocumentPublishInfoMultiError, or nil if none found.
func (m *InterfaceDocumentPublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceDocumentPublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseExecutionMode

	// no validation rules for InterfaceDocumentId

	// no validation rules for InterfaceExecuteId

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InterfaceDocumentPublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InterfaceDocumentPublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InterfaceDocumentPublishInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceDocumentPublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceDocumentPublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceDocumentPublishInfoValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetInterfaceCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceDocumentPublishInfoValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceDocumentPublishInfoValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceDocumentPublishInfoValidationError{
					field:  fmt.Sprintf("InterfaceCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for PlanName

	if len(errors) > 0 {
		return InterfaceDocumentPublishInfoMultiError(errors)
	}

	return nil
}

// InterfaceDocumentPublishInfoMultiError is an error wrapping multiple
// validation errors returned by InterfaceDocumentPublishInfo.ValidateAll() if
// the designated constraints aren't met.
type InterfaceDocumentPublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceDocumentPublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceDocumentPublishInfoMultiError) AllErrors() []error { return m }

// InterfaceDocumentPublishInfoValidationError is the validation error returned
// by InterfaceDocumentPublishInfo.Validate if the designated constraints
// aren't met.
type InterfaceDocumentPublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceDocumentPublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceDocumentPublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceDocumentPublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceDocumentPublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceDocumentPublishInfoValidationError) ErrorName() string {
	return "InterfaceDocumentPublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceDocumentPublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceDocumentPublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceDocumentPublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceDocumentPublishInfoValidationError{}

// Validate checks the field values on SuitePublishInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SuitePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuitePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SuitePublishInfoMultiError, or nil if none found.
func (m *SuitePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SuitePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseExecutionMode

	// no validation rules for SuiteId

	// no validation rules for SuiteExecuteId

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SuitePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SuitePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SuitePublishInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SuitePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SuitePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SuitePublishInfoValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SuitePublishInfoValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SuitePublishInfoValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SuitePublishInfoValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanName

	if len(errors) > 0 {
		return SuitePublishInfoMultiError(errors)
	}

	return nil
}

// SuitePublishInfoMultiError is an error wrapping multiple validation errors
// returned by SuitePublishInfo.ValidateAll() if the designated constraints
// aren't met.
type SuitePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuitePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuitePublishInfoMultiError) AllErrors() []error { return m }

// SuitePublishInfoValidationError is the validation error returned by
// SuitePublishInfo.Validate if the designated constraints aren't met.
type SuitePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuitePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuitePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuitePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuitePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuitePublishInfoValidationError) ErrorName() string { return "SuitePublishInfoValidationError" }

// Error satisfies the builtin error interface
func (e SuitePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuitePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuitePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuitePublishInfoValidationError{}

// Validate checks the field values on CasePublishInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CasePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CasePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CasePublishInfoMultiError, or nil if none found.
func (m *CasePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CasePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseId

	// no validation rules for CaseExecuteId

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CasePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CasePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CasePublishInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CasePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CasePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CasePublishInfoValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Version

	// no validation rules for SuiteId

	// no validation rules for SuiteExecuteId

	// no validation rules for CaseName

	// no validation rules for MaintainedBy

	// no validation rules for SuiteName

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for PlanName

	if len(errors) > 0 {
		return CasePublishInfoMultiError(errors)
	}

	return nil
}

// CasePublishInfoMultiError is an error wrapping multiple validation errors
// returned by CasePublishInfo.ValidateAll() if the designated constraints
// aren't met.
type CasePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CasePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CasePublishInfoMultiError) AllErrors() []error { return m }

// CasePublishInfoValidationError is the validation error returned by
// CasePublishInfo.Validate if the designated constraints aren't met.
type CasePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CasePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CasePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CasePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CasePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CasePublishInfoValidationError) ErrorName() string { return "CasePublishInfoValidationError" }

// Error satisfies the builtin error interface
func (e CasePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCasePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CasePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CasePublishInfoValidationError{}

// Validate checks the field values on InterfaceCasePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceCasePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceCasePublishInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceCasePublishInfoMultiError, or nil if none found.
func (m *InterfaceCasePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceCasePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InterfaceCaseId

	// no validation rules for InterfaceCaseExecuteId

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InterfaceCasePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InterfaceCasePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InterfaceCasePublishInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceCasePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceCasePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceCasePublishInfoValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Version

	// no validation rules for InterfaceId

	// no validation rules for InterfaceExecuteId

	// no validation rules for DocumentId

	// no validation rules for CaseName

	// no validation rules for MaintainedBy

	// no validation rules for DocumentName

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for PlanName

	if len(errors) > 0 {
		return InterfaceCasePublishInfoMultiError(errors)
	}

	return nil
}

// InterfaceCasePublishInfoMultiError is an error wrapping multiple validation
// errors returned by InterfaceCasePublishInfo.ValidateAll() if the designated
// constraints aren't met.
type InterfaceCasePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceCasePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceCasePublishInfoMultiError) AllErrors() []error { return m }

// InterfaceCasePublishInfoValidationError is the validation error returned by
// InterfaceCasePublishInfo.Validate if the designated constraints aren't met.
type InterfaceCasePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceCasePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceCasePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceCasePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceCasePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceCasePublishInfoValidationError) ErrorName() string {
	return "InterfaceCasePublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceCasePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceCasePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceCasePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceCasePublishInfoValidationError{}

// Validate checks the field values on ComponentGroupPublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ComponentGroupPublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComponentGroupPublishInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComponentGroupPublishInfoMultiError, or nil if none found.
func (m *ComponentGroupPublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ComponentGroupPublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentGroupId

	// no validation rules for ComponentGroupExecuteId

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComponentGroupPublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComponentGroupPublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComponentGroupPublishInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentGroupPublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentGroupPublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentGroupPublishInfoValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Version

	if len(errors) > 0 {
		return ComponentGroupPublishInfoMultiError(errors)
	}

	return nil
}

// ComponentGroupPublishInfoMultiError is an error wrapping multiple validation
// errors returned by ComponentGroupPublishInfo.ValidateAll() if the
// designated constraints aren't met.
type ComponentGroupPublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComponentGroupPublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComponentGroupPublishInfoMultiError) AllErrors() []error { return m }

// ComponentGroupPublishInfoValidationError is the validation error returned by
// ComponentGroupPublishInfo.Validate if the designated constraints aren't met.
type ComponentGroupPublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComponentGroupPublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComponentGroupPublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComponentGroupPublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComponentGroupPublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComponentGroupPublishInfoValidationError) ErrorName() string {
	return "ComponentGroupPublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ComponentGroupPublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComponentGroupPublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComponentGroupPublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComponentGroupPublishInfoValidationError{}

// Validate checks the field values on UICasePublishInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UICasePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UICasePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UICasePublishInfoMultiError, or nil if none found.
func (m *UICasePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UICasePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UiCaseId

	// no validation rules for UiCaseExecuteId

	// no validation rules for UiSuiteId

	// no validation rules for UiSuiteExecuteId

	// no validation rules for UiPlanId

	// no validation rules for UiPlanExecuteId

	if all {
		switch v := interface{}(m.GetUiCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UICasePublishInfoValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UICasePublishInfoValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUiCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UICasePublishInfoValidationError{
				field:  "UiCase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UICasePublishInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UICasePublishInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UICasePublishInfoValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UICasePublishInfoMultiError(errors)
	}

	return nil
}

// UICasePublishInfoMultiError is an error wrapping multiple validation errors
// returned by UICasePublishInfo.ValidateAll() if the designated constraints
// aren't met.
type UICasePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UICasePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UICasePublishInfoMultiError) AllErrors() []error { return m }

// UICasePublishInfoValidationError is the validation error returned by
// UICasePublishInfo.Validate if the designated constraints aren't met.
type UICasePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UICasePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UICasePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UICasePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UICasePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UICasePublishInfoValidationError) ErrorName() string {
	return "UICasePublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UICasePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUICasePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UICasePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UICasePublishInfoValidationError{}

// Validate checks the field values on UISuitePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UISuitePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UISuitePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UISuitePublishInfoMultiError, or nil if none found.
func (m *UISuitePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UISuitePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseExecutionMode

	// no validation rules for UiSuiteId

	// no validation rules for UiSuiteExecuteId

	// no validation rules for UiPlanId

	// no validation rules for UiPlanExecuteId

	for idx, item := range m.GetUiCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UISuitePublishInfoValidationError{
						field:  fmt.Sprintf("UiCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UISuitePublishInfoValidationError{
						field:  fmt.Sprintf("UiCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UISuitePublishInfoValidationError{
					field:  fmt.Sprintf("UiCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetUiSuite()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UISuitePublishInfoValidationError{
					field:  "UiSuite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UISuitePublishInfoValidationError{
					field:  "UiSuite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUiSuite()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UISuitePublishInfoValidationError{
				field:  "UiSuite",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UISuitePublishInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UISuitePublishInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UISuitePublishInfoValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UISuitePublishInfoMultiError(errors)
	}

	return nil
}

// UISuitePublishInfoMultiError is an error wrapping multiple validation errors
// returned by UISuitePublishInfo.ValidateAll() if the designated constraints
// aren't met.
type UISuitePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UISuitePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UISuitePublishInfoMultiError) AllErrors() []error { return m }

// UISuitePublishInfoValidationError is the validation error returned by
// UISuitePublishInfo.Validate if the designated constraints aren't met.
type UISuitePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UISuitePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UISuitePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UISuitePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UISuitePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UISuitePublishInfoValidationError) ErrorName() string {
	return "UISuitePublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UISuitePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUISuitePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UISuitePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UISuitePublishInfoValidationError{}

// Validate checks the field values on UIPlanPublishInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UIPlanPublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIPlanPublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIPlanPublishInfoMultiError, or nil if none found.
func (m *UIPlanPublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UIPlanPublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UiPlanId

	// no validation rules for CallbackUrl

	// no validation rules for CallbackTimeout

	if all {
		switch v := interface{}(m.GetUiPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIPlanPublishInfoValidationError{
					field:  "UiPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIPlanPublishInfoValidationError{
					field:  "UiPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUiPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIPlanPublishInfoValidationError{
				field:  "UiPlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UIPlanPublishInfoMultiError(errors)
	}

	return nil
}

// UIPlanPublishInfoMultiError is an error wrapping multiple validation errors
// returned by UIPlanPublishInfo.ValidateAll() if the designated constraints
// aren't met.
type UIPlanPublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIPlanPublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIPlanPublishInfoMultiError) AllErrors() []error { return m }

// UIPlanPublishInfoValidationError is the validation error returned by
// UIPlanPublishInfo.Validate if the designated constraints aren't met.
type UIPlanPublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIPlanPublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIPlanPublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIPlanPublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIPlanPublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIPlanPublishInfoValidationError) ErrorName() string {
	return "UIPlanPublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UIPlanPublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIPlanPublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIPlanPublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIPlanPublishInfoValidationError{}

// Validate checks the field values on PrecisionSuitePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PrecisionSuitePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrecisionSuitePublishInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PrecisionSuitePublishInfoMultiError, or nil if none found.
func (m *PrecisionSuitePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PrecisionSuitePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseExecutionMode

	// no validation rules for SuiteId

	// no validation rules for SuiteExecuteId

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrecisionSuitePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrecisionSuitePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrecisionSuitePublishInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrecisionSuitePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrecisionSuitePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrecisionSuitePublishInfoValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrecisionSuitePublishInfoValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrecisionSuitePublishInfoValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrecisionSuitePublishInfoValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SuiteName

	// no validation rules for PlanName

	if len(errors) > 0 {
		return PrecisionSuitePublishInfoMultiError(errors)
	}

	return nil
}

// PrecisionSuitePublishInfoMultiError is an error wrapping multiple validation
// errors returned by PrecisionSuitePublishInfo.ValidateAll() if the
// designated constraints aren't met.
type PrecisionSuitePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrecisionSuitePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrecisionSuitePublishInfoMultiError) AllErrors() []error { return m }

// PrecisionSuitePublishInfoValidationError is the validation error returned by
// PrecisionSuitePublishInfo.Validate if the designated constraints aren't met.
type PrecisionSuitePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrecisionSuitePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrecisionSuitePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrecisionSuitePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrecisionSuitePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrecisionSuitePublishInfoValidationError) ErrorName() string {
	return "PrecisionSuitePublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PrecisionSuitePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrecisionSuitePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrecisionSuitePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrecisionSuitePublishInfoValidationError{}

// Validate checks the field values on PrecisionInterfaceDocumentPublishInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PrecisionInterfaceDocumentPublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrecisionInterfaceDocumentPublishInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PrecisionInterfaceDocumentPublishInfoMultiError, or nil if none found.
func (m *PrecisionInterfaceDocumentPublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PrecisionInterfaceDocumentPublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseExecutionMode

	// no validation rules for InterfaceDocumentId

	// no validation rules for InterfaceExecuteId

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrecisionInterfaceDocumentPublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrecisionInterfaceDocumentPublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrecisionInterfaceDocumentPublishInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrecisionInterfaceDocumentPublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrecisionInterfaceDocumentPublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrecisionInterfaceDocumentPublishInfoValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetInterfaceCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrecisionInterfaceDocumentPublishInfoValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrecisionInterfaceDocumentPublishInfoValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrecisionInterfaceDocumentPublishInfoValidationError{
					field:  fmt.Sprintf("InterfaceCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for DocumentName

	// no validation rules for PlanName

	if len(errors) > 0 {
		return PrecisionInterfaceDocumentPublishInfoMultiError(errors)
	}

	return nil
}

// PrecisionInterfaceDocumentPublishInfoMultiError is an error wrapping
// multiple validation errors returned by
// PrecisionInterfaceDocumentPublishInfo.ValidateAll() if the designated
// constraints aren't met.
type PrecisionInterfaceDocumentPublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrecisionInterfaceDocumentPublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrecisionInterfaceDocumentPublishInfoMultiError) AllErrors() []error { return m }

// PrecisionInterfaceDocumentPublishInfoValidationError is the validation error
// returned by PrecisionInterfaceDocumentPublishInfo.Validate if the
// designated constraints aren't met.
type PrecisionInterfaceDocumentPublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrecisionInterfaceDocumentPublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrecisionInterfaceDocumentPublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrecisionInterfaceDocumentPublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrecisionInterfaceDocumentPublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrecisionInterfaceDocumentPublishInfoValidationError) ErrorName() string {
	return "PrecisionInterfaceDocumentPublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PrecisionInterfaceDocumentPublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrecisionInterfaceDocumentPublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrecisionInterfaceDocumentPublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrecisionInterfaceDocumentPublishInfoValidationError{}

// Validate checks the field values on ServicePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServicePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServicePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServicePublishInfoMultiError, or nil if none found.
func (m *ServicePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ServicePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceId

	// no validation rules for ServiceExecuteId

	// no validation rules for ServiceName

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServicePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServicePublishInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServicePublishInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServicePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServicePublishInfoValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServicePublishInfoValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServicePublishInfoValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServicePublishInfoValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServicePublishInfoValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetInterfaceCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServicePublishInfoValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServicePublishInfoValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServicePublishInfoValidationError{
					field:  fmt.Sprintf("InterfaceCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanName

	if len(errors) > 0 {
		return ServicePublishInfoMultiError(errors)
	}

	return nil
}

// ServicePublishInfoMultiError is an error wrapping multiple validation errors
// returned by ServicePublishInfo.ValidateAll() if the designated constraints
// aren't met.
type ServicePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServicePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServicePublishInfoMultiError) AllErrors() []error { return m }

// ServicePublishInfoValidationError is the validation error returned by
// ServicePublishInfo.Validate if the designated constraints aren't met.
type ServicePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServicePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServicePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServicePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServicePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServicePublishInfoValidationError) ErrorName() string {
	return "ServicePublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ServicePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServicePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServicePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServicePublishInfoValidationError{}

// Validate checks the field values on PerfCasePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfCasePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCasePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCasePublishInfoMultiError, or nil if none found.
func (m *PerfCasePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCasePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerfCaseId

	// no validation rules for PerfCaseExecuteId

	// no validation rules for PerfSuiteId

	// no validation rules for PerfSuiteExecuteId

	// no validation rules for PerfPlanId

	// no validation rules for PerfPlanExecuteId

	if all {
		switch v := interface{}(m.GetPerfCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCasePublishInfoValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCasePublishInfoValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCasePublishInfoValidationError{
				field:  "PerfCase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCasePublishInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCasePublishInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCasePublishInfoValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPerfPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCasePublishInfoValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCasePublishInfoValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCasePublishInfoValidationError{
				field:  "PerfPlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfCasePublishInfoMultiError(errors)
	}

	return nil
}

// PerfCasePublishInfoMultiError is an error wrapping multiple validation
// errors returned by PerfCasePublishInfo.ValidateAll() if the designated
// constraints aren't met.
type PerfCasePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCasePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCasePublishInfoMultiError) AllErrors() []error { return m }

// PerfCasePublishInfoValidationError is the validation error returned by
// PerfCasePublishInfo.Validate if the designated constraints aren't met.
type PerfCasePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCasePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCasePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCasePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCasePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCasePublishInfoValidationError) ErrorName() string {
	return "PerfCasePublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PerfCasePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCasePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCasePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCasePublishInfoValidationError{}

// Validate checks the field values on PerfSuitePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfSuitePublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfSuitePublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfSuitePublishInfoMultiError, or nil if none found.
func (m *PerfSuitePublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfSuitePublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerfSuiteId

	// no validation rules for PerfSuiteExecuteId

	// no validation rules for PerfPlanId

	// no validation rules for PerfPlanExecuteId

	if all {
		switch v := interface{}(m.GetPerfSuite()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfSuitePublishInfoValidationError{
					field:  "PerfSuite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfSuitePublishInfoValidationError{
					field:  "PerfSuite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfSuite()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfSuitePublishInfoValidationError{
				field:  "PerfSuite",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPerfCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfSuitePublishInfoValidationError{
						field:  fmt.Sprintf("PerfCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfSuitePublishInfoValidationError{
						field:  fmt.Sprintf("PerfCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfSuitePublishInfoValidationError{
					field:  fmt.Sprintf("PerfCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CaseExecutionMode

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfSuitePublishInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfSuitePublishInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfSuitePublishInfoValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPerfPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfSuitePublishInfoValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfSuitePublishInfoValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfSuitePublishInfoValidationError{
				field:  "PerfPlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfSuitePublishInfoMultiError(errors)
	}

	return nil
}

// PerfSuitePublishInfoMultiError is an error wrapping multiple validation
// errors returned by PerfSuitePublishInfo.ValidateAll() if the designated
// constraints aren't met.
type PerfSuitePublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfSuitePublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfSuitePublishInfoMultiError) AllErrors() []error { return m }

// PerfSuitePublishInfoValidationError is the validation error returned by
// PerfSuitePublishInfo.Validate if the designated constraints aren't met.
type PerfSuitePublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfSuitePublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfSuitePublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfSuitePublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfSuitePublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfSuitePublishInfoValidationError) ErrorName() string {
	return "PerfSuitePublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PerfSuitePublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfSuitePublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfSuitePublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfSuitePublishInfoValidationError{}

// Validate checks the field values on PerfPlanPublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfPlanPublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlanPublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfPlanPublishInfoMultiError, or nil if none found.
func (m *PerfPlanPublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlanPublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerfPlanId

	// no validation rules for CallbackUrl

	// no validation rules for CallbackTimeout

	if all {
		switch v := interface{}(m.GetPerfPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanPublishInfoValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanPublishInfoValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanPublishInfoValidationError{
				field:  "PerfPlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfPlanPublishInfoMultiError(errors)
	}

	return nil
}

// PerfPlanPublishInfoMultiError is an error wrapping multiple validation
// errors returned by PerfPlanPublishInfo.ValidateAll() if the designated
// constraints aren't met.
type PerfPlanPublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanPublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanPublishInfoMultiError) AllErrors() []error { return m }

// PerfPlanPublishInfoValidationError is the validation error returned by
// PerfPlanPublishInfo.Validate if the designated constraints aren't met.
type PerfPlanPublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanPublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanPublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanPublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanPublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanPublishInfoValidationError) ErrorName() string {
	return "PerfPlanPublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PerfPlanPublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlanPublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanPublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanPublishInfoValidationError{}

// Validate checks the field values on StabilityPlanPublishInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityPlanPublishInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityPlanPublishInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityPlanPublishInfoMultiError, or nil if none found.
func (m *StabilityPlanPublishInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityPlanPublishInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StabilityPlanId

	if len(errors) > 0 {
		return StabilityPlanPublishInfoMultiError(errors)
	}

	return nil
}

// StabilityPlanPublishInfoMultiError is an error wrapping multiple validation
// errors returned by StabilityPlanPublishInfo.ValidateAll() if the designated
// constraints aren't met.
type StabilityPlanPublishInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityPlanPublishInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityPlanPublishInfoMultiError) AllErrors() []error { return m }

// StabilityPlanPublishInfoValidationError is the validation error returned by
// StabilityPlanPublishInfo.Validate if the designated constraints aren't met.
type StabilityPlanPublishInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityPlanPublishInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityPlanPublishInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityPlanPublishInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityPlanPublishInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityPlanPublishInfoValidationError) ErrorName() string {
	return "StabilityPlanPublishInfoValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityPlanPublishInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityPlanPublishInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityPlanPublishInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityPlanPublishInfoValidationError{}
