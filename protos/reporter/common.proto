syntax = "proto3";

package reporter;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb";

import "common/enum.proto";


message PerfData {
  message Series {
    string name = 1; // 指标名称
    repeated string y = 2; // Y轴数据
  }

  common.PerfDataType data_type = 1; // 数据类型
  string unit = 2; // 单位
  repeated string x = 3; // X轴数据
  repeated Series series = 4; // 数据
}

message DeviceActivity {
  string name = 1;  // 活动名称
  bool covered = 2; // 是否覆盖
}