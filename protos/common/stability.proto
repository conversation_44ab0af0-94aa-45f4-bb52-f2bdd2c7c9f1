syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "google/protobuf/struct.proto";
import "validate/validate.proto";

import "common/config.proto";


// StabilityCustomDevices 稳定性测试自定义设备
message StabilityCustomDevices {
  oneof devices {
    google.protobuf.ListValue udids = 1; // 设备编号列表（指定设备）
    uint32 count = 2 [(validate.rules).uint32 = {ignore_empty: true, gte: 0}]; // 设备数量（随机选择设备）
  }
}

// StabilityCustomScript 稳定性测试自定义脚本
message StabilityCustomScript {
  oneof script {
    GitConfig git_config = 1; // 自定义脚本（Git仓库）
    string image = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 自定义脚本（镜像）
  }
}
