syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "common/enum.proto";
import "common/stability.proto";
import "manager/base.proto";

message StabilityPlan {
  string project_id = 1;                           // 项目ID
  string category_id = 2;                          // 分类ID  
  string plan_id = 3;                              // 计划ID
  
  string name = 11;                                // 计划名称
  string description = 12;                         // 描述
  CommonState state = 13;                          // 状态
  common.TriggerMode type = 14;                    // 计划类型（手动、定时）
  common.PriorityType priority_type = 15;          // 优先级
  string cron_expression = 16;                     // 定时触发计划的Cron表达式
  repeated string tags = 17;                       // 标签

  string account_config_id = 31;                   // 池账号配置ID
  common.DeviceType device_type = 32;              // 设备类型（真机、云手机）
  common.PlatformType platform_type = 33;          // 平台类型（Android、iOS）
  common.StabilityCustomDevices devices = 34;      // 自定义设备
  string package_name = 35;                        // 包名，用于启动APP
  string app_download_link = 36;                   // APP下载地址
  uint32 duration = 37;                            // 运行时长（单位：分钟）
  repeated string activities = 38;                 // 指定的Activity列表
  common.StabilityCustomScript custom_script = 39; // 自定义脚本
                                        
  string maintained_by = 95;                       // 维护者
  string created_by = 96;                          // 创建者
  string updated_by = 97;                          // 更新者
  int64  created_at = 98;                          // 创建时间
  int64  updated_at = 99;                          // 更新时间
}
