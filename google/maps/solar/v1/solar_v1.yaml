type: google.api.Service
config_version: 3
name: solar.googleapis.com
title: Solar API

apis:
- name: google.maps.solar.v1.Solar

documentation:
  summary: Solar API.
  overview: '<!--#include file="/geo/platform/solar/g3doc/overview.md"-->'

authentication:
  rules:
  - selector: 'google.maps.solar.v1.Solar.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1356349
  documentation_uri: https://developers.google.com/maps/documentation/solar/overview
  api_short_name: solar
  github_label: 'api: solar'
  doc_tag_prefix: solar
  organization: GEO
  library_settings:
  - version: google.maps.solar.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://developers.google.com/maps/documentation/solar/reference/rest
