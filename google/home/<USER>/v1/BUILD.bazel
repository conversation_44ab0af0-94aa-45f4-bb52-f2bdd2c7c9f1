# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "graph_proto",
    srcs = [
        "device.proto",
        "homegraph.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library_with_info(
    name = "graph_proto_with_info",
    deps = [
        ":graph_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "graph_java_proto",
    deps = [":graph_proto"],
)

java_grpc_library(
    name = "graph_java_grpc",
    srcs = [":graph_proto"],
    deps = [":graph_java_proto"],
)

java_gapic_library(
    name = "graph_java_gapic",
    srcs = [":graph_proto_with_info"],
    grpc_service_config = "homegraph_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "homegraph_v1.yaml",
    test_deps = [
        ":graph_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":graph_java_proto",
    ],
)

java_gapic_test(
    name = "graph_java_gapic_test_suite",
    test_classes = [
        "com.google.home.graph.v1.HomeGraphApiServiceClientHttpJsonTest",
        "com.google.home.graph.v1.HomeGraphApiServiceClientTest",
    ],
    runtime_deps = [":graph_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-home-graph-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":graph_java_gapic",
        ":graph_java_grpc",
        ":graph_java_proto",
        ":graph_proto",
    ],
)

go_proto_library(
    name = "graph_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/home/<USER>/v1",
    protos = [":graph_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "graph_go_gapic",
    srcs = [":graph_proto_with_info"],
    grpc_service_config = "homegraph_grpc_service_config.json",
    importpath = "google.golang.org/api/homegraph/v1;graph",
    rest_numeric_enums = True,
    service_yaml = "homegraph_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":graph_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-home-graph-v1-go",
    deps = [
        ":graph_go_gapic",
        ":graph_go_gapic_srcjar-snippets.srcjar",
        ":graph_go_gapic_srcjar-test.srcjar",
        ":graph_go_proto",
    ],
)

py_gapic_library(
    name = "graph_py_gapic",
    srcs = [":graph_proto"],
    grpc_service_config = "homegraph_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "homegraph_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "graph_py_gapic_test",
    srcs = [
        "graph_py_gapic_pytest.py",
        "graph_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":graph_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "home-graph-v1-py",
    deps = [
        ":graph_py_gapic",
    ],
)

php_proto_library(
    name = "graph_php_proto",
    deps = [":graph_proto"],
)

php_gapic_library(
    name = "graph_php_gapic",
    srcs = [":graph_proto_with_info"],
    rest_numeric_enums = True,
    service_yaml = "homegraph_v1.yaml",
    transport = "grpc+rest",
    deps = [":graph_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-home-graph-v1-php",
    deps = [
        ":graph_php_gapic",
        ":graph_php_proto",
    ],
)

nodejs_gapic_library(
    name = "graph_nodejs_gapic",
    src = ":graph_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "homegraph_grpc_service_config.json",
    package = "google.home.graph.v1",
    rest_numeric_enums = True,
    service_yaml = "homegraph_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "home-graph-v1-nodejs",
    deps = [
        ":graph_nodejs_gapic",
        ":graph_proto",
    ],
)

ruby_proto_library(
    name = "graph_ruby_proto",
    deps = [":graph_proto"],
)

ruby_grpc_library(
    name = "graph_ruby_grpc",
    srcs = [":graph_proto"],
    deps = [":graph_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "graph_ruby_gapic",
    srcs = [":graph_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-graph-v1"],
    rest_numeric_enums = True,
    service_yaml = "homegraph_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":graph_ruby_grpc",
        ":graph_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-home-graph-v1-ruby",
    deps = [
        ":graph_ruby_gapic",
        ":graph_ruby_grpc",
        ":graph_ruby_proto",
    ],
)

csharp_proto_library(
    name = "graph_csharp_proto",
    deps = [":graph_proto"],
)

csharp_grpc_library(
    name = "graph_csharp_grpc",
    srcs = [":graph_proto"],
    deps = [":graph_csharp_proto"],
)

csharp_gapic_library(
    name = "graph_csharp_gapic",
    srcs = [":graph_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "homegraph_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "homegraph_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":graph_csharp_grpc",
        ":graph_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-home-graph-v1-csharp",
    deps = [
        ":graph_csharp_gapic",
        ":graph_csharp_grpc",
        ":graph_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
