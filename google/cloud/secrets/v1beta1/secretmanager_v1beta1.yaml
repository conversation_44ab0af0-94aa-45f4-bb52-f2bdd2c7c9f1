type: google.api.Service
config_version: 3
name: secretmanager.googleapis.com
title: Secret Manager API

apis:
- name: google.cloud.secrets.v1beta1.SecretManagerService

documentation:
  summary: |-
    Stores sensitive data such as API keys, passwords, and certificates.
    Provides convenience while improving security.
  overview: Secret Manager Overview

backend:
  rules:
  - selector: 'google.cloud.secrets.v1beta1.SecretManagerService.*'
    deadline: 10.0

authentication:
  rules:
  - selector: 'google.cloud.secrets.v1beta1.SecretManagerService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
