# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "secretmanager_proto",
    srcs = [
        "resources.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "secretmanager_proto_with_info",
    deps = [
        ":secretmanager_proto",
        "//google/cloud/location:location_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "secretmanager_java_proto",
    deps = [":secretmanager_proto"],
)

java_grpc_library(
    name = "secretmanager_java_grpc",
    srcs = [":secretmanager_proto"],
    deps = [":secretmanager_java_proto"],
)

java_gapic_library(
    name = "secretmanager_java_gapic",
    srcs = [":secretmanager_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "secretmanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "secretmanager_v1beta2.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":secretmanager_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":secretmanager_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "secretmanager_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.secretmanager.v1beta2.SecretManagerServiceClientHttpJsonTest",
        "com.google.cloud.secretmanager.v1beta2.SecretManagerServiceClientTest",
    ],
    runtime_deps = [":secretmanager_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-secretmanager-v1beta2-java",
    transport = "grpc+rest",
    deps = [
        ":secretmanager_java_gapic",
        ":secretmanager_java_grpc",
        ":secretmanager_java_proto",
        ":secretmanager_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "secretmanager_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/secretmanager/apiv1beta2/secretmanagerpb",
    protos = [":secretmanager_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

go_gapic_library(
    name = "secretmanager_go_gapic",
    srcs = [":secretmanager_proto_with_info"],
    grpc_service_config = "secretmanager_grpc_service_config.json",
    importpath = "cloud.google.com/go/secretmanager/apiv1beta2;secretmanager",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "secretmanager_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":secretmanager_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-secretmanager-v1beta2-go",
    deps = [
        ":secretmanager_go_gapic",
        ":secretmanager_go_gapic_srcjar-test.srcjar",
        ":secretmanager_go_gapic_srcjar-metadata.srcjar",
        ":secretmanager_go_gapic_srcjar-snippets.srcjar",
        ":secretmanager_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "secretmanager_py_gapic",
    srcs = [":secretmanager_proto"],
    grpc_service_config = "secretmanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "secretmanager_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "secretmanager_py_gapic_test",
    srcs = [
        "secretmanager_py_gapic_pytest.py",
        "secretmanager_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":secretmanager_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "secretmanager-v1beta2-py",
    deps = [
        ":secretmanager_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "secretmanager_php_proto",
    deps = [":secretmanager_proto"],
)

php_gapic_library(
    name = "secretmanager_php_gapic",
    srcs = [":secretmanager_proto_with_info"],
    grpc_service_config = "secretmanager_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "secretmanager_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":secretmanager_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-secretmanager-v1beta2-php",
    deps = [
        ":secretmanager_php_gapic",
        ":secretmanager_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "secretmanager_nodejs_gapic",
    package_name = "@google-cloud/secretmanager",
    src = ":secretmanager_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "secretmanager_grpc_service_config.json",
    package = "google.cloud.secretmanager.v1beta2",
    rest_numeric_enums = True,
    service_yaml = "secretmanager_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "secretmanager-v1beta2-nodejs",
    deps = [
        ":secretmanager_nodejs_gapic",
        ":secretmanager_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "secretmanager_ruby_proto",
    deps = [":secretmanager_proto"],
)

ruby_grpc_library(
    name = "secretmanager_ruby_grpc",
    srcs = [":secretmanager_proto"],
    deps = [":secretmanager_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "secretmanager_ruby_gapic",
    srcs = [":secretmanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=secretmanager.googleapis.com",
        "ruby-cloud-api-shortname=secretmanager",
        "ruby-cloud-env-prefix=SECRET_MANAGER",
        "ruby-cloud-gem-name=google-cloud-secret_manager-v1beta2",
        "ruby-cloud-product-url=https://cloud.google.com/secret-manager"
    ],
    grpc_service_config = "secretmanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "secretmanager_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":secretmanager_ruby_grpc",
        ":secretmanager_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-secretmanager-v1beta2-ruby",
    deps = [
        ":secretmanager_ruby_gapic",
        ":secretmanager_ruby_grpc",
        ":secretmanager_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "secretmanager_csharp_proto",
    extra_opts = [],
    deps = [":secretmanager_proto"],
)

csharp_grpc_library(
    name = "secretmanager_csharp_grpc",
    srcs = [":secretmanager_proto"],
    deps = [":secretmanager_csharp_proto"],
)

csharp_gapic_library(
    name = "secretmanager_csharp_gapic",
    srcs = [":secretmanager_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "secretmanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "secretmanager_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":secretmanager_csharp_grpc",
        ":secretmanager_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-secretmanager-v1beta2-csharp",
    deps = [
        ":secretmanager_csharp_gapic",
        ":secretmanager_csharp_grpc",
        ":secretmanager_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "secretmanager_cc_proto",
    deps = [":secretmanager_proto"],
)

cc_grpc_library(
    name = "secretmanager_cc_grpc",
    srcs = [":secretmanager_proto"],
    grpc_only = True,
    deps = [":secretmanager_cc_proto"],
)
