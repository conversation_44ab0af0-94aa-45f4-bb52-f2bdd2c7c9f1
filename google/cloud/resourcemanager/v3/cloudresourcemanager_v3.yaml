type: google.api.Service
config_version: 3
name: cloudresourcemanager.googleapis.com
title: Cloud Resource Manager API

apis:
- name: google.cloud.resourcemanager.v3.Folders
- name: google.cloud.resourcemanager.v3.Organizations
- name: google.cloud.resourcemanager.v3.Projects
- name: google.cloud.resourcemanager.v3.TagBindings
- name: google.cloud.resourcemanager.v3.TagHolds
- name: google.cloud.resourcemanager.v3.TagKeys
- name: google.cloud.resourcemanager.v3.TagValues
- name: google.longrunning.Operations

types:
- name: google.cloud.resourcemanager.v3.CreateFolderMetadata
- name: google.cloud.resourcemanager.v3.CreateProjectMetadata
- name: google.cloud.resourcemanager.v3.CreateTagBindingMetadata
- name: google.cloud.resourcemanager.v3.CreateTagKeyMetadata
- name: google.cloud.resourcemanager.v3.CreateTagValueMetadata
- name: google.cloud.resourcemanager.v3.DeleteFolderMetadata
- name: google.cloud.resourcemanager.v3.DeleteOrganizationMetadata
- name: google.cloud.resourcemanager.v3.DeleteProjectMetadata
- name: google.cloud.resourcemanager.v3.DeleteTagBindingMetadata
- name: google.cloud.resourcemanager.v3.DeleteTagKeyMetadata
- name: google.cloud.resourcemanager.v3.DeleteTagValueMetadata
- name: google.cloud.resourcemanager.v3.MoveFolderMetadata
- name: google.cloud.resourcemanager.v3.MoveProjectMetadata
- name: google.cloud.resourcemanager.v3.UndeleteFolderMetadata
- name: google.cloud.resourcemanager.v3.UndeleteOrganizationMetadata
- name: google.cloud.resourcemanager.v3.UndeleteProjectMetadata
- name: google.cloud.resourcemanager.v3.UpdateFolderMetadata
- name: google.cloud.resourcemanager.v3.UpdateProjectMetadata
- name: google.cloud.resourcemanager.v3.UpdateTagKeyMetadata
- name: google.cloud.resourcemanager.v3.UpdateTagValueMetadata

documentation:
  summary: |-
    Creates, reads, and updates metadata for Google Cloud Platform resource
    containers.

backend:
  rules:
  - selector: 'google.cloud.resourcemanager.v3.Folders.*'
    deadline: 30.0
  - selector: 'google.cloud.resourcemanager.v3.Organizations.*'
    deadline: 30.0
  - selector: 'google.cloud.resourcemanager.v3.Projects.*'
    deadline: 30.0
  - selector: 'google.cloud.resourcemanager.v3.TagBindings.*'
    deadline: 30.0
  - selector: 'google.cloud.resourcemanager.v3.TagHolds.*'
    deadline: 30.0
  - selector: 'google.cloud.resourcemanager.v3.TagKeys.*'
    deadline: 30.0
  - selector: 'google.cloud.resourcemanager.v3.TagValues.*'
    deadline: 30.0
  - selector: google.longrunning.Operations.GetOperation
    deadline: 30.0

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v3/{name=operations/**}'

authentication:
  rules:
  - selector: 'google.cloud.resourcemanager.v3.Folders.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.resourcemanager.v3.Folders.GetFolder
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.Folders.GetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.Folders.ListFolders
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.Folders.SearchFolders
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.cloud.resourcemanager.v3.Organizations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.Organizations.SetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.resourcemanager.v3.Projects.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.resourcemanager.v3.Projects.GetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.Projects.GetProject
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.Projects.ListProjects
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.Projects.SearchProjects
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.Projects.TestIamPermissions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagBindings.CreateTagBinding
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.resourcemanager.v3.TagBindings.DeleteTagBinding
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.resourcemanager.v3.TagBindings.ListEffectiveTags
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagBindings.ListTagBindings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagHolds.CreateTagHold
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.resourcemanager.v3.TagHolds.DeleteTagHold
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.resourcemanager.v3.TagHolds.ListTagHolds
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.cloud.resourcemanager.v3.TagKeys.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.resourcemanager.v3.TagKeys.GetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagKeys.GetNamespacedTagKey
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagKeys.GetTagKey
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagKeys.ListTagKeys
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.cloud.resourcemanager.v3.TagValues.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.resourcemanager.v3.TagValues.GetIamPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagValues.GetNamespacedTagValue
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagValues.GetTagValue
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.resourcemanager.v3.TagValues.ListTagValues
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
