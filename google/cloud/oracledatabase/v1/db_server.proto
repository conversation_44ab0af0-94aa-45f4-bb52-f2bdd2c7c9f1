// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.oracledatabase.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.OracleDatabase.V1";
option go_package = "cloud.google.com/go/oracledatabase/apiv1/oracledatabasepb;oracledatabasepb";
option java_multiple_files = true;
option java_outer_classname = "DbServerProto";
option java_package = "com.google.cloud.oracledatabase.v1";
option php_namespace = "Google\\Cloud\\OracleDatabase\\V1";
option ruby_package = "Google::Cloud::OracleDatabase::V1";

// Details of the database server resource.
// https://docs.oracle.com/en-us/iaas/api/#/en/database/20160918/DbServer/
message DbServer {
  option (google.api.resource) = {
    type: "oracledatabase.googleapis.com/DbServer"
    pattern: "projects/{project}/locations/{location}/cloudExadataInfrastructures/{cloud_exadata_infrastructure}/dbServers/{db_server}"
    plural: "dbServers"
    singular: "dbServer"
  };

  // Identifier. The name of the database server resource with the format:
  // projects/{project}/locations/{location}/cloudExadataInfrastructures/{cloud_exadata_infrastructure}/dbServers/{db_server}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. User friendly name for this resource.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Various properties of the database server.
  DbServerProperties properties = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Various properties and settings associated with Exadata database server.
message DbServerProperties {
  // The various lifecycle states of the database server.
  enum State {
    // Default unspecified value.
    STATE_UNSPECIFIED = 0;

    // Indicates that the resource is in creating state.
    CREATING = 1;

    // Indicates that the resource is in available state.
    AVAILABLE = 2;

    // Indicates that the resource is in unavailable state.
    UNAVAILABLE = 3;

    // Indicates that the resource is in deleting state.
    DELETING = 4;

    // Indicates that the resource is in deleted state.
    DELETED = 5;
  }

  // Output only. OCID of database server.
  string ocid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. OCPU count per database.
  int32 ocpu_count = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum OCPU count per database.
  int32 max_ocpu_count = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Memory allocated in GBs.
  int32 memory_size_gb = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum memory allocated in GBs.
  int32 max_memory_size_gb = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Local storage per VM.
  int32 db_node_storage_size_gb = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum local storage per VM.
  int32 max_db_node_storage_size_gb = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Vm count per database.
  int32 vm_count = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of the database server.
  State state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. OCID of database nodes associated with the database server.
  repeated string db_node_ids = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}
