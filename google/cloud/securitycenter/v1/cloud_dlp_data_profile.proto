// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "CloudDlpDataProfileProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";
option (google.api.resource_definition) = {
  type: "dlp.googleapis.com/TableDataProfile"
  pattern: "projects/{project}/tableProfiles/{table_profile}"
  pattern: "projects/{project}/locations/{location}/tableProfiles/{table_profile}"
};

// The [data profile](https://cloud.google.com/dlp/docs/data-profiles)
// associated with the finding.
message CloudDlpDataProfile {
  // Parents for configurations that produce data profile findings.
  enum ParentType {
    // Unspecified parent type.
    PARENT_TYPE_UNSPECIFIED = 0;

    // Organization-level configurations.
    ORGANIZATION = 1;

    // Project-level configurations.
    PROJECT = 2;
  }

  // Name of the data profile, for example,
  // `projects/123/locations/europe/tableProfiles/8383929`.
  string data_profile = 1 [(google.api.resource_reference) = {
    type: "dlp.googleapis.com/TableDataProfile"
  }];

  // The resource hierarchy level at which the data profile was generated.
  ParentType parent_type = 2;
}
