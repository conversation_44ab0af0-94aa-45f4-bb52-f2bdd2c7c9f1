type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
interfaces:
- name: google.cloud.metastore.v1.DataprocMetastore
  methods:
  - name: CreateService
    long_running:
      initial_poll_delay_millis: 60000  # 1 minute.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 120000  # 2 minutes.
      total_poll_timeout_millis: 4800000  # 80 minutes.
  - name: UpdateService
    long_running:
      initial_poll_delay_millis: 60000  # 1 minute.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 120000  # 2 minutes.
      total_poll_timeout_millis: 3000000  # 50 minutes.
  - name: DeleteService
    long_running:
      initial_poll_delay_millis: 10000  # 10 seconds.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute.
      total_poll_timeout_millis: 1500000  # 25 minutes.
  - name: CreateMetadataImport
    long_running:
      initial_poll_delay_millis: 60000  # 1 minute.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 120000  # 2 minutes.
      total_poll_timeout_millis: 4800000  # 80 minutes.
  - name: UpdateMetadataImport
    long_running:
      initial_poll_delay_millis: 60000  # 1 minute.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 120000  # 2 minutes.
      total_poll_timeout_millis: 1200000  # 20 minutes.
  - name: ExportMetadata
    long_running:
      initial_poll_delay_millis: 60000  # 1 minute.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 120000  # 2 minutes.
      total_poll_timeout_millis: 4800000  # 80 minutes.
  - name: RestoreService
    long_running:
      initial_poll_delay_millis: 60000  # 1 minute.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 120000  # 2 minutes.
      total_poll_timeout_millis: 4800000  # 80 minutes.
  - name: CreateBackup
    long_running:
      initial_poll_delay_millis: 60000  # 1 minute.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 120000  # 2 minutes.
      total_poll_timeout_millis: 4800000  # 80 minutes.
  - name: DeleteBackup
    long_running:
      initial_poll_delay_millis: 10000  # 10 seconds.
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 60000  # 1 minute.
      total_poll_timeout_millis: 1500000  # 25 minutes.
