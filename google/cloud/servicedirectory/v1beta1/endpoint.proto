// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.servicedirectory.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ServiceDirectory.V1Beta1";
option go_package = "cloud.google.com/go/servicedirectory/apiv1beta1/servicedirectorypb;servicedirectorypb";
option java_multiple_files = true;
option java_outer_classname = "EndpointProto";
option java_package = "com.google.cloud.servicedirectory.v1beta1";
option php_namespace = "Google\\Cloud\\ServiceDirectory\\V1beta1";
option ruby_package = "Google::Cloud::ServiceDirectory::V1beta1";
option (google.api.resource_definition) = {
  type: "servicedirectory.googleapis.com/Network"
  pattern: "projects/{project}/locations/global/networks/{network}"
};

// An individual endpoint that provides a
// [service][google.cloud.servicedirectory.v1beta1.Service]. The service must
// already exist to create an endpoint.
message Endpoint {
  option (google.api.resource) = {
    type: "servicedirectory.googleapis.com/Endpoint"
    pattern: "projects/{project}/locations/{location}/namespaces/{namespace}/services/{service}/endpoints/{endpoint}"
  };

  // Immutable. The resource name for the endpoint in the format
  // `projects/*/locations/*/namespaces/*/services/*/endpoints/*`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Optional. An IPv4 or IPv6 address. Service Directory rejects bad addresses
  // like:
  //
  // *   `8.8.8`
  // *   `*******:53`
  // *   `test:bad:address`
  // *   `[::1]`
  // *   `[::1]:8080`
  //
  // Limited to 45 characters.
  string address = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Service Directory rejects values outside of `[0, 65535]`.
  int32 port = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Metadata for the endpoint. This data can be consumed by service
  // clients.
  //
  // Restrictions:
  //
  // *   The entire metadata dictionary may contain up to 512 characters,
  //     spread accoss all key-value pairs. Metadata that goes beyond this
  //     limit are rejected
  // *   Valid metadata keys have two segments: an optional prefix and name,
  //     separated by a slash (/). The name segment is required and must be 63
  //     characters or less, beginning and ending with an alphanumeric character
  //     ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
  //     alphanumerics between. The prefix is optional. If specified, the prefix
  //     must be a DNS subdomain: a series of DNS labels separated by dots (.),
  //     not longer than 253 characters in total, followed by a slash (/).
  //     Metadata that fails to meet these requirements are rejected
  //
  // Note: This field is equivalent to the `annotations` field in the v1 API.
  // They have the same syntax and read/write to the same location in Service
  // Directory.
  map<string, string> metadata = 4 [(google.api.field_behavior) = OPTIONAL];

  // Immutable. The Google Compute Engine network (VPC) of the endpoint in the
  // format `projects/<project number>/locations/global/networks/*`.
  //
  // The project must be specified by project number (project id is rejected).
  // Incorrectly formatted networks are rejected, but no other validation
  // is performed on this field (ex. network or project existence, reachability,
  // or permissions).
  string network = 5 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "servicedirectory.googleapis.com/Network"
    }
  ];

  // Output only. The timestamp when the endpoint was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the endpoint was last updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A globally unique identifier (in UUID4 format) for this
  // endpoint.
  string uid = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
