// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ExecutionProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Instance of a general execution.
message Execution {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/Execution"
    pattern: "projects/{project}/locations/{location}/metadataStores/{metadata_store}/executions/{execution}"
  };

  // Describes the state of the Execution.
  enum State {
    // Unspecified Execution state
    STATE_UNSPECIFIED = 0;

    // The Execution is new
    NEW = 1;

    // The Execution is running
    RUNNING = 2;

    // The Execution has finished running
    COMPLETE = 3;

    // The Execution has failed
    FAILED = 4;

    // The Execution completed through Cache hit.
    CACHED = 5;

    // The Execution was cancelled.
    CANCELLED = 6;
  }

  // Output only. The resource name of the Execution.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User provided display name of the Execution.
  // May be up to 128 Unicode characters.
  string display_name = 2;

  // The state of this Execution. This is a property of the Execution, and does
  // not imply or capture any ongoing process. This property is managed by
  // clients (such as Vertex AI Pipelines) and the system does not prescribe
  // or check the validity of state transitions.
  State state = 6;

  // An eTag used to perform consistent read-modify-write updates. If not set, a
  // blind "overwrite" update happens.
  string etag = 9;

  // The labels with user-defined metadata to organize your Executions.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one Execution (System
  // labels are excluded).
  map<string, string> labels = 10;

  // Output only. Timestamp when this Execution was created.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Execution was last updated.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The title of the schema describing the metadata.
  //
  // Schema title and version is expected to be registered in earlier Create
  // Schema calls. And both are used together as unique identifiers to identify
  // schemas within the local metadata store.
  string schema_title = 13;

  // The version of the schema in `schema_title` to use.
  //
  // Schema title and version is expected to be registered in earlier Create
  // Schema calls. And both are used together as unique identifiers to identify
  // schemas within the local metadata store.
  string schema_version = 14;

  // Properties of the Execution.
  // Top level metadata keys' heading and trailing spaces will be trimmed.
  // The size of this field should not exceed 200KB.
  google.protobuf.Struct metadata = 15;

  // Description of the Execution
  string description = 16;
}
