// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/import_config.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "EstimateBillingServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Service for managing billing estimations resources.
service EstimateBillingService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Estimates the data size to be used by a customer.
  rpc EstimateDataSize(EstimateDataSizeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{location=projects/*/locations/*}:estimateDataSize"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1alpha.EstimateDataSizeResponse"
      metadata_type: "google.cloud.discoveryengine.v1alpha.EstimateDataSizeMetadata"
    };
  }
}

// Request message for
// [EstimateBillingService.EstimateDataSize][google.cloud.discoveryengine.v1alpha.EstimateBillingService.EstimateDataSize]
// method
message EstimateDataSizeRequest {
  // Data source is a set of website patterns that we crawl to get
  // the total number of websites.
  message WebsiteDataSource {
    // URI patterns that we use to crawl.
    message EstimatorUriPattern {
      // User provided URI pattern. For example, `foo.com/bar/*`.
      string provided_uri_pattern = 1;

      // Whether we infer the generated URI or use the exact provided one.
      bool exact_match = 2;

      // Whether the pattern is exclusive or not. If set to true, the pattern is
      // considered exclusive. If unset or set to false, the pattern is
      // considered inclusive by default.
      bool exclusive = 3;
    }

    // Required. The URI patterns to estimate the data sizes. At most 10
    // patterns are allowed, otherwise an INVALID_ARGUMENT error is thrown.
    repeated EstimatorUriPattern estimator_uri_patterns = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Data source contains files either in Cloud Storage or BigQuery.
  message FileDataSource {
    // Which source contains the files to be used.
    oneof source {
      // Cloud Storage location for the input content.
      GcsSource gcs_source = 1;

      // BigQuery input source.
      BigQuerySource bigquery_source = 2;
    }
  }

  // Data source for which we are estimating its size.
  oneof data_source {
    // Website data.
    WebsiteDataSource website_data_source = 2;

    // Structured or unstructured data.
    FileDataSource file_data_source = 3;
  }

  // Required. Full resource name of the location, such as
  // `projects/{project}/locations/{location}`.
  string location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Location"
    }
  ];
}

// Response of the EstimateDataSize request. If the long running
// operation was successful, then this message is returned by the
// google.longrunning.Operations.response field if the operation was successful.
message EstimateDataSizeResponse {
  // Data size in terms of bytes.
  int64 data_size_bytes = 1;

  // Total number of documents.
  int64 document_count = 2;
}

// Metadata related to the progress of the EstimateDataSize operation. This is
// returned by the google.longrunning.Operation.metadata field.
message EstimateDataSizeMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;
}
