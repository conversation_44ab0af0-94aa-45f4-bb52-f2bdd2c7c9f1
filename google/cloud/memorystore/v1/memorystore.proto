// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.memorystore.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Memorystore.V1";
option go_package = "cloud.google.com/go/memorystore/apiv1/memorystorepb;memorystorepb";
option java_multiple_files = true;
option java_outer_classname = "V1mainProto";
option java_package = "com.google.cloud.memorystore.v1";
option php_namespace = "Google\\Cloud\\Memorystore\\V1";
option ruby_package = "Google::Cloud::Memorystore::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/ForwardingRule"
  pattern: "projects/{project}/regions/{region}/forwardingRules/{forwarding_rule}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/ServiceAttachment"
  pattern: "projects/{project}/regions/{region}/serviceAttachments/{service_attachment}"
};

// Service describing handlers for resources
service Memorystore {
  option (google.api.default_host) = "memorystore.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Instances in a given project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Instance in a given project and location.
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Instance.
  rpc UpdateInstance(UpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{instance.name=projects/*/locations/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Instance.
  rpc DeleteInstance(DeleteInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details about the certificate authority for an Instance.
  rpc GetCertificateAuthority(GetCertificateAuthorityRequest)
      returns (CertificateAuthority) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}/certificateAuthority"
    };
    option (google.api.method_signature) = "name";
  }
}

// Status of the PSC connection.
enum PscConnectionStatus {
  // PSC connection status is not specified.
  PSC_CONNECTION_STATUS_UNSPECIFIED = 0;

  // The connection is active
  ACTIVE = 1;

  // Connection not found
  NOT_FOUND = 2;
}

// Type of a PSC connection
enum ConnectionType {
  // Connection Type is not set
  CONNECTION_TYPE_UNSPECIFIED = 0;

  // Connection that will be used for topology discovery.
  CONNECTION_TYPE_DISCOVERY = 1;

  // Connection that will be used as primary endpoint to access primary.
  CONNECTION_TYPE_PRIMARY = 2;

  // Connection that will be used as reader endpoint to access replicas.
  CONNECTION_TYPE_READER = 3;
}

// A Memorystore instance.
message Instance {
  option (google.api.resource) = {
    type: "memorystore.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
    plural: "instances"
    singular: "instance"
  };

  // Additional information about the state of the instance.
  message StateInfo {
    // Represents information about instance with state UPDATING.
    message UpdateInfo {
      // Output only. Target number of shards for the instance.
      optional int32 target_shard_count = 1
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Target number of replica nodes per shard for the instance.
      optional int32 target_replica_count = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    oneof info {
      // Output only. Describes ongoing update when instance state is UPDATING.
      UpdateInfo update_info = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
    }
  }

  // InstanceEndpoint consists of PSC connections that are created
  // as a group in each VPC network for accessing the instance. In each group,
  // there shall be one connection for each service attachment in the cluster.
  message InstanceEndpoint {
    // Optional. A group of PSC connections. They are created in the same VPC
    // network, one for each service attachment in the cluster.
    repeated ConnectionDetail connections = 1
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Information of each PSC connection.
  message ConnectionDetail {
    // A PSC connection to an instance could either be created through Service
    // Connectivity Automation (auto connection) during the cluster creation,
    // or it could be created by customer themeslves (user-created connection).
    oneof connection {
      // Detailed information of a PSC connection that is created through
      // service connectivity automation.
      PscAutoConnection psc_auto_connection = 1;

      // Detailed information of a PSC connection that is created by the user.
      PscConnection psc_connection = 2;
    }
  }

  // Possible states of the instance.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // Instance is being created.
    CREATING = 1;

    // Instance has been created and is usable.
    ACTIVE = 2;

    // Instance is being updated.
    UPDATING = 3;

    // Instance is being deleted.
    DELETING = 4;
  }

  // Possible authorization modes of the instance.
  enum AuthorizationMode {
    // Not set.
    AUTHORIZATION_MODE_UNSPECIFIED = 0;

    // Authorization disabled.
    AUTH_DISABLED = 1;

    // IAM basic authorization.
    IAM_AUTH = 2;
  }

  // Possible in-transit encryption modes of the instance.
  enum TransitEncryptionMode {
    // Not set.
    TRANSIT_ENCRYPTION_MODE_UNSPECIFIED = 0;

    // In-transit encryption is disabled.
    TRANSIT_ENCRYPTION_DISABLED = 1;

    // Server-managed encryption is used for in-transit encryption.
    SERVER_AUTHENTICATION = 2;
  }

  // Possible node types of the instance. See
  // https://cloud.google.com/memorystore/docs/valkey/instance-node-specification
  // for more information.
  enum NodeType {
    // Not set.
    NODE_TYPE_UNSPECIFIED = 0;

    // Shared core nano.
    SHARED_CORE_NANO = 1;

    // High memory medium.
    HIGHMEM_MEDIUM = 2;

    // High memory extra large.
    HIGHMEM_XLARGE = 3;

    // Standard small.
    STANDARD_SMALL = 4;
  }

  // The mode config, which is used to enable/disable cluster mode.
  enum Mode {
    // Mode is not specified.
    MODE_UNSPECIFIED = 0;

    // Instance is in standalone mode.
    STANDALONE = 1;

    // Instance is in cluster mode.
    CLUSTER = 2;
  }

  // Identifier. Unique name of the instance.
  // Format: projects/{project}/locations/{location}/instances/{instance}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Creation timestamp of the instance.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Latest update timestamp of the instance.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels to represent user-provided metadata.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Current state of the instance.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the state of the instance.
  StateInfo state_info = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. System assigned, unique identifier for the instance.
  string uid = 7 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. Number of replica nodes per shard. If omitted the default is 0
  // replicas.
  optional int32 replica_count = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Immutable. Authorization mode of the instance.
  AuthorizationMode authorization_mode = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Immutable. In-transit encryption mode of the instance.
  TransitEncryptionMode transit_encryption_mode = 10 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Number of shards for the instance.
  int32 shard_count = 11 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Endpoints clients can connect to the instance through.
  // Currently only one discovery endpoint is supported.
  repeated DiscoveryEndpoint discovery_endpoints = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. Machine type for individual nodes of the instance.
  NodeType node_type = 13 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Persistence configuration of the instance.
  PersistenceConfig persistence_config = 14
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Immutable. Engine version of the instance.
  string engine_version = 15 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. User-provided engine configurations for the instance.
  map<string, string> engine_configs = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Configuration of individual nodes of the instance.
  NodeConfig node_config = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. Zone distribution configuration of the instance for
  // node allocation.
  ZoneDistributionConfig zone_distribution_config = 18 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. If set to true deletion of the instance will fail.
  optional bool deletion_protection_enabled = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. User inputs and resource details of the auto-created
  // PSC connections.
  repeated PscAutoConnection psc_auto_connections = 20 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Endpoints for the instance.
  repeated InstanceEndpoint endpoints = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The mode config for the instance.
  Mode mode = 26 [(google.api.field_behavior) = OPTIONAL];
}

// Details of consumer resources in a PSC connection.
message PscAutoConnection {
  // Ports of the exposed endpoint.
  oneof ports {
    // Optional. Output only. port will only be set for Primary/Reader or
    // Discovery endpoint.
    int32 port = 9 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.field_behavior) = OPTIONAL
    ];
  }

  // Output only. The PSC connection id of the forwarding rule connected to the
  // service attachment.
  string psc_connection_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The IP allocated on the consumer network for the PSC
  // forwarding rule.
  string ip_address = 2 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. The URI of the consumer side forwarding rule.
  // Format:
  // projects/{project}/regions/{region}/forwardingRules/{forwarding_rule}
  string forwarding_rule = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/ForwardingRule"
    }
  ];

  // Required. The consumer project_id where PSC connections are established.
  // This should be the same project_id that the instance is being created in.
  string project_id = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The network where the PSC endpoints are created, in the form of
  // projects/{project_id}/global/networks/{network_id}.
  string network = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Output only. The service attachment which is the target of the PSC
  // connection, in the form of
  // projects/{project-id}/regions/{region}/serviceAttachments/{service-attachment-id}.
  string service_attachment = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/ServiceAttachment"
    }
  ];

  // Output only. The status of the PSC connection: whether a connection exists
  // and ACTIVE or it no longer exists. Please note that this value is updated
  // periodically. Please use Private Service Connect APIs for the latest
  // status.
  PscConnectionStatus psc_connection_status = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the PSC connection.
  ConnectionType connection_type = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// User created Psc connection configuration.
message PscConnection {
  // Output only. The PSC connection id of the forwarding rule connected to the
  // service attachment.
  string psc_connection_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The IP allocated on the consumer network for the PSC forwarding
  // rule.
  string ip_address = 2 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. The URI of the consumer side forwarding rule.
  // Format:
  // projects/{project}/regions/{region}/forwardingRules/{forwarding_rule}
  string forwarding_rule = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/ForwardingRule"
    }
  ];

  // Output only. The consumer project_id where the forwarding rule is created
  // from.
  string project_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The consumer network where the IP address resides, in the form of
  // projects/{project_id}/global/networks/{network_id}.
  string network = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Required. The service attachment which is the target of the PSC connection,
  // in the form of
  // projects/{project-id}/regions/{region}/serviceAttachments/{service-attachment-id}.
  string service_attachment = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/ServiceAttachment"
    }
  ];

  // Output only. The status of the PSC connection: whether a connection exists
  // and ACTIVE or it no longer exists. Please note that this value is updated
  // periodically. Please use Private Service Connect APIs for the latest
  // status.
  PscConnectionStatus psc_connection_status = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the PSC connection.
  ConnectionType connection_type = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents an endpoint for clients to connect to the instance.
message DiscoveryEndpoint {
  // Output only. IP address of the exposed endpoint clients connect to.
  string address = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The port number of the exposed endpoint.
  int32 port = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The network where the IP address of the discovery endpoint
  // will be reserved, in the form of
  // projects/{network_project}/global/networks/{network_id}.
  string network = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];
}

// Represents persistence configuration for a instance.
message PersistenceConfig {
  // Configuration for RDB based persistence.
  message RDBConfig {
    // Possible snapshot periods.
    enum SnapshotPeriod {
      // Not set.
      SNAPSHOT_PERIOD_UNSPECIFIED = 0;

      // One hour.
      ONE_HOUR = 1;

      // Six hours.
      SIX_HOURS = 2;

      // Twelve hours.
      TWELVE_HOURS = 3;

      // Twenty four hours.
      TWENTY_FOUR_HOURS = 4;
    }

    // Optional. Period between RDB snapshots.
    SnapshotPeriod rdb_snapshot_period = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Time that the first snapshot was/will be attempted, and to
    // which future snapshots will be aligned. If not provided, the current time
    // will be used.
    google.protobuf.Timestamp rdb_snapshot_start_time = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Configuration for AOF based persistence.
  message AOFConfig {
    // Possible fsync modes.
    enum AppendFsync {
      // Not set. Default: EVERY_SEC
      APPEND_FSYNC_UNSPECIFIED = 0;

      // Never fsync. Normally Linux will flush data every 30 seconds with this
      // configuration, but it's up to the kernel's exact tuning.
      NEVER = 1;

      // Fsync every second. You may lose 1 second of data if there is a
      // disaster.
      EVERY_SEC = 2;

      // Fsync every time new write commands are appended to the AOF. The best
      // data loss protection at the cost of performance.
      ALWAYS = 3;
    }

    // Optional. The fsync mode.
    AppendFsync append_fsync = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Possible persistence modes.
  enum PersistenceMode {
    // Not set.
    PERSISTENCE_MODE_UNSPECIFIED = 0;

    // Persistence is disabled, and any snapshot data is deleted.
    DISABLED = 1;

    // RDB based persistence is enabled.
    RDB = 2;

    // AOF based persistence is enabled.
    AOF = 3;
  }

  // Optional. Current persistence mode.
  PersistenceMode mode = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. RDB configuration. This field will be ignored if mode is not RDB.
  RDBConfig rdb_config = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. AOF configuration. This field will be ignored if mode is not AOF.
  AOFConfig aof_config = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Represents configuration for nodes of the instance.
message NodeConfig {
  // Output only. Memory size in GB of the node.
  double size_gb = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Zone distribution configuration for allocation of instance resources.
message ZoneDistributionConfig {
  // Possible zone distribution modes.
  enum ZoneDistributionMode {
    // Not Set. Default: MULTI_ZONE
    ZONE_DISTRIBUTION_MODE_UNSPECIFIED = 0;

    // Distribute resources across 3 zones picked at random within the
    // region.
    MULTI_ZONE = 1;

    // Provision resources in a single zone. Zone field must be specified.
    SINGLE_ZONE = 2;
  }

  // Optional. Defines zone where all resources will be allocated with
  // SINGLE_ZONE mode. Ignored for MULTI_ZONE mode.
  string zone = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Current zone distribution mode. Defaults to MULTI_ZONE.
  ZoneDistributionMode mode = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for [ListInstances][].
message ListInstancesRequest {
  // Required. The parent to list instances from.
  // Format: projects/{project}/locations/{location}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "memorystore.googleapis.com/Instance"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expression for filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Sort results by a defined order. Supported values: "name",
  // "create_time".
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for [ListInstances][].
message ListInstancesResponse {
  // If the {location} requested was "-" the response contains a list of
  // instances from all locations. Instances in unreachable locations will be
  // omitted.
  repeated Instance instances = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for [GetInstance][].
message GetInstanceRequest {
  // Required. The name of the instance to retrieve.
  // Format: projects/{project}/locations/{location}/instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memorystore.googleapis.com/Instance"
    }
  ];
}

// Request message for [CreateInstance][].
message CreateInstanceRequest {
  // Required. The parent resource where this instance will be created.
  // Format: projects/{project}/locations/{location}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "memorystore.googleapis.com/Instance"
    }
  ];

  // Required. The ID to use for the instance, which will become the final
  // component of the instance's resource name.
  //
  // This value is subject to the following restrictions:
  //
  // * Must be 4-63 characters in length
  // * Must begin with a letter or digit
  // * Must contain only lowercase letters, digits, and hyphens
  // * Must not end with a hyphen
  // * Must be unique within a location
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The instance to create.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Request message for [UpdateInstance][].
message UpdateInstanceRequest {
  // Optional. The list of fields to be updated on the instance. At least one
  // field must be specified.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The instance to update.
  Instance instance = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Request message for [DeleteInstance][].
message DeleteInstanceRequest {
  // Required. The name of the instance to delete.
  // Format: projects/{project}/locations/{location}/instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memorystore.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Request message for [GetCertificateAuthority][].
message GetCertificateAuthorityRequest {
  // Required. The name of the certificate authority.
  // Format:
  // projects/{project}/locations/{location}/instances/{instance}/certificateAuthority
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memorystore.googleapis.com/Instance"
    }
  ];
}

// A certificate authority for an instance.
message CertificateAuthority {
  option (google.api.resource) = {
    type: "memorystore.googleapis.com/CertificateAuthority"
    pattern: "projects/{project}/locations/{location}/instances/{instance}/certificateAuthority"
    plural: "certificateAuthorities"
    singular: "certificateAuthority"
  };

  // A managed certificate authority.
  message ManagedCertificateAuthority {
    // A certificate chain.
    message CertChain {
      // The certificates that form the CA chain in order of leaf to root.
      repeated string certificates = 1;
    }

    // PEM encoded CA certificate chains for managed server authentication.
    repeated CertChain ca_certs = 1;
  }

  // Information about the server certificate authority.
  oneof server_ca {
    // A managed server certificate authority.
    ManagedCertificateAuthority managed_server_ca = 2;
  }

  // Identifier. Unique name of the certificate authority.
  // Format:
  // projects/{project}/locations/{location}/instances/{instance}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];
}

// Represents the metadata of a long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
