// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.chromeos.moblab.v1beta1;

import "google/api/resource.proto";

option go_package = "google.golang.org/genproto/googleapis/chromeos/moblab/v1beta1;moblab";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.chromeos.moblab.v1beta1";
option optimize_for = SPEED;

// Resource that represents a build target.
// -- NEXT_TAG: 2 --
message BuildTarget {
  option (google.api.resource) = {
    type: "chromeosmoblab.googleapis.com/BuildTarget"
    pattern: "buildTargets/{build_target}"
  };

  // The resource name of the build target.
  // Format: buildTargets/{build_target}
  string name = 1;
}

// Resource that represents a model. Each model belongs to a build target. For
// non-unified build, the model name is the same as its build target name.
// -- NEXT_TAG: 2 --
message Model {
  option (google.api.resource) = {
    type: "chromeosmoblab.googleapis.com/Model"
    pattern: "buildTargets/{build_target}/models/{model}"
  };

  // The resource name of the model.
  // Format: buildTargets/{build_target}/models/{model}
  string name = 1;
}

// Resource that represents a chrome OS milestone.
// -- NEXT_TAG: 2 --
message Milestone {
  option (google.api.resource) = {
    type: "chromeosmoblab.googleapis.com/Milestone"
    pattern: "milestones/{milestone}"
  };

  // The resource name of the milestone.
  // Format: milestones/{milestone}
  string name = 1;
}

// Resource that represents a build for the given build target, model, milestone
// and build version.
// -- NEXT_TAG: 9 --
message Build {
  option (google.api.resource) = {
    type: "chromeosmoblab.googleapis.com/Build"
    pattern: "buildTargets/{build_target}/models/{model}/builds/{build}"
  };

  // The build status types.
  enum BuildStatus {
    // No build status is specified.
    BUILD_STATUS_UNSPECIFIED = 0;

    // Complete Status: The build passed.
    PASS = 1;

    // Complete Status: The build failed.
    FAIL = 2;

    // Intermediate Status: The build is still running.
    RUNNING = 3;

    // Complete Status: The build was aborted.
    ABORTED = 4;
  }

  // The build types.
  enum BuildType {
    // Invalid build type.
    BUILD_TYPE_UNSPECIFIED = 0;

    // The release build.
    RELEASE = 1;

    // The firmware build.
    FIRMWARE = 2;
  }

  // The resource name of the build.
  // Format: buildTargets/{build_target}/models/{model}/builds/{build}
  // Example: buildTargets/octopus/models/bobba/builds/1234.0.0
  string name = 1;

  // The milestone that owns the build.
  // Format: milestones/{milestone}
  string milestone = 2 [(google.api.resource_reference) = {
    type: "chromeosmoblab.googleapis.com/Milestone"
  }];

  // The build version of the build, e.g. 1234.0.0.
  string build_version = 3;

  // The status of the build.
  BuildStatus status = 4;

  // The type of the build.
  BuildType type = 5;

  // The branch of the build.
  string branch = 6;

  // The firmware version of the software that is flashed to the chip on the
  // Chrome OS device.
  string rw_firmware_version = 7;

  // The labels of the build.
  repeated string labels = 8;
}

// Resource that represents a build artifact stored in Google Cloud Storage for
// the given build target, model, build version and bucket.
// -- NEXT_TAG: 6 --
message BuildArtifact {
  option (google.api.resource) = {
    type: "chromeosmoblab.googleapis.com/BuildArtifact"
    pattern: "buildTargets/{build_target}/models/{model}/builds/{build}/artifacts/{artifact}"
  };

  // The resource name of the build artifact.
  // Format:
  // buildTargets/{build_target}/models/{model}/builds/{build}/artifacts/{artifact}
  // Example:
  // buildTargets/octopus/models/bobba/builds/1234.0.0/artifacts/chromeos-moblab-peng-staging
  string name = 1;

  // The build metadata of the build artifact.
  string build = 2 [(google.api.resource_reference) = {
    type: "chromeosmoblab.googleapis.com/Build"
  }];

  // The bucket that stores the build artifact.
  string bucket = 3;

  // The path of the build artifact in the bucket.
  string path = 4;

  // The number of objects in the build artifact folder. The object number can
  // be used to calculated the stage progress by comparing the source build
  // artifact with the destination build artifact.
  uint32 object_count = 5;
}
