# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "accounts_proto",
    srcs = [
        "accessright.proto",
        "account_tax.proto",
        "accountissue.proto",
        "accounts.proto",
        "accountservices.proto",
        "autofeedsettings.proto",
        "businessidentity.proto",
        "businessinfo.proto",
        "customerservice.proto",
        "emailpreferences.proto",
        "homepage.proto",
        "online_return_policy.proto",
        "phoneverificationstate.proto",
        "programs.proto",
        "regions.proto",
        "shippingsettings.proto",
        "tax_rule.proto",
        "termsofservice.proto",
        "termsofserviceagreementstate.proto",
        "termsofservicekind.proto",
        "user.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        # Manual edit: Types -> Type
        "//google/shopping/type:type_proto",
        "//google/type:date_proto",
        "//google/type:datetime_proto",
        "//google/type:interval_proto",
        "//google/type:phone_number_proto",
        "//google/type:postal_address_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "accounts_proto_with_info",
    deps = [
        ":accounts_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "accounts_java_proto",
    deps = [":accounts_proto"],
)

java_grpc_library(
    name = "accounts_java_grpc",
    srcs = [":accounts_proto"],
    deps = [":accounts_java_proto"],
)

java_gapic_library(
    name = "accounts_java_gapic",
    srcs = [":accounts_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "accounts_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    test_deps = [
        ":accounts_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":accounts_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "accounts_java_gapic_test_suite",
    test_classes = [
        "com.google.shopping.merchant.accounts.v1beta.AccountIssueServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.AccountIssueServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.AccountTaxServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.AccountTaxServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.AccountsServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.AccountsServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.AutofeedSettingsServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.AutofeedSettingsServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.BusinessIdentityServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.BusinessIdentityServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.BusinessInfoServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.BusinessInfoServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.EmailPreferencesServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.EmailPreferencesServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.HomepageServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.HomepageServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.OnlineReturnPolicyServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.OnlineReturnPolicyServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.ProgramsServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.ProgramsServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.RegionsServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.RegionsServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.ShippingSettingsServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.ShippingSettingsServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.TermsOfServiceAgreementStateServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.TermsOfServiceAgreementStateServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.TermsOfServiceServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.TermsOfServiceServiceClientTest",
        "com.google.shopping.merchant.accounts.v1beta.UserServiceClientHttpJsonTest",
        "com.google.shopping.merchant.accounts.v1beta.UserServiceClientTest",
    ],
    runtime_deps = [":accounts_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-merchant-accounts-v1beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":accounts_java_gapic",
        ":accounts_java_grpc",
        ":accounts_java_proto",
        ":accounts_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "accounts_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb",
    protos = [":accounts_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        # Manual edit: Types -> Type
        "//google/shopping/type:type_go_proto",
        "//google/type:date_go_proto",
        "//google/type:datetime_go_proto",
        "//google/type:interval_go_proto",
        "//google/type:phone_number_go_proto",
        "//google/type:postaladdress_go_proto",
    ],
)

go_gapic_library(
    name = "accounts_go_gapic",
    srcs = [":accounts_proto_with_info"],
    grpc_service_config = "accounts_grpc_service_config.json",
    importpath = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta;accounts",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":accounts_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-merchant-accounts-v1beta-go",
    deps = [
        ":accounts_go_gapic",
        ":accounts_go_gapic_srcjar-metadata.srcjar",
        ":accounts_go_gapic_srcjar-snippets.srcjar",
        ":accounts_go_gapic_srcjar-test.srcjar",
        ":accounts_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "py_test",
)

py_import(
    name = "shopping_type",
    srcs = [
        "//google/shopping/type:type_py_gapic",
    ],
)

py_gapic_library(
    name = "accounts_py_gapic",
    srcs = [":accounts_proto"],
    grpc_service_config = "accounts_grpc_service_config.json",
    opt_args = [
        "proto-plus-deps=google.shopping.type",
        "python-gapic-name=merchant_accounts",
        "python-gapic-namespace=google.shopping",
    ],
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":shopping_type",  # Added manually
    ],
)

py_test(
    name = "accounts_py_gapic_test",
    srcs = [
        "accounts_py_gapic_pytest.py",
        "accounts_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":accounts_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "merchant-accounts-v1beta-py",
    deps = [
        ":accounts_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "accounts_php_proto",
    deps = [":accounts_proto"],
)

php_gapic_library(
    name = "accounts_php_gapic",
    srcs = [":accounts_proto_with_info"],
    grpc_service_config = "accounts_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":accounts_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-merchant-accounts-v1beta-php",
    deps = [
        ":accounts_php_gapic",
        ":accounts_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "accounts_nodejs_gapic",
    package_name = "@google-shopping/accounts",
    src = ":accounts_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "accounts_grpc_service_config.json",
    package = "google.shopping.merchant.accounts.v1beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "merchant-accounts-v1beta-nodejs",
    deps = [
        ":accounts_nodejs_gapic",
        ":accounts_proto",
        "//google/shopping/type:type_proto",  # Added manually
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "accounts_ruby_proto",
    deps = [":accounts_proto"],
)

ruby_grpc_library(
    name = "accounts_ruby_grpc",
    srcs = [":accounts_proto"],
    deps = [":accounts_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "accounts_ruby_gapic",
    srcs = [":accounts_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-extra-dependencies=google-shopping-type=>0.0+<2.a",
        "ruby-cloud-gem-name=google-shopping-merchant-accounts-v1beta",
    ],
    grpc_service_config = "accounts_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":accounts_ruby_grpc",
        ":accounts_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-merchant-accounts-v1beta-ruby",
    deps = [
        ":accounts_ruby_gapic",
        ":accounts_ruby_grpc",
        ":accounts_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "accounts_csharp_proto",
    deps = [":accounts_proto"],
)

csharp_grpc_library(
    name = "accounts_csharp_grpc",
    srcs = [":accounts_proto"],
    deps = [":accounts_csharp_proto"],
)

csharp_gapic_library(
    name = "accounts_csharp_gapic",
    srcs = [":accounts_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "accounts_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":accounts_csharp_grpc",
        ":accounts_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-merchant-accounts-v1beta-csharp",
    deps = [
        ":accounts_csharp_gapic",
        ":accounts_csharp_grpc",
        ":accounts_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "accounts_cc_proto",
    deps = [":accounts_proto"],
)

cc_grpc_library(
    name = "accounts_cc_grpc",
    srcs = [":accounts_proto"],
    grpc_only = True,
    deps = [":accounts_cc_proto"],
)
