// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "RealEstatePlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing Real Estate placeholder fields.

// Values for Real Estate placeholder fields.
// For more information about dynamic remarketing feeds, see
// https://support.google.com/google-ads/answer/6053288.
message RealEstatePlaceholderFieldEnum {
  // Possible values for Real Estate placeholder fields.
  enum RealEstatePlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: STRING. Unique ID.
    LISTING_ID = 2;

    // Data Type: STRING. Main headline with listing name to be shown in dynamic
    // ad.
    LISTING_NAME = 3;

    // Data Type: STRING. City name to be shown in dynamic ad.
    CITY_NAME = 4;

    // Data Type: STRING. Description of listing to be shown in dynamic ad.
    DESCRIPTION = 5;

    // Data Type: STRING. Complete listing address, including postal code.
    ADDRESS = 6;

    // Data Type: STRING. Price to be shown in the ad.
    // Example: "100.00 USD"
    PRICE = 7;

    // Data Type: STRING. Formatted price to be shown in the ad.
    // Example: "Starting at $100.00 USD", "$80 - $100"
    FORMATTED_PRICE = 8;

    // Data Type: URL. Image to be displayed in the ad.
    IMAGE_URL = 9;

    // Data Type: STRING. Type of property (house, condo, apartment, etc.) used
    // to group like items together for recommendation engine.
    PROPERTY_TYPE = 10;

    // Data Type: STRING. Type of listing (resale, rental, foreclosure, etc.)
    // used to group like items together for recommendation engine.
    LISTING_TYPE = 11;

    // Data Type: STRING_LIST. Keywords used for product retrieval.
    CONTEXTUAL_KEYWORDS = 12;

    // Data Type: URL_LIST. Final URLs to be used in ad when using Upgraded
    // URLs; the more specific the better (for example, the individual URL of a
    // specific listing and its location).
    FINAL_URLS = 13;

    // Data Type: URL_LIST. Final mobile URLs for the ad when using Upgraded
    // URLs.
    FINAL_MOBILE_URLS = 14;

    // Data Type: URL. Tracking template for the ad when using Upgraded URLs.
    TRACKING_URL = 15;

    // Data Type: STRING. Android app link. Must be formatted as:
    // android-app://{package_id}/{scheme}/{host_path}.
    // The components are defined as follows:
    // package_id: app ID as specified in Google Play.
    // scheme: the scheme to pass to the application. Can be HTTP, or a custom
    //   scheme.
    // host_path: identifies the specific content within your application.
    ANDROID_APP_LINK = 16;

    // Data Type: STRING_LIST. List of recommended listing IDs to show together
    // with this item.
    SIMILAR_LISTING_IDS = 17;

    // Data Type: STRING. iOS app link.
    IOS_APP_LINK = 18;

    // Data Type: INT64. iOS app store ID.
    IOS_APP_STORE_ID = 19;
  }
}
