// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/change_status_operation.proto";
import "google/ads/googleads/v17/enums/change_status_resource_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ChangeStatusProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the Change Status resource.

// Describes the status of returned resource. ChangeStatus could have up to 3
// minutes delay to reflect a new change.
message ChangeStatus {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ChangeStatus"
    pattern: "customers/{customer_id}/changeStatus/{change_status_id}"
  };

  // Output only. The resource name of the change status.
  // Change status resource names have the form:
  //
  // `customers/{customer_id}/changeStatus/{change_status_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ChangeStatus"
    }
  ];

  // Output only. Time at which the most recent change has occurred on this
  // resource.
  optional string last_change_date_time = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents the type of the changed resource. This dictates
  // what fields will be set. For example, for AD_GROUP, campaign and ad_group
  // fields will be set.
  google.ads.googleads.v17.enums.ChangeStatusResourceTypeEnum
      .ChangeStatusResourceType resource_type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Campaign affected by this change.
  optional string campaign = 17 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The AdGroup affected by this change.
  optional string ad_group = 18 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Output only. Represents the status of the changed resource.
  google.ads.googleads.v17.enums.ChangeStatusOperationEnum.ChangeStatusOperation
      resource_status = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The AdGroupAd affected by this change.
  optional string ad_group_ad = 25 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAd"
    }
  ];

  // Output only. The AdGroupCriterion affected by this change.
  optional string ad_group_criterion = 26 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupCriterion"
    }
  ];

  // Output only. The CampaignCriterion affected by this change.
  optional string campaign_criterion = 27 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignCriterion"
    }
  ];

  // Output only. The Feed affected by this change.
  optional string feed = 28 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Feed" }
  ];

  // Output only. The FeedItem affected by this change.
  optional string feed_item = 29 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/FeedItem"
    }
  ];

  // Output only. The AdGroupFeed affected by this change.
  optional string ad_group_feed = 30 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupFeed"
    }
  ];

  // Output only. The CampaignFeed affected by this change.
  optional string campaign_feed = 31 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignFeed"
    }
  ];

  // Output only. The AdGroupBidModifier affected by this change.
  optional string ad_group_bid_modifier = 32 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupBidModifier"
    }
  ];

  // Output only. The SharedSet affected by this change.
  string shared_set = 33 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/SharedSet"
    }
  ];

  // Output only. The CampaignSharedSet affected by this change.
  string campaign_shared_set = 34 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignSharedSet"
    }
  ];

  // Output only. The Asset affected by this change.
  string asset = 35 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // Output only. The CustomerAsset affected by this change.
  string customer_asset = 36 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerAsset"
    }
  ];

  // Output only. The CampaignAsset affected by this change.
  string campaign_asset = 37 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignAsset"
    }
  ];

  // Output only. The AdGroupAsset affected by this change.
  string ad_group_asset = 38 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupAsset"
    }
  ];

  // Output only. The CombinedAudience affected by this change.
  string combined_audience = 40 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CombinedAudience"
    }
  ];

  // Output only. The AssetGroup affected by this change.
  string asset_group = 41 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AssetGroup"
    }
  ];
}
