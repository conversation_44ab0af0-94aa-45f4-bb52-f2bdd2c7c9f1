// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "SmartCampaignSettingProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the Smart campaign setting resource.

// Settings for configuring Smart campaigns.
message SmartCampaignSetting {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/SmartCampaignSetting"
    pattern: "customers/{customer_id}/smartCampaignSettings/{campaign_id}"
  };

  // Phone number and country code in smart campaign settings.
  message PhoneNumber {
    // Phone number of the smart campaign.
    optional string phone_number = 1;

    // Upper-case, two-letter country code as defined by ISO-3166.
    optional string country_code = 2;
  }

  // Settings for configuring a business profile optimized for ads as this
  // campaign's landing page.
  message AdOptimizedBusinessProfileSetting {
    // Enabling a lead form on your business profile enables prospective
    // customers to contact your business by filling out a simple form,
    // and you'll receive their information through email.
    optional bool include_lead_form = 1;
  }

  // Immutable. The resource name of the Smart campaign setting.
  // Smart campaign setting resource names have the form:
  //
  // `customers/{customer_id}/smartCampaignSettings/{campaign_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/SmartCampaignSetting"
    }
  ];

  // Output only. The campaign to which these settings apply.
  string campaign = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Phone number and country code.
  PhoneNumber phone_number = 3;

  // The language code to advertise in from the set of
  // [supported language codes]
  // (https://developers.google.com/google-ads/api/reference/data/codes-formats#languages).
  string advertising_language_code = 7;

  // The landing page of this campaign.
  oneof landing_page {
    // The user-provided landing page URL for this Campaign.
    string final_url = 8;

    // Settings for configuring a business profile optimized for ads as this
    // campaign's landing page.  This campaign must be linked to a business
    // profile to use this option.  For more information on this feature,
    // consult https://support.google.com/google-ads/answer/9827068.
    AdOptimizedBusinessProfileSetting ad_optimized_business_profile_setting = 9;
  }

  // The business setting of this campaign.
  oneof business_setting {
    // The name of the business.
    string business_name = 5;

    // The resource name of a Business Profile location.
    // Business Profile location resource names can be fetched through the
    // Business Profile API and adhere to the following format:
    // `locations/{locationId}`.
    //
    // See the [Business Profile API]
    // (https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations)
    // for additional details.
    string business_profile_location = 10;
  }
}
