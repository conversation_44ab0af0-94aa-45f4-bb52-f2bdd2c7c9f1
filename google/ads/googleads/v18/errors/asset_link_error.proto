// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AssetLinkErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing asset link errors.

// Container for enum describing possible asset link errors.
message AssetLinkErrorEnum {
  // Enum describing possible asset link errors.
  enum AssetLinkError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Pinning is not supported for the given asset link field.
    PINNING_UNSUPPORTED = 2;

    // The given field type is not supported to be added directly through asset
    // links.
    UNSUPPORTED_FIELD_TYPE = 3;

    // The given asset's type and the specified field type are incompatible.
    FIELD_TYPE_INCOMPATIBLE_WITH_ASSET_TYPE = 4;

    // The specified field type is incompatible with the given campaign type.
    FIELD_TYPE_INCOMPATIBLE_WITH_CAMPAIGN_TYPE = 5;

    // The campaign advertising channel type cannot be associated with the given
    // asset due to channel-based restrictions on the asset's fields.
    INCOMPATIBLE_ADVERTISING_CHANNEL_TYPE = 6;

    // The image asset provided is not within the dimension constraints
    // specified for the submitted asset field.
    IMAGE_NOT_WITHIN_SPECIFIED_DIMENSION_RANGE = 7;

    // The pinned field is not valid for the submitted asset field.
    INVALID_PINNED_FIELD = 8;

    // The media bundle asset provided is too large for the submitted asset
    // field.
    MEDIA_BUNDLE_ASSET_FILE_SIZE_TOO_LARGE = 9;

    // Not enough assets are available for use with other fields since other
    // assets are pinned to specific fields.
    NOT_ENOUGH_AVAILABLE_ASSET_LINKS_FOR_VALID_COMBINATION = 10;

    // Not enough assets with fallback are available. When validating the
    // minimum number of assets, assets without fallback (for example, assets
    // that contain location tag without default value "{LOCATION(City)}") will
    // not be counted.
    NOT_ENOUGH_AVAILABLE_ASSET_LINKS_WITH_FALLBACK = 11;

    // This is a combination of the
    // NOT_ENOUGH_AVAILABLE_ASSET_LINKS_FOR_VALID_COMBINATION and
    // NOT_ENOUGH_AVAILABLE_ASSET_LINKS_WITH_FALLBACK errors. Not enough assets
    // with fallback are available since some assets are pinned.
    NOT_ENOUGH_AVAILABLE_ASSET_LINKS_WITH_FALLBACK_FOR_VALID_COMBINATION = 12;

    // The YouTube video referenced in the provided asset has been removed.
    YOUTUBE_VIDEO_REMOVED = 13;

    // The YouTube video referenced in the provided asset is too long for the
    // field submitted.
    YOUTUBE_VIDEO_TOO_LONG = 14;

    // The YouTube video referenced in the provided asset is too short for the
    // field submitted.
    YOUTUBE_VIDEO_TOO_SHORT = 15;

    // The specified field type is excluded for given campaign or ad group.
    EXCLUDED_PARENT_FIELD_TYPE = 16;

    // The status is invalid for the operation specified.
    INVALID_STATUS = 17;

    // The YouTube video referenced in the provided asset has unknown duration.
    // This might be the case for a livestream video or a video being currently
    // uploaded to YouTube. In both cases, the video duration should eventually
    // get resolved.
    YOUTUBE_VIDEO_DURATION_NOT_DEFINED = 18;

    // User cannot create automatically created links.
    CANNOT_CREATE_AUTOMATICALLY_CREATED_LINKS = 19;

    // Advertiser links cannot link to automatically created asset.
    CANNOT_LINK_TO_AUTOMATICALLY_CREATED_ASSET = 20;

    // Automatically created links cannot be changed into advertiser links or
    // the reverse.
    CANNOT_MODIFY_ASSET_LINK_SOURCE = 21;

    // Lead Form asset with Location answer type can't be linked to the
    // Customer/Campaign because there are no Location assets.
    CANNOT_LINK_LOCATION_LEAD_FORM_WITHOUT_LOCATION_ASSET = 22;

    // Customer is not verified.
    CUSTOMER_NOT_VERIFIED = 23;

    // Call to action value is not supported.
    UNSUPPORTED_CALL_TO_ACTION = 24;

    // For Performance Max campaigns where brand_guidelines_enabled is false,
    // business name and logo assets must be linked as AssetGroupAssets.
    BRAND_ASSETS_NOT_LINKED_AT_ASSET_GROUP_LEVEL = 25;

    // For Performance Max campaigns where brand_guidelines_enabled is true,
    // business name and logo assets must be linked as CampaignAssets.
    BRAND_ASSETS_NOT_LINKED_AT_CAMPAIGN_LEVEL = 26;
  }
}
