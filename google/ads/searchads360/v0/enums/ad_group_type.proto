// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdGroupTypeProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing ad group types.

// Defines types of an ad group, specific to a particular campaign channel
// type. This type drives validations that restrict which entities can be
// added to the ad group.
message AdGroupTypeEnum {
  // Enum listing the possible types of an ad group.
  enum AdGroupType {
    // The type has not been specified.
    UNSPECIFIED = 0;

    // The received value is not known in this version.
    //
    // This is a response-only value.
    UNKNOWN = 1;

    // The default ad group type for Search campaigns.
    SEARCH_STANDARD = 2;

    // The default ad group type for Display campaigns.
    DISPLAY_STANDARD = 3;

    // The ad group type for Shopping campaigns serving standard product ads.
    SHOPPING_PRODUCT_ADS = 4;

    // The type for ad groups that are limited to serving Showcase or Merchant
    // ads in Shopping results.
    SHOPPING_SHOWCASE_ADS = 5;

    // The default ad group type for Hotel campaigns.
    HOTEL_ADS = 6;

    // The type for ad groups in Smart Shopping campaigns.
    SHOPPING_SMART_ADS = 7;

    // Short unskippable in-stream video ads.
    VIDEO_BUMPER = 8;

    // TrueView (skippable) in-stream video ads.
    VIDEO_TRUE_VIEW_IN_STREAM = 9;

    // TrueView in-display video ads.
    VIDEO_TRUE_VIEW_IN_DISPLAY = 10;

    // Unskippable in-stream video ads.
    VIDEO_NON_SKIPPABLE_IN_STREAM = 11;

    // Outstream video ads.
    VIDEO_OUTSTREAM = 12;

    // Ad group type for Dynamic Search Ads ad groups.
    SEARCH_DYNAMIC_ADS = 13;

    // The type for ad groups in Shopping Comparison Listing campaigns.
    SHOPPING_COMPARISON_LISTING_ADS = 14;

    // The ad group type for Promoted Hotel ad groups.
    PROMOTED_HOTEL_ADS = 15;

    // Video responsive ad groups.
    VIDEO_RESPONSIVE = 16;

    // Video efficient reach ad groups.
    VIDEO_EFFICIENT_REACH = 17;

    // Ad group type for Smart campaigns.
    SMART_CAMPAIGN_ADS = 18;

    // Ad group type for Travel campaigns.
    TRAVEL_ADS = 19;
  }
}
