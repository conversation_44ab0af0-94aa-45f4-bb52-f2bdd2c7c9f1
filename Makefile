
MAKEFILE_DIR := $(patsubst %/,%,$(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
#GOMOD_FILES := $(foreach file,$(shell find $(MAKEFILE_DIR) -type f -name 'go.mod'),$(subst //,/,$(file)))
#GOMOD_DIRS := $(foreach dir,$(GOMOD_FILES),$(subst /go.mod,,$(dir)))
#MAKEFILE_FILES := $(foreach file,$(shell find $(MAKEFILE_DIR) -type f -name 'Makefile'),$(subst //,/,$(file)))
SUB_MF_FILES := $(foreach file, $(wildcard $(MAKEFILE_DIR)/*/Makefile), $(subst //,/,$(file)))
SUB_MF_DIRS := $(foreach dir, $(SUB_MF_FILES), $(subst /Makefile,,$(dir)))

export GITLAB_GROUP = "gitlab.ttyuyin.com/TestDevelopment"

export VERSION := $(shell [ -f "VERSION" ] && cat VERSION || echo "0.0.1")
export VERSION_PACKAGE := $(GITLAB_GROUP)/qet-backend-common/version

export GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
export GIT_TAG := $(shell [ "`git describe --tags --abbrev=0 2>/dev/null`" != "" ] && git describe --tags --abbrev=0 || git log --pretty=format:'%h' -n 1)
export GIT_COMMIT := $(shell git rev-parse --short HEAD)
export BUILD_DATETIME := $(shell TZ=Asia/Shanghai date +'%F %T %Z')
export BUILD_GO_VERSION := $(shell go env GOVERSION)
export PLATFORM := $(shell uname)

export GCFLAGS := "-N -l"
export LDFLAGS := "-s -w -X \"${VERSION_PACKAGE}.Version=${VERSION}\" -X \"${VERSION_PACKAGE}.GitBranch=${GIT_BRANCH}\" -X \"${VERSION_PACKAGE}.GitTag=${GIT_TAG}\" -X \"${VERSION_PACKAGE}.GitCommit=${GIT_COMMIT}\" -X \"${VERSION_PACKAGE}.BuildDatetime=${BUILD_DATETIME}\" -X \"${VERSION_PACKAGE}.BuildGoVersion=${BUILD_GO_VERSION}\""

# check if `gofumpt` command exists
GOFUMPT_EXISTS := $(shell command -v gofumpt >/dev/null 2>&1 && echo 1 || echo 0)

GO_FORMAT_CMD := gofmt -s -w
ifeq (1, $(GOFUMPT_EXISTS))
GO_FORMAT_CMD = gofumpt -l -w -extra
endif

define cmd-execute
	@for dir in $^; do \
		if [ -d $$dir ]; then \
			echo "make $@ in $$dir"; \
            $(MAKE) $@ -C $$dir; \
		fi \
	done
endef

.PHONY: all
all: all-mac all-linux
	$(cmd-execute)

.PHONY: echo
echo: $(SUB_MF_DIRS)
	@echo "MAKEFILE_DIR: $(MAKEFILE_DIR)"
	@echo "SUB_MF_FILES: $(SUB_MF_FILES)"
	@echo "SUB_MF_DIRS: $(SUB_MF_DIRS)"
	@echo "GITLAB_GROUP: $(GITLAB_GROUP)"
	@echo "VERSION: $(VERSION)"
	@echo "VERSION_PACKAGE: $(VERSION_PACKAGE)"
	@echo "GIT_BRANCH: $(GIT_BRANCH)"
	@echo "GIT_TAG: $(GIT_TAG)"
	@echo "GIT_COMMIT: $(GIT_COMMIT)"
	@echo "BUILD_DATETIME: $(BUILD_DATETIME)"
	@echo "BUILD_GO_VERSION: $(BUILD_GO_VERSION)"
	@echo "PLATFORM: $(PLATFORM)"
	@echo "LDFLAGS: $(LDFLAGS)"
	@echo ""

	$(cmd-execute)

.PHONY: fmt
fmt:
	@go list -f {{.Dir}} $(MAKEFILE_DIR)/... | xargs $(GO_FORMAT_CMD)
	@goimports -l -w -local $(GITLAB_GROUP) $(MAKEFILE_DIR)

.PHONY: lint
lint:
	@golangci-lint run -c $(MAKEFILE_DIR)/.golangci.yaml

.PHONY: all-mac
all-mac: $(SUB_MF_DIRS)
	$(cmd-execute)

.PHONY: all-linux
all-linux: $(SUB_MF_DIRS)
	$(cmd-execute)

.PHONY: all-win
all-win: $(SUB_MF_DIRS)
	$(cmd-execute)
