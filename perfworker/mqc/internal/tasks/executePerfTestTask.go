package tasks

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/core/trace"
	"go.uber.org/atomic"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/logic/perftest"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
)

const stopReason = "满足压测停止规则"

var _ base.Handler = (*ProcessorExecutePerfTest)(nil)

type ProcessorExecutePerfTest struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorExecutePerfTest(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorExecutePerfTest{
		svcCtx: svcCtx,
	}
}

func (processor *ProcessorExecutePerfTest) ProcessTask(ctx context.Context, task *base.Task) (
	result []byte, err error,
) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	var req dispatcherpb.WorkerReq
	if err = protobuf.UnmarshalJSON(task.Payload, &req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of perf test task, payload: %s, error: %+v", task.Payload, err,
		)
	}

	logic, err := NewExecutePerfTestTaskLogic(ctx, processor.svcCtx, &req)
	if err != nil {
		return nil, err
	}

	err = logic.Run()
	if err != nil {
		logger.Errorf(
			"failed to execute the perf test task, task_id: %s, execute_id: %s, error: %+v",
			logic.taskInfo.GetTaskId(), logic.taskInfo.GetExecuteId(), err,
		)
	}

	return []byte(constants.SUCCESS), nil
}

type ExecutePerfTestTaskLogic struct {
	logx.Logger
	ctx    context.Context
	cancel context.CancelFunc
	svcCtx *svc.ServiceContext

	taskReq  *dispatcherpb.WorkerReq
	taskInfo *dispatcherpb.PerfTestTaskInfo
	perfCase *commonpb.PerfCaseContentV2
	state    *atomic.String
	rules    map[appInsight.MetricType]config.StopRule
	stopCh   chan lang.PlaceholderType

	metricCache sync.Map
	alarmItem   *metricCacheItem
}

type (
	metricReqItem struct {
		key  string
		req  *appInsight.QueryRangeMetricsReq
		rule config.StopRule
	}

	metricCacheItem struct {
		MetricType appInsight.MetricType `json:"metric_type"`
		Service    string                `json:"service"`
		Namespace  string                `json:"namespace"`
		Method     string                `json:"method"`
		Threshold  float64               `json:"threshold"`
		ReachedAt  time.Time             `json:"reached_at"`
		LatestAt   time.Time             `json:"latest_at"`
		Points     []appInsight.Point    `json:"points"`
		Alarm      bool                  `json:"alarm"`
	}
)

func NewExecutePerfTestTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, req *dispatcherpb.WorkerReq,
) (*ExecutePerfTestTaskLogic, error) {
	rules, err := getStopRulesFromTaskReq(req, svcCtx.Config.StopRules)
	if err != nil {
		return nil, err
	}

	info, err := getTaskInfoFromTaskReq(req)
	if err != nil {
		return nil, err
	}

	perfCase, err := getPerfCaseFromTaskReq(req)
	if err != nil {
		return nil, err
	}

	return &ExecutePerfTestTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		taskReq:  req,
		taskInfo: info,
		perfCase: perfCase,
		state:    atomic.NewString(""),
		rules:    rules,
		stopCh:   make(chan lang.PlaceholderType),
	}, nil
}

func getStopRulesFromTaskReq(
	req *dispatcherpb.WorkerReq, defaults map[string]config.StopRule,
) (map[appInsight.MetricType]config.StopRule, error) {
	metaData := req.GetPerfCase().GetMetaData()
	if metaData == nil {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"the metadata of perf plan is null, task payload: %s",
			protobuf.MarshalJSONIgnoreError(req),
		)
	}

	rules := make(map[appInsight.MetricType]config.StopRule, len(metaData.GetRules()))
	for _, rule := range metaData.GetRules() {
		rules[rule.GetMetricType().ConvertToZH()] = config.StopRule{
			Threshold: rule.GetThreshold(),
			Duration:  time.Duration(rule.GetDuration()) * time.Second,
		}
	}

	if len(rules) == 0 {
		for metricType, rule := range defaults {
			rules[appInsight.MetricType(metricType)] = rule
		}
	}

	return rules, nil
}

func getTaskInfoFromTaskReq(req *dispatcherpb.WorkerReq) (*dispatcherpb.PerfTestTaskInfo, error) {
	metaData := req.GetPerfCase().GetMetaData()
	if metaData == nil {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"the metadata of perf plan is null, task payload: %s",
			protobuf.MarshalJSONIgnoreError(req),
		)
	}

	info := &dispatcherpb.PerfTestTaskInfo{
		ProjectId:     req.GetProjectId(),
		PlanId:        req.GetPerfCase().GetPerfPlanId(),
		TaskId:        req.GetTaskId(),
		ExecuteId:     req.GetExecuteId(),
		TestTarget:    req.GetNodeData().GetPerfCase().GetPath(),
		Protocol:      metaData.GetProtocol(),
		GeneralConfig: req.GetGeneralConfig(),
		Duration:      metaData.GetDuration(),
		Times:         1,
		TriggerMode:   req.GetTriggerMode(),
		TriggerRule:   req.GetTriggerRule(),
		ExecuteType:   req.GetExecuteType(),
		CallbackType:  dispatcherpb.CallbackType_CallbackType_PERF_CASE,
		PerfCase:      req.GetPerfCase(),
		Keepalive:     metaData.GetKeepalive(),
		RateLimits:    metaData.GetRateLimits(),
	}

	return info, nil
}

func getPerfCaseFromTaskReq(req *dispatcherpb.WorkerReq) (*commonpb.PerfCaseContentV2, error) {
	perfCase := req.GetNodeData().GetPerfCase()
	if perfCase == nil {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"the perf case data is null, task payload: %s",
			protobuf.MarshalJSONIgnoreError(req),
		)
	}

	return &commonpb.PerfCaseContentV2{
		SetupSteps:    perfCase.GetSetupSteps(),
		SerialSteps:   perfCase.GetSerialSteps(),
		ParallelSteps: perfCase.GetParallelSteps(),
		TeardownSteps: perfCase.GetTeardownSteps(),
	}, nil
}

func (l *ExecutePerfTestTaskLogic) Run() (err error) {
	var (
		errCh = make(chan error, 1)

		taskID    = l.taskReq.GetTaskId()
		targetEnv = l.taskReq.GetPerfCase().GetMetaData().GetTargetEnv()
	)

	l.ctx, l.cancel = context.WithCancel(l.ctx)
	defer l.cancel()

	threading.GoSafe(
		func() {
			l.watchStopSignal()
		},
	)

	if targetEnv == commonpb.TargetEnvironment_TE_PRODUCTION ||
		targetEnv == commonpb.TargetEnvironment_TE_CANARY ||
		targetEnv == commonpb.TargetEnvironment_TE_STAGING {
		l.Infof("need to watch service metrics, task_id: %s, target_env: %s", taskID, targetEnv)
		threading.GoSafe(l.watchServiceMetrics)
	} else {
		l.Infof("no need to watch service metrics, task_id: %s, target_env: %s", taskID, targetEnv)
	}

	fn := func() (err error) {
		defer func() {
			if err == nil {
				l.state.Store(dispatcherpb.ComponentState_Success.String())
			}
		}()

		// new logic to execute perf test
		logic := perftest.NewExecutePerfTestLogic(l.ctx, l.svcCtx, l.taskReq, l.taskInfo, l.state, l.stopCh)
		err = logic.Execute()
		if err != nil {
			return err
		}

		return nil
	}
	threading.GoSafe(
		func() {
			errCh <- fn()
		},
	)

	return <-errCh
}

func (l *ExecutePerfTestTaskLogic) watchStopSignal() {
	ticker := timewheel.NewTicker(common.DefaultPeriodOfWatchStopSignal)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if err := l.isStopped(); err != nil {
				close(l.stopCh)
				l.cancel()
				l.Errorf("broadcast the stop signal and cancel context, error: %+v", err)
				return
			}
		}
	}
}

func (l *ExecutePerfTestTaskLogic) isStopped() (err error) {
	taskID := l.taskReq.GetTaskId()

	defer func() {
		if r := recover(); r != nil {
			l.state.Store(dispatcherpb.ComponentState_Panic.String())
			err = errorx.Errorf(
				codes.ExecuteSubTaskFailure,
				"got a panic while checking the stop status, task_id: %s, error: %+v",
				taskID, r,
			)
		}
	}()

	stop, err := utils.GetStopStatus(l.ctx, l.svcCtx.DispatcherRedis, taskID)
	if err != nil {
		l.state.Store(dispatcherpb.ComponentState_Panic.String())
		return errorx.Errorf(
			codes.ExecuteSubTaskFailure,
			"failed to get the stop status of task, task_id: %s,  err: %+v", taskID, err,
		)
	} else if stop {
		l.state.Store(dispatcherpb.ComponentState_Stop.String())
		return errorx.Errorf(
			codes.ExecuteSubTaskFailure, "got a stop signal of task, task_id: %s", taskID,
		)
	}

	return nil
}

func (l *ExecutePerfTestTaskLogic) watchServiceMetrics() {
	ticker := timewheel.NewTicker(common.DefaultPeriodOfWatchServiceMetrics)
	defer ticker.Stop()

	t := time.Now().Truncate(30 * time.Second)
	l.Infof("the stopping rules, task_id: %s, rules: %s", l.taskReq.GetTaskId(), jsonx.MarshalIgnoreError(l.rules))

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if err := l.watchMetrics(t); err != nil {
				l.Infof(
					"satisfy the stopping rules, task_id: %s, item: %s",
					l.taskReq.GetTaskId(), jsonx.MarshalIgnoreError(l.alarmItem),
				)
				l.state.Store(dispatcherpb.ComponentState_Stop.String())

				_, _ = l.svcCtx.DispatcherRPC.Stop(
					l.ctx, &dispatcherpb.StopReq{
						ProjectId:   l.taskReq.GetProjectId(),
						TaskId:      l.taskReq.GetTaskId(),
						Id:          l.taskReq.GetPerfCase().GetPerfPlanId(),
						ExecuteType: managerpb.ApiExecutionDataType_PERF_PLAN,
						ExecuteId:   l.taskReq.GetPerfCase().GetPerfPlanExecuteId(),

						Metadata: &dispatcherpb.StopMetadata{
							StopType: dispatcherpb.StopType_StopType_Auto,
							Reason:   stopReason,
							Detail: &dispatcherpb.StopMetadata_Rule{
								Rule: l.generateStopDetail(),
							},
						},
					},
				)
				return
			}
		}

		t = t.Add(common.DefaultPeriodOfWatchServiceMetrics)
	}
}

func (l *ExecutePerfTestTaskLogic) watchMetrics(now time.Time) error {
	var (
		setupSteps    = l.perfCase.GetSetupSteps()
		serialSteps   = l.perfCase.GetSerialSteps()
		parallelSteps = l.perfCase.GetParallelSteps()
		teardownSteps = l.perfCase.GetTeardownSteps()

		count   = len(setupSteps) + len(serialSteps) + len(parallelSteps) + len(teardownSteps)
		checked = make(map[string]lang.PlaceholderType, count)
	)

	return mr.MapReduceVoid[*metricReqItem, any](
		func(source chan<- *metricReqItem) {
			for _, steps := range [][]*commonpb.PerfCaseStepV2{
				setupSteps,
				serialSteps,
				parallelSteps,
				teardownSteps,
			} {
				for _, step := range steps {
					if step.GetService() == "" || step.GetNamespace() == "" || step.GetQueryPath() == "" {
						continue
					}

					for metricType, rule := range l.rules {
						key := fmt.Sprintf(
							"%s:%s:%s:%s", step.GetService(), step.GetNamespace(), step.GetQueryPath(), metricType,
						)
						if _, ok := checked[key]; ok {
							continue
						} else {
							checked[key] = lang.Placeholder
						}

						source <- &metricReqItem{
							key: key,
							req: &appInsight.QueryRangeMetricsReq{
								Metric:    metricType,
								Workload:  step.GetService(),
								Namespace: step.GetNamespace(),
								Method:    step.GetQueryPath(),
								StartedAt: now.Add(-2 * time.Minute),
								EndedAt:   now,
							},
							rule: rule,
						}
					}
				}
			}
		}, func(item *metricReqItem, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			var (
				v         any
				cacheItem *metricCacheItem
				ok, ok1   bool
			)
			v, ok = l.metricCache.Load(item.key)
			if ok {
				cacheItem, ok1 = v.(*metricCacheItem)
				if !ok1 {
					l.metricCache.Delete(item.key)
					ok = false
				}
			}

			out, err := l.svcCtx.AppInsightClient.QueryRangeMetrics(item.req)
			if err != nil {
				l.Error(err)
				return
			}

			for _, point := range out.Points {
				pointedAt := time.Unix(int64(point.Timestamp), 0)
				if cacheItem != nil && cacheItem.LatestAt.Compare(pointedAt) >= 0 {
					continue
				}

				pointedValue := float64(point.Value)
				if item.req.Metric == appInsight.MetricTypeOfFailRatio {
					pointedValue = pointedValue * 100
				}

				// check whether the metric value exceeds the threshold
				if pointedValue > item.rule.Threshold {
					if ok && cacheItem != nil {
						cacheItem.LatestAt = pointedAt
						cacheItem.Points = append(cacheItem.Points, point)

						// check whether the metric value exceeds the threshold for a long time
						if pointedAt.Sub(cacheItem.ReachedAt) >= item.rule.Duration {
							cacheItem.Alarm = true
							l.alarmItem = cacheItem

							cancel(nil) // return a `mr.ErrCancelWithNil` error
							return
						}
					} else {
						// when the metric value exceeds the threshold firstly, store the cache item
						cacheItem = &metricCacheItem{
							MetricType: item.req.Metric,
							Service:    item.req.Workload,
							Namespace:  item.req.Namespace,
							Method:     item.req.Method,
							Threshold:  item.rule.Threshold,
							Points: make(
								[]appInsight.Point, 0,
								(item.rule.Duration+30*time.Second-1)/(30*time.Second), // round up
							),
							ReachedAt: pointedAt,
							LatestAt:  pointedAt,
						}
						cacheItem.Points = append(cacheItem.Points, point)
						ok = true
						l.metricCache.Store(item.key, cacheItem)
					}
				} else if cacheItem != nil {
					// when the metric value is within the threshold, delete the cache item
					cacheItem = nil
					ok = false
					l.metricCache.Delete(item.key)
				}
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx), mr.WithWorkers(10),
	)
}

func (l *ExecutePerfTestTaskLogic) generateStopDetail() *dispatcherpb.StopDetailOfPerfStopRule {
	if l.alarmItem == nil {
		return nil
	}

	points := make([]*dispatcherpb.StopDetailOfPerfStopRule_MetricPoint, 0, len(l.alarmItem.Points))
	for _, point := range l.alarmItem.Points {
		points = append(
			points, &dispatcherpb.StopDetailOfPerfStopRule_MetricPoint{
				Timestamp: int64(point.Timestamp),
				Value:     float64(point.Value),
			},
		)
	}

	return &dispatcherpb.StopDetailOfPerfStopRule{
		MetricType: string(l.alarmItem.MetricType),
		Service:    l.alarmItem.Service,
		Namespace:  l.alarmItem.Namespace,
		Method:     l.alarmItem.Method,
		Threshold:  l.alarmItem.Threshold,
		ReachedAt:  l.alarmItem.ReachedAt.UnixMilli(),
		LatestAt:   l.alarmItem.LatestAt.UnixMilli(),
		Points:     points,
	}
}
