package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

type Config struct {
	zrpc.RpcServerConf

	DB types.DBConfig

	ReleaseRules []common.ReleaseRuleConfig `json:",omitempty,optional"`
}

func (c Config) ListenOn() string {
	return c.RpcServerConf.ListenOn
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}
