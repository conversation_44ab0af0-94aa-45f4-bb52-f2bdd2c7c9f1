package common

import "time"

const (
	AccountRedisKeyPrefix = "account:"

	ConstLockAcquireAccountTablePrefix = "lock:acquireAccount:table"
	ConstLockReleaseAccount            = "lock:releaseAccount"

	ConstRPCAcquireAccountTimeout = 20 * time.Second
	ConstRPCReleaseAccountTimeout = 20 * time.Second
)

const (
	BuiltinTableFieldOfAccount          = "account"
	BuiltinTableFieldOfPassword         = "password"
	BuiltinTableFieldOfCreateTime       = "create_time"
	BuiltinTableFieldOfAcquireTime      = "acquire_time"
	BuiltinTableFieldOfReturnTime       = "return_time"
	BuiltinTableFieldOfCoolingTime      = "cooling_time"
	BuiltinTableFieldOfOccupyState      = "occupy_state"
	BuiltinTableFieldOfRelatedExecuteID = "related_execute_id"
)

type PoolType int64 // 账户池类型
const (
	Repeatable   PoolType = 1 // 可复选
	Unrepeatable PoolType = 2 // 不可复选
)

type ColumnType int64 // 账户池字段类型
const (
	UNKNOWN   ColumnType = 0
	VARCHAR   ColumnType = 1
	INT       ColumnType = 2
	FLOAT     ColumnType = 3
	TINYINT   ColumnType = 4
	DATETIME  ColumnType = 5
	TIMESTAMP ColumnType = 6
)

var (
	ColumnTypeName = map[int64]string{
		0: "UNKNOWN",
		1: "VARCHAR",
		2: "INT",
		3: "FLOAT",
		4: "TINYINT",
		5: "DATETIME",
		6: "TIMESTAMP",
	}
	ColumnTypeValue = map[string]int64{
		"UNKNOWN":   0,
		"VARCHAR":   1,
		"INT":       2,
		"FLOAT":     3,
		"TINYINT":   4,
		"DATETIME":  5,
		"TIMESTAMP": 6,
	}
)

type YesOrNo int64 // 账户池字段是否允许为空，账户池字段是否为主键，账户池字段是否可见，是否允许查询数据调数小于筛选数量
const (
	YES YesOrNo = 1
	NO  YesOrNo = 2
)

var (
	YesOrNoName = map[int64]string{
		1: "YES",
		2: "NO",
	}
	YesOrNoValue = map[string]int64{
		"YES": 1,
		"NO":  2,
	}
)

type PoolEnvDataUploadPurpose int64 // 账户池环境数据上传用途
const (
	PurposeAddData    PoolEnvDataUploadPurpose = 1 // 新增数据
	PurposeDeleteData PoolEnvDataUploadPurpose = 2 // 删除数据
	PurposeModifyData PoolEnvDataUploadPurpose = 3 // 修改数据
)

var (
	PoolEnvDataUploadPurposeName = map[int64]string{
		1: "ADD_DATA",
		2: "DELETE_DATA",
		3: "MODIFY_DATA",
	}
	PoolEnvDataUploadPurposeValue = map[string]int64{
		"ADD_DATA":    1,
		"DELETE_DATA": 2,
		"MODIFY_DATA": 3,
	}
)

type EnvDataProcessState int64 // 账户池环境数据处理状态
const (
	ProcessUnchecked      EnvDataProcessState = 1  // 待检测
	ProcessChecking       EnvDataProcessState = 21 // 检测中
	ProcessQuerying       EnvDataProcessState = 22 // 筛选中
	ProcessDetected       EnvDataProcessState = 31 // 已检测
	ProcessEndOfQuery     EnvDataProcessState = 32 // 已筛选
	ProcessToBeUpdate     EnvDataProcessState = 33 // 待更新
	ProcessDataProcessing EnvDataProcessState = 4  // 数据处理中
	ProcessFinishing      EnvDataProcessState = 51 // 数据处理完成, 从这里开始为结束状态
	ProcessTerminated     EnvDataProcessState = 52 // 已终止
	ProcessException      EnvDataProcessState = 53 // 数据处理出现异常
	ProcessNoneValidData  EnvDataProcessState = 54 // 没有任何有效的数据
)

var (
	EnvDataProcessStateName = map[int64]string{
		1:  "UNCHECKED",
		21: "CHECKING",
		22: "QUERYING",
		31: "DETECTED",
		32: "END_OF_QUERY",
		33: "TO_BE_UPDATE",
		4:  "DATA_PROCESSING",
		51: "FINISHING",
		52: "TERMINATED",
		53: "EXCEPTION",
		54: "NONE_VALID_DATA",
	}
	EnvDataProcessStateValue = map[string]int64{
		"UNCHECKED":       1,
		"CHECKING":        21,
		"QUERYING":        22,
		"DETECTED":        31,
		"END_OF_QUERY":    32,
		"TO_BE_UPDATE":    33,
		"DATA_PROCESSING": 4,
		"FINISHING":       51,
		"TERMINATED":      52,
		"EXCEPTION":       53,
		"NONE_VALID_DATA": 54,
	}
)

type EnvDataProcessFirstWay int64 // 账户池环境数据处理第一个步骤名称
const (
	UploadToAddData    EnvDataProcessFirstWay = 1  // 上传excel新增账户数据
	UploadToDeleteData EnvDataProcessFirstWay = 21 // 上传excel删除账户数据
	QueryToDeleteData  EnvDataProcessFirstWay = 22 // 筛选出待删除的账户数据
	UploadToModifyData EnvDataProcessFirstWay = 31 // 上传excel修改账户数据
	QueryToModifyData  EnvDataProcessFirstWay = 32 // 筛选出待修改的账户数据
)

type EnvDataOccupyState int64 // 账户池环境数据占用状态
const (
	OccupyStateUnused     EnvDataOccupyState = 1 // 未使用
	OccupyStateUsing      EnvDataOccupyState = 2 //(被筛选出来)用于测试
	OccupyStateToBeUpdate EnvDataOccupyState = 3 //(被筛选出来)待更新
	OccupyStateToBeDelete EnvDataOccupyState = 4 //(被筛选出来)待删除
)

var (
	EnvDataOccupyStateName = map[int64]string{
		1: "UNUSED",
		2: "USING",
		3: "TO_BE_UPDATE",
		4: "TO_BE_DELETE",
	}

	EnvDataOccupyStateValue = map[string]int64{
		"UNUSED":       1,
		"USING":        2,
		"TO_BE_UPDATE": 3,
		"TO_BE_DELETE": 4,
	}
)

type QueryPoolEnvResponseCode int64 // 筛选账号池数据响应码
const (
	ResCodeNoEnoughAccount QueryPoolEnvResponseCode = 100 // 筛选到满足条件的账号个数为m, 0 < m < n
	ResCodeOk              QueryPoolEnvResponseCode = 200 // 筛选到符合条件的n个账号并都成功修改为「USING」状态
	ResCodeException       QueryPoolEnvResponseCode = 500 // 筛选的过程中发生异常
)

type ExecuteWay int64 // 流水操作类型
const (
	AddData    ExecuteWay = 1 // 新增数据
	DeleteData ExecuteWay = 2 // 删除数据
	ModifyData ExecuteWay = 3 // 修噶数据
)

var (
	ExecuteWayName = map[int64]string{
		1: "ADD_DATA",
		2: "DELETE_DATA",
		3: "MODIFY_DATA",
	}
	ExecuteWayValue = map[string]int64{
		"ADD_DATA":    1,
		"DELETE_DATA": 2,
		"MODIFY_DATA": 3,
	}
)
