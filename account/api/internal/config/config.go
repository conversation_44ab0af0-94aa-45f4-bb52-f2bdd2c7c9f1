package config

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

type FileStoreConf struct {
	ExcelPath string `json:"excelPath" yaml:"excel_path"`
	SqlPath   string `json:"sqlPath" yaml:"sql_path"`
}

type Config struct {
	rest.RestConf

	DB         types.DBConfig
	Redis      redis.RedisConf
	Cache      cache.CacheConf
	FileStore  FileStoreConf
	UserRpc    zrpc.RpcClientConf
	AccountRpc zrpc.RpcClientConf
}

func (c Config) ListenOn() string {
	return fmt.Sprintf("%s:%d", c.<PERSON>, c.<PERSON>)
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}
