package utils

import (
	"fmt"
	"strings"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

const (
	ConstProjectIdPrefix           = "project_id:"
	ConstCategoryIdPrefix          = "category_id:"
	ConstTagIdPrefix               = "tag_id:"
	ConstGeneralConfigIdPrefix     = "general_config_id:"
	ConstAccountConfigIdPrefix     = "account_config_id:"
	ConstGitConfigIDPrefix         = "git_config_id:"
	ConstProtobufConfigIDPrefix    = "protobuf_config_id:"
	ConstComponentGroupIdPrefix    = "component_group_id:"
	ConstCaseIdPrefix              = "case_id:"
	ConstSuiteIdPrefix             = "suite_id:"
	ConstServiceIdPrefix           = "service_id:"
	ConstPlanIdPrefix              = "plan_id:"
	ConstInterfaceDocumentIdPrefix = "interface_document_id:"
	ConstInterfaceSchemaIdPrefix   = "interface_schema_id:"
	ConstInterfaceConfigIdPrefix   = "interface_config_id:"
	ConstInterfaceCaseIdPrefix     = "interface_case_id:"
	ConstStepIDPrefix              = "step_id:"
	ConstUICaseIDPrefix            = "ui_case_id:"
	ConstUISuiteIDPrefix           = "ui_suite_id:"
	ConstUIPlanIDPrefix            = "ui_plan_id:"
	ConstVersionPrefix             = "version:"
	ConstTaskIdPrefix              = "task_id:"
	ConstExecuteIdPrefix           = "execute_id:"
	ConstNotifyIdPrefix            = "notify_id:"
	ConstReviewIdPrefix            = "review_id:"
	ConstRuleIDPrefix              = "rule_id:"
	ConstAlertIDPrefix             = "alert_id:"
	ConstFileIDPrefix              = "file_id:"
	ConstPerfDataIDPrefix          = "perf_data_id:"
	ConstPerfCaseIDPrefix          = "perf_case_id:"
	ConstPerfCaseStepIDPrefix      = "perf_case_step_id:"
	ConstPerfPlanIDPrefix          = "perf_plan_id:"
	ConstPerfStopRuleIDPrefix      = "perf_stop_rule_id:"
	ConstClientIDPrefix            = "client_id:"
	ConstAiPrefix                  = "ai:"
	ConstAiAssistantIdPrefix       = ConstAiPrefix + "assistant_id:"
	ConstAiDocumentIdPrefix        = ConstAiPrefix + "document_id:"
	ConstAiSessionIdPrefix         = ConstAiPrefix + "session_id:"
	ConstAiDirIdPrefix             = ConstAiPrefix + "dir_id:"
	ConstAiCaseIdPrefix            = ConstAiPrefix + "case_id:"
	ConstAiKnowledgeIdPrefix       = ConstAiPrefix + "knowledge_id:"
	ConstAiRevisionIdPrefix        = ConstAiPrefix + "revision_id:"
	ConstAiDataIdPrefix            = ConstAiPrefix + "case_data_id:"
	ConstAiRefIdPrefix             = ConstAiPrefix + "case_ref_id:"
	ConstAiMapIdPrefix             = ConstAiPrefix + "map_id:"
	ConstStabilityPlanIDPrefix     = "stability_plan_id:"

	ConstAiTwBetaIdPrefix = ConstAiPrefix + "tw_beta_id:"

	TaskInfoPrefix          = "task:info"
	TaskInfoKeyTaskId       = "task_id"
	TaskInfoKeyExecuteId    = "execute_id"
	TaskInfoKeyProjectId    = "project_id"
	TaskInfoKeyExecuteType  = "execute_type"
	TaskInfoKeyTotal        = "total"
	TaskInfoKeyStarttime    = "starttime"
	TaskInfoKeyPriorityType = "priority_type"
	TaskStatusPrefix        = "task:status"
	TaskMemberPrefix        = "task:member"
	TaskScanTime            = "task:scan:time"

	ReporterMQCPrefix = "reporter_mqc:"
)

func GenProjectId() string {
	return utils.GenNanoId(ConstProjectIdPrefix)
}

func GenTagId() string {
	return utils.GenNanoId(ConstTagIdPrefix)
}

func GenCategoryId() string {
	return utils.GenNanoId(ConstCategoryIdPrefix)
}

func GenGeneralConfigId() string {
	return utils.GenNanoId(ConstGeneralConfigIdPrefix)
}

func GenAccountConfigId() string {
	return utils.GenNanoId(ConstAccountConfigIdPrefix)
}

func GenGitConfigID() string {
	return utils.GenNanoId(ConstGitConfigIDPrefix)
}

func GenProtobufConfigID() string {
	return utils.GenNanoId(ConstProtobufConfigIDPrefix)
}

func GenComponentGroupId() string {
	return utils.GenNanoId(ConstComponentGroupIdPrefix)
}

func GenCaseId() string {
	return utils.GenNanoId(ConstCaseIdPrefix)
}

func GenSuiteId() string {
	return utils.GenNanoId(ConstSuiteIdPrefix)
}

func GenServiceId() string {
	return utils.GenNanoId(ConstServiceIdPrefix)
}

func GenPlanId() string {
	return utils.GenNanoId(ConstPlanIdPrefix)
}

func GenInterfaceDocumentId() string {
	return utils.GenNanoId(ConstInterfaceDocumentIdPrefix)
}

func GenInterfaceSchemaId() string {
	return utils.GenNanoId(ConstInterfaceSchemaIdPrefix)
}

func GenInterfaceConfigId() string {
	return utils.GenNanoId(ConstInterfaceConfigIdPrefix)
}

func GenInterfaceCaseId() string {
	return utils.GenNanoId(ConstInterfaceCaseIdPrefix)
}

func GenStepID() string {
	return utils.GenNanoId(ConstStepIDPrefix)
}

func GenUICaseID() string {
	return utils.GenNanoId(ConstUICaseIDPrefix)
}

func GenUISuiteID() string {
	return utils.GenNanoId(ConstUISuiteIDPrefix)
}

func GenUIPlanID() string {
	return utils.GenNanoId(ConstUIPlanIDPrefix)
}

func GenVersion() string {
	return fmt.Sprintf(
		"%s%s:%s", ConstVersionPrefix, strings.ReplaceAll(time.Now().Format("20060102150405.000"), ".", ""),
		utils.GenNanoId("", 5),
	)
}

func GenTaskId() string {
	return utils.GenNanoId(ConstTaskIdPrefix)
}

func GenExecuteId() string {
	return utils.GenNanoId(ConstExecuteIdPrefix)
}

func GenNotifyId() string {
	return utils.GenNanoId(ConstNotifyIdPrefix)
}

func GenReviewId() string {
	return utils.GenNanoId(ConstReviewIdPrefix)
}

func GenRuleID() string {
	return utils.GenNanoId(ConstRuleIDPrefix)
}

func GenAlertID() string {
	return utils.GenNanoId(ConstAlertIDPrefix)
}

func GenFileID() string {
	return utils.GenNanoId(ConstFileIDPrefix)
}

func GenPerfDataID() string {
	return utils.GenNanoId(ConstPerfDataIDPrefix)
}

func GenPerfCaseID() string {
	return utils.GenNanoId(ConstPerfCaseIDPrefix)
}

func GenPerfCaseStepID() string {
	return utils.GenNanoId(ConstPerfCaseStepIDPrefix)
}

func GenPerfPlanID() string {
	return utils.GenNanoId(ConstPerfPlanIDPrefix)
}

func GenPerfStopRuleID() string {
	return utils.GenNanoId(ConstPerfStopRuleIDPrefix)
}

func GenClientID() string {
	return utils.GenNanoId(ConstClientIDPrefix)
}

func GenTaskInfoKey(subKey string) string {
	return fmt.Sprintf("%s::%s", TaskInfoPrefix, subKey)
}

func GenTaskStatusKey(subKey string) string {
	return fmt.Sprintf("%s::%s", TaskStatusPrefix, subKey)
}

func GenTaskMemberKey(subKey string) string {
	return fmt.Sprintf("%s::%s", TaskMemberPrefix, subKey)
}

func GenReporterMQCName() string {
	return utils.GenNanoId(ReporterMQCPrefix)
}

func GenAiAssistantId() string {
	return utils.GenNanoId(ConstAiAssistantIdPrefix)
}

func GenAiDocumentId() string {
	return utils.GenNanoId(ConstAiDocumentIdPrefix)
}

func GenAiSessionId() string {
	return utils.GenNanoId(ConstAiSessionIdPrefix)
}

func GenAiDirId() string {
	return utils.GenNanoId(ConstAiDirIdPrefix)
}

func GenAiCaseId() string {
	return utils.GenNanoId(ConstAiCaseIdPrefix)
}

func GenAiKnowledgeId() string {
	return utils.GenNanoId(ConstAiKnowledgeIdPrefix)
}

func GenAiRevisionId() string {
	return utils.GenNanoId(ConstAiRevisionIdPrefix)
}

func GenAiDataId() string {
	return utils.GenNanoId(ConstAiDataIdPrefix)
}

func GenAiRefId() string {
	return utils.GenNanoId(ConstAiRefIdPrefix)
}

func GenAiMapId() string {
	return utils.GenNanoId(ConstAiMapIdPrefix)
}

func GenTwBetaId() string {
	return utils.GenNanoId(ConstAiTwBetaIdPrefix)
}

func GenStabilityPlanID() string {
	return utils.GenNanoId(ConstStabilityPlanIDPrefix)
}
