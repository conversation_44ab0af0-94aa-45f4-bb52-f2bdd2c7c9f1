package utils

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/electricbubble/gadb"
	gid "github.com/electricbubble/gidevice"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
)

const (
	strOfFailedToAuthenticate = "failed to authenticate to"

	usbMuxPort = 27015

	delayTimeOfADBConnect = 2 * time.Second
)

func ADBConnect(udid, remoteAddress string) (client *gadb.Client, device *gadb.Device, err error) {
	client, err = gadb.NewClient()
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to create adb client, udid: %s", udid)
	}

	if udid == "" && remoteAddress == "" {
		devices, err := client.ListDevices()
		if err != nil {
			return nil, nil, errors.Wrap(err, "failed to list devices")
		} else if len(devices) == 0 {
			return nil, nil, errors.New("not found any devices")
		}

		return client, devices[0], nil
	}

	var serial string
	if udid != "" {
		serial = udid
	}
	if remoteAddress != "" {
		hostAndPort := strings.Split(remoteAddress, ":")
		if len(hostAndPort) != 2 {
			return nil, nil, errors.Errorf(
				"invalid device remote address, udid: %s, remote_address: %s", udid, remoteAddress,
			)
		}

		port, err := strconv.Atoi(hostAndPort[1])
		if err != nil {
			return nil, nil, errors.Wrapf(
				err,
				"cannot convert the device remote address port to integer, udid: %s, remote_address: %s, port: %s",
				udid, remoteAddress, hostAndPort[1],
			)
		}

		if err = client.Connect(hostAndPort[0], gadb.WithPort(port)); err != nil {
			// ignore the `failed to authenticate to` error because it might have actually connected successfully.
			// if it indeed fails to connect, an error will be returned in the subsequent 'FindDeviceBySerial' call.
			if !strings.Contains(err.Error(), strOfFailedToAuthenticate) {
				return nil, nil, errors.Wrapf(
					err,
					"failed to connect to the device, udid: %s, remote_address: %s",
					udid, remoteAddress,
				)
			}

			// even if it has connected successfully, there is a possibility of encountering a
			// 'device unauthorized' situation, so we delay here for a bit.
			time.Sleep(delayTimeOfADBConnect)
		}

		serial = remoteAddress
	}

	device, err = client.FindDeviceBySerial(serial)
	if err != nil {
		return nil, nil, errors.Wrapf(
			err,
			"failed to find the device by remote address, udid: %s, remote_address: %s",
			udid, remoteAddress,
		)
	}

	return client, device, nil
}

func GIDConnect(udid, remoteAddress string) (usb gid.Usbmux, device gid.Device, err error) {
	u, err := url.Parse(remoteAddress)
	if err != nil {
		return nil, nil, errors.Wrapf(
			err,
			"failed to parse the device remote address, udid: %s, remote_address: %s",
			udid, remoteAddress,
		)
	}
	address := fmt.Sprintf("%s:%d", u.Hostname(), usbMuxPort)

	if err = caller.RetryDo(
		caller.MaxRetryCount, func() error {
			usb, err = gid.NewUsbmuxWithAddress(address)
			if err != nil {
				return errors.Wrapf(err, "failed to create an usbmux, udid: %s, address: %s", udid, address)
			}

			device, err = usb.FindDeviceByUDID(udid)
			if err != nil {
				return errors.Wrapf(err, "failed to find the device by udid, udid: %s", udid)
			}

			return nil
		},
	); err != nil {
		return nil, nil, err
	}

	return usb, device, nil
}
