package collector

import (
	"context"
	"sync"
	"time"

	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/instruments"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"
)

var _ Collector = (*IOSCollector)(nil)

type IOSCollector struct {
	*basicCollector

	device   *ios.DeviceEntry
	udid     string
	bundleID string
}

func NewIOSCollector(ctx context.Context, device *ios.DeviceEntry, bundleID string, options ...Option) *IOSCollector {
	bc := &basicCollector{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		dataTypes:        defaultDataTypes.Clone(),
		intervalOfCPU:    defaultIntervalOfCPU,
		intervalOfMemory: defaultIntervalOfMemory,
		intervalOfFPS:    defaultIntervalOfFPS,
		workers:          defaultWorkers,

		stopCh: make(chan lang.PlaceholderType),
		dataCh: make(chan *MetricData, defaultDataTypes.Size()),
	}
	bc.callback = bc.log
	for _, option := range options {
		option(bc)
	}

	c := &IOSCollector{
		basicCollector: bc,

		device:   device,
		udid:     device.Properties.SerialNumber,
		bundleID: bundleID,
	}
	c.fixupOptions()
	return c
}

func (c *IOSCollector) fixupOptions() {
	if c.dataTypes.Has(CPU) && c.dataTypes.Has(MEMORY) {
		interval := min(c.intervalOfCPU, c.intervalOfMemory)
		c.intervalOfCPU = interval
		c.intervalOfMemory = interval
	}
}

func (c *IOSCollector) Start() error {
	var err error

	c.startOnce.Do(
		func() {
			proc.AddShutdownListener(
				func() {
					_ = c.Stop()
				},
			)

			threading.GoSafeCtx(c.ctx, c.collect)
			threading.GoSafeCtx(c.ctx, c.handleData)
		},
	)

	return err
}

func (c *IOSCollector) Stop() error {
	c.stopOnce.Do(
		func() {
			close(c.stopCh)
			time.Sleep(time.Second)
		},
	)

	return nil
}

func (c *IOSCollector) Finished() <-chan lang.PlaceholderType {
	return c.stopCh
}

func (c *IOSCollector) DeviceID() string {
	return c.udid
}

func (c *IOSCollector) collect() {
	var (
		udid     = c.udid
		bundleID = c.bundleID
	)

	defer func() {
		close(c.dataCh)
		_ = c.Stop()
	}()

	if c.dataTypes.Has(CPU) || c.dataTypes.Has(MEMORY) {
		threading.GoSafeCtx(c.ctx, c.collectCPUAndMemory)
	}
	if c.dataTypes.Has(FPS) {
		threading.GoSafeCtx(c.ctx, c.collectFPS)
	}

	select {
	case <-c.ctx.Done():
		c.Infof("got a done signal while collecting metric data, udid: %s, bundle_id: %s", udid, bundleID)
		break
	case <-c.stopCh:
		c.Infof("got a stop signal while collecting metric data, udid: %s, bundle_id: %s", udid, bundleID)
		break
	}
}

func (c *IOSCollector) collectCPUAndMemory() {
	var (
		udid     = c.udid
		bundleID = c.bundleID
	)

	svc, err := instruments.NewSysmontapService(
		*c.device,
		instruments.WithBundleIDOfSysmontap(bundleID),
		instruments.WithIntervalOfSysmontap(c.intervalOfCPU),
		instruments.WithCallbackFuncOfSysmontap(
			func(data *instruments.SysmontapData) {
				if c.dataTypes.Has(CPU) {
					c.dataCh <- &MetricData{
						Type: CPU,
						Data: &CPUData{
							Timestamp: data.Timestamp,
							Usage:     data.Usage,
						},
					}
				}
				if c.dataTypes.Has(MEMORY) {
					c.dataCh <- &MetricData{
						Type: MEMORY,
						Data: &MemoryDataOfIOS{
							Timestamp: data.Timestamp,
							PSS:       data.PSS,
							RSS:       data.RSS,
						},
					}
				}
			},
		),
	)
	if err != nil {
		c.Errorf("failed to create sysmontap service, udid: %s, bundle_id: %s, error: %s", udid, bundleID, err)
		return
	}
	defer func(svc *instruments.SysmontapService) {
		if svc != nil {
			_ = svc.Close()
		}
	}(svc)

	for {
		select {
		case <-c.ctx.Done():
			c.Debugf("got a done signal while collecting cpu and memory, udid: %s, bundle_id: %s", udid, bundleID)
			return
		case <-c.stopCh:
			c.Debugf("got a stop signal while collecting cpu and memory, udid: %s, bundle_id: %s", udid, bundleID)
			return
		}
	}
}

func (c *IOSCollector) collectFPS() {
	var (
		udid     = c.udid
		bundleID = c.bundleID
	)

	svc, err := instruments.NewGraphicsService(
		*c.device,
		instruments.WithIntervalOfGraphics(c.intervalOfFPS),
		instruments.WithCallbackFuncOfGraphics(
			func(data *instruments.GraphicsData) {
				c.dataCh <- &MetricData{
					Type: FPS,
					Data: &FPSData{
						Timestamp: data.Timestamp,
						FPS:       data.FPS,
					},
				}
			},
		),
	)
	if err != nil {
		c.Errorf("failed to create graphics service, udid: %s, bundle_id: %s, error: %s", udid, bundleID, err)
		return
	}
	defer func(svc *instruments.GraphicsService) {
		if svc != nil {
			_ = svc.Close()
		}
	}(svc)

	for {
		select {
		case <-c.ctx.Done():
			c.Debugf("got a done signal while collecting fps, udid: %s, bundle_id: %s", udid, bundleID)
			return
		case <-c.stopCh:
			c.Debugf("got a stop signal while collecting fps, udid: %s, bundle_id: %s", udid, bundleID)
			return
		}
	}
}

func (c *IOSCollector) handleData() {
	var (
		udid     = c.udid
		bundleID = c.bundleID

		wg sync.WaitGroup
	)

	defer func() {
		wg.Wait()
		drain(c.dataCh)
	}()

	pool := make(chan lang.PlaceholderType, c.workers)
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-c.stopCh:
			return
		case pool <- lang.Placeholder:
			data, ok := <-c.dataCh
			if !ok {
				<-pool
				return
			}

			wg.Add(1)
			threading.GoSafe(
				func() {
					defer func() {
						if r := recover(); r != nil {
							c.Errorf(
								"failed to handle data by callback function, udid: %s, bundle_id: %s, data: %s, error: %+v",
								udid, bundleID, jsonx.MarshalIgnoreError(data), r,
							)
						}

						wg.Done()
						<-pool
					}()

					// NOTE: async callback execution prevents `wg.Wait()` block caused by long-running callbacks
					done := make(chan lang.PlaceholderType)
					threading.GoSafe(
						func() {
							defer close(done)

							c.callback(data)
						},
					)

					select {
					case <-c.ctx.Done():
						return
					case <-c.stopCh:
						return
					case <-done:
						return
					}
				},
			)
		}
	}
}
