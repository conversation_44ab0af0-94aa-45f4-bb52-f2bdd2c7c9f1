package collector

import (
	"strconv"
	"time"
)

type (
	CallBackFunc func(*MetricData)
	EachFunc     func(dataType DataType, series Series, x, y string)

	IMetricData interface {
		Each(fn EachFunc)
	}

	MetricData struct {
		Type DataType    `json:"type"` // 数据类型
		Data IMetricData `json:"data"` // 指标数据
	}

	CPUData struct {
		Timestamp time.Time `json:"timestamp"` // 时间戳
		Usage     float64   `json:"usage"`     // CPU使用率
	}

	MemoryDataOfAndroid struct {
		Timestamp time.Time `json:"timestamp"` // 时间戳
		PSS       int64     `json:"pss"`       // 进程占用的物理内存大小，会按比例分担共享内存（单位：KB）
		RSS       int64     `json:"rss"`       // 进程占用的物理内存大小，分担全部共享内存（单位：KB）
		Swap      int64     `json:"swap"`      // 进程占用的Swap内存大小（单位：KB）
	}

	MemoryDataOfIOS struct {
		Timestamp time.Time `json:"timestamp"` // 时间戳
		PSS       int64     `json:"pss"`       // 应用占用的物理内存大小（单位：KB）
		RSS       int64     `json:"rss"`       // 应用分配的物理内存大小（单位：KB）
		VSS       int64     `json:"vss"`       // 应用占用的虚拟内存大小（单位：KB）
	}

	FPSData struct {
		Timestamp time.Time `json:"timestamp"` // 时间戳
		FPS       float64   `json:"fps"`       // 帧率
	}
)

func (x *CPUData) Each(fn EachFunc) {
	fn(CPU, SeriesOfUsage, x.Timestamp.Format(time.DateTime), strconv.FormatFloat(x.Usage, 'f', 2, 64))
}

func (x *MemoryDataOfAndroid) Each(fn EachFunc) {
	fn(MEMORY, SeriesOfPSS, x.Timestamp.Format(time.DateTime), strconv.FormatInt(x.PSS/1024, 10))
	fn(MEMORY, SeriesOfRSS, x.Timestamp.Format(time.DateTime), strconv.FormatInt(x.RSS/1024, 10))
	fn(MEMORY, SeriesOfSwap, x.Timestamp.Format(time.DateTime), strconv.FormatInt(x.Swap/1024, 10))
}

func (x *MemoryDataOfIOS) Each(fn EachFunc) {
	fn(MEMORY, SeriesOfPSS, x.Timestamp.Format(time.DateTime), strconv.FormatInt(x.PSS/1024, 10))
	fn(MEMORY, SeriesOfRSS, x.Timestamp.Format(time.DateTime), strconv.FormatInt(x.RSS/1024, 10))
	fn(MEMORY, SeriesOfVSS, x.Timestamp.Format(time.DateTime), strconv.FormatInt(x.VSS/1024, 10))
}

func (x *FPSData) Each(fn EachFunc) {
	fn(FPS, SeriesOfFPS, x.Timestamp.Format(time.DateTime), strconv.FormatFloat(x.FPS, 'f', 2, 64))
}
