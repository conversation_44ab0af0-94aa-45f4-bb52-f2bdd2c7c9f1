package logger

import (
	"bytes"
	"context"
	"fmt"
	"io"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/atomic"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

var (
	_ io.Writer = (*StepLogger)(nil)

	bufPool = utils.NewPool()
)

type StepLogger struct {
	logx.Logger

	b *bytes.Buffer
	l *zap.Logger

	flag *atomic.Bool
}

func NewStepLogger(ctx context.Context, logConf logx.LogConf) *StepLogger {
	logger := logx.WithContext(ctx)
	logger.WithCallerSkip(2)

	buf := bufPool.Get()

	return &StepLogger{
		Logger: logger,

		b: buf,
		l: log.NewZapLoggerWithWriter(logConf, buf, zap.AddCaller(), zap.AddCallerSkip(-1)),

		flag: atomic.NewBool(false),
	}
}

func (l *StepLogger) Free() {
	l.flag.Store(true)

	bufPool.Put(l.b)
	l.b = nil
	l.l = nil
}

func (l *StepLogger) Sync() string {
	if l.flag.Load() {
		return ""
	}

	defer l.Reset()

	output := l.String()
	return output
}

func (l *StepLogger) Bytes() []byte {
	if l.flag.Load() {
		return nil
	}

	return l.b.Bytes()
}

func (l *StepLogger) String() string {
	if l.flag.Load() {
		return ""
	}

	return l.b.String()
}

func (l *StepLogger) Reset() {
	if !l.flag.Load() {
		l.b.Reset()
	}
}

func (l *StepLogger) Write(p []byte) (int, error) {
	return l.b.Write(p)
}

func (l *StepLogger) Error(v ...any) {
	l.error(fmt.Sprint(v...))
}

func (l *StepLogger) Errorf(format string, v ...any) {
	l.error(fmt.Sprintf(format, v...))
}

func (l *StepLogger) Warn(v ...any) {
	l.warn(fmt.Sprint(v...))
}

func (l *StepLogger) Warnf(format string, v ...any) {
	l.warn(fmt.Sprintf(format, v...))
}

func (l *StepLogger) Info(v ...any) {
	l.info(fmt.Sprint(v...))
}

func (l *StepLogger) Infof(format string, v ...any) {
	l.info(fmt.Sprintf(format, v...))
}

func (l *StepLogger) Debug(v ...any) {
	l.debug(fmt.Sprint(v...))
}

func (l *StepLogger) Debugf(format string, v ...any) {
	l.debug(fmt.Sprintf(format, v...))
}

func (l *StepLogger) error(msg string) {
	if !l.flag.Load() {
		l.Logger.Error(msg)
		l.l.Error(msg)
	}
}

func (l *StepLogger) warn(msg string) {
	if !l.flag.Load() {
		l.Logger.Warn(msg)
		l.l.Warn(msg)
	}
}

func (l *StepLogger) info(msg string) {
	if !l.flag.Load() {
		l.Logger.Info(msg)
		l.l.Info(msg)
	}
}

func (l *StepLogger) debug(msg string) {
	if !l.flag.Load() {
		l.Logger.Debug(msg)
		l.l.Debug(msg)
	}
}
