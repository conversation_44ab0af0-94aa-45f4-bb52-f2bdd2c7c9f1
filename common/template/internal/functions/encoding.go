package functions

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"io"
)

// Md5 returns the MD5 checksum of s as a hexadecimal encoded string
func Md5(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

// Sha1 returns the SHA1 checksum of s as a hexadecimal encoded string.
func Sha1(s string) string {
	h := sha1.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

// Sha256 returns the SHA256 checksum of s as a hexadecimal encoded string.
func Sha256(s string) string {
	h := sha256.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

// HmacSHA1 the HMAC-SHA1 tag for the given message and key as a base64 encoded string
func HmacSHA1(message, key string) string {
	mac := hmac.New(sha1.New, []byte(key))
	_, _ = io.WriteString(mac, message)
	s := base64.StdEncoding.EncodeToString(mac.Sum(nil))
	return s
}

// HmacSHA256 returns the HMAC-SHA256 tag for the given message and key as a base64 encoded string
func HmacSHA256(message, key string) string {
	mac := hmac.New(sha256.New, []byte(key))
	_, _ = io.WriteString(mac, message)
	s := base64.StdEncoding.EncodeToString(mac.Sum(nil))
	return s
}

// Hex returns the hexadecimal encoding of s
func Hex(s string) string {
	if s == "" {
		return s
	}
	return hex.EncodeToString([]byte(s))
}
