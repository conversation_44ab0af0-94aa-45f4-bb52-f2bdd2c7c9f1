// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/stability.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// StabilityCustomDevices 稳定性测试自定义设备
type StabilityCustomDevices struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Devices:
	//
	//	*StabilityCustomDevices_Udids
	//	*StabilityCustomDevices_Count
	Devices       isStabilityCustomDevices_Devices `protobuf_oneof:"devices"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityCustomDevices) Reset() {
	*x = StabilityCustomDevices{}
	mi := &file_common_stability_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityCustomDevices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityCustomDevices) ProtoMessage() {}

func (x *StabilityCustomDevices) ProtoReflect() protoreflect.Message {
	mi := &file_common_stability_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityCustomDevices.ProtoReflect.Descriptor instead.
func (*StabilityCustomDevices) Descriptor() ([]byte, []int) {
	return file_common_stability_proto_rawDescGZIP(), []int{0}
}

func (x *StabilityCustomDevices) GetDevices() isStabilityCustomDevices_Devices {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *StabilityCustomDevices) GetUdids() *structpb.ListValue {
	if x != nil {
		if x, ok := x.Devices.(*StabilityCustomDevices_Udids); ok {
			return x.Udids
		}
	}
	return nil
}

func (x *StabilityCustomDevices) GetCount() uint32 {
	if x != nil {
		if x, ok := x.Devices.(*StabilityCustomDevices_Count); ok {
			return x.Count
		}
	}
	return 0
}

type isStabilityCustomDevices_Devices interface {
	isStabilityCustomDevices_Devices()
}

type StabilityCustomDevices_Udids struct {
	Udids *structpb.ListValue `protobuf:"bytes,1,opt,name=udids,proto3,oneof"` // 设备编号列表（指定设备）
}

type StabilityCustomDevices_Count struct {
	Count uint32 `protobuf:"varint,2,opt,name=count,proto3,oneof"` // 设备数量（随机选择设备）
}

func (*StabilityCustomDevices_Udids) isStabilityCustomDevices_Devices() {}

func (*StabilityCustomDevices_Count) isStabilityCustomDevices_Devices() {}

// StabilityCustomScript 稳定性测试自定义脚本
type StabilityCustomScript struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Script:
	//
	//	*StabilityCustomScript_GitConfig
	//	*StabilityCustomScript_Image
	Script        isStabilityCustomScript_Script `protobuf_oneof:"script"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityCustomScript) Reset() {
	*x = StabilityCustomScript{}
	mi := &file_common_stability_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityCustomScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityCustomScript) ProtoMessage() {}

func (x *StabilityCustomScript) ProtoReflect() protoreflect.Message {
	mi := &file_common_stability_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityCustomScript.ProtoReflect.Descriptor instead.
func (*StabilityCustomScript) Descriptor() ([]byte, []int) {
	return file_common_stability_proto_rawDescGZIP(), []int{1}
}

func (x *StabilityCustomScript) GetScript() isStabilityCustomScript_Script {
	if x != nil {
		return x.Script
	}
	return nil
}

func (x *StabilityCustomScript) GetGitConfig() *GitConfig {
	if x != nil {
		if x, ok := x.Script.(*StabilityCustomScript_GitConfig); ok {
			return x.GitConfig
		}
	}
	return nil
}

func (x *StabilityCustomScript) GetImage() string {
	if x != nil {
		if x, ok := x.Script.(*StabilityCustomScript_Image); ok {
			return x.Image
		}
	}
	return ""
}

type isStabilityCustomScript_Script interface {
	isStabilityCustomScript_Script()
}

type StabilityCustomScript_GitConfig struct {
	GitConfig *GitConfig `protobuf:"bytes,1,opt,name=git_config,json=gitConfig,proto3,oneof"` // 自定义脚本（Git仓库）
}

type StabilityCustomScript_Image struct {
	Image string `protobuf:"bytes,2,opt,name=image,proto3,oneof"` // 自定义脚本（镜像）
}

func (*StabilityCustomScript_GitConfig) isStabilityCustomScript_Script() {}

func (*StabilityCustomScript_Image) isStabilityCustomScript_Script() {}

var File_common_stability_proto protoreflect.FileDescriptor

var file_common_stability_proto_rawDesc = []byte{
	0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7a, 0x0a, 0x16,
	0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x75, 0x64, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x48, 0x00, 0x52, 0x05, 0x75, 0x64, 0x69, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04,
	0x28, 0x00, 0x40, 0x01, 0x48, 0x00, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x09, 0x0a,
	0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x79, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x12, 0x32, 0x0a, 0x0a, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47,
	0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x67, 0x69, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01,
	0x48, 0x00, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74,
	0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_stability_proto_rawDescOnce sync.Once
	file_common_stability_proto_rawDescData = file_common_stability_proto_rawDesc
)

func file_common_stability_proto_rawDescGZIP() []byte {
	file_common_stability_proto_rawDescOnce.Do(func() {
		file_common_stability_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_stability_proto_rawDescData)
	})
	return file_common_stability_proto_rawDescData
}

var file_common_stability_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_common_stability_proto_goTypes = []any{
	(*StabilityCustomDevices)(nil), // 0: common.StabilityCustomDevices
	(*StabilityCustomScript)(nil),  // 1: common.StabilityCustomScript
	(*structpb.ListValue)(nil),     // 2: google.protobuf.ListValue
	(*GitConfig)(nil),              // 3: common.GitConfig
}
var file_common_stability_proto_depIdxs = []int32{
	2, // 0: common.StabilityCustomDevices.udids:type_name -> google.protobuf.ListValue
	3, // 1: common.StabilityCustomScript.git_config:type_name -> common.GitConfig
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_common_stability_proto_init() }
func file_common_stability_proto_init() {
	if File_common_stability_proto != nil {
		return
	}
	file_common_config_proto_init()
	file_common_stability_proto_msgTypes[0].OneofWrappers = []any{
		(*StabilityCustomDevices_Udids)(nil),
		(*StabilityCustomDevices_Count)(nil),
	}
	file_common_stability_proto_msgTypes[1].OneofWrappers = []any{
		(*StabilityCustomScript_GitConfig)(nil),
		(*StabilityCustomScript_Image)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_stability_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_stability_proto_goTypes,
		DependencyIndexes: file_common_stability_proto_depIdxs,
		MessageInfos:      file_common_stability_proto_msgTypes,
	}.Build()
	File_common_stability_proto = out.File
	file_common_stability_proto_rawDesc = nil
	file_common_stability_proto_goTypes = nil
	file_common_stability_proto_depIdxs = nil
}
