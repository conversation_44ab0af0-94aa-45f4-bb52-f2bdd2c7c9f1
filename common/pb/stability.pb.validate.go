// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/stability.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StabilityCustomDevices with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityCustomDevices) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityCustomDevices with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityCustomDevicesMultiError, or nil if none found.
func (m *StabilityCustomDevices) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityCustomDevices) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Devices.(type) {
	case *StabilityCustomDevices_Udids:
		if v == nil {
			err := StabilityCustomDevicesValidationError{
				field:  "Devices",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUdids()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StabilityCustomDevicesValidationError{
						field:  "Udids",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StabilityCustomDevicesValidationError{
						field:  "Udids",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUdids()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StabilityCustomDevicesValidationError{
					field:  "Udids",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StabilityCustomDevices_Count:
		if v == nil {
			err := StabilityCustomDevicesValidationError{
				field:  "Devices",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetCount() != 0 {

			if m.GetCount() < 0 {
				err := StabilityCustomDevicesValidationError{
					field:  "Count",
					reason: "value must be greater than or equal to 0",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StabilityCustomDevicesMultiError(errors)
	}

	return nil
}

// StabilityCustomDevicesMultiError is an error wrapping multiple validation
// errors returned by StabilityCustomDevices.ValidateAll() if the designated
// constraints aren't met.
type StabilityCustomDevicesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityCustomDevicesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityCustomDevicesMultiError) AllErrors() []error { return m }

// StabilityCustomDevicesValidationError is the validation error returned by
// StabilityCustomDevices.Validate if the designated constraints aren't met.
type StabilityCustomDevicesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityCustomDevicesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityCustomDevicesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityCustomDevicesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityCustomDevicesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityCustomDevicesValidationError) ErrorName() string {
	return "StabilityCustomDevicesValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityCustomDevicesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityCustomDevices.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityCustomDevicesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityCustomDevicesValidationError{}

// Validate checks the field values on StabilityCustomScript with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityCustomScript) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityCustomScript with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityCustomScriptMultiError, or nil if none found.
func (m *StabilityCustomScript) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityCustomScript) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Script.(type) {
	case *StabilityCustomScript_GitConfig:
		if v == nil {
			err := StabilityCustomScriptValidationError{
				field:  "Script",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGitConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StabilityCustomScriptValidationError{
						field:  "GitConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StabilityCustomScriptValidationError{
						field:  "GitConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGitConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StabilityCustomScriptValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StabilityCustomScript_Image:
		if v == nil {
			err := StabilityCustomScriptValidationError{
				field:  "Script",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetImage() != "" {

			if utf8.RuneCountInString(m.GetImage()) < 1 {
				err := StabilityCustomScriptValidationError{
					field:  "Image",
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StabilityCustomScriptMultiError(errors)
	}

	return nil
}

// StabilityCustomScriptMultiError is an error wrapping multiple validation
// errors returned by StabilityCustomScript.ValidateAll() if the designated
// constraints aren't met.
type StabilityCustomScriptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityCustomScriptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityCustomScriptMultiError) AllErrors() []error { return m }

// StabilityCustomScriptValidationError is the validation error returned by
// StabilityCustomScript.Validate if the designated constraints aren't met.
type StabilityCustomScriptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityCustomScriptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityCustomScriptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityCustomScriptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityCustomScriptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityCustomScriptValidationError) ErrorName() string {
	return "StabilityCustomScriptValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityCustomScriptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityCustomScript.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityCustomScriptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityCustomScriptValidationError{}
