package deviceservicelogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

const (
	constLockExpireTime = 5 * time.Second
)

type Func func() error

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		converters: []commonutils.TypeConverter{
			commonpb.StringToDeviceType(),
			commonpb.StringToPlatformType(),

			pb.StringToDeviceState(),
			pb.SqlNullStringToDeviceMetadata(),
		},
	}
}

func (l *BaseLogic) checkDeviceByUDID(udid string) (*model.Device, error) {
	device, err := l.svcCtx.DeviceModel.FindOneByUdid(l.ctx, udid)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find the device, udid: %s, error: %+v", udid, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf("the device to be acquired does not exist, udid: %s", udid),
				),
			)
		}
	}

	return device, nil
}

func (l *BaseLogic) callWithLock(key string, fn Func, opts ...redislock.Option) error {
	return caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, opts...)
}

func (l *BaseLogic) idleDevicesNotify() {
	devices, err := l.svcCtx.DeviceModel.FindIdleDevices(l.ctx)
	if err != nil {
		l.Errorf("failed to find idle devices, error: %+v", err)
		return
	}

	flag := false
	info := &pb.IdleDevicesNotifyInfo{}
	for _, device := range devices {
		switch device.Type {
		case int64(commonpb.DeviceType_REAL_PHONE):
			switch device.Platform {
			case int64(commonpb.PlatformType_ANDROID):
				info.RealPhonesOfAndroid += 1
				flag = true
			case int64(commonpb.PlatformType_IOS):
				info.RealPhonesOfIos += 1
				flag = true
			}
		case int64(commonpb.DeviceType_CLOUD_PHONE):
			switch device.Platform {
			case int64(commonpb.PlatformType_ANDROID):
				info.CloudPhonesOfAndroid += 1
				flag = true
			case int64(commonpb.PlatformType_IOS):
				info.CloudPhonesOfIos += 1
				flag = true
			}
		}
	}

	if flag {
		_info := protobuf.MarshalJSONToStringIgnoreError(info)
		if val, err := l.svcCtx.RedisNode.Publish(
			l.ctx, common.ConstChannelNameOfIdleDevicesNotify, _info,
		).Result(); err != nil {
			l.Errorf(
				"failed to send the info of idle devices notify to the channel[%s], info: %s, error: %+v",
				common.ConstChannelNameOfIdleDevicesNotify, _info, err,
			)
		} else {
			l.Infof(
				"succeeded to send the info of idle devices notify to the channel[%s], info: %s, subscribers: %d",
				common.ConstChannelNameOfIdleDevicesNotify, _info, val,
			)
		}
	} else {
		l.Infof("no need to send the info of idle devices notify")
	}
}
