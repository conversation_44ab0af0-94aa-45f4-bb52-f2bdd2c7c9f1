package hub

import (
	"io"
	"sync"
	"sync/atomic"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
)

var hub = NewHub()

func Register(c IClient) {
	hub.Register(c)
}

func UnRegister(c IClient) {
	hub.UnRegister(c)
}

func IsActiveProvider(c IClient) bool {
	return hub.IsActiveProvider(c)
}

type Hub struct {
	mutex   sync.RWMutex
	clients map[IClient]lang.PlaceholderType
	// key: providerURL, value: latest client (for providers that have providerURL)
	providerIndex map[string]IClient

	closing *atomic.Bool
}

type IClient interface {
	io.Closer
	GetProviderURL() string // 返回空字符串表示不是provider连接
	IsActive() bool
}

func NewHub() *Hub {
	h := &Hub{
		clients:       make(map[IClient]lang.PlaceholderType),
		providerIndex: make(map[string]IClient),
		closing:       new(atomic.Bool),
	}

	proc.AddShutdownListener(h.Close)
	return h
}

func (h *Hub) Register(c IClient) {
	if h.closing.Load() {
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 添加到客户端列表
	h.clients[c] = lang.Placeholder

	// 如果是provider连接，处理provider级别的注册
	providerURL := c.GetProviderURL()
	if providerURL != "" {
		// 如果已存在同一个provider的连接，先关闭旧连接
		if existingClient, exists := h.providerIndex[providerURL]; exists && existingClient.IsActive() {
			logx.Infof("closing existing provider connection for URL: %s", providerURL)
			_ = existingClient.Close()
		}

		h.providerIndex[providerURL] = c
		logx.Infof("registered new provider connection for URL: %s", providerURL)
	}
}

func (h *Hub) UnRegister(c IClient) {
	if h.closing.Load() {
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 从客户端列表中移除
	delete(h.clients, c)

	// 如果是provider连接，从provider索引中移除
	providerURL := c.GetProviderURL()
	if providerURL != "" {
		// 只有当前注册的连接才能注销
		if currentClient, exists := h.providerIndex[providerURL]; exists && currentClient == c {
			delete(h.providerIndex, providerURL)
			logx.Infof("unregistered provider connection for URL: %s", providerURL)
		}
	}
}

func (h *Hub) IsActiveProvider(c IClient) bool {
	if h.closing.Load() {
		return false
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	providerURL := c.GetProviderURL()
	if providerURL == "" {
		return false
	}

	currentClient, exists := h.providerIndex[providerURL]
	return exists && currentClient == c && currentClient.IsActive()
}

func (h *Hub) Close() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.closing.Store(true)

	// `Hub`退出时把管理的客户端全部关闭
	for c := range h.clients {
		_ = c.Close()
		delete(h.clients, c)
	}

	// 清空provider索引
	for providerURL := range h.providerIndex {
		delete(h.providerIndex, providerURL)
	}
}
