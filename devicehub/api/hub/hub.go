package hub

import (
	"io"
	"sync"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"go.uber.org/atomic"
)

var hub = NewHub()

func Register(c IClient) {
	hub.Register(c)
}

func UnRegister(c IClient) {
	hub.UnRegister(c)
}

func RegisterProvider(c IProviderClient) {
	hub.RegisterProvider(c)
}

func UnRegisterProvider(c IProviderClient) {
	hub.UnRegisterProvider(c)
}

func IsActiveProvider(c IProviderClient) bool {
	return hub.IsActiveProvider(c)
}

type Hub struct {
	mutex   sync.Mutex
	clients map[IClient]lang.PlaceholderType

	// Provider连接管理
	providerMutex sync.RWMutex
	providers     map[string]IProviderClient // key: providerURL, value: latest client

	closing *atomic.Bool
}

type IClient interface {
	io.Closer
}

type IProviderClient interface {
	IClient
	GetProviderURL() string
	IsActive() bool
}

func NewHub() *Hub {
	h := &Hub{
		clients:   make(map[IClient]lang.PlaceholderType),
		providers: make(map[string]IProviderClient),
		closing:   atomic.NewBool(false),
	}

	proc.AddShutdownListener(h.Close)
	return h
}

func (h *Hub) Register(c IClient) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[c] = lang.Placeholder
}

func (h *Hub) UnRegister(c IClient) {
	if !h.closing.Load() {
		h.mutex.Lock()
		defer h.mutex.Unlock()

		delete(h.clients, c)
		// 注：主动注销，由客户端自己做`Close`操作
		//_ = c.Close()
	}
}

func (h *Hub) RegisterProvider(c IProviderClient) {
	if h.closing.Load() {
		return
	}

	h.providerMutex.Lock()
	defer h.providerMutex.Unlock()

	providerURL := c.GetProviderURL()
	if providerURL == "" {
		logx.Warn("provider URL is empty, skipping provider registration")
		return
	}

	// 如果已存在同一个provider的连接，先关闭旧连接
	if existingClient, exists := h.providers[providerURL]; exists && existingClient.IsActive() {
		logx.Infof("closing existing provider connection for URL: %s", providerURL)
		_ = existingClient.Close()
	}

	h.providers[providerURL] = c
	logx.Infof("registered new provider connection for URL: %s", providerURL)
}

func (h *Hub) UnRegisterProvider(c IProviderClient) {
	if h.closing.Load() {
		return
	}

	h.providerMutex.Lock()
	defer h.providerMutex.Unlock()

	providerURL := c.GetProviderURL()
	if providerURL == "" {
		return
	}

	// 只有当前注册的连接才能注销
	if currentClient, exists := h.providers[providerURL]; exists && currentClient == c {
		delete(h.providers, providerURL)
		logx.Infof("unregistered provider connection for URL: %s", providerURL)
	}
}

func (h *Hub) Close() {
	h.mutex.Lock()
	h.providerMutex.Lock()
	defer h.mutex.Unlock()
	defer h.providerMutex.Unlock()

	h.closing.Store(true)

	// `Hub`退出时把管理的客户端全部关闭
	for c := range h.clients {
		_ = c.Close()
		delete(h.clients, c)
	}

	// 关闭所有provider连接
	for providerURL, c := range h.providers {
		_ = c.Close()
		delete(h.providers, providerURL)
	}
}

func (h *Hub) IsActiveProvider(c IProviderClient) bool {
	if h.closing.Load() {
		return false
	}

	h.providerMutex.RLock()
	defer h.providerMutex.RUnlock()

	providerURL := c.GetProviderURL()
	if providerURL == "" {
		return false
	}

	currentClient, exists := h.providers[providerURL]
	return exists && currentClient == c && currentClient.IsActive()
}
