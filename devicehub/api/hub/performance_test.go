package hub

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"go.uber.org/atomic"
)

// HubWithoutIndex 不使用providerIndex的Hub实现
type HubWithoutIndex struct {
	mutex   sync.RWMutex
	clients map[IClient]lang.PlaceholderType
	closing *atomic.Bool
}

func NewHubWithoutIndex() *HubWithoutIndex {
	return &HubWithoutIndex{
		clients: make(map[IClient]lang.PlaceholderType),
		closing: &atomic.Bool{},
	}
}

func (h *HubWithoutIndex) Register(c IClient) {
	if h.closing.Load() {
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[c] = lang.Placeholder

	providerURL := c.GetProviderURL()
	if providerURL != "" {
		// 遍历找到同一providerURL的其他活跃连接并关闭
		for client := range h.clients {
			if client != c && client.GetProviderURL() == providerURL && client.IsActive() {
				_ = client.Close()
			}
		}
	}
}

func (h *HubWithoutIndex) IsActiveProvider(c IClient) bool {
	if h.closing.Load() {
		return false
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	providerURL := c.GetProviderURL()
	if providerURL == "" {
		return false
	}

	// 遍历所有客户端，找到同一providerURL的活跃连接
	for client := range h.clients {
		if client.GetProviderURL() == providerURL && client.IsActive() {
			return client == c
		}
	}
	return false
}

func (h *HubWithoutIndex) Close() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.closing.Store(true)

	for c := range h.clients {
		_ = c.Close()
		delete(h.clients, c)
	}
}

// 性能测试
func BenchmarkHubWithIndex_IsActiveProvider(b *testing.B) {
	hub := NewHub()
	defer hub.Close()

	// 创建100个客户端，其中10个是provider
	clients := make([]*MockClient, 100)
	for i := 0; i < 100; i++ {
		if i < 10 {
			clients[i] = NewMockClient(fmt.Sprintf("http://provider%d:3600", i))
		} else {
			clients[i] = NewMockClient("") // 非provider客户端
		}
		hub.Register(clients[i])
	}

	// 测试查找第5个provider
	targetClient := clients[5]

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		hub.IsActiveProvider(targetClient)
	}
}

func BenchmarkHubWithoutIndex_IsActiveProvider(b *testing.B) {
	hub := NewHubWithoutIndex()
	defer hub.Close()

	// 创建100个客户端，其中10个是provider
	clients := make([]*MockClient, 100)
	for i := 0; i < 100; i++ {
		if i < 10 {
			clients[i] = NewMockClient(fmt.Sprintf("http://provider%d:3600", i))
		} else {
			clients[i] = NewMockClient("") // 非provider客户端
		}
		hub.Register(clients[i])
	}

	// 测试查找第5个provider
	targetClient := clients[5]

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		hub.IsActiveProvider(targetClient)
	}
}

// 测试不同规模下的性能差异
func BenchmarkComparison(b *testing.B) {
	sizes := []int{10, 50, 100, 500, 1000}

	for _, size := range sizes {
		b.Run(fmt.Sprintf("WithIndex_%d", size), func(b *testing.B) {
			hub := NewHub()
			defer hub.Close()

			clients := make([]*MockClient, size)
			for i := 0; i < size; i++ {
				if i < size/10 { // 10%是provider
					clients[i] = NewMockClient(fmt.Sprintf("http://provider%d:3600", i))
				} else {
					clients[i] = NewMockClient("")
				}
				hub.Register(clients[i])
			}

			targetClient := clients[0] // 测试第一个provider

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				hub.IsActiveProvider(targetClient)
			}
		})

		b.Run(fmt.Sprintf("WithoutIndex_%d", size), func(b *testing.B) {
			hub := NewHubWithoutIndex()
			defer hub.Close()

			clients := make([]*MockClient, size)
			for i := 0; i < size; i++ {
				if i < size/10 { // 10%是provider
					clients[i] = NewMockClient(fmt.Sprintf("http://provider%d:3600", i))
				} else {
					clients[i] = NewMockClient("")
				}
				hub.Register(clients[i])
			}

			targetClient := clients[0] // 测试第一个provider

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				hub.IsActiveProvider(targetClient)
			}
		})
	}
}

// 功能正确性测试
func TestHubWithoutIndexFunctionality(b *testing.T) {
	hub := NewHubWithoutIndex()
	defer hub.Close()

	providerURL := "http://test-provider:3600"

	// 创建第一个连接
	client1 := NewMockClient(providerURL)
	hub.Register(client1)

	// 验证第一个连接是活跃的
	if !hub.IsActiveProvider(client1) {
		b.Error("client1 should be active")
	}

	// 创建第二个连接（模拟重连）
	client2 := NewMockClient(providerURL)
	hub.Register(client2)

	// 等待一小段时间确保处理完成
	time.Sleep(10 * time.Millisecond)

	// 验证第一个连接被关闭
	if !client1.IsClosed() {
		b.Error("client1 should be closed when client2 registers")
	}

	// 验证第二个连接是活跃的
	if !hub.IsActiveProvider(client2) {
		b.Error("client2 should be active")
	}
}
