package hub

import (
	"sync"
	"testing"
	"time"

	"go.uber.org/atomic"
)

// MockClient 模拟的客户端
type MockClient struct {
	providerURL string
	active      *atomic.Bool
	closed      *atomic.Bool
	mu          sync.Mutex
}

func NewMockClient(providerURL string) *MockClient {
	return &MockClient{
		providerURL: providerURL,
		active:      atomic.NewBool(true),
		closed:      atomic.NewBool(false),
	}
}

func (m *MockClient) GetProviderURL() string {
	return m.providerURL
}

func (m *MockClient) IsActive() bool {
	return m.active.Load()
}

func (m *MockClient) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.active.Store(false)
	m.closed.Store(true)
	return nil
}

func (m *MockClient) IsClosed() bool {
	return m.closed.Load()
}

func TestProviderConnectionReplacement(t *testing.T) {
	hub := NewHub()
	defer hub.Close()

	providerURL := "http://test-provider:3600"

	// 创建第一个连接
	client1 := NewMockClient(providerURL)
	hub.Register(client1)

	// 验证第一个连接是活跃的
	if !hub.IsActiveProvider(client1) {
		t.Error("client1 should be active")
	}

	// 创建第二个连接（模拟重连）
	client2 := NewMockClient(providerURL)
	hub.Register(client2)

	// 等待一小段时间确保处理完成
	time.Sleep(10 * time.Millisecond)

	// 验证第一个连接被关闭
	if !client1.IsClosed() {
		t.Error("client1 should be closed when client2 registers")
	}

	// 验证第二个连接是活跃的
	if !hub.IsActiveProvider(client2) {
		t.Error("client2 should be active")
	}

	// 验证第一个连接不再是活跃的
	if hub.IsActiveProvider(client1) {
		t.Error("client1 should not be active after client2 registers")
	}
}

func TestProviderUnregistration(t *testing.T) {
	hub := NewHub()
	defer hub.Close()

	providerURL := "http://test-provider:3600"

	// 创建连接
	client := NewMockClient(providerURL)
	hub.Register(client)

	// 验证连接是活跃的
	if !hub.IsActiveProvider(client) {
		t.Error("client should be active")
	}

	// 注销连接
	hub.UnRegister(client)

	// 验证连接不再是活跃的
	if hub.IsActiveProvider(client) {
		t.Error("client should not be active after unregistration")
	}
}

func TestMultipleProvidersIndependence(t *testing.T) {
	hub := NewHub()
	defer hub.Close()

	providerURL1 := "http://test-provider1:3600"
	providerURL2 := "http://test-provider2:3600"

	// 创建两个不同provider的连接
	client1 := NewMockClient(providerURL1)
	client2 := NewMockClient(providerURL2)

	hub.Register(client1)
	hub.Register(client2)

	// 验证两个连接都是活跃的
	if !hub.IsActiveProvider(client1) {
		t.Error("client1 should be active")
	}
	if !hub.IsActiveProvider(client2) {
		t.Error("client2 should be active")
	}

	// 注销第一个连接
	hub.UnRegister(client1)

	// 验证只有第一个连接被注销
	if hub.IsActiveProvider(client1) {
		t.Error("client1 should not be active after unregistration")
	}
	if !hub.IsActiveProvider(client2) {
		t.Error("client2 should still be active")
	}
}

func TestNonProviderClient(t *testing.T) {
	hub := NewHub()
	defer hub.Close()

	// 创建一个非provider连接（providerURL为空）
	client := NewMockClient("")
	hub.Register(client)

	// 验证非provider连接不被认为是活跃的provider
	if hub.IsActiveProvider(client) {
		t.Error("non-provider client should not be active provider")
	}
}
