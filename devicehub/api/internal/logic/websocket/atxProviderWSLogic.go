package websocket

import (
	"bytes"
	"context"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/zrpc"
	"go.uber.org/atomic"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/hub"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

const (
	// Time allowed writing a message to the peer.
	writeWait = 10 * time.Second

	// Time allowed reading the next pong message from the peer.
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait.
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer.
	maxMessageSize = 512

	// send buffer size
	bufSize = 256

	closeText = "websocket server is closing"

	keyCommand       = "command"
	handShakeCommand = "handshake"
	updateCommand    = "update"
	pingCommand      = "ping"
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
	pong    = []byte("pong")
	failure = []byte(`{"success": false}`)

	_ hub.IClient         = (*ATXProviderWSLogic)(nil)
	_ hub.IProviderClient = (*ATXProviderWSLogic)(nil)
)

type ATXProviderWSLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	conn     *websocket.Conn
	messages chan []byte

	providerURL string

	close     chan lang.PlaceholderType
	closeOnce sync.Once
	closed    *atomic.Bool
}

func NewATXProviderWSLogic(ctx context.Context, svcCtx *svc.ServiceContext, conn *websocket.Conn) *ATXProviderWSLogic {
	l := &ATXProviderWSLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		conn:     conn,
		messages: make(chan []byte, bufSize),
		close:    make(chan lang.PlaceholderType),
		closed:   atomic.NewBool(false),
	}
	hub.Register(l)

	return l
}

// GetProviderURL implements hub.IProviderClient interface
func (l *ATXProviderWSLogic) GetProviderURL() string {
	return l.providerURL
}

// IsActive implements hub.IProviderClient interface
func (l *ATXProviderWSLogic) IsActive() bool {
	return !l.closed.Load()
}

// shouldHandleOffline 检查当前连接是否应该处理离线事件
// 只有当前连接是该provider的活跃连接时才应该处理离线事件
func (l *ATXProviderWSLogic) shouldHandleOffline() bool {
	if l.providerURL == "" {
		return false
	}

	// 通过hub检查当前连接是否仍然是活跃的provider连接
	return hub.IsActiveProvider(l)
}

func (l *ATXProviderWSLogic) ProviderWS() {
	threading.GoSafeCtx(l.ctx, l.receive)
	threading.GoSafeCtx(l.ctx, l.send)

	<-l.close
}

func (l *ATXProviderWSLogic) Close() error {
	var err error

	l.closeOnce.Do(
		func() {
			l.Infof("ready to close the client of provider websocket, provider_url: %s", l.providerURL)

			defer close(l.close)

			// set to be closed
			l.closed.Store(true)

			// send a close message
			err = l.conn.WriteControl(
				websocket.CloseMessage,
				websocket.FormatCloseMessage(websocket.CloseNormalClosure, closeText),
				time.Now().Add(time.Second),
			)
			// close the connection of websocket
			_ = l.conn.Close()

			// unregister current logic
			hub.UnRegister(l)
			hub.UnRegisterProvider(l)
		},
	)

	return err
}

func (l *ATXProviderWSLogic) drain() {
	// drain the channel of messages
	for range l.messages {
	}
}

func (l *ATXProviderWSLogic) receive() {
	defer func() {
		if !l.closed.Load() && l.shouldHandleOffline() {
			l.Infof("the provider is offline, url: %s", l.providerURL)

			// all operations must be completed before `Close`, otherwise the context will be canceled.
			if _, err := l.svcCtx.DeviceHubRPC.ModifyDeviceByProviderOffline(
				l.ctx, &pb.ModifyDeviceByProviderOfflineReq{
					Provider:     l.providerURL,
					ProviderType: pb.ProviderType_ATX,
				},
			); err != nil {
				l.Errorf("failed to modify devices of the offline provider, url: %s, error: %+v", l.providerURL, err)
			}

			if err := l.Close(); err != nil {
				l.Errorf(
					"failed to close the websocket connection of the offline provider, url: %s, error: %+v",
					l.providerURL, err,
				)
			}
		} else if !l.closed.Load() {
			l.Infof("skipping offline handling for provider %s as it's no longer the active connection", l.providerURL)
		}

		// drain the channel of messages
		close(l.messages)
		l.drain()
	}()

	l.conn.SetReadLimit(maxMessageSize)
	_ = l.conn.SetReadDeadline(time.Now().Add(pongWait))
	l.conn.SetPongHandler(func(string) error { _ = l.conn.SetReadDeadline(time.Now().Add(pongWait)); return nil })

	for {
		select {
		case <-l.ctx.Done():
			l.Errorf("got a done signal of context, error: %v", l.ctx.Err())
			return
		case <-l.close:
			l.Info("got a close signal of channel")
			return
		default:
			messageType, message, err := l.conn.ReadMessage()
			if err != nil {
				l.Errorf("got an error while reading message from websocket, error: %v", err)
				if websocket.IsUnexpectedCloseError(
					err, websocket.CloseNormalClosure, websocket.CloseGoingAway, websocket.CloseAbnormalClosure,
				) {
					l.Errorf("got an unexpected close error from websocket: %v", err)
				}

				return
			}

			l.Infof("got a message from websocket, type: %d, message: %s", messageType, message)

			message = bytes.TrimSpace(bytes.Replace(message, newline, space, -1))
			l.messages <- message
		}
	}
}

func (l *ATXProviderWSLogic) send() {
	ticker := timewheel.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		//_ = l.Close()
	}()

	for {
		select {
		case message, ok := <-l.messages:
			_ = l.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// the message channel has been closed.
				_ = l.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			resp, err := l.handle(message)
			if err != nil {
				l.Errorf("failed to handle the message, message: %s, error: %v", message, err)
				resp = []byte(err.Error())
			}

			w, err := l.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				l.Errorf("failed to get the next writer, error: %v", err)
				return
			}

			if _, err = w.Write(resp); err != nil {
				l.Errorf("failed to write message, error: %v", err)
			}

			if err = w.Close(); err != nil {
				l.Errorf("failed to close the writer, error: %v", err)
				return
			}
		case <-ticker.C:
			_ = l.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := l.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				l.Errorf("failed to send ping message, error: %v", err)
				return
			}
		case <-l.ctx.Done():
			l.Errorf("got a done signal of context, error: %v", l.ctx.Err())
			return
		case <-l.close:
			l.Info("got a close signal of channel")
			return
		}
	}
}

func (l *ATXProviderWSLogic) handle(in []byte) (out []byte, err error) {
	var m map[string]any
	if err = jsonx.Unmarshal(in, &m); err != nil {
		return nil, err
	}

	command, ok := m[keyCommand]
	if !ok {
		return nil, errors.Errorf("the required field[%s] is missing from the message: %s", keyCommand, in)
	}

	switch command {
	case handShakeCommand:
		// handshake command content:
		// {"command": "handshake", "name": "pyclient", "owner": "<EMAIL>", "secret": "", "url": "http://**************:3600", "priority": 2}

		var message types.HandshakeCommandMessage
		if err = jsonx.Unmarshal(in, &message); err != nil {
			return nil, err
		}

		return l.handshakeHandler(&message)
	case updateCommand:
		// update command content:
		// iOS:
		// 	prepare: {"colding":false,"command":"update","platform":"apple","properties":{"brand":"Apple","name":"new的iPhone","product":"iPhone 14 Pro Max","serial":"YRQPRX7V1","version":"16.3.1"},"provider":null,"udid":"00008120-000C19242E90C01E"}
		// 	ready:   {"command":"update","platform":"apple","properties":{"ip":"**************","sdkVersion":"16.4","version":"16.3.1"},"provider":{"wdaUrl":"http://**************:20002"},"udid":"00008120-000C19242E90C01E"}
		// 	fatal:   {"command":"update","platform":"apple","provider":null,"udid":"00008120-000C19242E90C01E"}
		// Android:
		// 	online:  {"colding":false,"command":"update","platform":"android","properties":{"brand":"HUAWEI","model":"ANG-AN00","name":"ANG-AN00","serial":"NBLDU20C19010200","version":"10"},"provider":{"atxAgentAddress":"**************:20001","remoteConnectAddress":"**************:20005","whatsInputAddress":"**************:20004"},"udid":"NBLDU20C19010200"}
		// 	offline: {"command":"update","platform":"android","provider":null,"udid":"NBLDU20C19010200"}

		var message types.UpdateCommandMessage
		if err = jsonx.Unmarshal(in, &message); err != nil {
			return nil, err
		}

		return l.updateHandler(&message)
	case pingCommand:
		return pong, nil
	default:
		l.Warnf("unknown command: %s", in)
	}

	return out, nil
}

func (l *ATXProviderWSLogic) handshakeHandler(message *types.HandshakeCommandMessage) ([]byte, error) {
	if message == nil {
		return failure, nil
	}

	oldProviderURL := l.providerURL
	l.providerURL = message.URL

	// 注册到provider管理器，这会自动处理重复连接的问题
	hub.RegisterProvider(l)

	if oldProviderURL != "" && oldProviderURL != message.URL {
		l.Infof("the provider has been changed, name: %s, url: %q => %q", message.Name, oldProviderURL, message.URL)
	} else {
		l.Infof("the provider is online, name: %s, url: %s", message.Name, message.URL)
	}

	return jsonx.MarshalIgnoreError(
		&ProviderWSResp{
			Success: true,
			ID:      l.providerURL,
		},
	), nil
}

func (l *ATXProviderWSLogic) updateHandler(message *types.UpdateCommandMessage) ([]byte, error) {
	platform := common.ConvertToPlatformType(message.Platform)
	req := &pb.CreateOrModifyDeviceReq{
		Udid:         message.UDID,
		Type:         commonpb.DeviceType_REAL_PHONE, // the devices reported by the atx-*-provider are all real phones
		Platform:     platform,
		Provider:     l.providerURL,
		ProviderType: pb.ProviderType_ATX, // the devices reported by the atx-*-provider
	}

	if message.Provider == nil {
		// device offline
		req.State = pb.DeviceState_OFFLINE
	} else {
		// device update
		req.State = pb.DeviceState_IDLE

		switch platform {
		case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
			req.RemoteAddress = message.Provider.RemoteConnectAddress
		case commonpb.PlatformType_IOS:
			req.RemoteAddress = message.Provider.WdaURL
		}
	}
	if message.Properties != nil {
		req.Name = message.Properties.Name
		req.Brand = message.Properties.Brand
		req.Model = message.Properties.Model
		req.Serial = message.Properties.Serial
		req.Version = message.Properties.Version
	}

	resp := &ProviderWSResp{
		Success: true,
		ID:      l.providerURL,
		UDID:    message.UDID,
	}
	if err := caller.RetryDo(
		caller.MaxRetryCount, func() error {
			_, err := l.svcCtx.DeviceHubRPC.CreateOrModifyDevice(
				l.ctx, req, zrpc.WithCallTimeout(common.ConstRPCModifyDeviceTimeout),
			)
			return err
		},
	); err != nil {
		resp.Success = false
		return jsonx.MarshalIgnoreError(resp), err
	}

	return jsonx.MarshalIgnoreError(resp), nil
}
