package config

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

type Config struct {
	rest.RestConf

	Validator types.ValidatorConfig

	DeviceHub zrpc.RpcClientConf
}

func (c Config) ListenOn() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

func (c Config) LogConfig() logx.LogConf {
	return c.RestConf.ServiceConf.Log
}
