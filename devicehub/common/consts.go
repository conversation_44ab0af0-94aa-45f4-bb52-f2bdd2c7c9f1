package common

import "time"

const (
	ConstChannelNameOfIdleDevicesNotify = "chan:devicehub:idleDevicesNotify"

	ConstLockWatchDevice          = "lock:watch:device"
	ConstLockDeviceUDIDPrefix     = "lock:device:udid"     // `device`
	ConstLockDeviceProviderPrefix = "lock:device:provider" // `device`

	ConstBrandOfHuaweiCloud = "HUAWEICLOUD" // 华为云
	ConstPortTypeOfADB      = "adb"

	ConstLockExpireTime                = 10 * time.Second
	ConstLockTimeout                   = 6 * time.Second
	ConstRPCModifyDeviceTimeout        = 7 * time.Second
	ConstRPCSearchAcquireDeviceTimeout = 6 * time.Second
	ConstRPCAcquireDeviceTimeout       = 6 * time.Second
	ConstRPCReleaseDeviceTimeout       = 7 * time.Second
)

type DeviceField string

const (
	DeviceFieldOfUDID     DeviceField = "udid"
	DeviceFieldOfType     DeviceField = "type"
	DeviceFieldOfPlatform DeviceField = "platform"
	DeviceFieldOfState    DeviceField = "state"
)

var DeviceStates = []DeviceState{Idle, InUse, Releasing, Offline, Reserved}

type DeviceType int8

const (
	RealPhone  DeviceType = iota + 1 // 真机
	CloudPhone                       // 云手机
)

type DeviceTypeZH string

const (
	RealPhoneZH  DeviceTypeZH = "真机"
	CloudPhoneZH DeviceTypeZH = "云手机"
)

type DevicePlatform string

const (
	Android   DevicePlatform = "Android"
	IOS       DevicePlatform = "iOS"
	HarmonyOS DevicePlatform = "HarmonyOS" // 鸿蒙
)

type DeviceState string

const (
	Idle      DeviceState = "IDLE"      // 空闲中
	InUse     DeviceState = "IN_USE"    // 使用中
	Releasing DeviceState = "RELEASING" // 释放中
	Offline   DeviceState = "OFFLINE"   // 已下线
	Reserved  DeviceState = "RESERVED"  // 已预留
)

type DeviceStateZH string

const (
	IdleZH      DeviceStateZH = "空闲中"
	InUseZH     DeviceStateZH = "使用中"
	ReleasingZH DeviceStateZH = "释放中"
	OfflineZH   DeviceStateZH = "已下线"
	ReservedZH  DeviceStateZH = "已预留"
)

type ProviderType int8

const (
	ATX         ProviderType = iota + 1 // atx-*-provider
	HUAWEICLOUD                         // 华为云
)

type CPHStatus int32

const (
	CPHStatusDefault        CPHStatus = 0  // 创建中
	CPHStatusCreating       CPHStatus = 1  // 创建中
	CPHStatusRunning        CPHStatus = 2  // 运行中
	CPHStatusResetting      CPHStatus = 3  // 重置中
	CPHStatusRebooting      CPHStatus = 4  // 重启中
	CPHStatusFrozen         CPHStatus = 6  // 冻结
	CPHStatusShuttingDown   CPHStatus = 7  // 正在关机
	CPHStatusShutdown       CPHStatus = 8  // 已关机
	CPHStatusResetFailed    CPHStatus = -5 // 重置失败
	CPHStatusRebootFailed   CPHStatus = -6 // 重启失败
	CPHStatusPhoneException CPHStatus = -7 // 手机异常
	CPHStatusCreationFailed CPHStatus = -8 // 创建失败
	CPHStatusShutdownFailed CPHStatus = -9 // 关机失败
)

type CPHJobStatus int32

const (
	CPHJobStatusRunning CPHJobStatus = 1  // 运行中
	CPHJobStatusSuccess CPHJobStatus = 2  // 成功
	CPHJobStatusFailure CPHJobStatus = -1 // 失败
)
